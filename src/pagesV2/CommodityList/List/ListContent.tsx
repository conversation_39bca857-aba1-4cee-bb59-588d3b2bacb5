import React, { useMemo } from 'react'
import { View, StyleSheet } from 'react-native'
import isEqual from 'lodash.isequal'
import {
  CommodityDisplayProductItem,
  CommodityDisplayProducts,
} from '../../../typesV2/commodity'
import Commodity from '../../../componentsV2/Categories/Commodity'
import styled from 'styled-components'

type Props = {
  commodities: CommodityDisplayProducts
  onClickEventReport?: (commodity: CommodityDisplayProductItem) => void
  onItemShowEventReport?: (commodity: CommodityDisplayProductItem) => void
}
const Wrapper = styled(View)`
  width: 100%;
  overflow: hidden;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-top: 12px;
`

const styles = StyleSheet.create({
  list: {
    flex: 1,
  },
  mrm: {
    marginRight: 12
  }
});

const ListContent: React.FC<Props> = ({
  commodities,
  onClickEventReport,
  onItemShowEventReport,
}) => {
  const renderItem = ({
    item,
    index,
  }: {
    item: CommodityDisplayProductItem
    index: number
  }) => {
    return (
      <Commodity
        commodity={item}
        key={item.id + item.commodityName}
        index={index}
        onClickEventReport={onClickEventReport}
        onShowEventReport={onItemShowEventReport}
      />
    )
  }

  const getColumn = useMemo(
    () => (numberOfN: number, colIndex: number) => {
      return commodities.filter((_, index) => index % numberOfN === colIndex)
    },
    [commodities]
  )

  return (
    <Wrapper>
      <View style={[styles.list, styles.mrm]}>
        {getColumn(2, 0).map((commodity, index) =>
          renderItem({ item: commodity, index })
        )}
      </View>
      <View style={[styles.list]}>
        {getColumn(2, 1).map((commodity, index) =>
          renderItem({ item: commodity, index })
        )}
      </View>
    </Wrapper>
  )
}

export default React.memo(ListContent, isEqual)
