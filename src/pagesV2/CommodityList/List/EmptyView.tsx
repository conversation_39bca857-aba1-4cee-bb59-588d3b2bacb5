import React from 'react'
import { Text } from '@xmly/rn-components'
import { Image, View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import icon_login_empty_book_dark from '../../../appImagesV2/icon_login_empty_book_dark'

type Props = {}

const Wrapper = styled(View)`
  flex: 1;
  align-items: center;
  justify-content: center;
`

const Icon = styled(Image)`
  width: 76px;
  height: 76px;
  margin-bottom: 7px;
`

const Label = styled(Text)`
  font-size: 13px;
  font-weight: 400;
  color: #999999;
`

const EmptyView: React.FC<Props> = (props) => {
  return (
    <Wrapper>
      <Icon source={icon_login_empty_book_dark} />
      <Label>暂无商品</Label>
    </Wrapper>
  )
}

export default React.memo(EmptyView, isEqual)
