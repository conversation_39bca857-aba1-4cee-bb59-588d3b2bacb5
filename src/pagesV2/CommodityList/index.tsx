import React, { useCallback, useEffect, useState } from 'react'
import { NativeModules, Platform, View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import HeaderPlain from '../../componentsV2/common/Headers/HeaderPlain'
import getECommerceComponentListByPageId from '../../servicesV2/getECommerceComponentListByPageId'
import { ECommerceComponentVos } from '../../typesV2/ECommerce'
import CommodityManager from '../../modulesV2/commodityManager'
import {
  CommodityDisplayProducts,
  CommodityDisplayTabItem,
} from '../../typesV2/commodity'
import TabBar from './TabBar'
import SubTabBar from './SubTabBar'
import List from './List'
import { useNavigation, useRoute } from '@react-navigation/native'
import { FetchStatus } from '../../typesV2/common'
import xmlog from '../../utilsV2/xmlog'
import PageAnalytics from '@xmly/react-native-page-analytics'
import { ThemeStyle } from '../../typesV2/themeInfo'
// import rnEnv from '../../../rnEnv'
import { usePageOnResume } from '@xmly/rn-utils'
type Props = {}

const Container = styled(View)`
  flex: 1;
  background-color: ${(props: { theme: ThemeStyle }) =>
    props.theme.container.bg_color};
`

const commodityManagerMap: {
  [id: number]: CommodityManager
} = {}

let headerTitle = '';
const CommodityList: React.FC<Props> = (props) => {
  const route = useRoute();
  const { pageId } = route.params as { pageId: number }
  const { pageTitle = '' } = route.params as { pageTitle: string }
  const navigation = useNavigation()
  const [componentList, setComponentList] = useState<ECommerceComponentVos[]>(
    []
  )
  const [hasMore, setHasMore] = useState(true)
  const [fetchStatus, setFetchStatus] = useState<FetchStatus>(
    FetchStatus.loading
  )
  const [currentTabIndex, setCurrentTabIndex] = useState(0)
  const [currentSubTabIndex, setCurrentSubTabIndex] = useState(0)
  const [currentSubTabs, setCurrentSubTabs] = useState<
    CommodityDisplayTabItem[]
  >([])

  const [displayProducts, setDisplayProducts] =
    useState<CommodityDisplayProducts>([])

  usePageOnResume(() => {
    if (Platform.OS == 'ios') {
      NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
    }
  })

  const handlePressBack = () => {
    navigation.goBack()
  }

  const customPageView = useCallback(() => {
    // 任务中心_会员权益  页面展示
    xmlog.pageView(46406, 'task_vip', { currPage: 'task_vip' })
  }, [])

  const customPageExit = useCallback(() => {
    // 任务中心_会员权益  页面离开
    xmlog.pageExit(46407, { currPage: 'task_vip' })
  }, [])

  PageAnalytics.useScreen({
    customPageView,
    customPageExit,
    ...props,
  })

  const fetchComponentList = async () => {
    try {
      setHasMore(true)
      setFetchStatus(FetchStatus.loading)
      // const pageId = rnEnv.isOnline() ? 6 : 62
      const res = await getECommerceComponentListByPageId(pageId)
      if (res.data?.componentVos && res.data?.componentVos.length > 0) {
        const componentList = res.data?.componentVos
        setComponentList(componentList)
        componentList.forEach(
          (component) =>
            (commodityManagerMap[component.id] = new CommodityManager())
        )

        const firstTabComponentId = componentList[0].id
        const { displayTabs, productForDisplayInfo } =
          await commodityManagerMap[firstTabComponentId].initProduct(
            firstTabComponentId
          )
        setCurrentSubTabs(displayTabs)
        setDisplayProducts(productForDisplayInfo.list)
        setHasMore(productForDisplayInfo.hasMore)
      } else {
        setCurrentSubTabs([])
        setDisplayProducts([])
        setHasMore(false)
      }
      setFetchStatus(FetchStatus.success)
    } catch (err) {
      console.log(err)
      setFetchStatus(FetchStatus.fail)
    }
  }

  // 切换一级tab
  const changeTab = async (index: number) => {
    const selectTab = componentList[index]
    const commodityManager = commodityManagerMap[selectTab.id]
    setFetchStatus(FetchStatus.loading)
    setHasMore(true)
    setCurrentTabIndex(index)
    setCurrentSubTabIndex(0)
    if (commodityManager.isInit) {
      const displayTabs = commodityManager.displayTabs

      const productForDisplayInfo =
        commodityManager.getECommerceProductDataForDisplay(
          displayTabs[0] ? displayTabs[0].id : selectTab.id
        )
      setCurrentSubTabs(displayTabs)
      setDisplayProducts(productForDisplayInfo.list)
      setHasMore(productForDisplayInfo.hasMore)
    } else {
      const { displayTabs, productForDisplayInfo } =
        await commodityManager.initProduct(selectTab.id)
      setCurrentSubTabs(displayTabs)
      setDisplayProducts(productForDisplayInfo.list)
      setHasMore(productForDisplayInfo.hasMore)
    }
    setFetchStatus(FetchStatus.success)
  }

  // 切换二级tab
  const changeSubTab = async (index: number) => {
    try {
      setFetchStatus(FetchStatus.loading)
      setHasMore(true)
      setCurrentSubTabIndex(index)
      const selectTab = componentList[currentTabIndex] // 找到当前的一级tab
      const commodityManager = commodityManagerMap[selectTab.id] // 找到当前的一级tab 对应的commodityManager
      const targetSubTab = currentSubTabs[index]
      let productForDisplayInfo =
        commodityManager.getECommerceProductDataForDisplay(targetSubTab.id)
      if (productForDisplayInfo.list.length === 0) {
        productForDisplayInfo =
          await commodityManager.getECommerceProductListByPoolId({
            poolId: targetSubTab.poolId,
            componentId: targetSubTab.id,
          })
      }
      setDisplayProducts(productForDisplayInfo.list)
      setHasMore(productForDisplayInfo.hasMore)
      setFetchStatus(FetchStatus.success)
    } catch (err) {
      console.log(err)
      setFetchStatus(FetchStatus.fail)
    }
  }

  useEffect(() => {
    fetchComponentList()
    if (pageTitle === '') {
       headerTitle = '权益列表'
    }
  }, [])

  const handlePressTabBar = (index: number) => {
    changeTab(index)
  }

  const handlePressSubTabBar = (index: number) => {
    changeSubTab(index)
  }

  const handleLoadMore = async () => {
    try {
      if (componentList.length === 0 || displayProducts.length === 0) return
      const commodityManager =
        commodityManagerMap[componentList[currentTabIndex].id]
      console.log('currentSubTabs', currentSubTabs)
      const currentSubTabInfo =
        currentSubTabs.length > 0
          ? currentSubTabs[currentSubTabIndex]
          : componentList[currentTabIndex]
      const currentDisplayTabProductInfo =
        commodityManager.getECommerceProductDataForDisplay(currentSubTabInfo.id)
      if (currentDisplayTabProductInfo.hasMore) {
        console.log('load more start ～')

        const productsForDisplayInfoNew =
          await commodityManager.getECommerceProductListByPoolId({
            pageNo: currentDisplayTabProductInfo.pageNo + 1,
            poolId: currentSubTabInfo.poolId,
            componentId: currentSubTabInfo.id,
          })

        setDisplayProducts(productsForDisplayInfoNew.list)
        setHasMore(productsForDisplayInfoNew.hasMore)
      } else {
        console.log('commodity list no more')
        return
      }
    } catch (err) {
      console.log(err)
    }
  }

  const handleReload = () => {
    fetchComponentList()
  }

  return (
    <Container>
      <HeaderPlain label={headerTitle} onPressBack={handlePressBack} />
      <TabBar
        componentList={componentList}
        currentTabIndex={currentTabIndex}
        onPressTabBar={handlePressTabBar}
      />
      <SubTabBar
        currentSubTabIndex={currentSubTabIndex}
        currentSubTabs={currentSubTabs}
        onPressSubTabBar={handlePressSubTabBar}
      />
      <List
        currentTabId={
          componentList.length > 0 ? componentList[currentTabIndex].id : -1
        }
        onReload={handleReload}
        commodities={displayProducts}
        onLoadMore={handleLoadMore}
        hasMore={hasMore}
        fetchStatus={fetchStatus}
      />
    </Container>
  )
}

export default React.memo(CommodityList, isEqual)
