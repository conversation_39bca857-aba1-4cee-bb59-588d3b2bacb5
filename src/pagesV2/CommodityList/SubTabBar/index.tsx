import React from 'react'
import { ScrollView, View } from 'react-native'
import isEqual from 'lodash.isequal'
import { CommodityDisplayTabItem } from '../../../typesV2/commodity'
import styled from 'styled-components'
import SubTabBarItem from './SubTabBarItem'
import { ThemeStyle } from '../../../typesV2/themeInfo'

type Props = {
  currentSubTabIndex: number
  currentSubTabs: CommodityDisplayTabItem[]
  onPressSubTabBar: (index: number) => void
}

const Wrapper = styled(View)`
  padding: 12px;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.bg_color};
`

const SubTabBar: React.FC<Props> = ({
  currentSubTabIndex,
  currentSubTabs,
  onPressSubTabBar,
}) => {
  if (currentSubTabs.length > 1) {
    return (
      <Wrapper>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {currentSubTabs.map((tab, index) => (
            <SubTabBarItem
              onPress={onPressSubTabBar}
              tabId={tab.id}
              tabLabel={tab.name}
              key={tab.id}
              currentSubTabIndex={currentSubTabIndex}
              index={index}
            />
          ))}
        </ScrollView>
      </Wrapper>
    )
  }
  return null
}

export default React.memo(SubTabBar, isEqual)
