import React, { useContext } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { DarkModeContext } from '../../../contextV2/darkModeContext'

type Props = {
  currentSubTabIndex: number
  index: number
  tabId: number
  tabLabel: string
  onPress: (index: number) => void
}

const Wrapper = styled(Touch)`
  padding: 3.5px 16px;
  border-radius: 15px;
  margin-right: 16px;
`

const Label = styled(Text)`
  font-size: 12px;
  font-weight: 500;
  color: #333333;
`

const SubTabBarItem: React.FC<Props> = ({
  tabLabel,
  index,
  currentSubTabIndex,
  onPress,
}) => {
  const { isDarkMode } = useContext(DarkModeContext)
  const isCurrent = index === currentSubTabIndex

  const backgroundColor = isCurrent
    ? isDarkMode
      ? '#1e1e1e'
      : '#F6F7F8'
    : 'transparent'

  const labelColor = isCurrent ? '#ff4c2e' : isDarkMode ? '#cfcfcf' : '#333333'

  const handlePress = () => {
    onPress(index)
  }

  return (
    <Wrapper style={{ backgroundColor }} onPress={handlePress}>
      <Label
        style={{
          includeFontPadding: false,
          textAlignVertical: 'center',
          color: labelColor,
          fontWeight: isCurrent ? 'bold' : '400',
        }}
      >
        {tabLabel}
      </Label>
    </Wrapper>
  )
}

export default React.memo(SubTabBarItem, isEqual)
