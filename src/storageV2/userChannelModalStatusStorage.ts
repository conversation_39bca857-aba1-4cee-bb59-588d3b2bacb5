import dayjs from 'dayjs'
import storage from '.'

// 前端本地存储用户打开各个渠道弹窗的时间
const Key = 'userChannelModalStatus'

const getUserChannelModalStatusStorage = async (channelName: string) => {
  try {
    const res = await storage.get(`${Key}-${channelName}`)
    const resParsed = JSON.parse(res)
    if (!resParsed) return ''
    return resParsed
  } catch (error) {
    return ''
  }
}
const setUserChannelModalStatusStorage = (channelName: string) => {
  const date = dayjs().format('YYYY-MM-DD')
  storage.set(`${Key}-${channelName}`, date)
}

export default {
  get: getUserChannelModalStatusStorage,
  set: setUserChannelModalStatusStorage,
}
