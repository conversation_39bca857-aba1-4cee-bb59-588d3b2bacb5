import storage from "."
import { UserSignStatusType } from "../typesV2/userSignStatusType"

const Key = 'userSignStatus'


const getUserSignStatus = async () => {
  try {
    const res = await storage.get(`${ Key }`)
    return JSON.parse(res)
  } catch (error) {
    setUserSignStatus(UserSignStatusType.new)
    return UserSignStatusType.new
  }
}

const setUserSignStatus = (status: UserSignStatusType) => {
  storage.set(`${ Key }`, status)
}

export { getUserSignStatus, setUserSignStatus }
