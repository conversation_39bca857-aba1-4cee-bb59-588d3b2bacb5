import storage from '.'

const Key = 'monthlyTicketGuideStatus'

let monthlyTicketGuideStatusCache = ''

const getMonthlyTicketGuideStatus = async () => {
  try {
    if (monthlyTicketGuideStatusCache) return monthlyTicketGuideStatusCache
    const res = await storage.get(`${Key}`)
    const data = JSON.parse(res)
    monthlyTicketGuideStatusCache = data
    if (!monthlyTicketGuideStatusCache) {
      setMonthlyTicketGuideStatus('1')
    }
    return monthlyTicketGuideStatusCache
  } catch (error) {
    return ''
  }
}

const setMonthlyTicketGuideStatus = (status: '1') => {
  storage.set(`${Key}`, status)
}

export { getMonthlyTicketGuideStatus, setMonthlyTicketGuideStatus }
