import dayjs from 'dayjs'
import storage from '.'

const Key = 'notificationGuideStorage'

const getNotificationGuideStorage = async () => {
  try {
    const res = await storage.get(`${Key}`)
    const resParsed = JSON.parse(res)
    if (!resParsed) return ''
    return resParsed
  } catch (error) {
    return ''
  }
}

const setNotificationGuideStorage = () => {
  const date = dayjs().format('YYYY-MM-DD')
  storage.set(`${Key}`, date)
}

export default {
  get: getNotificationGuideStorage,
  set: setNotificationGuideStorage,
}
