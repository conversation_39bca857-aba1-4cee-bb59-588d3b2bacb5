import dayjs from 'dayjs'
import storage from '.'
import userInfoDetail from '../modulesV2/userInfoDetail'
import { TaskItemType } from '../typesV2/taskList'

type TodayHasShownTaskListStorageData = {
  [key: string]: { date: number; normalIds: string; adDpTaskIds: string }
}

const Key = 'todayHasShownTaskListStorage'

const getCurrentUserStorageKey = ({ aid, uid }: { aid: number; uid: number }) => `${uid}+${aid}`

const getStorageData = async () => {
  try {
    const res = await storage.get(Key)
    const data = JSON.parse(res) as TodayHasShownTaskListStorageData | null
    if (data) {
      return data
    }
    throw new Error('empty data')
  } catch (err) {
    return {}
  }
}

const getHasShownTaskList = async ({ aid }: { aid: number }) => {
  try {
    const uid = userInfoDetail.getDetail().uid || -1
    const originData = await getStorageData()
    console.log('getHasShownTaskList', originData)
    const currentUserData = originData[getCurrentUserStorageKey({ aid, uid })]
    console.log('currentUserData', currentUserData)
    if (currentUserData && dayjs(currentUserData.date).isSame(dayjs(), 'day')) {
      const hasShownNormalTaskIds = currentUserData.normalIds ? currentUserData.normalIds.split(',').map((id) => Number(id)) : []
      const hasShownAdDPTaskIds = currentUserData.adDpTaskIds ? currentUserData.adDpTaskIds.split(',').map((id) => Number(id)) : []
      return {
        hasShownNormalTaskIds,
        hasShownAdDPTaskIds,
      }
    } else {
      throw new Error('empty data')
    }
  } catch (error) {
    return {
      hasShownNormalTaskIds: [],
      hasShownAdDPTaskIds: [],
    }
  }
}

const setHasShownTaskList = async ({
  normalTaskList,
  AdDPTaskList,
  aid,
  clear,
}: {
  normalTaskList: TaskItemType[]
  AdDPTaskList: TaskItemType[]
  aid: number
  clear?: boolean
}) => {
  const uid = userInfoDetail.getDetail().uid || -1
  const originData = await getStorageData()
  const currentUserData = originData[getCurrentUserStorageKey({ aid, uid })]

  let normalIds = currentUserData?.normalIds || ''
  let adDpTaskIds = currentUserData?.adDpTaskIds || ''
  const date = Date.now()

  if (clear) {
    if (normalTaskList.length === 0) {
      normalIds = ''
    }
    if (AdDPTaskList.length === 0) {
      adDpTaskIds = ''
    }
    originData[getCurrentUserStorageKey({ aid, uid })] = {
      date,
      adDpTaskIds,
      normalIds,
    }
    await storage.set(`${Key}`, originData)
    return
  }

  const filteredTaskList = {
    normal: normalTaskList.filter((task) => !normalIds.includes(String(task.id))),
    adDP: AdDPTaskList.filter((task) => !adDpTaskIds.includes(String(task.id))),
  }

  if (currentUserData && currentUserData.date && dayjs(currentUserData.date).isSame(dayjs(), 'day') && normalIds.length > 0) {
    normalIds = normalIds + ',' + filteredTaskList.normal.map((task) => task.id).join(',')
  } else {
    normalIds = filteredTaskList.normal.map((task) => task.id).join(',')
  }

  if (currentUserData && currentUserData.date && dayjs(currentUserData.date).isSame(dayjs(), 'day') && adDpTaskIds.length > 0) {
    adDpTaskIds = adDpTaskIds + ',' + filteredTaskList.adDP.map((task) => task.id).join(',')
  } else {
    adDpTaskIds = filteredTaskList.adDP.map((task) => task.id).join(',')
  }

  originData[getCurrentUserStorageKey({ aid, uid })] = {
    date,
    adDpTaskIds,
    normalIds,
  }

  await storage.set(`${Key}`, originData)
}

const removeData = async ({ AdDPTaskList, aid, normalTaskList }: { aid: number; normalTaskList: TaskItemType[]; AdDPTaskList: TaskItemType[] }) => {
  return await setHasShownTaskList({
    aid,
    normalTaskList,
    AdDPTaskList,
    clear: true,
  })
}

const todayHasShownTaskListStorage = { getHasShownTaskList, setHasShownTaskList, removeData }

export default todayHasShownTaskListStorage
