import storage from '.'
import { AdDPTaskItem } from '../typesV2/adDPTask'

// 广告任务过期时间
const expireInterval = 10 * 60 * 60
// const expireInterval = 10

type StorageDataType = {
  lastDate: number
  responseId: number
  list: AdDPTaskItem[]
}

const Key = 'adDPTaskListStorageKey'

const getStorageData = async () => {
  try {
    const res = await storage.get(Key)
    const data: StorageDataType = JSON.parse(res)
    if (data) {
      return data
    }
    throw new Error('empty data')
  } catch (err) {
    return {
      lastDate: Date.now() + expireInterval + 1, // 让默认的时间过期
      list: [],
      responseId: 0,
    }
  }
}

const get = async () => {
  try {
    const originData = await getStorageData()
    const currentDate = Date.now()
    console.log({ originData })
    // 没过期，直接返回
    if (currentDate - originData.lastDate <= expireInterval) {
      return { list: originData.list, responseId: originData.responseId }
    } else {
      throw new Error('缓存过期')
    }
  } catch (error) {
    return { list: [], responseId: 0 }
  }
}

const set = async ({ list, responseId, clearAll }: { list: AdDPTaskItem[]; responseId: number; clearAll?: boolean }) => {
  try {
    const currentDate = Date.now()
    if (clearAll) {
      const data: StorageDataType = {
        lastDate: currentDate,
        list,
        responseId,
      }
      await storage.set(`${Key}`, data)
      return
    }
    const currentStorageData = await getStorageData()
    // 如果缓存未过期，则不处理
    if (currentDate - currentStorageData.lastDate <= expireInterval && currentStorageData.list.length > 0) {
      console.log('缓存未过期, 不处理')
    } else {
      const data: StorageDataType = {
        lastDate: currentDate,
        list,
        responseId,
      }
      await storage.set(`${Key}`, data)
    }
  } catch (err) {
    console.log('adDPTaskListStorage set error ', err)
  }
}

const removeData = async () => {
  await storage.remove(Key)
}

const adDPTaskListStorage = { get, set, removeData }

export default adDPTaskListStorage
