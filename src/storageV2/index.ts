import { NativeModules } from 'react-native'
import AsyncStorage from '@react-native-community/async-storage'
import { isIOS } from '@xmly/rn-utils/dist/device'
const DEBUG = false

const Storage = NativeModules.Storage
const bundleName = 'rn_credit_center'

export default {
  set: async (key: string, value: any) => {
    try {
      if (isIOS) {
        await AsyncStorage.setItem(key, JSON.stringify(value))
      } else {
        await Storage.save({
          key,
          value: JSON.stringify(value),
          bundleName,
        })
      }
    } catch (error) {
      console.log(`本地存储信息失败, key:${key}, value: ${value}`)
    }
  },

  get: async (key: string): Promise<string> => {
    try {
      let value

      if (isIOS) {
        value = await AsyncStorage.getItem(key)
      } else {
        value = await Storage.getValue(bundleName, key)
      }
      // ios 初始获取时值为null
      DEBUG && console.log('getItem', { key, value })
      return value
    } catch (error) {
      console.log('getItem error', error)
      // android 初始获取时会被catch住
      console.log(`获取本地存储信息失败, key: ${key}, error: ${error}`)
      return 'null'
    }
  },

  remove: async (key: string, callback?: (error?: Error) => void) => {
    try {
      if (isIOS) {
        await AsyncStorage.removeItem(key)
      } else {
        DEBUG && console.log('storage remove', { key })
        const res = await Storage.delete(key)
        DEBUG && console.log('storage remove', { key, res })
      }
      callback && callback()
    } catch (error) {
      console.log(`删除本地存储信息失败, key:${key}, error:${error}`)
    }
  },
}
