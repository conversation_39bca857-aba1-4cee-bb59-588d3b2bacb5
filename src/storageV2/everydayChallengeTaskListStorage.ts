import dayjs from 'dayjs'
import storage from '.'
import userInfoDetail from '../modulesV2/userInfoDetail'
import { TaskItemType } from '../typesV2/taskList'

type SingleTaskListStorageData = {
  date: number
  ids: string
  adDPTaskIds: string
  normalTaskIdToAdDPTaskIdMap: { [key: number]: number } | null
}

type TaskListStorageDataType = {
  [key: string]: SingleTaskListStorageData
}

const Key = 'everydayChallengeTaskListStorageKey'

const getCurrentUserStorageKey = ({ aid, uid }: { aid: number; uid: number }) => `${uid}+${aid}`

const getStorageData = async () => {
  try {
    const res = await storage.get(Key)
    const data = JSON.parse(res) as TaskListStorageDataType | null
    if (data) {
      return data
    } else {
      throw new Error('没有缓存')
    }
  } catch (err) {
    return {}
  }
}

const get = async ({ aid }: { aid: number }) => {
  try {
    const uid = userInfoDetail.getDetail().uid || -1
    const originData = await getStorageData()
    const currentUserData = originData[getCurrentUserStorageKey({ aid, uid })]

    if (currentUserData && dayjs(currentUserData.date).isSame(dayjs(), 'day')) {
      const normalTaskIds = currentUserData.ids ? currentUserData.ids.split(',').map((id) => Number(id)) : []
      const adDpTaskIds = currentUserData.adDPTaskIds ? currentUserData.adDPTaskIds.split(',').map((id) => Number(id)) : []
      const normalTaskIdToAdDPTaskIdMap = currentUserData.normalTaskIdToAdDPTaskIdMap

      return {
        normalTaskIds,
        adDpTaskIds,
        normalTaskIdToAdDPTaskIdMap,
      }
    } else {
      throw new Error('过期了')
    }
  } catch (error) {
    return {
      normalTaskIds: [],
      adDpTaskIds: [],
      normalTaskIdToAdDPTaskIdMap: null,
    }
  }
}

const set = async ({
  displayedAdDpTaskList,
  displayedNormalTaskList,
  aid,
  normalTaskIdToAdDPTaskIdMap,
}: {
  displayedAdDpTaskList: TaskItemType[]
  displayedNormalTaskList: TaskItemType[]
  aid: number
  normalTaskIdToAdDPTaskIdMap: { [key: number]: number } | null
}) => {
  const uid = userInfoDetail.getDetail().uid || -1
  const originData = await getStorageData()
  const date = Date.now()

  const ids = displayedNormalTaskList.map((task) => task.id).join(',')
  const adDPTaskIds = displayedAdDpTaskList.map((task) => task.id).join(',')

  originData[getCurrentUserStorageKey({ uid, aid })] = {
    date,
    ids,
    adDPTaskIds,
    normalTaskIdToAdDPTaskIdMap,
  }

  await storage.set(`${Key}`, originData)
}

const everydayChallengeTaskListStorage = { get, set }

export default everydayChallengeTaskListStorage
