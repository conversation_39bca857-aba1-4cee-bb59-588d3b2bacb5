import dayjs from 'dayjs'
import storage from '.'

const Key = 'entrancePromptConfirmStorage'

const getEntrancePromptConfirmStorage = async () => {
  try {
    const res = await storage.get(`${Key}`)
    const resParsed = JSON.parse(res)
    if (!resParsed) return ''
    return resParsed
  } catch (error) {
    return ''
  }
}

const setEntrancePromptConfirmStorage = () => {
  const date = dayjs().format('YYYY-MM-DD')
  storage.set(`${Key}`, date)
}

export default {
  get: getEntrancePromptConfirmStorage,
  set: setEntrancePromptConfirmStorage,
}
