import dayjs from 'dayjs'
import storage from '.'

const Key = 'noLoginCheckInStorage'

const getoLoginCheckInStorage = async () => {
  try {
    const res = await storage.get(`${Key}`)
    console.log('res', res)
    const resParsed = JSON.parse(res)
    console.log('resParsed', resParsed)
    if (!resParsed) return ''
    return resParsed
  } catch (error) {
    return ''
  }
}

const setNoLoginCheckInStorage = () => {
  const date = dayjs().format('YYYY-MM-DD')
  console.log('setNoLoginCheckInStorage date', date)
  storage.set(`${Key}`, date)
}

export default {
  get: getoLoginCheckInStorage,
  set: setNoLoginCheckInStorage,
}
