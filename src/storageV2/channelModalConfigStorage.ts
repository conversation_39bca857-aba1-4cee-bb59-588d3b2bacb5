// 前端本地存储各个渠道弹窗的配置
import storage from '.'
import { ChannelModalConfigType } from '../typesV2/channelModal'

const Key = 'channelModalConfig'
const getChannelModalConfigStorage = async () => {
  try {
    const res = await storage.get(Key)
    return JSON.parse(res) || []
  } catch (error) {
    return []
  }
}
const setChannelModalConfigStorage = (data: ChannelModalConfigType[]) => {
  storage.set(Key, data)
}

export default {
  get: getChannelModalConfigStorage,
  set: setChannelModalConfigStorage,
}
