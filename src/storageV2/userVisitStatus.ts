import dayjs from "dayjs"
import storage from "."
import { UserVisitStatusType } from "../typesV2/userVisitStatusType"


const Key = 'userVisitStatus'

const getUserVisitStatus = async () => {
  try {
    // 记得是时间戳
    const res = await storage.get(`${ Key }`)
    const isSame = dayjs().isSame(dayjs(Number(JSON.parse(res))).format('YYYY/MM/DD'), 'day')
    if (isSame) {
      return UserVisitStatusType.notFirst
    } else {
      setUserVisitStatus(Date.now().toString())
      return UserVisitStatusType.first
    }
  } catch (error) {
    console.log(error)
    setUserVisitStatus(Date.now().toString())
    return UserVisitStatusType.first
  }
}

const setUserVisitStatus = (date: string) => {
  console.log({ date })
  storage.set(`${ Key }`, date)
}

export { getUserVisitStatus }
