import React, { Component } from 'react'
import {
  NativeSyntheticEvent,
  NativeScrollEvent,
  ScrollViewProps,
  ScrollView,
} from 'react-native'
import Animated from 'react-native-reanimated'
import { PagerProps } from './typesv2'
import { getChildren } from './shared'

type Props = PagerProps<ScrollViewProps>

interface AnimatedScrollView extends ScrollView {
  getNode: () => ScrollView
}

export default class PagerIOS extends Component<Props> {
  constructor (props: Props) {
    super(props)
    this.props.onInstance(this)
  }

  private initOffsetX = { x: (this.props.initialPage || 0) * this.props.containerWidth, y: 0 }
  private tempPage = (this.props.initialPage || 0)
  private scrollView: null | AnimatedScrollView = null

  private onScrollViewRef = (ref: AnimatedScrollView) => (this.scrollView = ref)

  private handlePageScroll = Animated.event(
    [ { nativeEvent: { contentOffset: { x: this.props.scrollXIOS } } } ],
  )

  goToPage = (pageNumber: number, withoutAnimation: boolean) => {
    if (this.scrollView) {
      const offset = pageNumber * this.props.containerWidth
      if (this.scrollView) {
        this.scrollView
          .getNode()
          .scrollTo({ x: offset, y: 0, animated: !withoutAnimation })
      }
    }
  }


  private renderScrollableContent = () => {
    const childrenLength = getChildren(this.props.children).length
    return (
      <Animated.ScrollView
        key={ childrenLength }
        horizontal
        pagingEnabled
        automaticallyAdjustContentInsets={ false }
        contentOffset={ this.initOffsetX }
        ref={ this.onScrollViewRef }
        onScroll={ this.handlePageScroll }
        onMomentumScrollBegin={ this.handleMomentumScrollBeginAndEnd }
        onMomentumScrollEnd={ this.handleMomentumScrollBeginAndEnd }
        scrollEventThrottle={ 1 }
        scrollsToTop={ false }
        showsHorizontalScrollIndicator={ false }
        scrollEnabled={ !this.props.locked }
        directionalLockEnabled
        alwaysBounceVertical={ false }
        keyboardDismissMode='on-drag'
        style={ { flex: 1 } }
        { ...this.props.contentProps }
      >
        { this.props.children }
      </Animated.ScrollView>
    )
  }

  handleMomentumScrollBeginAndEnd = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    e.persist()
    this._onMomentumScrollBeginAndEnd(e)
  }

  _onMomentumScrollBeginAndEnd = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
    const offsetX = e.nativeEvent.contentOffset.x
    const page = Math.round(offsetX / this.props.containerWidth)
    if (this.tempPage !== page) {
      this.tempPage = page
      this.props.updateSelectedPage(page) //should from HOC props
    }
  }

  render () {
    
    return this.renderScrollableContent()
  }
}

