import React, { ReactElement, ReactNode } from "react"
import { ViewStyle, StyleProp, TextStyle, GestureResponderEvent } from "react-native"
import Animated from "react-native-reanimated"
import PagerIOS from "./PagerIos"
import PagerAndroid from "./PagerAndroid"

export enum TabBarPosition {
  top = 'top',
  bottom = 'bottom',
  overlayTop = 'overlayTop',
  overlayBottom = 'overlayBottom',
}

type OnChangeArg = {
  i: number,
  ref: ReactNode,
  from: number,
}

export interface Props {
  onMoveShouldSetResponderCapture?: (event: GestureResponderEvent) => boolean
  onStartShouldSetResponderCapture?: () => boolean
  onMoveShouldSetResponder?: () => boolean
  onStartShouldSetResponder?: () => boolean
  renderEmptyView?: () => ReactNode
  withoutTabBar?: boolean
  tabBarPosition: TabBarPosition
  initialPage?: number
  page?: number
  onChangeTab?: (arg: OnChangeArg) => void
  onScroll?: (value: number) => void
  renderTabBar?: (props: any) => ReactElement
  tabBarUnderlineStyle?: StyleProp<ViewStyle>
  tabBarBackgroundColor?: string
  tabBarActiveTextColor?: string
  tabBarInactiveTextColor?: string
  tabBarTextStyle?: StyleProp<TextStyle>
  style?: StyleProp<ViewStyle>
  contentProps?: any
  scrollWithoutAnimation?: boolean
  locked?: boolean
  prerenderingSiblingsNumber?: number
  parentAnimationValue?: Animated.Value<number>
  onChangeDebounce?: number
  simultaneousHandlers?: ((instance: any) => void) | React.RefObject<any> | React.Ref<any>[] | null | undefined
  androidNativeViewRef?: any
}

export type State = {
  currentPage: number
  containerWidth: number
}

export type TabBarProps = {
  goToPage: (page: number) => void
  tabs: string[]
  activeTab: number
  scrollValue: Animated.Value<number>
  containerWidth: number
  backgroundColor?: string
  activeTextColor?: string
  inactiveTextColor?: string
  textStyle?: StyleProp<TextStyle>
  tabStyle?: StyleProp<ViewStyle>
  underlineStyle?: StyleProp<ViewStyle>
  style?: StyleProp<ViewStyle>
  renderTab?: (
    name: string,
    page: number,
    isTabActive: boolean,
    onPressHandler: (page: number) => void
  ) => ReactNode
}

export interface PagerProps<T> {
  onInstance: (WrappedComp: PagerIOS | PagerAndroid) => void
  containerWidthAnimationValue: Animated.Value<number>
  initialPage: number
  containerWidth: number
  updateSelectedPage: (page: number) => void
  contentProps?: T
  locked?: boolean
  offsetAndroid: Animated.Value<number>
  positionAndroid: Animated.Value<number>
  scrollXIOS: Animated.Value<number>
}