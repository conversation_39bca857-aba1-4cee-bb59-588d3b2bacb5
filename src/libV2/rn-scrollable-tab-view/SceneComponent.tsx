import React, { Component } from 'react'
import StaticContainer from './StaticContainer'



type Props = {
  shouldUpdate: boolean
  containerWidth: number
}

class SceneComponent extends Component<Props> {
  shouldComponentUpdate (nextProps: Props) {
    return this.props.containerWidth !== nextProps.containerWidth || !!nextProps.shouldUpdate;
  }

  render () {
    const { shouldUpdate } = this.props;
    return (
      <StaticContainer shouldUpdate={ shouldUpdate }>
        { this.props.children }
      </StaticContainer>
    )
  }
}

export default SceneComponent;