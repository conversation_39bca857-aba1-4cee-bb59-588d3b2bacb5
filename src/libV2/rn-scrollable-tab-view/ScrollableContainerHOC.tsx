import React, { Component, ReactNode, ComponentType } from 'react';
import { View, StyleSheet, Platform, PanResponder, LayoutChangeEvent } from 'react-native'
import { Props, State, TabBarProps, PagerProps } from './types'
import SceneComponent from './SceneComponent'
import { DEFAULT_CONTAINER_WIDTH } from './constants'

import {
  newSceneKeys,
  _makeSceneKey,
  _keyExists,
  _shouldRenderSceneKey,
  getChildren,
} from './shared'

import Animated from 'react-native-reanimated'

import PagerIOS from './PagerIos';
import PagerAndroid from './PagerAndroid';

type WrapperComponentClass = PagerIOS | PagerAndroid

const DEBUG = false

export default function ScrollableContainerHOC<T> (WrapperComponent: ComponentType<PagerProps<T>>, force?: boolean) {
  return class ScrollableContainer extends Component<Props, State> {
    constructor (props: Props) {
      super(props)
      this.state = this.getInitialState()
      this.setChildrenLabels()
    }

    static defaultProps = {
      tabBarPosition: 'top',
      initialPage: 0,
      page: -1,
      onChangeTab: () => { },
      onScroll: () => { },
      contentProps: {},
      scrollWithoutAnimation: false,
      locked: false,
      prerenderingSiblingsNumber: 0,
    }


    private containerWidthAnimationValue: Animated.Value<number> = new Animated.Value(DEFAULT_CONTAINER_WIDTH)
    private containerWidth = DEFAULT_CONTAINER_WIDTH
    private childrenLabels: string[] = []
    private sceneKeys: string[] = []
    private wrappedCompInstance: WrapperComponentClass
    private offsetAndroid: Animated.Value<number> = new Animated.Value(0)
    private positionAndroid: Animated.Value<number> = new Animated.Value(this.props.initialPage || 0)
    private scrollXIOS: Animated.Value<number> = new Animated.Value((this.props.initialPage || 0) * DEFAULT_CONTAINER_WIDTH)
    private scrollValue: Animated.Node<number> = (Platform.OS === 'ios' || force) ? Animated.divide(this.scrollXIOS, this.containerWidthAnimationValue) : Platform.OS === 'android' ? Animated.add(this.positionAndroid, this.offsetAndroid) : new Animated.Value(0)

    private setChildrenLabels = () => {
      return (this.childrenLabels = getChildren(this.props.children).map(
        (child: ReactNode) => child.props.tabLabel
      ))
    }

    private _updateSelectedPage = (nextPage: number) => {
      DEBUG && console.log('_updateSelectedPage ???? ?', nextPage)
      let localNextPage = nextPage
      if (this.state.currentPage !== nextPage) {
        const currentPage = this.state.currentPage
        this.updateSceneKeys({
          page: localNextPage,
          callback: () => this._onChangeTab(currentPage, localNextPage),
          from: '_updateSelectedPage',
        })
      }
    }

    private updateSceneKeys = ({
      page,
      children = this.props.children,
      callback = () => { },
      from,
    }: {
      page: number
      children?: ReactNode
      callback?: () => void
      from: string
    }) => {
      DEBUG && console.log('why updateSceneKeys🕵️‍♂️🕵️‍♂️🕵️‍♂️🕵️‍♂️🕵️‍♂️', from)
      let newKeys = newSceneKeys({
        previousKeys: this.sceneKeys,
        currentPage: page,
        children,
        prerenderingSiblingsNumber: this.props.prerenderingSiblingsNumber || 0,
      })
      this.setSceneKeys(newKeys)
      this.setState({ currentPage: page }, callback)
    }

    private setSceneKeys = (sceneKeys: string[]) => {
      this.sceneKeys = sceneKeys
    }

    private getInitialState = () => {
      this.setSceneKeys(newSceneKeys({
        currentPage: this.props.initialPage,
        children: this.props.children,
        prerenderingSiblingsNumber: this.props.prerenderingSiblingsNumber || 0,
      }))
      return {
        currentPage: this.props.initialPage || 0,
        containerWidth: DEFAULT_CONTAINER_WIDTH,

      }
    }

    private _onChangeTab = (prevPage: number, currentPage: number) => {
      DEBUG && console.log('? ? ? ?!!! _onChangeTab')
      if (this.props.onChangeTab) {
        // const currentRef = getChildren(this.props.children)[currentPage]
        this.props.onChangeTab({
          i: currentPage,
          ref: null,
          from: prevPage,
        })
      }

    }

    goToPage = (pageNumber: number) => {
      if (this.wrappedCompInstance) {
        this.wrappedCompInstance.goToPage(pageNumber, this.props.scrollWithoutAnimation || false)
      }

      const currentPage = this.state.currentPage
      this.updateSceneKeys({
        page: pageNumber,
        callback: this._onChangeTab.bind(this, currentPage, pageNumber),
        from: 'goToPage',
      })
    }


    private _handleLayout = (e: LayoutChangeEvent) => {
      const { width } = e.nativeEvent.layout
      if (!width || width <= 0 || Math.round(width) === Math.round(this.containerWidth)) {
        return
      }
      this.setContainerWidth(width)
      requestAnimationFrame(() => {
        DEBUG && console.log('this.props.initialPage', this.props.initialPage)
        DEBUG && console.log('this.state.currentPage', this.state.currentPage)
        if (this.props.initialPage !== this.state.currentPage && this.props.initialPage !== 0) {
          this.goToPage(this.state.currentPage)
        }
      })
    }

    private setContainerWidth = (value: number) => {
      this.containerWidth = value
      this.containerWidthAnimationValue.setValue(value)
      this.setState({
        containerWidth: value
      })
    }


    private getChildrenLabels = () => {
      if (
        this.childrenLabels.length > 0 &&
        this.childrenLabels.length === getChildren(this.props.children).length
      ) {
        return this.childrenLabels
      } else {
        return this.setChildrenLabels()
      }
    }

    private _composeScenes = () => {
      return getChildren(this.props.children).map((child: ReactNode, idx: number) => {
        const key = _makeSceneKey(child, idx)
        const keyExists = _keyExists(this.sceneKeys, key)
        const _shouldRender = _shouldRenderSceneKey(
          idx,
          this.state.currentPage,
          this.props.prerenderingSiblingsNumber || 0
        )
        return (
          <SceneComponent
            key={ child && child.key }
            containerWidth={ this.state.containerWidth }
            shouldUpdate={ _shouldRender }
          >
            {(keyExists || _shouldRender) ? child : <View tabLabel={ child.props.tabLabel } style={ { flex: 1 } } /> }
          </SceneComponent>
        )
      })
    }

    private renderTabBar = () => {
      const props = this.getTabBarProps()
      if (!!this.props.renderTabBar === false) {
        return null // <DefaultTabBar { ...props } />
      } else if (this.props.renderTabBar) {
        return React.cloneElement(this.props.renderTabBar(props), props)
      } else {
        return null // <DefaultTabBar { ...props } />
      }
    }

    private getWrappedCompInstance = (WrappedComp: WrapperComponentClass) => {
      this.wrappedCompInstance = WrappedComp
    }

    componentWillReceiveProps (props: Props) {
      if (typeof props.page === 'number' && props.page >= 0 && props.page !== this.state.currentPage) {
        DEBUG && console.log('componentWillReceiveProps page changed🌟')
        this.goToPage(props.page)
      }
      this.setChildrenLabels()
    }


    renderScrollableContent = () => {
      const scenes = this._composeScenes()
      return (
        <WrapperComponent
          onInstance={ this.getWrappedCompInstance }
          containerWidthAnimationValue={ this.containerWidthAnimationValue }
          containerWidth={ this.containerWidth }
          initialPage={ this.props.initialPage || 0 }
          updateSelectedPage={ this._updateSelectedPage }
          contentProps={ this.props.contentProps }
          locked={ this.props.locked }
          positionAndroid={ this.positionAndroid }
          offsetAndroid={ this.offsetAndroid }
          scrollXIOS={ this.scrollXIOS }
        >
          {scenes }
        </WrapperComponent>
      )
    }

    private getTabBarProps = (): TabBarProps => {
      let overlayTabs =
        this.props.tabBarPosition === 'overlayTop' || this.props.tabBarPosition === 'overlayBottom'
      let tabBarProps: TabBarProps = {
        goToPage: this.goToPage,
        tabs: this.getChildrenLabels(),
        activeTab: this.state.currentPage,
        scrollValue: this.scrollValue as Animated.Value<number>,
        containerWidth: this.state.containerWidth,
      }
      if (this.props.tabBarBackgroundColor) {
        tabBarProps.backgroundColor = this.props.tabBarBackgroundColor
      }
      if (this.props.tabBarActiveTextColor) {
        tabBarProps.activeTextColor = this.props.tabBarActiveTextColor
      }
      if (this.props.tabBarInactiveTextColor) {
        tabBarProps.inactiveTextColor = this.props.tabBarInactiveTextColor
      }
      if (this.props.tabBarTextStyle) {
        tabBarProps.textStyle = this.props.tabBarTextStyle
      }
      if (this.props.tabBarUnderlineStyle) {
        tabBarProps.underlineStyle = this.props.tabBarUnderlineStyle
      }
      if (overlayTabs) {
        tabBarProps.style = {
          position: 'absolute',
          left: 0,
          right: 0,
          [ this.props.tabBarPosition === 'overlayTop' ? 'top' : 'bottom' ]: 0,
        }
      }
      return tabBarProps
    }

    pan = PanResponder.create({
      onStartShouldSetPanResponderCapture: (e, state) => {
        const { dy, dx } = state
        if (Math.abs(dx) === 0) return false
        return Math.abs(dy) / Math.abs(dx) > 0.5
      },
      onMoveShouldSetPanResponderCapture: (e, state) => {
        const { dy, dx } = state
        if (Math.abs(dx) === 0) return false
        return Math.abs(dy) / Math.abs(dx) > 0.5
      }
    })

    render () {
      DEBUG && console.log('render render render render render🔞🔞🔞🔞🔞')
      let overlayTabs =
        this.props.tabBarPosition === 'overlayTop' || this.props.tabBarPosition === 'overlayBottom'

      return (
        <>
          <View
            collapsable={ false }
            style={ [ styles.container, this.props.style ] }
            onLayout={ this._handleLayout }
          >
            { this.props.renderEmptyView && this.props.renderEmptyView() }
            { this.props.tabBarPosition === 'top' && !this.props.withoutTabBar && this.renderTabBar() }


            { this.renderScrollableContent() }


            { (this.props.tabBarPosition === 'bottom' || overlayTabs) && !this.props.withoutTabBar && this.renderTabBar() }
          </View>

          {
            this.props.onScroll && (
              <Animated.Code
                exec={ Animated.block([
                  Animated.call([ this.scrollValue ], (value) => {
                    this.props.onScroll && this.props.onScroll(value[ 0 ])
                  }),
                ]) }
              />
            )
          }


          {
            this.props.parentAnimationValue && (
              <Animated.Code
                exec={ Animated.block([
                  Animated.set(this.props.parentAnimationValue, this.scrollValue)
                ]) }
              />
            )
          }
          { DEBUG && <Animated.Code exec={
            Animated.block([
              Animated.call([ this.positionAndroid, this.offsetAndroid ], (value) => {
                console.log(value)
              })
            ])
          } /> }
        </>
      )
    }
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollableContentAndroid: {
    flex: 1,
  },
})
