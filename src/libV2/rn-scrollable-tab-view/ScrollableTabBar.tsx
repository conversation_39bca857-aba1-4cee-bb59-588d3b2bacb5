import { Touch } from '@xmly/rn-components'
import React from 'react'
import { LayoutChangeEvent, LayoutRectangle, Platform, ScrollView, StyleProp, StyleSheet, Text, TextStyle, View, ViewStyle } from 'react-native'
import { PanGestureHandler, State as GestureState } from 'react-native-gesture-handler'
import Animated, { call, floor, interpolate, modulo, set, sub, multiply, add, cond, eq, onChange } from 'react-native-reanimated'
import { DEFAULT_CONTAINER_WIDTH } from './constantsV2'
import StaticReAnimatedCode from './StaticReAnimatedCode'
import { TabBarProps } from './typesv2'

type Props = {
  goToPage: () => void,
  activeTab: number,
  tabs: [],
  backgroundColor: string,
  activeTextColor: string,
  inactiveTextColor: string,
  scrollOffset: number,
  style: StyleProp<ViewStyle>,
  tabStyle: StyleProp<ViewStyle>,
  tabsContainerStyle: StyleProp<ViewStyle>,
  textStyle: StyleProp<TextStyle>,
  renderTab: (name: string, page: number, isTabActive: boolean, onPressHandler: (page: number) => void, onLayoutHandler: (e: LayoutChangeEvent) => void) => JSX.Element,
  underlineStyle: StyleProp<ViewStyle>,
  onScroll: () => void,
} & TabBarProps

type State = {
  _containerWidth: number | null
}

class ScrollableTabBar extends React.Component<Props, State> {
  constructor (props: Props) {
    super(props)
    this.state = {
      _containerWidth: null
    }
  }

  private _scrollView: ScrollView | null = null
  private _tabsMeasurements: ({ left: number, right: number, width: number, height: number })[] = []
  private _tabContainerMeasurements: LayoutRectangle | null = null
  private _containerMeasurements: LayoutRectangle | null = null
  private gestureX = new Animated.Value<number>(0)
  private gestureState = new Animated.Value<GestureState>(0)
  private offsetX = new Animated.Value<number>(0)

  private underlineTranslateX = interpolate(this.props.scrollValue, {
    inputRange: [ 0, 1 ],
    outputRange: [ 0, 0 ]
  })

  componentDidMount () { }

  updateView = (offset: { value: number }) => {
    const position = Math.floor(offset.value);
    const pageOffset = offset.value % 1;
    const tabCount = this.props.tabs.length;
    const lastTabPosition = tabCount - 1;
    if (tabCount === 0 || offset.value < 0 || offset.value > lastTabPosition) {
      return;
    }

    if (this.necessarilyMeasurementsCompleted(position, position === lastTabPosition)) {
      this.updateTabPanel(position, pageOffset);
    }
  }

  necessarilyMeasurementsCompleted = (position: number, isLastTab: boolean) => {
    return this._tabsMeasurements[ position ] &&
      (isLastTab || this._tabsMeasurements[ position + 1 ]) &&
      this._tabContainerMeasurements &&
      this._containerMeasurements;
  }

  onTabContainerLayout = (e: LayoutChangeEvent) => {
    this._tabContainerMeasurements = e.nativeEvent.layout;
    let width = this._tabContainerMeasurements.width;
    if (width < DEFAULT_CONTAINER_WIDTH) {
      width = DEFAULT_CONTAINER_WIDTH;
    }
    this.setState({ _containerWidth: width, });
    // this.updateView({ value:  this.props.scrollValue.__getValue(),});
  }

  onContainerLayout = (e: LayoutChangeEvent) => {
    this._containerMeasurements = e.nativeEvent.layout;
    // this.updateView({ value: this.props.scrollValue.__getValue(), });
  }

  renderTab = (name: string, page: number, isTabActive: boolean, onPressHandler: (page: number) => void, onLayoutHandler: (e: LayoutChangeEvent) => void) => {
    const { activeTextColor, inactiveTextColor, textStyle, } = this.props;
    const textColor = isTabActive ? activeTextColor : inactiveTextColor;
    const fontWeight = isTabActive ? 'bold' : 'normal';

    return <Touch
      key={ `${ name }_${ page }` }
      accessible={ true }
      accessibilityLabel={ name }
      accessibilityTraits='button'
      onPress={ () => onPressHandler(page) }
      onLayout={ onLayoutHandler }
    >
      <View style={ [ styles.tab, this.props.tabStyle, ] }>
        <Text style={ [ { color: textColor, fontWeight, }, textStyle, ] }>
          { name }
        </Text>
      </View>
    </Touch>;
  }

  measureTab = (page: number) => (event: LayoutChangeEvent) => {
    const { x, width, height, } = event.nativeEvent.layout;
    this._tabsMeasurements[ page ] = { left: x, right: x + width, width, height, };
    if (this._tabsMeasurements.length === this.props.tabs.length) {
      if (!this._tabsMeasurements.includes(undefined as any)) {
        console.log('🌟', this._tabsMeasurements)
        const result: { inputRange: number[], outputRange: number[] } = { inputRange: [ 0, 1 ], outputRange: [ 0, 0 ] }
        this._tabsMeasurements.forEach((itemM, index) => {
          console.log({ index })
          console.log({ itemM })
          result.inputRange[ index ] = index
          result.outputRange[ index ] = itemM.left
        })

        this.underlineTranslateX = interpolate(this.props.scrollValue, result)
        this.tabContainerTranslateX = Animated.block([
          cond(
            eq(this.gestureState, GestureState.ACTIVE),
            [
              add(this.offsetX, this.gestureX)
            ],
            onChange([ this.props.scrollValue ], [ multiply(-1, add(this.underlineTranslateX, this.absolutePageOffset)) ]),
          )
        ])
      }
    }
  }



  componentDidUpdate (prevProps: Props) {
    // If the tabs change, force the width of the tabs container to be recalculated
    if (JSON.stringify(prevProps.tabs) !== JSON.stringify(this.props.tabs) && this.state._containerWidth) {
      this.setState({ _containerWidth: null, });
    }
  }

  tabContainerTranslateX: Animated.Adaptable<number> = new Animated.Value<number>(0)

  private handleGestureEvent = Animated.event([
    {
      nativeEvent: {
        translationX: this.gestureX,
        state: this.gestureState,
      },
    },
  ])

  render () {
    return (
      <>
        <View
          style={ [ styles.container, { backgroundColor: this.props.backgroundColor, borderWidth: 1 }, this.props.style ] }
          onLayout={ this.onContainerLayout }
        >
          {/* <ScrollView
            ref={ (scrollView) => { this._scrollView = scrollView; } }
            horizontal={ true }
            showsHorizontalScrollIndicator={ false }
            showsVerticalScrollIndicator={ false }
            directionalLockEnabled={ true }
            bounces={ false }
            scrollsToTop={ false }
          > */}
          <PanGestureHandler
            onGestureEvent={ this.handleGestureEvent }
            onHandlerStateChange={ this.handleGestureEvent }
          >
            <Animated.View
              style={ [ styles.tabs, this.props.tabsContainerStyle, { transform: [ { translateX: this.tabContainerTranslateX } ], borderWidth: 1, borderColor: 'yellow' } ] }
              ref={ 'tabContainer' }
              onLayout={ this.onTabContainerLayout }
            >
              { this.props.tabs.map((name, page) => {
                const isTabActive = this.props.activeTab === page;
                const renderTab = this.props.renderTab || this.renderTab;
                return renderTab(name, page, isTabActive, this.props.goToPage, this.measureTab(page));
              }) }
              <Animated.View style={ [ styles.tabUnderlineStyle, { transform: [ { translateX: this.underlineTranslateX } ] }, this.props.underlineStyle ] } />
            </Animated.View>
          </PanGestureHandler>
          {/* </ScrollView> */ }
        </View>
        <Animated.Code
          exec={ Animated.block([
            call([ this.props.scrollValue, this.underlineTranslateX, this.currentTabWidth, this.absolutePageOffset ], (value) => {
              this.updateView({ value: value[ 0 ] })
              console.log(value)
            }),
            // set(this.tabContainerTranslateX, add(this.underlineTranslateX, this.absolutePageOffset))
          ]) }
        />
        <Animated.Code
          exec={
            Animated.block([
              cond(
                eq(this.gestureState, GestureState.END),
                [
                  set(this.offsetX, this.tabContainerTranslateX),
                ]
              ),
            ])
          }
        />
      </>
    )
  }

  currentTabWidth = floor(this.props.scrollValue)
  pageOffsetAnimatedValue = modulo(this.props.scrollValue, 1)
  absolutePageOffset = multiply(this.currentTabWidth, this.pageOffsetAnimatedValue)

  updateTabPanel = (position: number, pageOffset: number) => {
    return
    if (!this._containerMeasurements || !this._tabContainerMeasurements || !this._scrollView) return
    const containerWidth = this._containerMeasurements.width;
    const tabWidth = this._tabsMeasurements[ position ].width;
    const nextTabMeasurements = this._tabsMeasurements[ position + 1 ];
    const nextTabWidth = nextTabMeasurements && nextTabMeasurements.width || 0;
    const tabOffset = this._tabsMeasurements[ position ].left;
    const absolutePageOffset = pageOffset * tabWidth;
    let newScrollX = tabOffset + absolutePageOffset;

    // center tab and smooth tab change (for when tabWidth changes a lot between two tabs)
    newScrollX -= (containerWidth - (1 - pageOffset) * tabWidth - pageOffset * nextTabWidth) / 2;
    newScrollX = newScrollX >= 0 ? newScrollX : 0;

    if (Platform.OS === 'android') {
      this._scrollView.scrollTo({ x: newScrollX, y: 0, animated: false, });
    } else {
      const rightBoundScroll = this._tabContainerMeasurements.width - (this._containerMeasurements.width);
      newScrollX = newScrollX > rightBoundScroll ? rightBoundScroll : newScrollX;
      this._scrollView.scrollTo({ x: newScrollX, y: 0, animated: false, });
    }
  }

}


const styles = StyleSheet.create({
  tab: {
    height: 49,
    alignItems: 'center',
    justifyContent: 'center',
    paddingLeft: 20,
    paddingRight: 20,
  },
  container: {
    height: 50,
    borderWidth: 1,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderRightWidth: 0,
    borderColor: '#ccc',
    maxWidth: '100%'
  },
  tabs: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    position: 'absolute',
    left: 0,
  },
  tabUnderlineStyle: {
    position: 'absolute',
    height: 4,
    backgroundColor: 'navy',
    bottom: 0,
  }
});


export default ScrollableTabBar