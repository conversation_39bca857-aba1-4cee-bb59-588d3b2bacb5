import React, { Component } from 'react'
import {
  StyleSheet,
  ViewPagerAndroidProps,
  NativeSyntheticEvent,
  ViewPagerAndroidOnPageSelectedEventData,
} from 'react-native'
import ViewPager from '@react-native-community/viewpager'
import Animated from 'react-native-reanimated';
import { getChildren } from './shared';
import { PagerProps } from './typesv2';
import { NativeViewGestureHandler, createNativeWrapper } from 'react-native-gesture-handler';

const AnimatedViewPagerAndroid = Animated.createAnimatedComponent(createNativeWrapper(ViewPager, {
  disallowInterruption: true,
}))

interface AnimatedViewPager extends ViewPagerAndroidProps {
  getNode: () => ViewPager
}

type Props = PagerProps<ViewPagerAndroidProps>

export default class PagerAndroid extends Component<Props> {
  private scrollView = React.createRef<AnimatedViewPager>()

  static NativeGestureHandlerRef = React.createRef<NativeViewGestureHandler>()

  constructor (props: Props) {
    super(props)
    this.props.onInstance(this)
  }

  private handlePageSelected = (e: NativeSyntheticEvent<ViewPagerAndroidOnPageSelectedEventData>) => {
    e.persist()
    const { nativeEvent } = e
    this.props.updateSelectedPage(nativeEvent.position)
  }

  handlePageScroll = Animated.event(
    [ {
      nativeEvent: {
        position: this.props.positionAndroid,
        offset: this.props.offsetAndroid,
      },
    }, ]
  )

  goToPage = (pageNumber: number, withoutAnimation: boolean) => {
    if (this.scrollView.current) {
      if (withoutAnimation) {
        this.scrollView.current.getNode().setPageWithoutAnimation(pageNumber);
      } else {
        this.scrollView.current.getNode().setPage(pageNumber);
      }
    }
  }

  renderScrollableContent = () => {
    const childrenLength = getChildren(this.props.children).length
    return (
      <AnimatedViewPagerAndroid
        key={ childrenLength }
        ref={ this.scrollView }
        style={ styles.scrollableContentAndroid }
        initialPage={ this.props.initialPage }
        onPageSelected={ this.handlePageSelected }
        keyboardDismissMode="on-drag"
        scrollEnabled={ !this.props.locked }
        onPageScroll={ this.handlePageScroll }
        { ...this.props.contentProps }
      >
        { this.props.children }
      </AnimatedViewPagerAndroid>
    )
  }


  render () {
    // console.log('render render render render render android android android android android android 🔞🔞🔞🔞🔞')
    return this.renderScrollableContent()
  }
}

const styles = StyleSheet.create({
  scrollableContentAndroid: {
    flex: 1,
  },
});
