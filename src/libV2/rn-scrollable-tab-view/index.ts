import { Platform } from 'react-native'
import PagerIOS from './PagerIos'
import ScrollableContainerHOC from './ScrollableContainerHOC'
import PagerAndroid from './PagerAndroid'

const ScrollableTabView = Platform.select({
  ios: ScrollableContainerHOC(PagerIOS),
  android: ScrollableContainerHOC(PagerAndroid),
}) as ReturnType<typeof ScrollableContainerHOC>

export const ScrollableTabViewIOS = ScrollableContainerHOC(PagerIOS, true)

export default ScrollableTabView