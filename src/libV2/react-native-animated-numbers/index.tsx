import isEqual from 'lodash.isequal'
import React, { useCallback } from 'react'
import {
  LayoutChangeEvent,
  Text,
  TextStyle,
  View,
  StyleProp,
} from 'react-native'
import Animated, { Easing } from 'react-native-reanimated'

const NUMBERS = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]

const usePrevious = (value: number) => {
  const ref = React.useRef<number>()
  React.useEffect(() => {
    ref.current = value
  })

  if (typeof ref.current === 'undefined') {
    return value
  }

  return ref.current
}

function parseNumber(n: string) {
  return n === '.' ? n : Number(n);
}

type AnimatedNumberProps = {
  animateToNumber: number
  fontStyle?: StyleProp<TextStyle>
  animationDuration?: number
  easing?: Animated.EasingFunction
  onReady?: () => void
  emptyText?: string | number
  onAnimationFinished?: () => void
}

const AnimatedNumber: React.FC<AnimatedNumberProps> = ({
  animateToNumber,
  fontStyle,
  animationDuration,
  easing,
  onReady,
  emptyText,
  onAnimationFinished,
}) => {
  const prevNumber = usePrevious(animateToNumber)
  const animateToNumberString = String(Math.abs(animateToNumber))
  const prevNumberString = String(Math.abs(prevNumber))

  const animateToNumbersArr = Array.from(animateToNumberString, parseNumber)
  const prevNumberersArr = Array.from(prevNumberString, parseNumber)

  const [numberHeight, setNumberHeight] = React.useState(0)
  const animations = React.useMemo(() => animateToNumbersArr.map((value, index) => {
    if (typeof prevNumberersArr[index] !== 'number') {
      return new Animated.Value(0)
    }

    const animationHeight = -1 * (numberHeight * prevNumberersArr[index])
    return new Animated.Value(animationHeight)
  }), [animateToNumber])

  const setButtonLayout = useCallback(
    (e: LayoutChangeEvent) => {
      typeof onReady === 'function' && onReady()
      setNumberHeight(e.nativeEvent.layout.height)
    },
    [onReady]
  )

  const runAnimation = (animation: Animated.Value<number>, index: number) => {
    return new Promise((resolve) => {
      if (typeof animateToNumbersArr[index] !== 'number') {
        return resolve(true)
      }

      Animated.timing(animation, {
        toValue: -1 * (numberHeight * animateToNumbersArr[index]),
        duration: animationDuration || 1300,
        easing: easing || Easing.elastic(.8),
      }).start(() => {
        return resolve(true)
      })
    })
  }

  const runAllAnimation = async () => {
    await Promise.all(animations.map(runAnimation))
    typeof onAnimationFinished === 'function' && onAnimationFinished()
  }

  React.useEffect(() => {
    runAllAnimation()
  }, [animateToNumber, numberHeight])

  const getTranslateY = (index: number) => {
    return animations[index]
  }

  return (
    <>
      {numberHeight !== 0 ? (
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          {animateToNumber < 0 && (
            <Text style={[fontStyle, { height: numberHeight }]}>{'-'}</Text>
          )}
          {animateToNumbersArr.map((n, index) => {
            if (typeof n === 'string') {
              return (
                <Text key={index} style={[fontStyle, { height: numberHeight }]}>
                  {n}
                </Text>
              )
            }

            return (
              <View
                key={index}
                style={{
                  height: numberHeight,
                  overflow: 'hidden',
                }}
              >
                <Animated.View
                  style={[
                    {
                      transform: [
                        {
                          translateY: getTranslateY(index),
                        },
                      ],
                    },
                  ]}
                >
                  {NUMBERS.map((number, i) => (
                    <View
                      style={{
                        flexDirection: 'row',
                        justifyContent: 'center',
                      }}
                      key={i}
                    >
                      <Text
                        style={[
                          fontStyle,
                          {
                            height: numberHeight,
                            textAlign: 'center',
                            textAlignVertical: 'center',
                            includeFontPadding: false,
                          },
                        ]}
                      >
                        {number}
                      </Text>
                    </View>
                  ))}
                </Animated.View>
              </View>
            )
          })}
        </View>
      ) : (
        <Text style={[fontStyle]}>
          {typeof emptyText === 'undefined' ? 0 : emptyText}
        </Text>
      )}
      <Text
        style={[
          fontStyle,
          {
            position: 'absolute',
            top: -999999,
          },
        ]}
        onLayout={setButtonLayout}
      >
        {0}
      </Text>
    </>
  )
}

export default React.memo(AnimatedNumber, isEqual)
