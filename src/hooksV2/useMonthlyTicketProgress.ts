import { useSelector } from 'react-redux'
import { RootState } from '../store'
import { EnumVIPTag } from '../typesV2/signInNew'

function useMonthlyTicketProgress() {
  const monthlyTicketTaskInfo = useSelector(
    (state: RootState) => state.monthlyTicket.monthlyTicketTaskInfo
  )
  const vipTag = useSelector((state: RootState) => state.signInInfo.vipTag)
  const isVip = vipTag === EnumVIPTag.vip

  let isDone = false
  let progress = 0
  let condition = isVip ? 3 : 5
  let worth = 1
  let stillNeedCount = condition - progress

  if (monthlyTicketTaskInfo) {
    const taskCondition = monthlyTicketTaskInfo.condition || condition
    const taskProgress = monthlyTicketTaskInfo.progress || 0
    condition = taskCondition
    if (taskProgress > 0) {
      progress = taskProgress % taskCondition
      if (progress === 0) {
        isDone = true
        progress = condition
      }
    }

    stillNeedCount = isDone ? condition : condition - progress
  }

  return {
    isDone,
    progress,
    condition,
    isVip,
    worth,
    stillNeedCount,
  }
}

export default useMonthlyTicketProgress
