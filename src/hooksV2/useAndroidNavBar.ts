import { useEffect, useState } from "react"
import { NativeEventEmitter, NativeModules, PixelRatio, Platform, EventEmitter } from "react-native"

const { NavBar } = NativeModules
const isIOS = Platform.OS === 'ios'

const NavBarEventEmitter = isIOS ? null : new NativeEventEmitter(NavBar)

type useAndroidNavBarData = { hasNavBar: boolean, isNavBarShow: boolean, navBarHeight: number }

function useAndroidNavBar () {
  const [ data, setData ] = useState<useAndroidNavBarData>({
    hasNavBar: false,
    isNavBarShow: false,
    navBarHeight: 0
  })
  if (!isIOS) {
    useEffect(() => {
      NavBar.getNavBarInfo().then((res: { hasNavBar: boolean, isNavBarShow: boolean, navBarHeight: number }) => {
        if (res.isNavBarShow) {
          setData({
            hasNavBar: res.hasNavBar,
            isNavBarShow: res.isNavBarShow,
            navBarHeight: res.navBarHeight / PixelRatio.get() + 0.5
          })
        }
      })
      NavBar.setNavBarListener()
      /**
       * 
       * @param res  携带参数 ，类型为 number
       *  1 导航栏隐藏了
       *  0 导航栏显示了
       */
      function onNavBarChanged (res: 0 | 1) {
        setData({
          ...data,
          isNavBarShow: res === 0
        })
      }
      const onNavBarChangedEvent = (NavBarEventEmitter as EventEmitter).addListener('onNavBarChanged', onNavBarChanged)
      return () => {
        onNavBarChangedEvent && onNavBarChangedEvent.remove()
      }
    }, [])
  }

  return data
}

export default useAndroidNavBar