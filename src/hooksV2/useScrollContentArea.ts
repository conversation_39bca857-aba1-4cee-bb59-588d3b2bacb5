import { useRef } from "react"
import { useSafeAreaInsets } from "react-native-safe-area-context"
import { height } from '../constantsV2/dimensions'
import useAndroidNavBar from "./useAndroidNavBar"
const HEADER_HEIGHT = 44
const MAIN_TAB_HEADER_HEIGHT = 109

export default function useScrollContentArea () {
  const insets = useSafeAreaInsets()
  const windowHeight = height
  const { top } = insets
  const navBarInfo = useAndroidNavBar()
  const contentHeight = useRef({
    safeAreaTop: top,
    pageHeader: HEADER_HEIGHT,
    mainTabHeader: MAIN_TAB_HEADER_HEIGHT,
    contentHeight: windowHeight - HEADER_HEIGHT - top - MAIN_TAB_HEADER_HEIGHT - (navBarInfo.isNavBarShow ? navBarInfo.navBarHeight : 0)
  })
  return contentHeight.current
}