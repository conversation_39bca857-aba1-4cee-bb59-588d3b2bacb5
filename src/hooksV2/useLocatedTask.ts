import { HeaderContext } from "../contextV2/HeaderContext"
import { NativeInfoContext } from "../contextV2/nativeInfoContext"
import { PageScrollViewRefContext } from "../contextV2/PageScrollViewRefContext"
import { useCallback, useContext } from "react"
import { LayoutChangeEvent } from 'react-native'

interface LocatedTaskReturn {
  onLayoutProperty: { onLayout?: (event: LayoutChangeEvent) => void }
  extraReportParams: { sourceType?: string }
}

export default function useLocatedTask(taskId: string | number): LocatedTaskReturn {
  const pageScrollViewContext = useContext(PageScrollViewRefContext)
  const { height: headerHeight = 0 } = useContext(HeaderContext)
  const { srcChannel: sourceType = '', toTaskId } = useContext(NativeInfoContext)
  const isLocatedTask = toTaskId == taskId

  const onLayout = useCallback((event: LayoutChangeEvent) => {
    if (headerHeight > 0 && isLocatedTask) {
      event?.target.measure((x: number, y: number, width: number, height: number, pageX: number, pageY: number) => {
        pageScrollViewContext!.current.scrollTo({ x: 0, y: pageY - headerHeight })
      });
    }

  }, [headerHeight, isLocatedTask])

  return {
    onLayoutProperty: isLocatedTask ? { onLayout } : {},
    extraReportParams: isLocatedTask && sourceType ? { sourceType } : {},
  }
}