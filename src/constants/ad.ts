export enum AD_SOURCE {
  AD_SIGN_IN = 'AD_SIGN_IN', // 看广告签到
  SIGN_IN_LOTTERY = 'SIGN_IN_LOTTERY', // 签到抽奖
  AD_VIDEO = 'VIDEO_TASK', // 看视频
  DAILY = 'DAILY_TASK', // 日常任务
  FLIP = 'FLIP_CARD_TASK', // 翻卡牌任务
  SIGN_IN = 'SIGN_IN', // 签到
  VALUABLE = 'HIGH_PRICE_TASK', // 惊喜任务（高客单价）
  LISTEN_TASK = 'LISTEN_TASK', // 听书任务奖励
  SUSPEND_TOUCH = 'SUSPEND_TOUCH', // TOUCH
  AUTO_SIGN_IN = 'AUTO_SIGN_IN', // 自动签到弹窗
  RANDOM_AD_REWARD = 'RANDOM_AD_REWARD', // 随机广告奖励
  SIGN_IN_WAKE_UP = 'SIGN_IN_WAKE_UP', // 签到唤端广告奖励
  SHARE_COINS = 'SHARE_COINS', // 分享得金币
}

export enum RewardType {
  SIGN_IN = 1, // 签到
  AD_SIGN_IN = 2, // 看广告签到
  LAUNCH = 3, // 唤端
  VALUABLE = 4, // 惊喜任务（高客单价）
  AD_VIDEO = 5, // 看视频
  DAILY = 6, // 日常任务
  FLIP = 7, // 翻卡牌任务
  SIGN_IN_LOTTERY = 8, // 签到抽奖
  TREASURE_BOX = 9, // 宝箱
  RED_PACKET_RAIN = 10, // 红包雨
  AD_RED_PACKET_RAIN = 11, // 看广告领红包雨
  LISTEN_TASK = 12,   // 听书任务奖励
  EXTRA_REWARDS = 13,  // 领取额外奖励
  SHARE_COINS = 14,  // 分享得金币
  RECEIVE_SHARE_COINS = 15, // 领取瓜分大奖
}

export enum NonCoinRewardType {
  SHARE_COINS = 1
}

// export const AD_POSITION = {
//   slotId: 307,
//   positionName: 'incentive_welfare',
// }

// export const LISTEN_TASK_POSITION = {
//   slotId: 307,
//   positionName: 'incentive_welfare',
// }

// 适配极速版广告位 id
export const AD_POSITION = {
  slotId: 309,
  positionName: 'lite_incentive_welfare',
}

export const LISTEN_TASK_POSITION = {
  slotId: 309,
  positionName: 'lite_incentive_welfare',
}

// 喜马拉雅极速版
export const XM_LITE_APP_ID = '1463'

export enum FallbackReqType {
  NORMAL = 0,  // 正常曝光完成任务发放奖励  
  FALLBACK = 1 // 未正常曝光，异常兜底发放的奖励
}

export enum TransactionType {
  INCOME = 1, // 收入
  EXPENSE = 2, // 支出
  WITHDRAW = 3, // 提现
}