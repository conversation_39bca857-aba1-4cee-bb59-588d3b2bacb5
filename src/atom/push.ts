import { atom } from 'jotai';

type PushMessage = {
  title: string;
  subtitle: string;
}

export const pushArrayAtom = atom<PushMessage[]>([]);

export const addMessageAtom = atom(null, (get, set, messages: PushMessage[]) => {
  set(pushArray<PERSON>tom, [...get(pushArray<PERSON>tom), ...messages]);
});

export const removeMessageAtom = atom(null, (get, set) => {
  set(pushArrayAtom, get(pushArrayAtom).slice(1));
});

export const pushMessageAtom = atom<PushMessage | null>((get) => {
  return get(pushArray<PERSON>tom)[0]
});
