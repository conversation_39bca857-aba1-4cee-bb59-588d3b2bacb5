import { AD_SOURCE } from 'constants/ad';
import { atom } from 'jotai';
import type { DailyTaskItem } from 'services/welfare';
import { TouchTaskInfo } from 'services/welfare/touch';

// 控制红包雨是否显示的atom
export const showRedPacketRainAtom = atom(false);
export const redPacketRainTaskAtom = atom<DailyTaskItem | TouchTaskInfo>();
export const redPacketRainPositionAtom = atom<AD_SOURCE>(AD_SOURCE.DAILY);

// 存储游戏结束回调的atom
interface RedPacketRainGameEndCallback {
  callback?: (score: number) => Promise<void> | void;
}

export const redPacketRainGameEndCallbackAtom = atom<RedPacketRainGameEndCallback>({
  callback: undefined
}); 