import { atom } from 'jotai';
import { Platform, NativeModules, NativeEventEmitter } from 'react-native';

// 资源状态类型
export type ResourceStatus = 'idle' | 'loading' | 'success' | 'error';

// 优先级枚举
export enum ResourcePriority {
  HIGH = 0,    // 高优先级
  MEDIUM = 1,  // 中优先级
  LOW = 2      // 低优先级
}

// 资源项
export interface ResourceItem {
  url: string;     // 原始URL
  path: string;    // 本地路径（下载后）
  status: ResourceStatus;
  priority?: ResourcePriority; // 资源优先级
  error?: Error;
}

// 资源字典
export type ResourcesMap = Record<string, ResourceItem>;

// URL映射字典，用于记录URL到key的映射，实现排重
export type UrlKeyMap = Record<string, string[]>;

// 初始化资源管理atom
export const videoResourcesAtom = atom<ResourcesMap>({});

// 初始化URL映射字典atom
export const urlKeyMapAtom = atom<UrlKeyMap>({});

// 事件发射器单例
let downloadEventEmitter: NativeEventEmitter | null = null;

// 获取事件发射器
export const getDownloadEventEmitter = () => {
  if (!downloadEventEmitter && Platform.OS === 'android') {
    downloadEventEmitter = new NativeEventEmitter(NativeModules.Download);
  }
  return downloadEventEmitter;
};

// 资源下载状态更新atom
export const updateResourceStatusAtom = atom(
  null,
  (get, set, update: { key: string; status: ResourceStatus; url: string; path?: string; priority?: ResourcePriority; error?: Error }) => {
    const { key, status, url, path, priority, error } = update;
    const resources = get(videoResourcesAtom);
    const urlKeyMap = get(urlKeyMapAtom);

    // 更新资源状态
    set(videoResourcesAtom, {
      ...resources,
      [key]: {
        url,
        path: path || (resources[key]?.path || ''),
        priority: priority !== undefined ? priority : (resources[key]?.priority || ResourcePriority.MEDIUM),
        status,
        ...(error ? { error } : {})
      }
    });

    // 更新URL到key的映射
    if (!urlKeyMap[url]) {
      set(urlKeyMapAtom, {
        ...urlKeyMap,
        [url]: [key]
      });
    } else if (!urlKeyMap[url].includes(key)) {
      set(urlKeyMapAtom, {
        ...urlKeyMap,
        [url]: [...urlKeyMap[url], key]
      });
    }
  }
);

// 根据URL查找已加载的资源
export const findResourceByUrlAtom = atom(
  (get) => (url: string) => {
    const resources = get(videoResourcesAtom);
    const urlKeyMap = get(urlKeyMapAtom);

    // 查找使用相同URL的第一个成功加载的资源
    const keys = urlKeyMap[url] || [];
    for (const key of keys) {
      const resource = resources[key];
      if (resource && resource.status === 'success') {
        return { key, resource };
      }
    }

    return null;
  }
);

// 资源下载atom
export const downloadResourceAtom = atom(
  null,
  async (get, set, params: { key: string; url: string; priority?: ResourcePriority }) => {
    const { key, url, priority = ResourcePriority.MEDIUM } = params;
    const resources = get(videoResourcesAtom);
    const findResource = get(findResourceByUrlAtom);

    // 如果资源已存在并且下载成功，则不重复下载
    if (resources[key]?.status === 'success' && resources[key]?.path) {
      return resources[key].path;
    }

    // 检查是否有其他使用相同URL的资源已经成功加载
    const existingResource = findResource(url);
    if (existingResource) {
      // 复用已有资源的路径
      set(updateResourceStatusAtom, {
        key,
        status: 'success',
        url,
        path: existingResource.resource.path,
        priority
      });
      return existingResource.resource.path;
    }

    // 更新资源状态为加载中
    set(updateResourceStatusAtom, { key, status: 'loading', url, priority });

    try {
      // iOS直接使用原始URL
      if (Platform.OS === 'ios') {
        set(updateResourceStatusAtom, { key, status: 'success', url, path: url, priority });
        return url;
      }

      // Android需要下载视频
      // 添加优先级参数传递给原生模块
      console.log('downloadResourceAtom', url);
      await NativeModules.Download.startDownload(url);

      // 返回promise，等待下载完成
      return new Promise<string>((resolve, reject) => {
        // 获取事件发射器
        const emitter = getDownloadEventEmitter();
        if (!emitter) {
          set(updateResourceStatusAtom, {
            key,
            status: 'error',
            url,
            priority,
            error: new Error('Download emitter not available')
          });
          reject(new Error('Download emitter not available'));
          return;
        }

        // 直接使用NativeEventEmitter监听下载完成事件
        const subscription = emitter.addListener('onDownloadComplete', (data: any) => {
          if (data.downloadUrl === url) {
            set(updateResourceStatusAtom, {
              key,
              status: 'success',
              url,
              path: data.path,
              priority
            });

            // 清理事件监听和定时器
            subscription.remove();
            clearTimeout(timeout);
            resolve(data.path);
          }
        });

        // 超时处理
        const timeout = setTimeout(() => {
          subscription.remove();
          set(updateResourceStatusAtom, {
            key,
            status: 'error',
            url,
            priority,
            error: new Error('Download timeout')
          });
          reject(new Error('Download timeout'));
        }, 30000); // 30秒超时
      });
    } catch (error) {
      set(updateResourceStatusAtom, {
        key,
        status: 'error',
        url,
        priority,
        error: error as Error
      });
      throw error;
    }
  }
);

// 批量下载资源
export const downloadResourcesAtom = atom(
  null,
  async (get, set, resources: Record<string, { url: string; priority?: ResourcePriority } | string>) => {
    const results: Record<string, string> = {};
    const errors: Record<string, Error> = {};

    // 准备下载资源的队列，按优先级排序
    const downloadQueue = Object.entries(resources)
      .map(([key, resource]) => {
        // 处理两种可能的资源格式
        const url = typeof resource === 'string' ? resource : resource.url;
        const priority = typeof resource === 'string' ? ResourcePriority.LOW : (resource.priority ?? ResourcePriority.MEDIUM);
        return { key, url, priority };
      })
      // 按优先级排序（优先级值越小越优先）
      .sort((a, b) => a.priority - b.priority);

    // 按优先级顺序下载资源
    for (const { key, url, priority } of downloadQueue) {
      try {
        const path = await set(downloadResourceAtom, { key, url, priority });
        results[key] = path;
      } catch (error) {
        errors[key] = error as Error;
      }
    }

    return { results, errors };
  }
); 