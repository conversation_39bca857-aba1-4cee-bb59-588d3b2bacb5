import { Dimensions, Platform, TextStyle } from 'react-native'
import { StyledProps } from 'styled-components'
import { width } from './dimensions'

export const PAGE_HEADER_HEIGHT = 44

const MAIN_TAB_HEADER_HEIGHT_SCALE = width / 750
export const MAIN_TAB_HEADER_HEIGHT = MAIN_TAB_HEADER_HEIGHT_SCALE * 218

export const MAIN_TAB_TAB_BAR_HEIGHT = 51

export const CATEGORIES_TAB_BAR_HEIGHT = 50

export const MAIN_TAB_DEFAULT_OFFSET_Y = Platform.isPad ? 350 : 285

export const MAIN_TAB_HALF_OFFSET_Y = Platform.isPad ? 230 : 180

export const SharePanelBaseHeight = 203

export const textAlignMiddleStyle: Partial<StyledProps<TextStyle>> = {
  includeFontPadding: false,
  textAlignVertical: 'center',
}
