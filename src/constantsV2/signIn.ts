import { UserIDTag } from '../typesV2/signInNew'

export const defaultSignInRecords = [
  {
    day: 1,
    name: '',
    desc: '',
    logo: '',
    unsignedLogo: '',
    isReal: false,
    awardStatus: 0,
    isPlaceholder: true,
  },
  {
    day: 2,
    name: '',
    desc: '',
    logo: '',
    unsignedLogo: '',
    isReal: false,
    awardStatus: 0,
    isPlaceholder: true,
  },
  {
    day: 3,
    name: '',
    desc: '',
    logo: '',
    unsignedLogo: '',
    isReal: false,
    awardStatus: 0,
    isPlaceholder: true,
  },
  {
    day: 4,
    name: '',
    desc: '',
    logo: '',
    unsignedLogo: '',
    isReal: false,
    awardStatus: 0,
    isPlaceholder: true,
  },
  {
    day: 5,
    name: '',
    desc: '',
    logo: '',
    unsignedLogo: '',
    isReal: false,
    awardStatus: 0,
    isPlaceholder: true,
  },
  {
    day: 6,
    name: '',
    desc: '',
    logo: '',
    unsignedLogo: '',
    isReal: false,
    awardStatus: 0,
    isPlaceholder: true,
  },
  {
    day: 7,
    name: '',
    desc: '',
    logo: '',
    unsignedLogo: '',
    isReal: false,
    awardStatus: 0,
    isPlaceholder: true,
  },
]

export const signInGiftTooltip = {
  [UserIDTag.UNSET]: '',
  [UserIDTag.activeDevice]: '',
  [UserIDTag.newDevice]: '新人+100%',
  [UserIDTag.recalledDevice]: '回归+100%',
}


export const signInUserIDLabel = {
  [UserIDTag.UNSET]: '',
  [UserIDTag.activeDevice]: '活跃用户',
  [UserIDTag.newDevice]: '新用户',
  [UserIDTag.recalledDevice]: '召回用户',
}

