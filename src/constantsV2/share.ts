import icon_wechat_share from 'appImagesV2/icon_wechat_share'
import icon_moment_share from 'appImagesV2/icon_moment_share'
import icon_weibo_share from 'appImagesV2/icon_weibo_share'

import { NativeModules } from "react-native";
import icon_qq_share from '../appImagesV2/icon_qq_share';
const { Share: XMShare } = NativeModules


export enum XMShareChannel {
  wechatMoment = 'weixinGroup',
  wechat = 'weixin',
  weibo = 'tSina',
  qqZone = 'qzone',
  qq = 'qq',
  xmGroup = 'xmGroup',
  tingZone = 'tingZone', //(听友圈iOS会默认弹出“已发送” 无法更改)
  xmCommunity = 'community',
  qrcode = 'qrcode',
  url = 'url',
  poster = 'poster',
  copylink = 'copylink'
}
export const SHARE_TYPE_DATA = [
  {
    type: XMShareChannel.wechat,
    title: '微信好友',
    icon: icon_wechat_share
  },
  {
    type: XMShareChannel.wechatMoment,
    title: '朋友圈',
    icon: icon_moment_share
  },
  {
    type: XMShareChannel.weibo,
    title: '微博',
    icon: icon_weibo_share
  },
  {
    type: XMShareChannel.qq,
    title: 'QQ',
    icon: icon_qq_share
  },
]
export enum XMShareType {
  link = 'link',
  music = 'music',
  picture = 'picture',
  video = 'video',
}

export interface XRNShareParams {
  type: XMShareType,
  channel: XMShareChannel[], //如果channel数组中只有一个值，那么就不会展示分享弹框;如果数组中有多个channel，那么会展示分享弹框（原生分享弹框）
  title: string,
  desc: string,
  link: string, // 微信 点击卡片的跳转链接
  dataUrl: string, //微信 点击播放按钮播放的链接
  imgUrl: string
}

export default async function XRNShare (options: XRNShareParams): Promise<any> {
  try {
    const { type, channel, title, desc, link, dataUrl, imgUrl } = options
    const shareParams = {
      shareType: 'default',
      channel: channel,
      params: {
        title: title,
        desc: desc,
        link: link, // 微信 点击卡片的跳转链接
        type: type,
        dataUrl: dataUrl, //微信 点击播放按钮播放的链接
        imgUrl: imgUrl
      }
    }
    await XMShare.share(shareParams)
  } catch (err) {
    console.log(err)
  }
}
