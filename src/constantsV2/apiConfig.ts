import { EnvName } from 'typesV2/common'
import rnEnv, { rnVersion } from '../../rnEnv'

const API_TEST = 'm.test.ximalaya.com/'
const API_UAT = 'm.uat.ximalaya.com/'
const API_PRODUCT = 'm.ximalaya.com/'

const API_TEST_M = 'mobile.test.ximalaya.com/'
const API_UAT_M = 'mobile.uat.ximalaya.com/'
const API_PRODUCT_M = 'mobile.ximalaya.com/'

const API_TEST_AD = `dubbing.test.ximalaya.com/`
const API_UAT_AD = `dubbing.uat.ximalaya.com/`
const API_PRODUCT_AD = `dubbing.ximalaya.com/`

const API_PRODUCT_HYBRID = 'hybrid.ximalaya.com/'
const API_UAT_HYBRID = 'hybrid.uat.ximalaya.com/'
const API_TEST_HYBRID = 'hybrid.test.ximalaya.com/'

const API_OPEN_API_PRODUCT = 'api.ximalaya.com/'
const API_OPEN_API_TEST = 'api.test.ximalaya.com/'
const API_OPEN_API_UAT = 'api.uat.ximalaya.com/'

const API_WWW_PRODUCT = 'pc.ximalaya.com/'
const API_WWW_TEST = 'test.ximalaya.com/'
const API_WWW_UAT = 'uat.ximalaya.com/'

const API_ADSE_PRODUCT = 'adse.ximalaya.com/'
const API_ADSE_TEST = 'adse.test.ximalaya.com/'
const API_ADSE_UAT = 'adse.uat.ximalaya.com/'

export const API_HYBRID = {
  [EnvName.prod]: API_PRODUCT_HYBRID,
  [EnvName.test]: API_TEST_HYBRID,
  [EnvName.uat]: API_UAT_HYBRID,
}

export const API_DEFAULT = {
  [EnvName.prod]: API_PRODUCT,
  [EnvName.test]: API_TEST,
  [EnvName.uat]: API_UAT,
}

export const API_M = {
  [EnvName.prod]: API_PRODUCT_M,
  [EnvName.test]: API_TEST_M,
  [EnvName.uat]: API_UAT_M,
}

export const API_AD = {
  [EnvName.prod]: API_PRODUCT_AD,
  [EnvName.test]: API_TEST_AD,
  [EnvName.uat]: API_UAT_AD,
}

export const API_OPEN_API = {
  [EnvName.prod]: API_OPEN_API_PRODUCT,
  [EnvName.test]: API_OPEN_API_TEST,
  [EnvName.uat]: API_OPEN_API_UAT,
}

export const API_WWW = {
  [EnvName.prod]: API_WWW_PRODUCT,
  [EnvName.test]: API_WWW_TEST,
  [EnvName.uat]: API_WWW_UAT,
}

export const API_ADSE = {
  [EnvName.prod]: API_ADSE_PRODUCT,
  [EnvName.test]: API_ADSE_TEST,
  [EnvName.uat]: API_ADSE_UAT,
}

export const newSignInAID = 87
export const monthlyTicketAID = () => (rnEnv.isTest() ? 143 : 144)

export const mobilePushSettingConfig = {
  businessType: 130,
  toId: 0,
}

export const ECommerceComponentId = () => (rnEnv.isOnline() ? 1 : 86) // 与电商部约定好的

export const Api = {
  credit: `web-hybrid-server/api/point/info`, // 获取积分
  signInList: (aid: number) => `web-activity/signIn/records?aid=${aid}`, // 签到列表
  // signIn: `web-activity/signIn/action`, // 签到
  signIn: (aid: number) => `web-activity/signIn/action?aid=${aid}`, // 签到
  excitationInfo: (aid: number, taskId: number) => `web-activity/task/queryTaskRecords?aid=${aid}&taskId=${taskId}`, // 看视频任务进度信息
  excitationToken: `web-activity/task/genTaskToken`, // 获取激励视频任务token
  excitationTokenV2: `web-activity/task/v2/genTaskToken`, // 获取激励视频任务token v2
  taskProgress: `web-activity/task/incrTaskProgress`, // 更新任务进度
  taskProgressV2: `x-web-activity/task/v2/incrTaskProgress`, // 更新任务进度 v2
  creditMarket: `xfm-activity-app/integral/autoLogin`, // 直接跳兑吧
  creditMarket2: (url: string) => `xfm-activity-app/integral/autoLogin?redirectUrl=${url}`, // banner、里程碑、糖葫芦上跳转链接
  topSectionInfo: (tabName: 'earn' | 'spend' | 'global') => `web-hybrid-server/api/welfareCenter/configInfo?tabName=${tabName}`, // tab内的通知+糖葫芦+banner数据
  miaoSha: `xfm-activity-app/integral/index?business_type=1`, // 获取秒杀信息
  postAward: `web-activity/task/drawTaskAward`, // 任务列表领取任务奖励
  postAwardNew: `web-activity/task/v2/drawTaskAward`, // 任务列表领取任务奖励(新的领取接口)
  postAwardNewV2: `x-web-activity/task/v2/drawTaskAward`, // 任务列表领取任务奖励(新的领取接口)
  postTaskList: `web-activity/task/taskRecords?tag=rn`, // 任务列表
  achieveMillstone: `xfm-integral-mall-activity-web/activity/millstone/achieveMillstone`, // 达到里程碑
  hasWaitingAward: `xfm-integral-mall-activity-web/activity/millstone/hasWaitingAward`, // 是否有悬浮窗
  closeAward: `xfm-integral-mall-activity-web/activity/millstone/closeAward`, // 关闭悬浮框
  receiveAward: `xfm-integral-mall-activity-web/activity/millstone/receiveAward`, // 领取奖励
  getActivityInfo: (url_suffix: string) => `ad-activity/welfare/activity?${url_suffix}`, // 开心游戏列表
  reportVideoWatchResult: (url_suffix: string) => `ad-activity/welfare/report?${url_suffix}`, // 看完视频领积分
  refreshClientTask: `web-activity/task/refreshClientTask`, // 通用点击型任务，在点击完成任务时就调用这个接口
  refreshClientTaskV2: `web-activity/task/v2/refreshClientTask`, // 通用点击型任务，在点击完成任务时就调用这个接口
  signInListNew: (aid?: number) => `x-web-activity/signIn/v2/querySignInInfo?aid=${typeof aid === 'number' ? aid : newSignInAID}&v=new`, // 新版签到列表接口
  signInListNewNotLogin: 'web-config/api/jc/queryAllData?app=web&group=hybrid&key=signin-days', // yu.guo 签到天数，配置接口 Y
  signInNew: `x-web-activity/signIn/v2/signIn?v=new`, // 新版签到列表接口
  posterList: 'daily-label-mobile/poster/list/', // 日签海报接口
  allTaskRecordsList: 'web-activity/task/queryTaskRecords', //获取所有任务列表
  postTaskListNew: 'web-activity/task/v2/taskRecords?tag=pc', //获取任务列表新地址
  postTaskListNewNotLogin: 'web-config/api/jc/queryAllData?app=web&group=hybrid&key=checkIn-welfareList', //  模拟数据-未登录状态，配置接口 Y
  commonConfig: (queryAll?: boolean) => `web-config/api/jc/${queryAll ? 'queryAllData' : 'queryData'}`, // 获取通用配置接口
  finishThirdpartyTask: 'openapi-feeds-stream-app/activity/complete/task', // 完成第三方换量任务接口
  thirdpartyTask: 'web-activity/task/v2/genGuideLink', // 获取第三方跳转路径
  giftReceiveStatus: (giftPackageNo: string) => `business-marketing-support-gift-mobile-web/giftpackage/v2/${giftPackageNo}/status/${Date.now()}`,
  userIsNew: 'pc-application-server/user/isNew', // 判断用户是否是pc客户端新用户
  eCommerceComponent: (componentId?: number) =>
    `ecommerce-mall-mobile-web/search/getComponentById?componentId=${typeof componentId === 'number' ? componentId : ECommerceComponentId()}&pageNo=1&pageSize=10`,
  eCommerceProductListByPoolId: (poolId: number, componentId: number, pageNo?: number) =>
    `ecommerce-mall-mobile-web/search/getProductListByPoolId?poolId=${poolId}&componentId=${componentId}${typeof pageNo === 'number' ? `&pageNo=${pageNo}&pageSize=10` : ''}`,
  eCommerceComponentListById: (pageId: number) => `ecommerce-mall-mobile-web/search/getResourceById?pageId=${pageId}`,
  giftPackageReceiveStatus: `business-marketing-support-gift-mobile-web/giftpackage/receive/status/batch/query`,
  getMonthlyTicketInfo: () => `monthly-ticket-mobile/my-ticket/general/${Date.now()}`,
  queryMonthlyTicketSignInInfo: () => `x-web-activity/signIn/v2/querySignInInfo?aid=${monthlyTicketAID()}`,
  monthlyTicketSignIn: 'x-web-activity/signIn/v2/signIn',
  monthlyTicketTaskInfo: 'x-web-activity/task/v2/taskRecords',
  getPushStatus: `mobile-settings/get?businessType=${mobilePushSettingConfig.businessType}&toId=${mobilePushSettingConfig.toId}`,
  setPushStatus: 'mobile-settings/v2/set',
  setPushStatusNoEncrypt: 'mobile-settings/set',
  generateNonce: 'mobile-settings/nonce',
  monthlyTicketGuide: 'x-web-activity/monthlyTicket/guide',
  earnGuideMonthlyTicket: 'x-web-activity/monthlyTicket/earnGuideMonthlyTicket',
  getSignUserType: `x-web-activity/signIn/v2/getSignUserType?aid=${newSignInAID}`,
  getCardListData: `point-http/card/cardList?ad=1`,
  activateAdMaterialSummaryReport: `point-http/activateAdMaterialSummaryReport`,
  getCardListDataNotLogIn: 'web-config/api/jc/queryAllData?app=web&group=hybrid&key=square-searoom', // 广场+海景房配置，未登录的数据配置，配置接口 Y
  getCommodityActivityList: `point-http/commodityActivity/list`,
  postTaskListV2: 'x-web-activity/task/v2/taskRecords/v2?tag=pc', //获取任务列表新地址v2 只用在隔日领奖需求
  getLocalPushTimeTable: 'x-web-activity/signIn/v2/signRemind', // 获取本地push 的时间表
  chooseTaskAward: 'x-web-activity/task/v2/chooseTaskAward', // 签到奖励多选一
  queryDrawActivityInfo: ({ activityId }: { activityId: number }) => `x-web-activity/draw/activity/queryDrawActivityInfo?activityId=${activityId}`,
  pointExchange: `x-web-activity/point/exchange/action`,
  pointExchangeV2: `x-web-activity/point/exchange/actionV2`,
  reportCashVisit: 'point-http/visitCash/point/visitReport', // 上报访问过金币活动
  cashBalance: 'point-http/account/cash', // 获取金币余额
  cashConfig: 'point-http/account/cash/config', // 获取金币的 AB 配置
  chipsBalance: (id: number) => `business-marketing-voucher-mobile/voucher/statistic/${id}`,
  refreshTaskList: `x-web-activity/point/refreshTask`, // 任务包换一换扣积分接口
  adDPTaskList: ({ ts, name }: { ts: number; name: string }) => `xm/ad/ts-${ts}?name=${name}`, // DP任务广告接口 文档:https://alidocs.dingtalk.com/i/nodes/MNDoBb60VLrORlpeS3mnoNje8lemrZQ3?sideCollapsed=true&iframeQuery=utm_source%253Dportal%2526utm_medium%253Dportal_new_tab_open&utm_scene=team_space&dontjump=true&corpId=ding51f195092fd77474
  getSignInAward: `x-web-activity/signIn/v2/receiveSignInAward?v=new`, // 领取签到奖励接口
  queryCashTaskInfo: ({ ts, ticket }: { ts: number, ticket: string }) => `incentive/ting/queryWelfareAwardInfo/ts-${ts}?ticket=${ticket}&rnVersion=${rnVersion}&requestVersion=${********}&sourceName=welfare`, // 查询现金任务信息 https://alidocs.dingtalk.com/i/nodes/N7dx2rn0JbZ9RE2eTK23qMgLJMGjLRb3?utm_scene=team_space
  carveUpCashAward: ({ ts }: { ts: number }) => `incentive/ting/rewardCarveUpCashAward/ts-${ts}?requestVersion=${********}`, // 瓜分现金 https://alidocs.dingtalk.com/i/nodes/EpGBa2Lm8azv7qpriLbR2zKYWgN7R35y
  // 高客单价任务状态检查 https://alidocs.dingtalk.com/i/nodes/ZX6GRezwJl7DEg5kHq0poZwaVdqbropQ?utm_scene=team_space&iframeQuery=anchorId%3Duu_m2oc42mvsf0b88tnk2
  checkAdAppTaskStatus: ({ ts }: { ts: number; }) => `incentive/ting/highPriceTaskStatusCheck/ts-${ts}`,
  highPriceTaskClickReport: ({ ts }: { ts: number; }) => `incentive/ting/highPriceTaskClickReport/ts-${ts}`,
  // 高客单价任务奖励领取 https://alidocs.dingtalk.com/i/nodes/ZX6GRezwJl7DEg5kHq0poZwaVdqbropQ?utm_scene=team_space&iframeQuery=anchorId%3Duu_m2oc4trg71af5xpna8
  rewardAdAppTask: ({ ts }: { ts: number; }) => `incentive/ting/rewardHighPriceTaskAward/ts-${ts}`,
  hideChipsHitTraits: ({ ts }: { ts: number; }) => `incentive/ting/hitTraits/ts-${ts}`,
}
