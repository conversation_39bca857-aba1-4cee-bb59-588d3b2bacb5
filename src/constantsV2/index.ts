import { isAndroid } from '@xmly/rn-utils/dist/device'
import { API_DEFAULT } from './apiConfig'
import { isEnv } from '../utilsV2/getEnv'
import rnEnv from '../../rnEnv'
import { CashConfigExpGroupId, CashConfigChangeTaskId } from '../typesV2/goldCoin'
import { XMAppVersionHelper } from '@xmly/rn-sdk'

export const checkSlotId = (isTest: boolean) => (isTest ? (isAndroid ? 945177808 : 945177806) : isAndroid ? 945198941 : 945177806)

// 签到AID
// export const signAid = env.isTest ? 17 : 8
export const signAid = async () => ((await isEnv()) ? 17 : 8)

// 任务AID
// export const excitationTaskAid = env.isTest ? 19 : 11
export const excitationTaskAid = async () => ((await isEnv()) ? 19 : 11)

// 任务ID
// export const excitationTaskId = env.isTest ? 24 : 46
export const excitationTaskId = async () => ((await isEnv()) ? 24 : 46)

// 任务ID
// export const excitationWelfareTaskId = env.isTest ? 29 : 47
export const excitationWelfareTaskId = async () => ((await isEnv()) ? 29 : 47)

// 任务ID
export const activeTaskId = 88 // 日签海报分享任务的aid

// 主要的任务活动ID
export const taskListAid = 112

// 每日挑战任务ID
export const everydayChallengeTaskAid = async () => {
  const isHigherVersion = await XMAppVersionHelper.notLowerThan('9.1.78')
  return (rnEnv.isOnline() ?
    isHigherVersion ? 183 : 177
    :
    161
  )
}

// 碎片 aid
export const ChipsAID = () => (rnEnv.isOnline() ? 137 : 147)

// export const sleepTaskId = env.isTest ? 31 : 48
export const sleepTaskId = async () => ((await isEnv()) ? 31 : 48)

export const imageDomain = '//imagev2.xmcdn.com'
export const imageTestDomain = '//imagev2.test.ximalaya.com/'

// export const creditDetailUrl = (isTest: boolean) =>
//   `https://${
//     isTest ? API_DEFAULT.test : API_DEFAULT.prod
//   }starwar/task/listen/layout/center/detail`

export const newCreditDetailUrl = (isTest: boolean, cookie: string) => `https://${isTest ? API_DEFAULT.test : API_DEFAULT.prod}gatekeeper/credit-earn-h5/home?code=${cookie}`
// export const newCreditDetailUrl = (isTest: boolean, cookie: string) =>
//   `http://dora.ximalaya.com:3000/home?code=${cookie}`

export const newCashUrl = ({ isTest, cookie }: { isTest: boolean; cookie: string }) =>
  `https://${isTest ? API_DEFAULT.test : API_DEFAULT.prod}gatekeeper/credit-earn-h5/home-new/cash?code=${cookie}&_full_with_transparent_bar=1&with_chips=1`

export const chipsDetailUrl = ({ isTest, noCash }: { isTest: boolean; cookie: string; noCash: boolean }) =>
  `https://${isTest ? API_DEFAULT.test : API_DEFAULT.prod}gatekeeper/credit-earn-h5/home-new/chips?_full_with_transparent_bar=1&with_chips=1${noCash ? '&no_cash=1' : ''}`

export const newPointUrl = ({ no_cash, isTest, cookie }: { no_cash: boolean; isTest: boolean; cookie: string }) =>
  `https://${isTest ? API_DEFAULT.test : API_DEFAULT.prod}gatekeeper/credit-earn-h5/home-new/point?code=${cookie}&_full_with_transparent_bar=1${no_cash ? '&no_cash=1' : '&with_chips=1'
  }`

// export const newCashUrl = (isTest: boolean, cookie: string) =>
//   `http://dora.test.ximalaya.com:3000/home-new/cash?code=${cookie}&_full_with_transparent_bar=1`

// export const newPointUrl = (isTest: boolean, cookie: string) =>
//   `http://dora.test.ximalaya.com:3000/home-new/point?code=${cookie}&_full_with_transparent_bar=1`

export const creditRuleURL = (isTest: boolean) => `https://${isTest ? API_DEFAULT.test : API_DEFAULT.prod}starwar/task/listen/layout/center/rule`

export const newCreditUrl = 'https://pages.ximalaya.com/mkt/act/eead617afb8bb3ac?'

export const coinCenterRuleUrl = 'https://pages.ximalaya.com/mkt/act/5308fbf344db89cf?'
export const userExperienceSurveyUrl = 'https://m.ximalaya.com/cs-flow-app/page/common-feedback?appKey=780d0890-d306-4a44-b952-7e64bc985b99&_fix_keyboard=1'

export const aiServiceUrl = 'https://m.ximalaya.com/cs-bridge-web/page/contact-cs?systemNum=b8lAOgq_Sicjw9nLCzikFw&_fix_keyboard=1'

export const THEME_COLOR = '#FF9755'

export const CAN_OPEN_XMLY_SCHEME = '8.3.12'

export const IMAGE_LOCAL_PATH_PREFIX = 'file://'

export const duibaRemainsCategoryName = () => (rnEnv.isOnline() ? '会员权益' : '喜马精选')

export const defaultCommodityCover = 'https://imagev2.xmcdn.com/storages/fe6d-audiofreehighqps/60/A0/GKwRINsGUUDnAAA4vQFQNkL0.png'

export const getXIMAMallUrl = () => `https://pages${rnEnv.isOnline() ? '' : '.test'}.ximalaya.com/mkt/act/a527a39b8381a08f?utm_source=gfscjf`

export const RNBroadcastSignSuccessKey = 'RNCreditCenter_SignInSuccess'

export const HomeScrollViewEventName = {
  onMomentumScrollBegin: 'home_scroll_view_event_onMomentumScrollBegin',
  onMomentumScrollEnd: 'home_scroll_view_event_onMomentumScrollEnd',
  onScroll: 'home_scroll_view_event_onScroll',
  onScrollBeginDrag: 'home_scroll_view_event_onScrollBeginDrag',
  onScrollEndDrag: 'home_scroll_view_event_onScrollEndDrag',
}

export const MonthlyTicketVoteModalSrcName = 'mt_vote_modal'

export const EverydayChallengeModalListID = 'EverydayChallengeModalList'

export const tomorrowAwardTaskAID = () => (rnEnv.isTest() ? 159 : 172)

export const XUID_ticketConfig = {
  withdraw: {
    businessId: 'social',
    scene: 'point_withdrawal',
  },
  finishTask: {
    businessId: 'web_activity',
    scene: 'activity',
  },
  videoTask: {
    businessId: '',
    scene: '',
  },
  cashTask: {
    businessId: 'incentives',
    scene: 'gold_reward',
  },
  coinTask: {
    businessId: 'incentives',
    scene: 'gold_reward',
    salt: '0ab3443b71834b33be675af744d76ef0aaa74cd2b60c4b67',
  },
}

export const defaultGoldCoinABConfig = { groupId: CashConfigExpGroupId.default, changeTasksId: CashConfigChangeTaskId.default, showTutorial: false, risky: false }

export const cashTaskTouchChannel = 'index_touch';
