// import bg_header_dark from '../appImagesV2/bg_header_dark'
import {
  Categories,
  CheckInBar,
  CheckInPop,
  Common,
  Container,
  Credit,
  CreditDetail,
  ErrorView,
  EverydayChallenge,
  GroundList,
  Icon,
  LoginButton,
  PcDialog,
  PosterHistory,
  Task,
  ThemeStyle,
} from '../typesV2/themeInfo'
import icon_poster_history_entry_dark from '../appImagesV2/icon_poster_history_entry_dark'
import icon_circle_arrow_dark from '../appImagesV2/icon_circle_arrow_dark'
import bg_credit_detail_dark from '../appImagesV2/bg_credit_detail_dark'
import img_network_dark from '../appImagesV2/img_network_dark'
import icon_wait_gift_light from '../appImagesV2/icon_wait_gift_light'

const bg_header_dark =
  'https://imagev2.xmcdn.com/storages/76af-audiofreehighqps/5C/30/GMCoOSQIKhpTAAHrFAIXkQoD.png'

const common: Common = {
  bg_color: '#131313',
  item_bg_color: '#1b1b1b',
  title_color: '#FFFFFF',
  title_color_new: '#CFCFCF',
  sub_title_color: '#888888',
  placeholder_bg_color: '#888',
  no_more_tip_color: '#888',
}
const icon: Icon = {
  icon_circle_arrow: icon_circle_arrow_dark,
  bg_credit_detail: bg_credit_detail_dark,
  icon_wait_gift: icon_wait_gift_light,
}
const container: Container = {
  bg_color: '#131313',
  bg_image: bg_header_dark,
  height: 200,
}
const header = { color: '#F8F8F8' }

const credit: Credit = {
  overlay_warp_bg_color: 'rgba(0, 0, 0, 0.8)',
  checkbar_overlay_warp_bg_color: 'rgba(0, 0, 0, 0)',
  bg_color: common.item_bg_color,
  line_color: '#000',
  number_color: '#fff',
  desc_color: '#66666B',
  label_color: '#fff',
  icon_poster_history_entry: icon_poster_history_entry_dark,
}

const notificationBar = {
  color: '#FF4646',
  bg_color: '#24221F',
  normalColor: '#FFCCA1',
}

const checkInBar: CheckInBar = {
  bg_color: '#433D37',
  new_bg_color_checked: '#2A2A2A',
  new_bg_color: '#3E362F',
  sub_title_color: '#888',
  not_received_bg_color: '#5B4B3C',
  common_label_color: '#8D8D91',
  tomorrow_bg_color: '#5B4B3C',
  notification_switch_label: '#66666B',
}

const groundList: GroundList = {
  name_color: '#CFCFCF',
  title_color: '#FFFFFF',
  btn_left_bg: '#663939',
  btn_left_color: '#F70909',
  btn_left_color_top: 'rgba(255, 232, 224, 0.2)',
  btn_left_color_bottom: 'rgba(255, 217, 204, 0)',
  btn_right_bg: '#5C4A3D',
  btn_right_color: '#E76D10',
  bg_right_color_top: 'rgba(255, 232, 224, 0.2)',
  bg_right_color_bottom: 'rgba(255, 217, 204, 0)',
}

const categories: Categories = {
  tabBar: {
    label_active_color: '#CFCFCF',
    backgroundColor: '#121212',
    label_color: '#CFCFCF',
  },
  commodity: {
    countdown_wrapper_bg: '#463535',
  },
}

const task: Task = {
  browseTask: {
    wrapper_bg: '#24221F',
    label_color: '#FFCCA1',
  },
  taskList: {
    top_border_color: '#121212',
  },
  processBar: {
    bg_color: '#000000',
  },
  check_more_button: '#DCDCDC',
  item: {
    title_color: '#DCDCDC',
    sub_title_color: '#66666B',
    actionButton: {
      can_receive_bg: '#FFA045',
      not_finish_bg: '#FF444426',
      finished_bg: 'transparent',
      can_receive_label_color: '#FFFFFF',
      can_receive_label_color_v2: 'rgba(255,255,255,0.5)',
      not_finish_label_color: '#FF4444',
      not_finish_label_color_v2: '#FFFFFF',
      finished_label_color: '#666666',
      finished_label_color_v2: '#666666',
      can_receive_border_color: 'transparent',
      not_finish_border_color: 'transparent',
      finished_border_color: 'rgba(102, 102, 102,0.5)',

      // new add
      bg_start_color_not_finish: 'rgba(255, 105, 86, 0.2)',
      bg_end_color_not_finish: 'rgba(255, 70, 116, 0.2)',
      bg_start_color_can_receive: 'rgba(255, 101, 82, 0.1)',
      bg_end_color_can_receive: 'rgba(255, 65, 112, 0.1)',
      bg_start_color_finished: 'transparent',
      bg_end_color_finished: 'transparent',
    },
  },
  multiStepTask: {
    progressBarBG: '#000',
  },
}

const posterHistory: PosterHistory = {
  bg_color: '#121212',
  border_color: 'rgba(207, 207, 207, 0.5)',
  color: '#fff',
}

const creditDetail: CreditDetail = {
  bg_start_color: '#1A1611',
  bg_end_color: '#4B453C',
}

const checkInPop: CheckInPop = {
  gift_wrapper_bg: 'rgba(255, 251, 251, 0.05)',
  gift_wrapper_border_color: '#776565',
  notification_check_button_label: '#888',
  wrapper_bg_color: '#282828',
  left_group_btn_label: '#8D8D91',
  left_group_btn_border: '#1B1B1B',
  title_color: '#CFCFCF',
}

const loginButton: LoginButton = {
  label_color: '#888',
  border_color: '#888',
  button_label_color: '#888',
}
const errorView: ErrorView = {
  label_color: '#999',
  button_label_color: '#999',
  button_border_color: '#333',
  error_icon: img_network_dark,
}

const pcDialog: PcDialog = {
  bg_img:
    'https://imagev2.xmcdn.com/storages/cf64-audiofreehighqps/70/21/GKwRINsF7PzCAADF2wEbwN_d.png',
  bg_color: '#1E1E1E',
  title_color: '#888888',
  sit_title: '#CFCFCF',
  sub_title_color: '#888888',
  sub_title_opacity: 0.7,
}

const everydayChallenge: EverydayChallenge = {
  taskListModal: {
    bg: '#000000',
    closeButtonIconTintColor: '#DCDCDC',
    sectionItemBg: '#282828',
    progress: { title: '#DCDCDC', subTitle: '#DCDCDC' },
    bg_start_color: '#000',
    bg_end_color: '#000',
    text: '#fff',
    bg_list_start_color: 'rgba(255, 101, 82, 0.2)',
    bg_list_end_color: 'rgba(255, 65, 112, 0.2)',
  },
  progress: {
    trackBG: '#444',
  },
  summaryTaskItem: {
    label: '#DCDCDC',
  },
  summaryTaskList: {
    bg_start_color: 'rgba(255, 105, 86, 0.2)',
    bg_end_color: 'rgba(255, 70, 116, 0.2)',
    bg_button: 'rgba(255, 255, 255, 0.1)',
    text: 'rgba(255, 148, 148, 0.9)',
    bg_item: 'rgba(255, 255, 255, 0.1)',
    sub_text: 'rgba(220, 220, 220, 0.9)',
    bg_progress: 'rgba(255,255,255, 0.1)',
    bg_progress_bar: 'rgba(255, 210, 200, 0.25)',
  },
}
const dark: ThemeStyle = {
  common,
  icon,
  container,
  header,
  credit,
  notificationBar,
  checkInBar,
  groundList,
  categories,
  task,
  posterHistory,
  creditDetail,
  checkInPop,
  loginButton,
  errorView,
  pcDialog,
  everydayChallenge,
}

export default dark
