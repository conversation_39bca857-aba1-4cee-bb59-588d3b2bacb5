// import bg_header_light from '../appImagesV2/bg_header_light'
import {
  Categories,
  CheckInBar,
  CheckInPop,
  Common,
  Credit,
  CreditDetail,
  ErrorView,
  EverydayChallenge,
  GroundList,
  Icon,
  LoginButton,
  PcDialog,
  PosterHistory,
  Task,
  ThemeStyle,
} from '../typesV2/themeInfo'
import icon_poster_history_entry_light from '../appImagesV2/icon_poster_history_entry_light'
import icon_circle_arrow_light from '../appImagesV2/icon_circle_arrow_light'
import bg_credit_detail_light from '../appImagesV2/bg_credit_detail_light'
import icon_wait_gift_light from '../appImagesV2/icon_wait_gift_light'
import img_network_error_poster_history from '../appImagesV2/img_network_error_poster_history'

const bg_header_light =
  'https://imagev2.xmcdn.com/storages/d164-audiofreehighqps/9C/68/GMCoOScIKhpUAAKJ8AIXkQpY.png'

const common: Common = {
  bg_color: '#fff',
  item_bg_color: '#fff',
  title_color: '#111111',
  title_color_new: '#333',
  sub_title_color: '#666666',
  no_more_tip_color: '#999',
  placeholder_bg_color: '#ebebeb',
}
const icon: Icon = {
  icon_circle_arrow: icon_circle_arrow_light,
  bg_credit_detail: bg_credit_detail_light,
  icon_wait_gift: icon_wait_gift_light,
}
const container = {
  bg_color: '#f8f8f8',
  bg_image: bg_header_light,
  height: 356,
}

const header = { color: '#121212' }

const credit: Credit = {
  overlay_warp_bg_color: 'rgba(255, 255, 255, 0.8)',
  checkbar_overlay_warp_bg_color: 'rgba(255, 255, 255, 0)',
  bg_color: '#fff',
  line_color: '#eeeeee',
  number_color: '#111111',
  desc_color: '#999',
  label_color: '#111111',
  icon_poster_history_entry: icon_poster_history_entry_light,
}

const notificationBar = {
  color: '#FF4646',
  bg_color: '#FFF7ED',
  normalColor: '#BC763B',
}

const checkInBar: CheckInBar = {
  bg_color: '#FFEFDF',
  new_bg_color: '#FFF3E5',
  new_bg_color_checked: '#F8F8FA',
  sub_title_color: '#666',
  not_received_bg_color: '#FFDDA8',
  tomorrow_bg_color: '#FFDDA8',
  common_label_color: '#999',
  notification_switch_label: '#999',
}
const groundList: GroundList = {
  name_color: '#666666',
  title_color: '#333333',
  btn_left_bg: '#FFECEC',
  btn_left_color: '#F70909',
  btn_left_color_top: '#FFE8E0',
  btn_left_color_bottom: 'rgba(255, 217, 204, 0)',
  btn_right_bg: '#FFF1E7',
  btn_right_color: '#E76D10',
  bg_right_color_top: '#FFF1E0',
  bg_right_color_bottom: 'rgba(255, 217, 204, 0)',
}

const categories: Categories = {
  tabBar: {
    label_active_color: '#111',
    label_color: '#333',
    backgroundColor: '#fff',
  },
  commodity: {
    countdown_wrapper_bg: '#fef3f3',
  },
}

const task: Task = {
  browseTask: {
    wrapper_bg: '#fff9f1',
    label_color: '#BC763B',
  },
  taskList: {
    top_border_color: '#EEEEEE',
  },
  processBar: {
    bg_color: '#F6F7F8',
  },
  check_more_button: '#333',
  item: {
    title_color: '#333333',
    sub_title_color: '#999',
    actionButton: {
      can_receive_bg: '#FFA045',
      not_finish_bg: '#FFEEEE',
      finished_bg: 'transparent',
      can_receive_label_color: '#FFFFFF',
      can_receive_label_color_v2: '#FFFFFF',
      not_finish_label_color: '#FF4444',
      not_finish_label_color_v2: '#FFFFFF',
      finished_label_color: '#666666',
      finished_label_color_v2: '#666666',
      can_receive_border_color: 'transparent',
      not_finish_border_color: 'transparent',
      finished_border_color: 'rgba(102, 102, 102,0.5)',

      bg_start_color_not_finish: '#FF6956',
      bg_end_color_not_finish: '#FF4674',
      bg_start_color_can_receive: 'rgba(255, 101, 82, 0.4)',
      bg_end_color_can_receive: 'rgba(255, 65, 112, 0.4)',
      bg_start_color_finished: 'transparent',
      bg_end_color_finished: 'transparent',
    },
  },
  multiStepTask: {
    progressBarBG: '#DDDDDD',
  },
}

const posterHistory: PosterHistory = {
  bg_color: '#F6F6F6',
  border_color: 'rgba(102, 102, 102, 0.5)',
  color: '#333',
}

const creditDetail: CreditDetail = {
  bg_start_color: '#FFE2BF',
  bg_end_color: '#FFF9F1',
}

const checkInPop: CheckInPop = {
  gift_wrapper_bg: '#FFFBFB',
  gift_wrapper_border_color: '#EFABAB',
  notification_check_button_label: '#333333',
  wrapper_bg_color: '#fff',
  left_group_btn_label: '#666666',
  left_group_btn_border: 'rgba(0, 0, 0, .0528)',
  title_color: '#000',
}
const loginButton: LoginButton = {
  label_color: '#999',
  border_color: 'rgba(153, 153, 153,0.5)',
  button_label_color: '#333',
}
const errorView: ErrorView = {
  label_color: '#999',
  button_label_color: '#333333',
  button_border_color: '#dddddd',
  error_icon: img_network_error_poster_history,
}

const pcDialog: PcDialog = {
  bg_img:
    'https://imagev2.xmcdn.com/storages/445c-audiofreehighqps/83/DE/GMCoOR4F68F8AADtiwEbRbIq.png',
  bg_color: '#fff',
  sit_title: '#111',
  title_color: '#666666',
  sub_title_color: '#999999',
  sub_title_opacity: 1,
}

const everydayChallenge: EverydayChallenge = {
  taskListModal: {
    bg: '#ffebe5',
    closeButtonIconTintColor: '#000',
    sectionItemBg: '#fff',
    progress: { title: '#000', subTitle: '#000' },
    bg_start_color: '#FFE3E0',
    bg_end_color: '#FFE4EB',
    text: '#980000',
    bg_list_start_color: '#FF6552',
    bg_list_end_color: '#FF4170',
  },
  progress: {
    trackBG: '#ffefec',
  },
  summaryTaskItem: {
    label: '#000',
  },
  summaryTaskList: {
    bg_start_color: '#FF6956',
    bg_end_color: '#FF4674',
    bg_button: 'rgba(255, 255, 255, 0.9)',
    text: '#980000',
    bg_item: 'rgba(255, 255, 255, 0.95)',
    sub_text: 'rgba(0, 0, 0, 0.4)',
    bg_progress: 'rgba(0, 0, 0, 0.06)',
    bg_progress_bar: 'rgba(255, 210, 200, 1)',
  },
}

const light: ThemeStyle = {
  common,
  icon,
  container,
  header,
  credit,
  notificationBar,
  checkInBar,
  groundList,
  categories,
  task,
  posterHistory,
  creditDetail,
  checkInPop,
  loginButton,
  errorView,
  pcDialog,
  everydayChallenge,
}

export default light
