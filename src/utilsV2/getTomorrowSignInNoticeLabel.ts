import { CheckInInfoAwardItem } from '../typesV2/signInNew'

function getTomorrowSignInNoticeLabel(awards: CheckInInfoAwardItem[]) {
  const filterPackageAwards = awards.filter((a) => !a.label.includes('连签礼包'));
  return filterPackageAwards
    .map((a) => {
      if (a.label.includes('积分') || a.label === '') {
        return '大量积分'
      } else {
        return `${a.label}`
      }
    })
    .join(' + ')
}

export default getTomorrowSignInNoticeLabel
