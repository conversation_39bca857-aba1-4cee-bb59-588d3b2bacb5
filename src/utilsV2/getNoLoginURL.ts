import { isAndroid } from '@xmly/rn-utils/dist/device'

const ios_duiba_fix = (failUrl: string) => `https://m.ximalaya.com/xfm-activity-app/integral/autoLoginAndRedirect?ts=${ Date.now() }&failUrl=${ failUrl }&redirectUrl=`

const getNoLoginURL = (baseURL: string, encode?: boolean) => {
  let url = ''
  if (isAndroid && encode) {
    url = `${ ios_duiba_fix(encodeURIComponent(encodeURIComponent(baseURL))) }${ encodeURIComponent(encodeURIComponent(baseURL)) }`
  } else {
    url = `${ ios_duiba_fix(encodeURIComponent(baseURL)) }${ encodeURIComponent(baseURL) }`
  }
  return url
}

export default getNoLoginURL



