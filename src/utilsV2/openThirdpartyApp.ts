import { Page } from '@xmly/rn-sdk'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import { Linking } from 'react-native'
import TipsText from '../componentsV2/OpenThirdpartyAppTipsText'
import getUrlToOpen from './getUrlToOpen'
import RNAlert from '../componentsV2/Confirm'

type OpenThirdpartyAppParams = {
  schema?: string
  h5Link?: string
  onSuccess?: () => void
  onFail?: (err?: any) => void
  confirmBeforeOpen?: boolean
  eventTrack?: (type: '吊起 app' | '吊起下载页') => void
}

const confirm = () => {
  return new Promise((resolve, reject) => {
    RNAlert({
      title: '【任务说明】',
      body: TipsText(),
      actions: [
        {
          text: '取消',
          onPress: reject,
        },
        {
          text: '确认',
          onPress: () => resolve(true),
          primary: true,
        },
      ],
    })
  })
}
const tryOpenH5 = async ({
  h5Link,
  onFail,
  onSuccess,
  confirmBeforeOpen,
  eventTrack,
}: {
  h5Link?: string
  onSuccess?: () => void
  onFail?: (err?: any) => void
  eventTrack?: (type: '吊起 app' | '吊起下载页') => void
  confirmBeforeOpen?: boolean
}) => {
  try {
    confirmBeforeOpen && (await confirm())
    if (h5Link) {
      Page.start(getUrlToOpen(h5Link, { asH5: true, encodeTwice: isAndroid }))
      typeof onSuccess === 'function' && onSuccess()
      typeof eventTrack === 'function' && eventTrack('吊起下载页')
    } else {
      throw new Error('h5 link empty!')
    }
  } catch (err) {
    typeof onFail === 'function' && onFail(err)
  }
}

export default async function openThirdpartyApp(
  options: OpenThirdpartyAppParams
) {
  const { onFail, onSuccess, schema, h5Link, confirmBeforeOpen, eventTrack } =
    options
  try {
    if (schema) {
      const supported = await Linking.canOpenURL(schema)
      if (supported) {
        typeof eventTrack === 'function' && eventTrack('吊起 app')
        Linking.openURL(schema)
        typeof onSuccess === 'function' && onSuccess()
      } else {
        tryOpenH5({ h5Link, onSuccess, onFail, confirmBeforeOpen, eventTrack })
      }
    } else {
      tryOpenH5({ h5Link, onSuccess, onFail, confirmBeforeOpen, eventTrack })
    }
  } catch (err) {
    try {
      if (schema) {
        typeof eventTrack === 'function' && eventTrack('吊起 app')
        await Linking.openURL(schema)
        typeof onSuccess === 'function' && onSuccess()
      } else {
        tryOpenH5({ h5Link, onSuccess, onFail, confirmBeforeOpen, eventTrack })
      }
    } catch (err) {
      tryOpenH5({ h5Link, onSuccess, onFail, confirmBeforeOpen, eventTrack })
    }
  }
}
