import { ConfigCenter } from "@xmly/rn-sdk"

const defaultValue = 20

const getPosterShareTaskConfig = async () => {
  try {
    const res = await ConfigCenter.getConfig('toc', 'daysign_integral')
    const numberValue = Number(res)
    return typeof numberValue === 'number' && !isNaN(numberValue) ? numberValue : defaultValue
  } catch (err) {
    return defaultValue
  }
}

export default getPosterShareTaskConfig
