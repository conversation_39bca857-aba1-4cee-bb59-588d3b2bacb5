import { ConfigCenter } from '@xmly/rn-sdk'
import lodashGet from 'lodash.get'
import SentryUtils from './sentryUtils'

const getTaskWhitelist = async () => {
  try {
    const res = await ConfigCenter.getConfig(
      'toc',
      'credit_center_task_whitelist'
    )
    const resJSON = JSON.parse(res) as { taskIds: string; uids: string }
    const taskIdsArr = lodashGet(resJSON, 'taskIds', '')
      .split(',')
      .map((s) => Number(s))
    const uidsArr = lodashGet(resJSON, 'uids', '')
      .split(',')
      .map((s) => Number(s))
    return {
      taskIds: taskIdsArr,
      uids: uidsArr,
    }
  } catch (error) {
    SentryUtils.captureException(error, {
      source: 'getTaskWhitelist',
    })
    return {
      taskIds: [],
      uids: [],
    }
  }
}

export default getTaskWhitelist
