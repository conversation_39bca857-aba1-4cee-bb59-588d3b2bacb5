import { ConfigCenter } from '@xmly/rn-sdk'
import { everydayChallengeTaskAid } from '../constantsV2'
import goldCoinABValue from '../modulesV2/goldCoinABValue'
import customReportError from './customReportError'

const getAidConfigFromFootball = async (abValue: string) => {
  try {
    console.log({ abValue })
    const configStr = await ConfigCenter.getConfig('toc', 'credit_center_RenWuBao_aid')
    const config = JSON.parse(configStr)
    if (config) {
      if (config[abValue]) {
        return config[abValue] as number
      }
      if (config.default) {
        return config.default as number
      }
    }
    throw new Error('Invalid config:' + configStr)
  } catch (err) {
    customReportError({ source: 'getAidConfigFromFootball', error: `${err?.toString()}_abValue:${abValue}` })
    return await everydayChallengeTaskAid()
  }
}

export default async function getEverydayChallengeAidByAB() {
  // 根据金币活动的 ab 实验值获取对应的任务包活动 aid，aid 从 football 中配置
  const abValue = goldCoinABValue.getValue().changeTasksId
  const changeTasksABConfigValue = `changeTasksId-${abValue}`
  const aid = await getAidConfigFromFootball(changeTasksABConfigValue)
  return aid
}
