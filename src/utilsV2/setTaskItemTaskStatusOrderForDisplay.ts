import { TaskItemType, TaskStatusOrderForDisplay, StepType, TaskStatus } from '../typesV2/taskList'
import isMultiStepTaskFinished from './isMultiStepTaskFinished'

const setTaskItemTaskStatusOrderForDisplay = (item: TaskItemType, channelTaskId?: number) => {
  if (item.id === channelTaskId) {
    item.statusOrderForDisplay = TaskStatusOrderForDisplay.fromChannel
    return
  }
  if (item.stepType === StepType.Multi) {
    if (isMultiStepTaskFinished(item)) {
      item.statusOrderForDisplay = TaskStatusOrderForDisplay.finished
      return
    }
    if (item.stepInfos!.some((step) => step.stepStatus === TaskStatus.finished)) {
      item.statusOrderForDisplay = TaskStatusOrderForDisplay.canReceive
      return
    }
    item.statusOrderForDisplay = TaskStatusOrderForDisplay.notFinish
  } else {
    switch (item.status) {
      case TaskStatus.finished:
        item.statusOrderForDisplay = TaskStatusOrderForDisplay.canReceive
        break
      case TaskStatus.received:
        item.statusOrderForDisplay = TaskStatusOrderForDisplay.finished
        break
      case TaskStatus.unfinished:
        item.statusOrderForDisplay = TaskStatusOrderForDisplay.notFinish
        break
      case TaskStatus.nonValid:
        item.statusOrderForDisplay = TaskStatusOrderForDisplay.nonValid
        break
    }
  }
}

export default setTaskItemTaskStatusOrderForDisplay
