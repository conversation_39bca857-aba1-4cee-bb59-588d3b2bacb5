import { safetyToString } from '@xmly/rn-utils'
import { NativeModules } from 'react-native'
import nativeInfoModule from '../modulesV2/nativeInfoModule'
import { checkPushPermission } from '../modulesV2/pushPermission'
import { checkContactPermission } from '../modulesV2/taskContactPermission'
import userInfoDetail from '../modulesV2/userInfoDetail'
import { TaskItemKind, TaskItemType, TaskListInfoActivityStatus, TaskListInfoRequestResult, TaskStatus } from '../typesV2/taskList'
import getXMRequestId from './getXMRequestId'
import setTaskItemTaskStatusOrderForDisplay from './setTaskItemTaskStatusOrderForDisplay'

const processTaskListData = async ({
  originTaskListData,
  whitelist,
  skipVideoTask,
  isPCNewUser,
  onSpecialTargetCapture,
  customDisplayOrder,
}: {
  originTaskListData: TaskListInfoRequestResult
  whitelist: { taskIds: number[]; uids: number[] }
  skipVideoTask: boolean
  isPCNewUser: boolean
  onSpecialTargetCapture?: (item: TaskItemType) => boolean
  customDisplayOrder?: (item: TaskItemType, channelTaskId?: number) => void
}) => {
  try {
    const xmRequestId = await getXMRequestId()
    let finalResult: TaskItemType[] = []
    if (typeof originTaskListData.taskItems !== 'undefined') {
      if (
        originTaskListData.activityStatus === TaskListInfoActivityStatus.active &&
        originTaskListData?.taskItems &&
        Array.isArray(originTaskListData.taskItems) &&
        originTaskListData.taskItems.length > 0
      ) {
        finalResult = [...originTaskListData.taskItems]
      } else {
        finalResult = []
      }
    }

    if (finalResult.length === 0) {
      return []
    }

    const useInfo = userInfoDetail.getDetail()

    // 如果有开启权限任务，先判断是否有权限，若有已经有权限就直接过滤掉
    const _finalResult = []
    const channelTaskId = nativeInfoModule.getInfo()?.channelTaskId || '-1'
    for (let i = 0; i < finalResult.length; i++) {
      const current = finalResult[i]
      const UBTMateInfo = {
        xmRequestId,
        contentType: 'task',
        contentId: safetyToString(current.id),
      }

      current.UBTMateInfo = UBTMateInfo

      if (typeof onSpecialTargetCapture === 'function' && onSpecialTargetCapture(current)) {
        // 如果抓取函数返回 true， 直接跳出这次循环
        continue
      }

      typeof customDisplayOrder === 'function' ? customDisplayOrder(current, Number(channelTaskId)) : setTaskItemTaskStatusOrderForDisplay(current, Number(channelTaskId))

      if ((current.tag && current.tag === 'app') || current.status === TaskStatus.nonValid) continue
      if ((whitelist.taskIds.includes(current.id) && useInfo.uid && !whitelist.uids.includes(useInfo.uid)) || (whitelist.taskIds.includes(current.id) && !useInfo.uid)) {
        continue
      }
      if (current.taskType === TaskItemKind.clientBehavior) {
        if (current.status === TaskStatus.finished) {
          _finalResult.push(current)
          continue
        }
        if (current.code === 1 && skipVideoTask) {
          continue
        }
        if (current.code === 3) {
          //推送权限
          const checkRes = await checkPushPermission()
          if (checkRes !== 'granted') {
            _finalResult.push(current)
          }
          continue
        }

        // 安卓锁屏权限开启任务
        if (current.code === 888) {
          const hasCheckFunction = typeof NativeModules.RNUserSetting.isLockScreenOpen === 'function'
          if (!hasCheckFunction) {
            continue
          }
          //判断用户是否开启锁屏权限
          const hasPermission = await NativeModules.RNUserSetting.isLockScreenOpen()
          if (hasPermission) {
            continue
          }
        }

        if (current.code === 2) {
          // 通讯录权限
          const checkRes = await checkContactPermission()
          if (checkRes === 'blocked') {
            continue
          }
        }

        if (current.code === 88) {
          //pc端下载引导任务
          if (isPCNewUser) {
            _finalResult.push(current)
          }
          continue
        }

        _finalResult.push(current)
      } else {
        _finalResult.push(current)
      }
    }

    const sortedList = _finalResult.slice().sort((taskA, taskB) => {
      return taskA.statusOrderForDisplay - taskB.statusOrderForDisplay
    })

    return sortedList
  } catch (err) {
    console.log(err)
    return []
  }
}

export default processTaskListData
