import getUserIsNew from '../servicesV2/getUserIsNew'
import SentryUtils from './sentryUtils'
class UserConfig {
  async getIsNewUser() {
    try {
      const res = await getUserIsNew()
      const isNew = res?.data?.isNew ?? false
      return isNew
    } catch (err) {
      SentryUtils.captureException(err, { source: 'PCUserConfig.getIsNewUser' })
      return false
    }
  }
}

export default new UserConfig()
