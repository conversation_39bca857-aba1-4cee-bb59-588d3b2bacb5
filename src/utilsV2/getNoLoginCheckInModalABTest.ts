import { NativeModules } from 'react-native'

const defaultValue = '1'

// 未登录签到弹窗AB实验，实验id - 测试：3555 | 正式：未知
const getNoLoginCheckInModalABTest = async () => {
  if (NativeModules && NativeModules.XMAbtest) {
    try {
      const res = (await NativeModules.XMAbtest.getString(
        'signpage_unlogin1',
        defaultValue
      )) as '1' | '2'
      return res
    } catch (err) {
      return defaultValue
    }
  } else {
    return defaultValue
  }
}

export default getNoLoginCheckInModalABTest
