

export default class DelayTask {

  static isTaskComplete: boolean = true

  static handlers: Function[] = []

  static timer: number;

  static addHandlers = (callbacks: Function[]): void => {
    if (DelayTask.isTaskComplete) {
      // console.log('delayTask addhandlers 立即执行')
      for (const item of callbacks) {
        item()
      }
    } else {
      // console.log('delayTask addhandlers 添加回调，后续执行')
      DelayTask.handlers = callbacks
    }
  }

  static start = (): void => {
    // console.log('delayTask start')
    DelayTask.isTaskComplete = false
    if (DelayTask.timer) {
      clearTimeout(DelayTask.timer)
    }
    DelayTask.timer = Number(setTimeout(DelayTask.end, 2000))
  }

  static end = (): void => {
    // console.log(`delayTask end ${DelayTask.isTaskComplete}`)
    if (DelayTask.isTaskComplete) {
    return
    }
    if (DelayTask.timer) {
      clearTimeout(DelayTask.timer)
    }
    DelayTask.isTaskComplete = true
    DelayTask.run()
  }

  static run = (): void => {
    if (DelayTask.isTaskComplete) {
      for (const item of DelayTask.handlers) {
        item()
      }
      DelayTask.handlers = []
    }
  }

  static clear (): void {
    DelayTask.handlers = []
  }
}