import { pageView, pageExit, click, clickButton, event } from '@xmly/xmlog-rn'

interface IProps {
  [ key: string ]: string
}

export default {
  // 用于上报页面曝光事件
  pageView: (metaId: number, currPage: string, props?: IProps): void => {
    pageView(metaId, currPage, props)
  },
  // 用于上报页面离开事件
  pageExit: (metaId: number, props?: IProps): void => {
    pageExit(metaId, props)
  },
  // 用于上报点击跳转事件
  click: (metaId: number, currModule?: string, props?: IProps): void => {
    click(metaId, currModule, props)
  },
  // 用于上报点击控件事件
  clickButton: (metaId: number, currModule?: string, props?: IProps): void => {
    clickButton(metaId, currModule, props)
  },
  // 作为以上方法的补充，可以通过传入 serviceId 实现自定义类型事件的上报
  event: (metaId: number, serviceId: string, props?: IProps): void => {
    event(metaId, serviceId, props)
  }
}