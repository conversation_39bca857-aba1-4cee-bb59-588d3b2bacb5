import { taskListAid } from '../constantsV2'
import refreshClientTaskV2 from '../servicesV2/refreshClientTaskV2'
import { store } from '../store'
import getEverydayChallengeAidByAB from './getEverydayChallengeAidByAB'
import SentryUtils from './sentryUtils'

const refreshTaskAndRefreshList = async (taskId: number, aid: number) => {
  try {
    await refreshClientTaskV2({ taskId }, aid)
    if (aid === taskListAid) {
      store.dispatch.taskCenter.getTaskList({ aid })
    } else if (aid === (await getEverydayChallengeAidByAB())) {
      // 处理任务包任务列表的逻辑
      store.dispatch.everydayChallenge.getTaskList({ aid })
    }
  } catch (err) {
    console.log('refreshTaskAndRefreshList err', err)
    SentryUtils.captureException(err, {
      source: 'performTask.refreshTaskAndRefreshList',
    })
  }
}

export default refreshTaskAndRefreshList
