// 定义 getRNUrlToOpen 函数，用于获取 RN 端的 URL 地址, 参数为 url 字符串类型，返回值为字符串类型, 参数以 rn:// 开头, 取出 rn:// 后面的字符串，再将字符串进行 encodeURIComponent 编码，返回编码后的字符串
function getRNUrlToOpen(url: string) {
  if (!url) return ''
  try {
    const toUrl = url.replace('rn://', '')
    return toUrl
  } catch (err) {
    return ''
  }
}

export default function parseRNUrl(url: string) {
  if (url.startsWith('ad-')) {
    const [taskId, aid, positionId, positionName] = url.split('-').slice(1);
    return { taskId, aid, positionId, positionName };
  }
  const rnUrl = getRNUrlToOpen(url)
  console.log('rnUrl:', rnUrl)
  const regex = /^(\w+)\?(.*)$/; // 匹配路由名称和参数的正则表达式
  const match = rnUrl.match(regex); // 执行正则表达式匹配
  if (!match) {
    return null; // URL 格式不正确，返回 null
  }
  const name = match[1]; // 提取路由名称
  const paramsStr = match[2]; // 提取参数字符串
  const params = {}; // 创建参数对象
  // 将参数字符串解析为参数对象
  paramsStr.split('&').forEach(param => {
    const [key, value] = param.split('=');
    params[key] = decodeURIComponent(value);
  });
  return { name, params }; // 返回路由名称和参数对象
}
