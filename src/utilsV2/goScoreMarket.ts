import { Page } from '@xmly/rn-sdk'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import jumpDuiba from 'servicesV2/jumpDuiba'
import getUrlToOpen from 'utilsV2/getUrlToOpen'


const duiba_auto_login_url =
  'https://activity.m.duiba.com.cn/autoLogin/autologin?'


export default async function goScoreMarket () {
  const res = await jumpDuiba(1)
  let url = res.data
  if (isAndroid) {
    const params = encodeURIComponent(res.data.replace(duiba_auto_login_url, ''))
    url = `${ duiba_auto_login_url }${ params }`
  }
  Page.start(getUrlToOpen(url))
}
