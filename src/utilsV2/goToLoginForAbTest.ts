import { Page, RNO<PERSON>, Toast } from "@xmly/rn-sdk"
import getNoLoginCheckInModalABTest from "./getNoLoginCheckInModalABTest"

const goToLoginForAbTest = async (half: boolean = false) => {
  if (half) {
    const halfLogin = 'iting://open?msg_type=21&type=2';
    Page.start(halfLogin).catch((error: any) => {
      Toast.info('请先进行登录')
    })
  } else {
    const result = await getNoLoginCheckInModalABTest()
    console.log('===ABTest result in Login:', result)
    if (result === '1') {
      Page.start(RNOpen.Login).catch((error: any) => {
        Toast.info('请先进行登录')
      })
    } else {
      const halfLogin = 'iting://open?msg_type=21&type=2';
      Page.start(halfLogin).catch((error: any) => {
        Toast.info('请先进行登录')
      })
    }
  }
}

export default goToLoginForAbTest