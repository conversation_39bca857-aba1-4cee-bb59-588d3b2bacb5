import { Page, Toast } from '@xmly/rn-sdk'
import isMonthlyTicketAvailable from './isMonthlyTicketAvailable'

export default async function goToMonthlyTicketVote(options?: {
  isGuide?: boolean
}) {
  const isAvailable = await isMonthlyTicketAvailable()
  if (isAvailable) {
    Page.start(
      `iting://open?msg_type=365${options?.isGuide ? '&source=guide' : ''}`
    )
  } else {
    Toast.info('新功能！更新APP即可体验～')
  }
}
