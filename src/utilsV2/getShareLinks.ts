import getEnv from './getEnv'

// h5 分享链接
const getH5ShareUrl = {
  /** 1.单音的声音播放页
   * https://m.ximalaya.com/sleepaudio/:trackId
   */

  SINGLE_TRACK: async (currSingleSceneId:number,currSingleTrack: number,mixTrackIds:string): Promise<string> => {
    return await getEnv(
      `https://m.test.ximalaya.com/gatekeeper/rn-asmr-h5-share-page?themeId=${currSingleSceneId}&bgTrackId=${currSingleTrack}&mixTrackId=${mixTrackIds}`,
      `https://m.uat.ximalaya.com/gatekeeper/rn-asmr-h5-share-page?themeId=${currSingleSceneId}&bgTrackId=${currSingleTrack}&mixTrackId=${mixTrackIds}`,
      `https://m.ximalaya.com/gatekeeper/rn-asmr-h5-share-page?themeId=${currSingleSceneId}&bgTrackId=${currSingleTrack}&mixTrackId=${mixTrackIds}`,
    )
  },
}

export default getH5ShareUrl
