import { Page } from "@xmly/rn-sdk";
import { PosterItemResourceType, PosterItemType } from "../typesV2/posterListType";
import getUrlToOpen from "./getUrlToOpen";

const handlePosterPress = (item: PosterItemType) => {
  if (item.resourceType === PosterItemResourceType.none || (item.resourceType === PosterItemResourceType.activity && !item.jumpUrl)) { return }
  switch (item.resourceType) {
    case PosterItemResourceType.album:
      Page.start(getUrlToOpen('iting://open?msg_type=13&album_id=' + item.resourceId))
      break;
    case PosterItemResourceType.track:
      Page.start(getUrlToOpen('iting://open?msg_type=11&track_id=' + item.resourceId))
      break;
    case PosterItemResourceType.activity:
      Page.start(getUrlToOpen(item.jumpUrl))
      break;
  }
}

export default handlePosterPress