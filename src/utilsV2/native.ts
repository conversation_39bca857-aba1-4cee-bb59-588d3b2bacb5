import { <PERSON>, RN<PERSON><PERSON>, Toast } from '@xmly/rn-sdk'
import { ViewStyle, StyleProp, NativeModules, Platform, NativeEventEmitter } from 'react-native'
import customReportError from './customReportError'
import { assignFields } from 'utils/assign'
import { FallbackReqType } from 'constants/ad'

const { Business, Encrypt } = NativeModules

interface ListenEarnRewardCoinParams {
  positionName: string
  slotId: number
  extInfo?: string
  rewardVideoStyle?: 1 | number
  sourceName?: string
  rewardType?: number
  coins?: number
  otherParams?: Record<string, any>
}

interface ListenEarnRewardCoinResult {
  clientCode: string
  adId?: number
  adResponseId?: number
  encryptType?: string
  ecpm?: string
  fallbackReq?: FallbackReqType
}

export const listenEarnRewardCoin = async (params: ListenEarnRewardCoinParams): Promise<ListenEarnRewardCoinResult> => {
  const {
    positionName,
    slotId,
    rewardVideoStyle,
    otherParams,
  } = params;
  const options = {
    positionName: positionName || 'integral_center_inspire_video',
    slot_id: typeof slotId === 'number' ? slotId : 254,
    renderTemplate: 1,
    rewardVideoStyle,
    ...otherParams
  }
  assignFields(options, params, ['extInfo', 'sourceName', 'rewardType', 'coins']);
  customReportError({ source: 'Business.listenEarnRewardCoinNew_params', error: JSON.stringify(options) })
  console.log({ 'listenEarnRewardCoin options ': options })
  return Business.listenEarnRewardCoinNew(options) as Promise<ListenEarnRewardCoinResult>
}

interface InspireAdViewParams {
  positionName: string
  slotId: number
  sourceName: string
  rewardCoin: number
  pointerEvents?: 'none' | 'auto' | 'box-none' | 'box-only';
  repeat?: boolean;
  muted?: boolean;
  style: StyleProp<ViewStyle>;
  onAdClick?: () => void
  onHeightChange?: (height: number) => void
  onRewardSuccess?: (adId: number, adResponseId: number) => void
  onRewardFail?: (msg: string) => void
}

interface InspireAdViewResult {
  view: any
  remove: () => void
}


/**
 * 返回原生激励视频广告View
 * @param params 
 * @returns 
 */
export const listenInspireAdView = async (params: InspireAdViewParams): Promise<InspireAdViewResult> => {
  const {
    positionName,
    slotId,
    sourceName,
    rewardCoin,
    onAdClick,
    onHeightChange,
    onRewardSuccess,
    onRewardFail
  } = params;
  
  const options = {
    // TODO: 极速版广告位福利页唤端 id 原308
    slot_id: typeof slotId === 'number' ? slotId : 310,
    // positionName: positionName || 'fuliyehuanduanguanggao',
    positionName: positionName || 'lite_incentive_welfare_inspire',
    rewardCoin,
    sourceName,
  }

  assignFields(options, params, ['slotId', 'positionName', 'rewardCoin', 'sourceName']);
  
  const eventEmitter = new NativeEventEmitter(NativeModules.Business);
  const subscriptions: { remove: () => void }[] = [];
  
  if (onAdClick) {
    const clickSubscription = eventEmitter.addListener('onAdClick', () => {
      onAdClick();
    });
    subscriptions.push(clickSubscription);
  }
  
  if (onHeightChange) {
    const heightSubscription = eventEmitter.addListener('onHeightChange', (height: number) => {
      onHeightChange(height);
    });
    subscriptions.push(heightSubscription);
  }
  
  if (onRewardSuccess) {
    const rewardSuccessSubscription = eventEmitter.addListener('onRewardSuccess', (data: { adId: number, adResponseId: number }) => {
      onRewardSuccess(data.adId, data.adResponseId);
    });
    subscriptions.push(rewardSuccessSubscription);
  }
  
  if (onRewardFail) {
    const rewardFailSubscription = eventEmitter.addListener('onRewardFail', (msg: string) => {
      onRewardFail(msg);
    });
    subscriptions.push(rewardFailSubscription);
  }
  
  return {
    view: await Business.InspireAdView(options),
    remove: () => {
      subscriptions.forEach(subscription => subscription.remove());
    }
  };
}


export const getUserListenData = async () => {
  return Business.getUserListenData()
}

/**
 * type	String	加密方式 md5、sha1、rsa
 * data	Object	加密后的键值对 (值仅支持字符串)
 */
export const encryptByType = async (type: 'md5' | 'sha1' | 'rsa', data: object) => {
  return Encrypt.encryptByType(type, data)
}

export const login = () => {
  Page.start(RNOpen.Login).catch((error: any) => {
    Toast.info('请先进行登录')
  })
}

export const encryptWithScene = async (options: { sceneName: string; encryptData: string }) => {
  if (typeof NativeModules.Encrypt.encryptWithScene === 'function') {
    return NativeModules.Encrypt.encryptWithScene(options.sceneName, options.encryptData)
  } else {
    return ''
  }
}

export const getXuidTicket = async ({ businessId, scene, uid }: { businessId: string; scene: string; uid: number }) => {
  if (typeof NativeModules.Encrypt.xuidTicket === 'function') {
    const attr = `b=${businessId}&s=${scene}&u=${uid}`
    return NativeModules.Encrypt.xuidTicket(attr) as Promise<string>
  } else {
    return ''
  }
}

/**
 * @description 在一些安卓手机上，主 App 的 autoSize 会导致 RN 页面的一些组件的尺寸在重新渲染的时候表现异常。这时候要把主 App 的 autoSize 禁用掉
 */
export const AndroidDisableAutoSize = () => {
  try {
    if (Platform.OS === 'android' && typeof NativeModules?.RNDeviceInfo?.cancelAdaptAutoSize === 'function') {
      NativeModules.RNDeviceInfo.cancelAdaptAutoSize()
    }
  } catch (err) {
    console.log(err)
  }
}
