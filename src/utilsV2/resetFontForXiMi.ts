import { NativeModules, Platform, Text } from "react-native";

export default function resetFontForXiMi () {
  // 兼容小米字体的代码
  if (Platform.OS === "android" && NativeModules.PlatformConstants) {
    const fingerprint = NativeModules.PlatformConstants.Fingerprint;

    if (fingerprint?.match(/^(xiaomi|redmi|mi|mix|poco).*\/v12\..*/i)) {
      //@ts-ignore
      const originTextRender = Text.render;

      //@ts-ignore
      Text.render = function render (props, ref) {
        return originTextRender.apply(this, [
          {
            ...props,
            style: [ { fontFamily: "" }, props.style ],
          },
          ref,
        ]);
      };
    }
  }
}