export enum TaskState {
	init,
	running,
	done
}

export interface TaskInfo {
	state: TaskState
	result: any
	task?: Function
	taskPromise?: Promise<any>
	taskPromiseResolve?: Function
	taskPromiseReject?: Function
}

export default class GetDataProxy {
	static taskMap: Map<Function, TaskInfo> = new Map()

	static async startTask(dataCreator: Function, task: Function) {
		const currentTaskInfo = GetDataProxy.taskMap.get(dataCreator) as TaskInfo
		if (!task) {
			return
		}
		currentTaskInfo.task = task
		currentTaskInfo.taskPromise = new Promise((resolve, reject) => {
			currentTaskInfo.taskPromiseResolve = resolve
			currentTaskInfo.taskPromiseReject = reject
		})
		currentTaskInfo.state = TaskState.running
		try {
			const data = await currentTaskInfo.task()
			currentTaskInfo.state = TaskState.done
			currentTaskInfo.result = data
			;(currentTaskInfo.taskPromiseResolve as Function)(data)
		} catch (error) {
			currentTaskInfo.state = TaskState.done
			currentTaskInfo.result = error
			;(currentTaskInfo.taskPromiseReject as Function)(error)
		}
	}

	static async proxy({
		dataCreator,
		params = null,
		useProxy = true
	}: {
		dataCreator: Function
		params?: any
		useProxy?: boolean
	}): Promise<any> {
		const useParams = params != null
		let task: Function
		if (useParams) {
			task = async (): Promise<any> => dataCreator(params)
		} else {
			task = async (): Promise<any> => dataCreator()
		}

		if (!useProxy) {
			return task()
		}

		if (!GetDataProxy.taskMap.get(dataCreator)) {
			GetDataProxy.taskMap.set(dataCreator, {
				state: TaskState.init,
				result: null
			})
		}

		const currentTaskInfo = GetDataProxy.taskMap.get(dataCreator) as TaskInfo
		const currentTaskState = currentTaskInfo.state

		if (currentTaskState === TaskState.init) {
			// console.log(`state TaskState.init`)
			GetDataProxy.startTask(dataCreator, task)
			return currentTaskInfo.taskPromise
		}

		if (currentTaskState === TaskState.running) {
			// console.log(`state TaskState.running`)
			return currentTaskInfo.taskPromise
		}

		if (currentTaskState === TaskState.done) {
			// console.log(`state TaskState.done`)
			return currentTaskInfo.result
		}
	}
}
