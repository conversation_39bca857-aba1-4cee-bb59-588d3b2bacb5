import { Toast } from '@xmly/rn-sdk'
import { NativeModules } from 'react-native'

const defaultValue = '1'

// 签到承接弹窗AB实验，实验id:3480
const getChannelModalABTest = async () => {
  if (NativeModules && NativeModules.XMAbtest) {
    try {
      const res = (await NativeModules.XMAbtest.getString(
        'landing_popup',
        defaultValue
      )) as '1' | '2'
      __DEV__ && Toast.info(res)
      return res
    } catch (err) {
      return defaultValue
    }
  } else {
    return defaultValue
  }
}

export default getChannelModalABTest
