import { encryptByType } from './native'
import SentryUtils from './sentryUtils'

const generateSig = async (
  params: object,
  options?: { needUpperCase?: boolean; needSecurityKey?: boolean }
) => {
  let result = ''
  try {
    const keysSorted = Object.keys(params).slice().sort()
    let toBeEncrypt = ''
    keysSorted.forEach((key, index, self) => {
      toBeEncrypt += `${key}=${params[key]}${
        index + 1 !== self.length ? '&' : ''
      }`
    })

    if (options?.needSecurityKey) {
      toBeEncrypt =
        toBeEncrypt +
        '&MOBILE-V1-TEST-63B2E1D0E0DD40928342D3D9BC8AC4956F9DD8637BF04853B49F0690FD3BE684'
    }

    if (options?.needUpperCase) {
      toBeEncrypt = toBeEncrypt.toLocaleUpperCase()
    }

    const encryptRes = await encryptByType('sha1', {
      toBeEncrypt,
    })
    result = encryptRes.toBeEncrypt
  } catch (err) {
    console.log(err)
    SentryUtils.captureException(err, {
      source: 'utils.generateSig',
    })
  } finally {
    return result
  }
}

export default generateSig
