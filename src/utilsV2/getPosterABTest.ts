import { NativeModules } from "react-native"

const defaultValue = "CD"

const getPosterABTest = async () => {
  if (NativeModules && NativeModules.XMAbtest) {
    try {
      const res = await NativeModules.XMAbtest.getString("day_sign", defaultValue) as 'A' | 'B' | 'CD'
      return res
    } catch (err) {
      return defaultValue
    }
  } else {
    return defaultValue
  }
}

export default getPosterABTest