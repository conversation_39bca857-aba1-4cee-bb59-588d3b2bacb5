import CryptoJS from 'crypto-js'
import nanoid from './genRandomId'

const iv = CryptoJS.enc.Utf8.parse('0123456789abcedf')

const AESTool = {
  encrypt: (message: string) => {
    const _nanoid = nanoid(16)
    const key = CryptoJS.enc.Utf8.parse(_nanoid)

    let sendData = CryptoJS.enc.Utf8.parse(message)
    let encrypted = CryptoJS.AES.encrypt(sendData, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return {
      result: encrypted.toString(), // Base64字符串
      key,
    }
  },
}

export default AESTool
