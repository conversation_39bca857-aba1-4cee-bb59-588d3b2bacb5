import { NativeModules } from 'react-native';
import { ListenData } from '../typesV2/listenData';
import { encryptByType } from './native';
import SentryUtils from './sentryUtils';
import userInfoDetail from '../modulesV2/userInfoDetail';
import dayjs from 'dayjs';
import lodashGet from 'lodash.get'

let listenDataResult: {
  checkData: any;
  date: number;
  listenTime: number;
} | undefined

const getListenData = async (useCache?: boolean) => {
  try {
    if (useCache && listenDataResult) {
      return listenDataResult
    }
    let listenInfo: ListenData = await NativeModules.Business.getUserListenData();
    // console.log({ 'listenInfo ✅✅✅✅✅✅✅': listenInfo })
    let {
      onDayListenTime, //分钟
      dailyListeningTask: { listenTime, uid } = { listenTime: 0, uid: '' }, //秒
    } = listenInfo;

    if (typeof onDayListenTime === 'string') {
      onDayListenTime = JSON.parse(onDayListenTime);
    }

    const date = lodashGet(onDayListenTime, 'date', Number(dayjs().format('YYYYMMDD')))
    const oldListenTime = lodashGet(onDayListenTime, 'listenTime', 0)

    if (!uid) {
      uid = userInfoDetail.getDetail().uid || -1
    }

    if (uid === -1) return null

    if (!listenTime) {
      listenTime = oldListenTime * 60;
    }
    const { checkData } = await encryptByType('rsa', {
      checkData: `date=${ date }&listenTime=${ listenTime }&uid=${ uid }`,
    });

    const result = { checkData, date, listenTime: Number(listenTime) };
    listenDataResult = result
    console.log({ listenDataResult })
    return listenDataResult
  } catch (err) {
    SentryUtils.captureException(err, { source: 'getListenData' });
    return null
  }
};

export default getListenData;
