export default function getUrlToOpen(
  url: string,
  options?: { xmlyAsIting?: boolean; asH5?: boolean; encodeTwice?: boolean; newIting?: boolean }
) {
  if (!url) return ''
  try {
    const addDecodeOnce = url.includes('&decodeOnce=1')
    const judgeXMLY = options && options.xmlyAsIting
    const asH5 = options && options.asH5
    const encodeTwice = options && options.encodeTwice
    const newIting = options && options.newIting
    const pretreatmentH5Link = !encodeTwice
      ? encodeURIComponent(url)
      : encodeURIComponent(encodeURIComponent(url))
    let H5LinkIting = `iting://open?msg_type=14&url=${pretreatmentH5Link}&_ka=1${
      addDecodeOnce ? '&decodeOnce=1' : ''
    }`
    if(newIting) {
      H5LinkIting = `iting://rnopen?name=H5&url=${pretreatmentH5Link}`
    }
    if (asH5) {
      return H5LinkIting
    }

    const ifIting =
      url.indexOf('iting') !== -1 ||
      (judgeXMLY && url.indexOf('xmly://') !== -1)
    const toUrl = ifIting ? url : H5LinkIting
    console.log({ toUrl })
    return toUrl
  } catch (err) {
    return ''
  }
}
