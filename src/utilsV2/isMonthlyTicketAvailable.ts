import { XMAppVersionHelper } from '@xmly/rn-sdk'
import { DeviceInfo } from '@xmly/rn-sdk/dist/DeviceInfo'

const isMonthlyTicketAvailable = async () => {
  try {
    const { appVersion } = await DeviceInfo.getDeviceInfo()
    const available = XMAppVersionHelper.isHigherThanSync(appVersion, '9.0.67')
    return available
  } catch (err) {
    return false
  }
}

export default isMonthlyTicketAvailable
