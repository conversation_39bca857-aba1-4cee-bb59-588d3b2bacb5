import { NativeModules } from 'react-native';
import { Env } from '../typesV2/common'
import GetDataProxy from './getDataProxy';

const run_prod = false;
export let global_envType: 'online' | 'uat' | 'test' | 'dev' | null = null

/**
 *
 * @param {*} test 测试域名
 * @param {*} uat 测试域名
 * @param {*} prod 生产域名
 */
export default async (test: string, uat: string, prod: string): Promise<string> => {
  return new Promise((resolve) => {
    if (run_prod && __DEV__) {
      resolve(prod);
    } else {
      try {
        if (global_envType) {
          if (global_envType === 'online') {
            resolve(prod);
          } else if (global_envType === 'uat') {
            resolve(uat);
          } else {
            resolve(test);
          }
        } else {
          GetDataProxy.proxy({
            dataCreator: NativeModules.Env.getEnv
          }).then((env: Env) => {
            global_envType = env.envType
            if (env.envType === 'online') {
              resolve(prod);
            } else if (env.envType === 'uat') {
              resolve(uat);
            } else {
              resolve(test);
            }
          });
        }

      } catch (error) {
        resolve(prod);
      }
    }
  });
};

export const isEnv = async (): Promise<boolean> => {
  return new Promise((resolve) => {
    if (run_prod && __DEV__) {
      resolve(false);
    } else {
      try {
        if (global_envType) {
          if (global_envType === 'online') {
            resolve(false);
          } else if (global_envType === 'uat') {
            resolve(false);
          } else {
            resolve(true);
          }
        } else {
          GetDataProxy.proxy({
            dataCreator: NativeModules.Env.getEnv
          }).then((env: Env) => {
            global_envType = env.envType
            if (env.envType === 'online') {
              resolve(false);
            } else if (env.envType === 'uat') {
              resolve(false);
            } else {
              resolve(true);
            }
          });
        }

      } catch (error) {
        resolve(false);
      }
    }
  });
}
