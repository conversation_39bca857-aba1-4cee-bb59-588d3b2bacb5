import Animated from "react-native-reanimated";

interface AnimateParams<S, C> {
  clock: Animated.Clock;
  fn: (
    clock: Animated.Clock,
    state: S,
    config: C
  ) => Animated.Adaptable<number>;
  state: S;
  config: C;
  from: Animated.Adaptable<number>;
}


export interface TimingParams {
  clock?: Animated.Clock;
  from?: Animated.Adaptable<number>;
  to?: Animated.Adaptable<number>;
  duration?: Animated.Adaptable<number>;
  easing?: (v: Animated.Adaptable<number>) => Animated.Node<number>;
}

export interface TimingAnimation {
  state: Animated.TimingState;
  config: Animated.TimingConfig;
}

export interface SpringAnimation {
  state: Animated.SpringState;
  config: Animated.SpringConfig;
}

export interface DecayAnimation {
  state: Animated.DecayState;
  config: Animated.DecayConfig;
}

export type Animation = SpringAnimation | DecayAnimation | TimingAnimation;
