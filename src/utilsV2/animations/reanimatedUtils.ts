import { block, cond, set, startClock, stopClock, clockRunning, not } from "react-native-reanimated";
import { AnimateParams, Animation } from "./reanimatedUtilsType";

export const animate = <T extends Animation> ({
  fn,
  clock,
  state,
  config,
  from,
}: AnimateParams<T[ "state" ], T[ "config" ]>) =>
  block([
    cond(not(clockRunning(clock)), [
      set(state.finished, 0),
      set(state.time, 0),
      set(state.position, from),
      startClock(clock),
    ]),
    fn(clock, state, config),
    cond(state.finished, stopClock(clock)),
    state.position,
  ]);