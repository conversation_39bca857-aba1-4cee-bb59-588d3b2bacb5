import { State } from "react-native-gesture-handler";
import Animated, { block, Clock, cond, Easing, eq, neq, set, startClock, timing, Value } from "react-native-reanimated";

export type TimingConfig = Partial<Omit<Animated.TimingConfig, "toValue">>;
export const withTransition = (
  value: Animated.Node<number>,
  timingConfig: TimingConfig = {},
  gestureState: Animated.Value<State> = new Value(State.UNDETERMINED)
) => {
  const clock = new Clock();
  const state = {
    finished: new Value(0),
    frameTime: new Value(0),
    position: new Value(0),
    time: new Value(0)
  };
  const config = {
    toValue: new Value(0),
    duration: 250,
    easing: Easing.linear,
    ...timingConfig
  };
  return block([
    startClock(clock),
    cond(neq(config.toValue, value), [
      set(state.frameTime, 0),
      set(state.time, 0),
      set(state.finished, 0),
      set(config.toValue, value)
    ]),
    cond(
      eq(gestureState, State.ACTIVE),
      [ set(state.position, value) ],
      timing(clock, state, config)
    ),
    state.position
  ]);
};
export const withTimingTransition = withTransition;