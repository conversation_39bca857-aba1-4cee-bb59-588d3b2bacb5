import Animated, { block, Clock, clockRunning, Easing, Value, cond, timing, startClock, set, not, and, or } from "react-native-reanimated"

interface LoopProps {
  clock?: Animated.Clock
  easing?: Animated.EasingFunction
  duration?: number
  boomerang?: boolean // 回旋镖
  autoStart?: boolean
  toValue?: number
  from?: Animated.Value<number>
  paused?: Animated.Node<number>
}

export const loopTiming = (loopConfig: LoopProps) => {
  const { clock, easing, duration, boomerang, autoStart, toValue = 1, from = new Animated.Value(0), paused = new Animated.Value(0) } = {
    clock: new Clock(),
    easing: Easing.linear,
    duration: 250,
    boomerang: false,
    autoStart: true,
    ...loopConfig,
  }
  const state = {
    finished: new Value(0),
    position: from,
    time: new Value(0),
    frameTime: new Value(0),
  }
  const config = {
    toValue: new Value(toValue),
    duration,
    easing,
  }

  return block([
    cond(and(not(clockRunning(clock)), autoStart ? 1 : 0, not(paused)), startClock(clock)),
    timing(clock, state, config),
    cond(or(state.finished, paused), [
      set(state.finished, 0),
      set(state.time, 0),
      set(state.frameTime, 0),
      boomerang ? set(config.toValue, cond(config.toValue, 0, toValue)) : set(state.position, 0),
    ]),
    state.position,
  ])
}