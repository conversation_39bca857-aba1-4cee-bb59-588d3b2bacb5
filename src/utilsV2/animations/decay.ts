import Animated, { block, Clock, set, Value, decay as reDecay, cond, not, clockRunning } from "react-native-reanimated";
import { animate } from "./reanimatedUtils";

export interface DecayParams {
  clock?: Animated.Clock;
  from?: Animated.Adaptable<number>;
  velocity?: Animated.Value<number>;
  deceleration?: Animated.Adaptable<number>;
}


interface DecayAnimation {
  state: Animated.DecayState;
  config: Animated.DecayConfig;
}
const decay = (params: DecayParams) => {
  const { clock, from, velocity, deceleration } = {
    clock: new Clock(),
    velocity: new Value(0),
    deceleration: 0.998,
    from: 0,
    ...params,
  };

  const state: Animated.DecayState = {
    finished: new Value(0),
    position: new Value(0),
    time: new Value(0),
    velocity: new Value(0),
  };

  const config: Animated.DecayConfig = {
    deceleration,
  };

  return block([
    cond(not(clockRunning(clock)), [ set(state.velocity, velocity) ]),
    animate<DecayAnimation>({
      clock,
      fn: reDecay,
      state,
      config,
      from,
    }),
  ] as ReadonlyArray<Animated.Node<number>>);
};

export default decay