import { imageDomain, imageTestDomain } from '../constantsV2'

/**
 * 对象转换成url
 * @param obj
 * @param encode
 */
const obj2url = (obj: object, encode = true) => {
  let path = ''
  Object.entries(obj).forEach(([key, value], index) => {
    if (index === 0) {
      path += `${key}=${value}`
    } else {
      path += `&${key}=${value}`
    }
  })
  return encode ? encodeURIComponent(path) : path
}

function addParams(url: string, param = {}, mark = '?') {
  if (typeof param !== 'object') {
    console.warn('expect a type of object but received type ' + typeof param)
    return ''
  }
  return url + mark + obj2url(param)
}

export function fillOrigin(url: string, type: number, test?: boolean) {
  if (url.startsWith('http') || url.startsWith('https')) {
    // const { origin } = new URL(url);
    // url = url.slice(origin.length);
    return url
  }
  if (type === 2) {
    return url
  } else {
    const domain = !test ? imageDomain : imageTestDomain
    return /\/$/.test(domain) || /^\//.test(url)
      ? domain + url
      : domain + '/' + url
  }
}

/**
 * 裁切图片
 * @param {*} originUrl
 * @param {*} [{ op_type = 3, width: columns, height: rows, ...rest }={}]
 * @returns
 */
export default function image2CustomSize(
  originUrl: string,
  { op_type = 3, width: columns, height: rows, test, ...rest }: any = {},
  type = 1
) {
  if (originUrl.startsWith('http')) return originUrl
  return addParams(
    fillOrigin(originUrl, type, test),
    { op_type, columns, rows, ...rest },
    '!'
  )
}
