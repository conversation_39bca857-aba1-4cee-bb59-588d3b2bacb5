import { TaskItemKind, TaskListType } from '../typesV2/taskList'

export default function (taskList: TaskListType, thirdpartyTaskExist: boolean) {
  return taskList.filter((task) => {
    const isNewVideoTask = task.taskType === TaskItemKind.newVideoTask && task?.contextMap?.style === 'new'
    return !(
      task.code === 98 && thirdpartyTaskExist // 当存在第三方任务时浏览任务不展示
      || isNewVideoTask // 新版视频任务不展示在任务列表
    )
  })
}
