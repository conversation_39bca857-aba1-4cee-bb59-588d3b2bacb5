import { APMLocalLog, safetyToString } from '@xmly/rn-utils'
import { NativeModules } from 'react-native'
import { AdDPTaskItem } from '../typesV2/adDPTask'
import customReportError from './customReportError'
import log from 'utils/log'

const AdModule = NativeModules?.AdModule

const isAdSupportDp: (adItems: AdDPTaskItem[]) => Promise<{ adid: string; result: 1 | 0 }[]> = async (adItems: AdDPTaskItem[]) => {
  try {
    if (typeof AdModule?.isAdSupportDp === 'function') {
      const res: { adid: string; result: 1 | 0 }[] = await AdModule.isAdSupportDp({ adItems })
      if (typeof res === 'string') {
        return JSON.parse(res)
      }
      return res
    } else {
      throw new Error('??? isAdSupportDp ')
    }
  } catch (error) {
    customReportError({ source: 'isAdSupportDp', error })
    return []
  }
}

const exposureWithAdItem = async ({ adItem, responseId }: { adItem: AdDPTaskItem; responseId: number }) => {
  try {
    if (typeof AdModule?.exposureWithAdItem === 'function') {
      log('debug_exposureWithAdItem', responseId)
      AdModule.exposureWithAdItem({ adItem, responseId: safetyToString(responseId), positionName: 'points_center_task_banner' })
    }
  } catch (error) {
    customReportError({ source: 'exposureWithAdItem', error })
  }
}

const reactivationWithAdItem = async ({ adItem, extInfo, responseId }: { adItem: AdDPTaskItem; extInfo: string; responseId: number }) => {
  try {
    if (typeof AdModule?.reactivationWithAdItem === 'function') {
      const res = await AdModule.reactivationWithAdItem({
        adItem,
        extInfo,
        responseId: safetyToString(responseId),
      })
      console.log('🚩🚩🚩🚩🚩🚩', { res }, extInfo, adItem.name)
      if (res) {
        return res
      } else {
        throw new Error()
      }
    } else {
      throw new Error('发现新功能，请升级 App')
    }
  } catch (error) {
    console.log(error)
    APMLocalLog.add(safetyToString(error), 'rn_credit_center_reactivationWithAdItem')
    customReportError({ source: 'reactivationWithAdItem', error })
  }
}

const filterAvailableAdDPTaskList = async ({ originList }: { originList: AdDPTaskItem[] }) => {
  try {
    // 根据 广告SDK 最终筛选
    const supportConfig = await isAdSupportDp(originList)
    const filteredList = originList.filter((item) => {
      return supportConfig.find((config) => config.adid === item.adid.toString() && config.result === 1)
    })
    console.log('广告 SDK 过滤结果', JSON.stringify(supportConfig, null, 2))
    if (filteredList.length === 0) {
      customReportError({ source: '广告 SDK 过滤结果为空 -> 接口数据：' + originList.length, error: '' })
    }
    return filteredList
  } catch (error) {
    console.log('没有被过滤', error)
    customReportError({ source: 'filterAvailableAdDPTaskList', error })
    return []
  }
}

const getIosRequestCookies = async () => {
  try {
    if (typeof AdModule?.getAdCookie === 'function') {
      const res = await AdModule.getAdCookie({})
      console.log('🇲🇵🇲🇵🇲🇵🇲🇵🇲🇵🇲🇵🇲🇵', res)
      if (typeof res.Cookie === 'string') {
        return { Cookie: res.Cookie }
      } else {
        throw new Error()
      }
    } else {
      throw new Error()
    }
  } catch (err) {
    console.log('🇲🇵🇲🇵🇲🇵🇲🇵🇲🇵🇲🇵🇲🇵 err', err)
    return null
  }
}

const adDPTaskCtrl = {
  filterAvailableAdDPTaskList: filterAvailableAdDPTaskList,
  onExpo: exposureWithAdItem,
  onClick: reactivationWithAdItem,
  getIosRequestCookies,
}

export default adDPTaskCtrl
