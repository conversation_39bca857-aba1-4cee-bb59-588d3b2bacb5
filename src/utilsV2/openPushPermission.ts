import { isIOS, isAndroid } from '@xmly/rn-utils/dist/device'
import { openSettings } from 'react-native-permissions'
import {
  checkPushPermission,
  getPushPermission,
} from '../modulesV2/pushPermission'
import scheduleTask, { scheduleTaskExecType } from '../modulesV2/scheduleTask'
import SentryUtils from './sentryUtils'
import RNAlert from '../componentsV2/Confirm'
import { NativeModules } from 'react-native'

const openSettingConfirm: (title?: string, body?: any) => Promise<boolean> = (
  title,
  body
) => {
  // if (isAndroid) {
  return new Promise<boolean>((resolve) => {
    RNAlert({
      message: title || '开启提醒，不怕断签',
      body,
      actions: [
        {
          text: '取消',
          onPress: () => resolve(false),
        },
        {
          text: '去开启',
          onPress: () => resolve(true),
          primary: true,
        },
      ],
    })
  })
  // } else {
  //   return new Promise<boolean>((resolve) => {
  //     try {
  //       Alert.alert('打开消息通知', title || '开启提醒，不怕断签', [
  //         { text: '取消', onPress: () => resolve(false), style: 'cancel' },
  //         { text: '去开启', onPress: () => resolve(true) },
  //       ])
  //     } catch(err) {
  //       Toast.info(err)

  //     }

  //   })
  // }
}

const openPushPermission = async ({
  onPermissionGranted,
  onPermissionRejected,
  onPermissionFailed,
  confirmOpenSetting,
  confirmOpenSettingTitle,
  confirmBody,
}: {
  onPermissionGranted: () => void
  onPermissionRejected: () => void
  onPermissionFailed?: () => void
  confirmOpenSetting?: boolean
  confirmOpenSettingTitle?: string
  confirmBody?: any
}) => {
  let shouldFinallyCheck = true
  try {
    const checkRes = await checkPushPermission()
    if (checkRes === 'blocked') {
      let confirm = true
      if (confirmOpenSetting) {
        confirm = await openSettingConfirm(confirmOpenSettingTitle, confirmBody)
      }
      if (!confirm) {
        typeof onPermissionFailed === 'function' && onPermissionFailed()
        return
      }
      if (NativeModules?.RNDeviceInfo?.goOpenNotificationSetting) {
        NativeModules.RNDeviceInfo.goOpenNotificationSetting()
      } else {
        openSettings()
      }
      shouldFinallyCheck = false
      scheduleTask.pushTask({
        action: async () => {
          const res = await checkPushPermission()
          console.log('openPushPermission scheduleTask 🌟 🌟 🌟 res ' + res)
          if (res === 'granted') {
            onPermissionGranted()
          } else {
            typeof onPermissionFailed === 'function' && onPermissionFailed()
          }
        },
        onActionType: isIOS
          ? scheduleTaskExecType.onAppChangeToActive
          : scheduleTaskExecType.onPageResume,
      })
    } else {
      if (checkRes === 'granted') {
        shouldFinallyCheck = false
        onPermissionGranted()
      } else {
        const res = await getPushPermission()
        if (isIOS) {
          if (res) {
            onPermissionGranted()
          } else {
            onPermissionRejected()
          }
        }
      }
    }
  } catch (err) {
    SentryUtils.captureException(err, {
      source: 'performTask.openPushPermission',
    })
  } finally {
    if (!shouldFinallyCheck) return
    if (isAndroid) {
      scheduleTask.pushTask({
        action: async () => {
          const res = await checkPushPermission()
          if (res === 'granted') {
            onPermissionGranted()
          } else {
            typeof onPermissionFailed === 'function' && onPermissionFailed()
          }
        },
        onActionType: scheduleTaskExecType.onPageResume,
      })
    }
  }
}

export default openPushPermission
