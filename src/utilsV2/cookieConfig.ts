interface UserInfo {
  uid: number | string
  token: string
  appid: string
}
class CookieConfig {
  uid: number | string
  token: string

  setCookie(userInfo: UserInfo) {
    const { uid, token } = userInfo
    this.uid = uid
    this.token = token

  }
  
  getCookie() {
    console.log('🌹🌹🌹🌹🌹', `${this.uid}&${this.token}`)
    return `${this.uid}&${this.token}`
  }
}

export default  new CookieConfig()
