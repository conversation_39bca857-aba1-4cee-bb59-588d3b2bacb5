import { NativeModules } from 'react-native'

interface User {
  [key: string]: any
  id?: string
  ip_address?: string
  email?: string
  username?: string
}

interface Tags {
  source: string // 用来判断主动上报错误的具体代码模块
  user?: User
  [key: string]: any
}

function stringifyError(error: unknown): string {
  if (error instanceof Error) {
    return JSON.stringify(
      {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      null,
      2
    );
  } else if (typeof error === 'string') {
    return error;
  } else {
    try {
      return JSON.stringify(error, null, 2);
    } catch {
      return String(error);
    }
  }
}

export default class SentryUtils {
  static HAS_INIT = false

  static init = () => {
    if (SentryUtils.HAS_INIT) {
      return
    }
    SentryUtils.HAS_INIT = true
  }

  static unmount = () => {
    SentryUtils.HAS_INIT = false
  }

  static async captureException(e: any, tags: Tags) {
    if (__DEV__) {
      console.warn('SentryUtils捕获到错误', tags.source, e)
      return
    }
    SentryUtils.init()
    NativeModules.XMApm.log({
      type: 'apm_rn',
      subType: 'error',
      log: tags.source + ' ' + stringifyError(e),
    })
  }
}
