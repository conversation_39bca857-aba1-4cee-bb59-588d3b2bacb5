export default function formatMonthlyTicketProgressLabel({
  isVip,
  progress,
  condition,
  isDone,
  stillNeedCount,
  worth,
}: {
  isDone: boolean
  isVip: boolean
  condition: number
  progress: number
  stillNeedCount: number
  worth: number
}) {
  let prefix = `已签到${progress}次，再签${stillNeedCount}次可获得`
  let suffix = `张月票`
  let highlight = worth

  if (progress === 0) {
    prefix = `当月每累计签到${condition}次可获得月票`
    suffix = '张'
  }

  if (isDone) {
    prefix = `今日已得月票，再签到${condition}次可得`
  }

  return {
    isVip,
    prefix,
    highlight,
    suffix,
  }
}
