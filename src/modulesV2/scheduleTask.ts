import SentryUtils from "utilsV2/sentryUtils"

export enum scheduleTaskExecType {
  onPageResume = 'onPageResume',
  onAppChangeToActive = 'onAppChangeToActive'
}

export type ScheduleTaskType = { taskName?: string, action: Function, onActionType: scheduleTaskExecType, _taskId: number }

export type CreateScheduleTaskType = Omit<ScheduleTaskType, '_taskId'>


const scheduleTask = () => {
  const _runTask = (onActionType: scheduleTaskExecType) => () => {
    const needToFlushTask = taskMap[ onActionType ] || []
    if (needToFlushTask.length > 0) {
      try {
        needToFlushTask.forEach((t) => {
          t.action()
        })
      } catch (err) {
        console.log(err)
      } finally {
        taskMap[ onActionType ] = []
      }
    }
  }

  const taskMap: { [ key: string ]: ScheduleTaskType[] } = {}

  const pushTask = (newTask: CreateScheduleTaskType) => {
    if (taskMap[ newTask.onActionType ]) {
      taskMap[ newTask.onActionType ].push({ ...newTask, _taskId: Date.now() })
    } else {
      taskMap[ newTask.onActionType ] = [ { ...newTask, _taskId: Date.now() } ]
    }
  }

  const flushTask = (onActionType: scheduleTaskExecType) => {
    const tasksNeedFlush = getScheduleTaskByActionType(onActionType).slice()
    if (tasksNeedFlush.length > 0) {
      taskMap[ onActionType ] = []
      try {
        tasksNeedFlush.forEach((t) => {
          t.action()
        })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'scheduleTask.flushTask'
        })
      } finally {
        console.log('task exec finished ~~')
      }
    } else {
      console.log('no task to exec ~~')
    }

    // if (tasksNeedFlush.length > 0) {
    //   taskMap[onActionType] = []
    //   console.log({ taskMap })
    //   console.log({ currentRunner })
    //   if (currentRunner.isRunning) {
    //     currentRunner.setNext(_runTask(onActionType))
    //   } else {
    //     currentRunner.exec(_runTask(onActionType))
    //   }
    // } else {
    //   console.log('no task to exec ~~')
    // }

  }

  const getAllScheduleTask = () => []

  const getScheduleTaskByActionType = (type: scheduleTaskExecType) => taskMap[ type ] || []

  return {
    pushTask,
    flushTask,
    getAllScheduleTask,
    getScheduleTaskByActionType
  }
}

export default scheduleTask()