import { CashConfigChangeTaskId, CashConfigExpGroupId } from '../typesV2/goldCoin'

function goldCoinABValue() {
  const config: {
    groupId: CashConfigExpGroupId
    changeTasksId: CashConfigChangeTaskId
    showTutorial: boolean
  } = {
    groupId: CashConfigExpGroupId.default,
    changeTasksId: CashConfigChangeTaskId.default,
    showTutorial: false,
  }

  const setValue = ({ groupId, changeTasksId, showTutorial }: { showTutorial: boolean; groupId: CashConfigExpGroupId; changeTasksId: CashConfigChangeTaskId }) => {
    config.groupId = groupId
    config.changeTasksId = changeTasksId
    config.showTutorial = showTutorial
  }

  const getValue = () => config

  return {
    setValue,
    getValue,
  }
}

export default goldCoinABValue()
