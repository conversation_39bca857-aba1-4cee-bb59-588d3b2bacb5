import { isAndroid } from "@xmly/rn-utils/dist/device";
import { openSettings, PERMISSIONS, request, RESULTS, check } from "react-native-permissions";

export const getContactPermission = () => {
  let permission = isAndroid ? PERMISSIONS.ANDROID.READ_CONTACTS : PERMISSIONS.IOS.CONTACTS
  return request(permission).then((res) => {
    return res;
    if (res === RESULTS.BLOCKED) {
      openSettings().catch(() => console.warn('cannot open settings'));
    } else {
      // Toast.info(res)
    }
  })
}

export const checkContactPermission = () => {
  let permission = isAndroid ? PERMISSIONS.ANDROID.READ_CONTACTS : PERMISSIONS.IOS.CONTACTS
  return check(permission).then((res) => {
    // Toast.info(res)
    return res
  })
}