import { PushNotification } from '@xmly/rn-sdk'
import dayjs from 'dayjs'
import rnEnv from '../../rnEnv'
import requestLocalPushTimeTable from '../servicesV2/requestLocalPushTimeTable'
import {
  LocalPushTimeTableDataMonth,
  LocalPushTimeTableDataTomorrow,
} from '../typesV2/notification'
import xmlog from '../utilsV2/xmlog'

const userInfo = {
  extra: {
    msg_type: '94',
    bundle: 'rn_credit_center',
    reuse: true,
    srcChannel: 'sign_notice_local_push',
  },
}

const isTest = () => __DEV__ || rnEnv.isTest()

export const fetchLocalPushTimeTable = async () => {
  try {
    const res = await requestLocalPushTimeTable()
    console.log(res)
    if (res.ret === 0) {
      return res.data
    } else {
      throw new Error()
    }
  } catch (err) {
    console.log(err)
    return null
  }
}

export const genLocalPushTimeTable = (
  tomorrowConfig: LocalPushTimeTableDataTomorrow,
  monthConfig: LocalPushTimeTableDataMonth
) => {
  const monthArr = Array(29)
    .fill(1)
    .map((item, index) => {
      const monthItem = {
        fireDate: isTest()
          ? dayjs()
              .add(5 * index, 'second')
              .valueOf()
          : dayjs()
              .add(2, 'day')
              .add(1 * index, 'day')
              .set('hour', 9)
              .set('minute', 0)
              .set('second', 0)
              .set('millisecond', 0)
              .valueOf(),
        alertBody: monthConfig.content,
        alertTitle: isTest()
          ? `${monthConfig.title} ,index: ${index + 1}`
          : monthConfig.title,
      }
      return monthItem
    })

  const tomorrowTime = dayjs()
    .add(1, 'day')
    .set('hour', 9)
    .set('minute', 0)
    .set('second', 0)
    .set('millisecond', 0)
    .valueOf()

  const tomorrowPushTime = {
    fireDate: isTest() ? dayjs().set('second', 10).valueOf() : tomorrowTime,
    alertBody: tomorrowConfig.content,
    alertTitle: tomorrowConfig.title,
  }

  const timeTable = [tomorrowPushTime, ...monthArr]
  console.log('timeTable', timeTable)
  return timeTable
}

export const cancelPush = () => {
  return PushNotification.cancelLocalNotifications(userInfo)
}

export const setSinglePush = ({
  fireDate,
  alertBody = '',
  alertTitle = '',
}: {
  fireDate: number
  hour?: number
  minute?: number
  alertTitle: string
  alertBody: string
  repeatPeriod?: number[]
}) => {
  try {
    // 积分中心 push 排查  其他事件
    xmlog.event(55686, 'others', {
      description: '种push ',
    })
    PushNotification.scheduleLocalNotification({
      fireDate,
      alertTitle,
      alertBody,
      alertAction: '',
      isSilent: false,
      category: '',
      userInfo,
      applicationIconBadgeNumber: 1,
      //@ts-ignore
      repeatInterval: '',
    })
  } catch (err) {}
}
