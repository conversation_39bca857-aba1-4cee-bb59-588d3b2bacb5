

type TinyCacheControlConfig = {
  size: number
}

const DEFAULT_CACHE_SIZE = 5

export default class TinyCacheControl<KeyT, ValueT> {
  constructor (config?: TinyCacheControlConfig) {
    this.size = config ? config.size : DEFAULT_CACHE_SIZE
    console.log('this.cacheStore', this.cacheStore)
  }

  private cacheStore = new Map<KeyT, ValueT>()
  private mapKeys: KeyT[] = []

  size: number

  private getFirstKey = () => {
    return this.mapKeys.shift()
  }

  private addKey = (key: KeyT) => {
    this.mapKeys.push(key)
  }

  private removeKey = (key: KeyT) => {
    this.mapKeys = this.mapKeys.filter(_key => _key !== key)
    return this.mapKeys
  }

  private removeAllKey = () => {
    this.mapKeys = []
  }

  getCurrentSize = () => this.cacheStore.size

  set = (key: KeyT, item: ValueT) => {
    if (this.getCurrentSize() >= this.size) {
      const lastKey = this.getFirstKey()
      lastKey && this.remove(lastKey)
    }
    this.cacheStore.set(key, item)
    this.addKey(key)
  }

  update = (key: KeyT) => (reducer: (target: ValueT | {}) => ValueT) => {
    const target = this.get(key)
    if (target) {
      console.log('更新缓存')
      this.cacheStore.set(key, reducer(target))
    } else {
      console.log('没有此缓存内容')
      console.log('创建缓存')
      this.set(key, reducer({}))
    }
  }

  get = (key: KeyT) => {
    return this.cacheStore.get(key)
  }

  has = (key: KeyT) => {
    return this.cacheStore.has(key)
  }

  remove = (key: KeyT) => {
    if (key) {
      this.cacheStore.delete(key)
      this.removeKey(key)
    }
  }

  removeAll = () => {
    this.cacheStore.clear()
    this.removeAllKey()
  }
}