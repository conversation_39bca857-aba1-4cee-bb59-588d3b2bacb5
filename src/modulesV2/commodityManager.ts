import { Toast } from '@xmly/rn-sdk'
import getECommerceComponent from '../servicesV2/getECommerceComponent'
import getECommerceProductByPoolId from '../servicesV2/getECommerceProductByPoolId'
import {
  CommodityActivityItemType,
  CommodityDisplayProducts,
  CommodityDisplayTabItem,
  CommodityDisplayTabs,
} from '../typesV2/commodity'
import { ECommerceComponent, ECommerceProduct } from '../typesV2/ECommerce'
import SentryUtils from '../utilsV2/sentryUtils'
import stringToNumber from '../utilsV2/stringToNumber'
import lodashGet from 'lodash.get'
import requestCommodityActivityList from '../servicesV2/requestCommodityActivityList'

type ECommerceProductsDataType = {
  pageNo: number
  hasMore: boolean
  list: CommodityDisplayProducts
}

const processActivityList: (
  listA: CommodityActivityItemType[]
) => CommodityDisplayProducts = (
  activityCommodityList: CommodityActivityItemType[]
) => {
  return activityCommodityList.map((commodity) => ({
    id: commodity.refId,
    commodityName: commodity.title, // 商品名
    commodityCover: commodity.cover, // 商品封面图
    exchangePrice: commodity?.ext?.other?.exchangePrice || 0, // 兑换积分值
    exchangeMoney: commodity?.ext?.other?.exchangeMoney || 0, // 实际价格 RMB
    isActivity: !!commodity.ext?.other?.isActivity, // 是否限时秒杀
    cornerImgUrl: commodity.ext?.other?.cornerImgUrl, // 商品角标
    tabId: commodity.ext?.other?.tabId, // tab id
    commodityUrl: commodity.landingPage, // 商品详情页链接
    startTime: commodity.startAt, // 秒杀开始时间
    endTime: commodity.endAt, // 秒杀结束时间
  }))
}

const fetActivityList = async (componentId: number) => {
  try {
    const res = await requestCommodityActivityList()
    if (res && res.data.length > 0) {
      console.log({ requestCommodityActivityList: res })
      const formattedList = processActivityList(res.data)
      return formattedList.filter((item) => item.tabId === componentId)
    } else {
      return []
    }
  } catch (err) {
    return []
  }
}

// 通过componentId 来获取组件下面配置的tabs 信息
const fetchECommerceTabs = async (componentId?: number) => {
  try {
    const res = await getECommerceComponent(componentId)
    return res?.data || null
  } catch (err) {
    SentryUtils.captureException(err, {
      source: 'CommodityManager.fetchECommerceTabs',
    })
    return null
  }
}

// 格式化电商接口返回的商品字段
const formatECommerceProductToDisplayProduct: (
  products: ECommerceProduct[]
) => CommodityDisplayProducts = (products: ECommerceProduct[]) => {
  return products.map((product) => ({
    id: product.productId,
    commodityName: product.productName, // 商品名
    commodityCover: product.coverUrl, // 商品封面图
    exchangePrice: product.exchangePoint, // 兑换积分值
    exchangeMoney: product.payAmount, // 实际价格 RMB
    isActivity: !!product.isSkill, // 是否限时秒杀
    cornerImgUrl: product.cornerImgUrl, // 商品角标
    // tabId?: number // tab id
    commodityUrl: product.clickUrl, // 商品详情页链接
    startTime: stringToNumber(product.startTime), // 秒杀开始时间
    endTime: stringToNumber(product.endTime), // 秒杀结束时间
  }))
}

// 格式化电商部tab 字段
const getDisplayTabsFromECommerceTabs: (
  eCommerceTabs: ECommerceComponent[]
) => CommodityDisplayTabs = (eCommerceTabs) => {
  return eCommerceTabs.map((tab) => ({
    poolId: tab.poolId,
    name: tab.name,
    id: tab.id,
  }))
}

// 请求componentId 下指定的poolId 的商品，并伴随分页逻辑
const fetchECommerceProductListByPoolIdAction = async ({
  poolId,
  pageNo,
  componentId,
  setECommerceProductsDataMap,
  getECommerceProductDataForDisplay,
}: {
  poolId: number
  componentId: number
  pageNo?: number
  setECommerceProductsDataMap: (
    componentId: number | string,
    data: ECommerceProductsDataType
  ) => void
  getECommerceProductDataForDisplay: (
    componentId: number
  ) => ECommerceProductsDataType
}) => {
  try {
    const res = await getECommerceProductByPoolId(
      poolId,
      componentId,
      pageNo || 1
    )
    let displayProductList: CommodityDisplayProducts = []
    const productVos =
      lodashGet(res, 'data.productVos', []) || ([] as ECommerceProduct[])

    if (typeof pageNo === 'number') {
      // pageNo 存在且为数字处理分页逻辑
      const originList = getECommerceProductDataForDisplay(componentId).list
      displayProductList = [
        ...originList,
        ...formatECommerceProductToDisplayProduct(productVos),
      ]
      console.log('productVos', productVos)
    } else {
      const activityList = await fetActivityList(componentId)
      displayProductList = [
        ...activityList,
        ...formatECommerceProductToDisplayProduct(productVos),
      ]
    }

    setECommerceProductsDataMap(componentId, {
      hasMore: res?.data?.hasMore,
      pageNo: res?.data?.pageNo,
      list: displayProductList,
    })
  } catch (err) {
    console.log(err)
    SentryUtils.captureException(err, {
      source: 'CommodityManager.fetchECommerceProductListByPoolId',
    })
  }
}

const initProductAction = async ({
  setECommerceProductsDataMap,
  getECommerceProductDataForDisplay,
  componentId,
}: {
  setECommerceProductsDataMap: (
    componentId: number | string,
    data: ECommerceProductsDataType
  ) => void
  getECommerceProductDataForDisplay: (
    componentId: number
  ) => ECommerceProductsDataType
  componentId?: number
}) => {
  try {
    const eCommerceTabsRes = await fetchECommerceTabs(componentId)
    if (!eCommerceTabsRes) {
      Toast.info('商品数据获取失败～')
      throw new Error('商品数据获取失败～')
    }
    let firstTab: Pick<ECommerceComponent, 'id' | 'poolId' | 'productVos'> = {
      id: eCommerceTabsRes.id,
      poolId: eCommerceTabsRes.poolId,
      productVos: eCommerceTabsRes.productVos || [],
    }

    if (Array.isArray(eCommerceTabsRes.childComponents)) {
      firstTab = eCommerceTabsRes.childComponents[0]
    }

    const firstTabProducts = firstTab?.productVos || []

    if (firstTabProducts.length > 0) {
      // 如果第一个tab 有数据，设置到缓存中
      // 如果组件下面没有childComponent，则设置成eCommerceTabsRes.id
      const activityList = await fetActivityList(firstTab.id)
      setECommerceProductsDataMap(firstTab.id, {
        list: [
          ...activityList,
          ...formatECommerceProductToDisplayProduct(firstTabProducts),
        ],
        pageNo: eCommerceTabsRes.pageNo,
        hasMore: eCommerceTabsRes.hasMore,
      })
    } else {
      // 兜底，防止默认第一个tab 没返回数据
      await fetchECommerceProductListByPoolIdAction({
        poolId: firstTab.poolId,
        componentId: firstTab.id,
        setECommerceProductsDataMap,
        getECommerceProductDataForDisplay,
      })
    }

    // 格式化展示的tab 数据
    const displayTabsFromECommerceTabs = getDisplayTabsFromECommerceTabs(
      eCommerceTabsRes?.childComponents || []
    )

    let displayTabs = [...displayTabsFromECommerceTabs]

    const productForDisplayInfo = getECommerceProductDataForDisplay(firstTab.id)

    return {
      displayTabs,
      productForDisplayInfo,
    }
  } catch (err) {
    return {
      displayTabs: [],
      productForDisplayInfo: {
        pageNo: 0,
        hasMore: false,
        list: [],
      },
    }
  }
}

class CommodityManager {
  isInit = false
  displayTabs: CommodityDisplayTabItem[] = []
  ECommerceProductsDataMap: { [key: string]: ECommerceProductsDataType } = {} // 保存在内存中的商品详情

  constructor() {}

  getECommerceProductDataForDisplay = (componentId: number) =>
    this.ECommerceProductsDataMap[componentId] || {
      pageNo: 0,
      hasMore: false,
      list: [],
    }

  // 设置电商部商品数据，与最终展示数据不同，保存在内存中。
  private setECommerceProductsDataMap = (
    componentId: number | string,
    data: ECommerceProductsDataType
  ) => {
    this.ECommerceProductsDataMap[componentId] = {
      hasMore: !!data.hasMore,
      pageNo: data.pageNo || 1,
      list: data.list,
    }
    console.log('设置商品缓存：', this.ECommerceProductsDataMap)
  }

  initProduct = async (componentId?: number) => {
    const data = await initProductAction({
      setECommerceProductsDataMap: this.setECommerceProductsDataMap,
      getECommerceProductDataForDisplay: this.getECommerceProductDataForDisplay,
      componentId,
    })
    this.displayTabs = data.displayTabs
    this.isInit = true
    return data
  }

  getECommerceProductListByPoolId = async ({
    poolId,
    pageNo,
    componentId,
  }: {
    poolId: number
    componentId: number
    pageNo?: number
  }) => {
    await fetchECommerceProductListByPoolIdAction({
      pageNo,
      poolId,
      componentId,
      getECommerceProductDataForDisplay: this.getECommerceProductDataForDisplay,
      setECommerceProductsDataMap: this.setECommerceProductsDataMap,
    })

    const productListInfo = this.getECommerceProductDataForDisplay(componentId)

    return productListInfo
  }
}

export default CommodityManager
