import { Alert } from "react-native";
import { openSettings,checkNotifications, requestNotifications } from "react-native-permissions";

enum PermissionStatus {
  UNAVAILABLE = "unavailable",
  DENIED = "denied",
  BLOCKED = "blocked",
  GRANTED = "granted"
}

export const getPushPermission = () => {
  return new Promise<boolean>((resolve, reject) => {
    requestNotifications([ 'alert', 'sound' ]).then(({ status }) => {
      switch (status) {
        case PermissionStatus.GRANTED:
          resolve(true)
          break
        default:
          resolve(false)
          break
      }
    }).catch(() => {
      resolve(false)
    })
  })
}

const goSetting = () => {
  Alert.alert(
    '需要开启推送权限',
    '请在手机的“设置-通知”选项中，允许喜马拉雅给您发送通知',
    [
      { text: '取消', onPress: () => { }, style: 'cancel' },
      { text: '去设置', onPress: () => openSettings() }
    ],
    { cancelable: false }
  )
}

export const checkPushPermission = (): Promise<PermissionStatus> => {
  return checkNotifications().then(({ status }) => {
    return status as PermissionStatus
  })
  // return new Promise((resolve, reject) => {
  //   let has = false
  //   checkNotifications().then(({ status }) => {
  //     return resolve(status)
  //     switch (status) {
  //       case PermissionStatus.GRANTED:
  //         callback && callback()
  //         has = true
  //         break
  //       case PermissionStatus.BLOCKED:
  //         has = false
  //         openSetting && goSetting()
  //         break
  //       case PermissionStatus.DENIED:
  //         has = false
  //         // getPushPermission(callback)
  //         break
  //       default:
  //         break
  //     }
  //     resolve(has)
  //   })
  // })
}