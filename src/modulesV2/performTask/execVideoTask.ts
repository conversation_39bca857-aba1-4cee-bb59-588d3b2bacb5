import { Toast } from '@xmly/rn-sdk'
import { isIOS, isAndroid } from '@xmly/rn-utils/dist/device'
import { NativeModules } from 'react-native'
import { taskListAid } from '../../constantsV2'
import getTokenV2 from '../../servicesV2/getTokenV2'
import { increaseTaskListItemProgressV2 } from '../../servicesV2/increaseTaskProgress'
import { store } from '../../store'
import { IncreaseTaskProgressStatus } from '../../typesV2/increaseTaskProgress'
import { TaskItemType } from '../../typesV2/taskList'
import DelayTask from '../../utilsV2/delayTask'
import { listenEarnRewardCoin, encryptByType } from '../../utilsV2/native'
import SentryUtils from '../../utilsV2/sentryUtils'
import lodashGet from 'lodash.get'

const execVideoTask = async (
  taskItem: Pick<TaskItemType, 'id'>,
  aid: number,
  extInfo?: Record<string, any>
) => {
  try {
    const mAid = typeof aid === 'number' ? aid : taskListAid
    const token = await getTokenV2(taskItem.id, mAid)
    if (!token) return
    const taskActivityContext = store.getState().taskCenter.activityContext
    const videoTaskInfo = lodashGet(
      taskActivityContext,
      'activityBasicInfo.extraInfo.videoTaskInfo',
      {
        positionId: 254,
        positionName: 'integral_center_inspire_video',
      }
    )
    const res = await listenEarnRewardCoin({
      positionName: videoTaskInfo.positionName,
      slotId: videoTaskInfo.positionId,
      extInfo: extInfo ? JSON.stringify(extInfo) : ''
    })
    // iOS端clientCode的返回值为'', 则成功；若失败，会走catch
    // android端clientCode的返回值为'success', 则成功；若失败，会走catch
    if (
      (res.clientCode === '' && isIOS) ||
      // (res.clientCode === 'success' && isAndroid)
      (String(res.clientCode) === '10003' && isAndroid)
    ) {
      const { uid } = await NativeModules.Account.getUserInfo()
      const data = { checkData: `token=${token}&uid=${uid}&` }
      const { checkData } = await encryptByType('md5', data)
      //  任务列表中更新视频任务进度
      DelayTask.start()
      const increaseTaskProgressRes = await increaseTaskListItemProgressV2({
        token,
        sign: checkData,
        aid: mAid,
        taskId: taskItem.id,
      })
      DelayTask.end()
      if (increaseTaskProgressRes) {
        if (
          increaseTaskProgressRes.ret === 0 &&
          increaseTaskProgressRes.data.status ===
            IncreaseTaskProgressStatus.RefreshTaskSucceeded &&
          increaseTaskProgressRes.data.awardCount
        ) {
          if (increaseTaskProgressRes.data.awardCount) {
            Toast.info(`${increaseTaskProgressRes.data.awardCount}积分已到账`)
          }
        }
      } else {
        throw new Error('任务完成失败')
      }
    }
  } catch (err) {
    DelayTask.end()
    console.log(`videoTaskError ${err}`)
    Toast.info('任务执行失败～')
    SentryUtils.captureException(err, {
      source: 'perform.videoTask',
    })
  }
}

export default execVideoTask
