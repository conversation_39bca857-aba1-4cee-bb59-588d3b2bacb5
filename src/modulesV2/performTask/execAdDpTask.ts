import { TaskItemType } from '../../typesV2/taskList'
import { isIOS } from '@xmly/rn-utils/dist/device'
import adDPTaskCtrl from '../../utilsV2/adDPTask'
import scheduleTask, { scheduleTaskExecType } from '../scheduleTask'
import { taskListAid } from '../../constantsV2'
import { store } from '../../store'
import getEverydayChallengeAidByAB from '../../utilsV2/getEverydayChallengeAidByAB'

const execAdDpTask = async ({ aid, taskItem }: { aid: number; taskItem: TaskItemType }) => {
  try {
    if (taskItem.adDpItemInfo) {
      await adDPTaskCtrl.onClick({
        adItem: taskItem.adDpItemInfo,
        extInfo: JSON.stringify({ aid, taskId: taskItem.id, sourceName: 'cashAppTask' }),
        responseId: taskItem.adDpResponseId!,
      })
    }
  } catch (error) {
  } finally {
    if (isIOS) {
      scheduleTask.pushTask({
        action: async () => {
          aid === taskListAid && store.dispatch.taskCenter.getTaskList({ aid }) // 主任务列表
          // 处理任务包任务列表的逻辑
          if (aid === (await getEverydayChallengeAidByAB())) {
            store.dispatch.everydayChallenge.getTaskList() // 每日挑战任务列表
          }
        },
        onActionType: isIOS ? scheduleTaskExecType.onAppChangeToActive : scheduleTaskExecType.onPageResume,
      })
    }
  }
}

export default execAdDpTask
