import { isIOS } from '@xmly/rn-utils/dist/device'
import { taskListAid } from '../../constantsV2'
import { store } from '../../store'
import { TaskItemType } from '../../typesV2/taskList'
import refreshTaskAndRefreshList from '../../utilsV2/refreshTaskAndRefreshList'
import SentryUtils from '../../utilsV2/sentryUtils'
import scheduleTask, { scheduleTaskExecType } from '../scheduleTask'
import {
  checkContactPermission,
  getContactPermission,
} from '../taskContactPermission'

const hideOneTask = (taskId: number, aid: number) => {
  if (aid === taskListAid) {
    store.dispatch.taskCenter.hideOneTask({ taskId })
  }
  // 处理任务包任务列表的逻辑
  store.dispatch.everydayChallenge.hideOneTask({ taskId, aid })
}

const openContactPermission = async (taskItem: TaskItemType, aid: number) => {
  try {
    const checkRes = await checkContactPermission()
    if (checkRes === 'granted') {
      refreshTaskAndRefreshList(taskItem.id, aid)
      return
    }
    if (checkRes === 'blocked') {
      hideOneTask(taskItem.id, aid)
      return
    }
    const res = await getContactPermission()
    if (isIOS) {
      if (res === 'granted') {
        refreshTaskAndRefreshList(taskItem.id, aid)
      }
      if (res === 'blocked') {
        hideOneTask(taskItem.id, aid)
      }
    } else {
      scheduleTask.pushTask({
        action: async () => {
          const res = await checkContactPermission()
          if (res === 'granted') {
            refreshTaskAndRefreshList(taskItem.id, aid)
          }
          if (res === 'blocked') {
            hideOneTask(taskItem.id, aid)
          }
        },
        onActionType: scheduleTaskExecType.onPageResume,
      })
    }
  } catch (err) {
    console.log(err)
    SentryUtils.captureException(err, {
      source: 'performTask.openContactPermission',
    })
  }
}

export default openContactPermission
