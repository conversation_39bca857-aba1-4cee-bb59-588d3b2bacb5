import { Page, Toast } from '@xmly/rn-sdk'
import refreshClientTaskV2 from 'servicesV2/refreshClientTaskV2'
import getUrlToOpen from 'utilsV2/getUrlToOpen'
import { TaskItemKind, TaskItemType } from '../../typesV2/taskList'
import { store } from '../../store'
import GlobalEventEmitter from 'utilsV2/globalEventEmitter'
import openPushPermission from '../../utilsV2/openPushPermission'
import refreshTaskAndRefreshList from '../../utilsV2/refreshTaskAndRefreshList'
import execVideoTask from './execVideoTask'
import openContactPermission from './openContactPermission'
import execThirdpartyTask from './execThirdpartyTask'
import execAddWidgetTask from './execAddWidgetTask'
import execPointExchangeTask from './execPointExchangeTask'
import { taskListAid } from '../../constantsV2'
import execVideoTaskNew from './execVideoTaskNew'
import execAdDpTask from './execAdDpTask'
import TaskListModalControl from '../../componentsV2/EverydayChallengeV2/TaskListModal/TaskListModalControl'

const performTaskAndComplete = (taskItem: TaskItemType, aid: number) => {
  refreshClientTaskV2({ taskId: taskItem.id }, aid)

  if (taskItem.guideLink && taskItem.guideLink.includes('action=taskListModal') && taskItem.guideLink.includes('bundle=rn_credit_center')) {
    // 打开任务包弹窗
    TaskListModalControl.open()
    return
  }

  taskItem.guideLink && Page.start(getUrlToOpen(taskItem.guideLink))
}

export default function performTask(taskItem: TaskItemType, aid: number, extInfo?: Record<string, any>) {
  try {
    const { taskType, code, id } = taskItem
    console.info('debug__performTask', { taskType, code });
    if (taskType === TaskItemKind.clientBehavior) {
      switch (code) {
        case 3:
          openPushPermission({
            onPermissionGranted: () => {
              refreshTaskAndRefreshList(taskItem.id, aid)
            },
            onPermissionRejected: () => {
              if (aid === taskListAid) {
                store.dispatch.taskCenter.hideOneTask({
                  taskId: taskItem.id,
                })
              }

              // 处理任务包任务列表的逻辑
              store.dispatch.everydayChallenge.hideOneTask({
                taskId: taskItem.id,
                aid,
              })
            },
          })
          break
        case 2:
          openContactPermission(taskItem, aid)
          break
        case 1:
          execVideoTask(taskItem, aid, extInfo)
          break
        case 88:
          // pc 导流任务
          GlobalEventEmitter.emit('changeDialog', { visible: true })
          break
        case 99:
          // 执行跳转前就要把任务标记为完成
          performTaskAndComplete(taskItem, aid)
          break
        case 98: // 浏览任务
          GlobalEventEmitter.emit('onPerformBrowseTask')
          return
        case 777: // 添加widget 挂件任务
          execAddWidgetTask(taskItem)
          return
        case 1189:
          execThirdpartyTask(id, taskItem.title || '', aid, true)
          return
        default:
          taskItem.guideLink && Page.start(getUrlToOpen(taskItem.guideLink))
          return
      }
    } else if (taskType === TaskItemKind.thirdpartyTask) {
      execThirdpartyTask(id, taskItem.title || '', aid)
    } else if (taskType === TaskItemKind.PCListenTask && code === 188) {
      // PC 收听任务弹窗
      GlobalEventEmitter.emit('changeDialog', {
        visible: true,
        isPCListen: true,
      })
    } else if (taskType === TaskItemKind.newVideoTask) {
      execVideoTaskNew(taskItem, aid, undefined, extInfo)
    } else if (taskType === TaskItemKind.AdDPSlotTask) {
      execAdDpTask({ taskItem, aid })
    } else {
      switch (code) {
        case 111: // 积分换任务
          const everydayChallengeSummaryTask = store.getState().everydayChallenge.summaryTask
          execPointExchangeTask({
            aid,
            taskId: taskItem.id,
            checkTaskId: everydayChallengeSummaryTask.id,
          })
          return
        default:
          taskItem.guideLink && Page.start(getUrlToOpen(taskItem.guideLink))
          return
      }
    }
  } catch (err) {
    console.log(err)
    Toast.info('执行任务失败～')
  }
}
