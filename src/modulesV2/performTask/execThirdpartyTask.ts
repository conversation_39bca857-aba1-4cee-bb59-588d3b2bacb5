import { ConfigCenter, Toast } from '@xmly/rn-sdk'
import { isIOS } from '@xmly/rn-utils/dist/device'
import { taskListAid } from '../../constantsV2'
import refreshClientTaskV2 from '../../servicesV2/refreshClientTaskV2'
import getThirdpartyTask from '../../servicesV2/thirdpartyTask'
import { store } from '../../store'
import getEverydayChallengeAidByAB from '../../utilsV2/getEverydayChallengeAidByAB'
import openThirdpartyApp from '../../utilsV2/openThirdpartyApp'
import xmlog from '../../utilsV2/xmlog'
import scheduleTask, { scheduleTaskExecType } from '../scheduleTask'

const getThirdpartyTaskDownloadConfirmConfig = async (taskId: number) => {
  try {
    const configRes = await ConfigCenter.getConfig(
      'toc',
      'thirdparty_task_app_download_confirm'
    )
    if (typeof configRes === 'string') {
      const arr = configRes.split(',')
      return !!arr.find((idStr) => Number(idStr) === taskId)
    }
    return false
  } catch (err) {
    return false
  }
}

const execThirdpartyTask = async (
  taskId: number,
  taskTitle: string,
  aid: number,
  performAndComplete?: boolean
) => {
  let h5Link = ''
  let schemaLink = ''
  try {
    const { data } = await getThirdpartyTask(taskId, aid)
    const shouldConfirm = await getThirdpartyTaskDownloadConfirmConfig(taskId)
    h5Link = data.h5Link || ''
    schemaLink = data.schemaLink || ''
    openThirdpartyApp({
      schema: schemaLink,
      h5Link,
      onFail: () => {
        Toast.info('执行任务失败～')
      },
      onSuccess: () => {
        if (performAndComplete) {
          refreshClientTaskV2({ taskId }, aid)
        }
      },
      confirmBeforeOpen: shouldConfirm,
      eventTrack: (type: '吊起 app' | '吊起下载页') => {
        // 吊起第三方 app  其他事件
        xmlog.event(49511, 'others', { type, from: taskTitle })
      },
    })
    scheduleTask.pushTask({
      action: async () => {
        if (isIOS) {
          aid === taskListAid && store.dispatch.taskCenter.getTaskList({ aid }) // 主任务列表

          // 处理任务包任务列表的逻辑
          if (aid === (await getEverydayChallengeAidByAB())) {
            store.dispatch.everydayChallenge.getTaskList() // 每日挑战任务列表
          }
        }
      },
      onActionType: isIOS
        ? scheduleTaskExecType.onAppChangeToActive
        : scheduleTaskExecType.onPageResume,
    })
  } catch (err) {
    Toast.info('执行任务失败～')
  }
}

export default execThirdpartyTask
