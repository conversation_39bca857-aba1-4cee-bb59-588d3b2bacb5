import { Toast } from '@xmly/rn-sdk'
import { pointExchange } from '../../servicesV2/pointExchange'
import { store } from '../../store'

const execPointExchangeTask = async ({
  aid,
  taskId,
  checkTaskId,
}: {
  aid: number
  taskId: number
  checkTaskId: number
}) => {
  try {
    const res = await pointExchange({ activityId: aid, taskId, checkTaskId })
    if (res && res.ret === 0) {
      Toast.info('跳过成功')
      store.dispatch.everydayChallenge.getTaskList({ aid })
      store.dispatch.credit.getCredit() // 拉取积分值
    } else {
      res?.msg && Toast.info(res?.msg || '')
    }
  } catch (error) {}
}

export default execPointExchangeTask
