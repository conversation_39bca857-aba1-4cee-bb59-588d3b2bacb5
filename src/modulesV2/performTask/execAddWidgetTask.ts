import { Page, Toast } from '@xmly/rn-sdk'
import { TaskItemType } from '../../typesV2/taskList'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import getUrlToOpen from '../../utilsV2/getUrlToOpen'
import { NativeModules } from 'react-native'

const goToHelpPage = (taskItem: TaskItemType) => {
  if (taskItem.guideLink) {
    Page.start(getUrlToOpen(taskItem.guideLink))
  } else {
    Toast.info('任务执行失败～')
  }
}

const testCanAddWidget = async () => {
  try {
    if (
      NativeModules.XMRNWidget &&
      typeof NativeModules.XMRNWidget.canAddSignInWidget === 'function' &&
      typeof NativeModules.XMRNWidget.addSignInWidget === 'function'
    ) {
      return await NativeModules.XMRNWidget.canAddSignInWidget()
    } else {
      throw new Error()
    }
  } catch (err) {
    return false
  }
}

const addWidgetAndroid = async () => {
  try {
    const res = await NativeModules.XMRNWidget.addSignInWidget()
    console.log(res)
  } catch (err) {}
}

export default async function (taskItem: TaskItemType) {
  if (isAndroid) {
    try {
      const canAddWidget = await testCanAddWidget()
      if (canAddWidget) {
        addWidgetAndroid()
      } else {
        throw new Error('can not add widget auto')
      }
    } catch (err) {
      goToHelpPage(taskItem)
    }
  } else {
    goToHelpPage(taskItem)
  }
}
