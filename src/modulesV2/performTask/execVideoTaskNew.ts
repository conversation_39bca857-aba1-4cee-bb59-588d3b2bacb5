import { Toast } from '@xmly/rn-sdk'
import { isIOS, isAndroid } from '@xmly/rn-utils/dist/device'
import { taskListAid } from '../../constantsV2'
import { store } from '../../store'
import { TaskItemType } from '../../typesV2/taskList'
import { listenEarnRewardCoin } from '../../utilsV2/native'
import lodashGet from 'lodash.get'
import customReportError from '../../utilsV2/customReportError'
import DelayTask from '../../utilsV2/delayTask'
import getEverydayChallengeAidByAB from '../../utilsV2/getEverydayChallengeAidByAB'

type videoTaskInfo = {
  positionId: number;
  positionName: string;
}

const execVideoTaskNew = async (taskItem: Pick<TaskItemType, 'id'>, aid: number, videoInfo?: videoTaskInfo, extInfo?: Record<string, any>): Promise<boolean | 'fallback'> => {
  console.info('debug_execVideoTaskNew', { taskItem, aid, videoInfo, extInfo });
  try {
    const mAid = typeof aid === 'number' ? aid : taskListAid
    let videoTaskInfo: videoTaskInfo = videoInfo || {
      positionId: 88,
      positionName: 'incentive_species',
    }
    if (aid === taskListAid) { // 如果aid与积分中心任务列表的aid相同，则读取积分中心活动上下文中的视频素材信息
      const taskActivityContext = store.getState().taskCenter.activityContext
      videoTaskInfo = lodashGet(
        taskActivityContext,
        'activityBasicInfo.extraInfo.videoTaskInfo',
        {
          positionId: 88,
          positionName: 'incentive_species',
        }
      )
    }

    const options = {
      positionName: videoTaskInfo.positionName,
      slotId: videoTaskInfo.positionId,
      extInfo: JSON.stringify({ aid: mAid, taskId: taskItem.id, ...extInfo }), // 透传给服务端的数据
      rewardVideoStyle: 1, // 表示使用新的看视频逻辑，客户端完成任务。
    }

    console.log('execVideoTaskNew 🚁🚁🚁🚁🚁🚁', options)

    const res = await listenEarnRewardCoin(options)
    console.log('🚎 🚎 🚎 🚎 🚎 🚎 listenEarnRewardCoin res', res)
    // iOS端clientCode的返回值为'', 则成功；若失败，会走catch
    // android端clientCode的返回值为'success', 则成功；若失败，会走catch
    if (
      (res.clientCode === '' && isIOS) ||
      // (res.clientCode === 'success' && isAndroid)
      (String(res.clientCode) === '10003' && isAndroid)
      || (res?.clientCode === 'fallBackReq' && isIOS) // iOS客户端超时兜底逻辑，会直接发放奖励
      || (String(res.clientCode) === '10004' && isAndroid) // android客户端超时兜底逻辑，会直接发放奖励
    ) {
      Toast.info('任务完成成功~')
      if (res?.clientCode === 'fallBackReq' || String(res?.clientCode) === '10004') { // 更新任务及余额信息
        DelayTask.addHandlers([
          store.dispatch.credit.getCredit,
          store.dispatch.taskCenter.getTaskList,
          store.dispatch.goldCoin.getBalance,
          store.dispatch.chips.getChipsBalance
        ])
        return 'fallback'
      }
      return true
    } else {
      customReportError({ source: 'execVideoTaskNew.invalid_result', error: res?.toString() === '[object Object]' ? JSON.stringify(res) : res })
      console.log(res)
      Toast.info('任务执行失败')
      return false
    }
  } catch (err) {
    customReportError({ source: 'execVideoTaskNew.catch', error: err })
    console.log(`🚎 🚎 🚎 🚎 🚎 🚎videoTaskError new ${err}`)
    Toast.info('任务执行失败～')
    return false
  } finally {
    if (aid === await getEverydayChallengeAidByAB()) {
      store.dispatch.everydayChallenge.getTaskList()
    }
  }
}

export default execVideoTaskNew
