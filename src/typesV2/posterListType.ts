
export enum PosterItemResourceType {
  none,
  track,
  album,
  activity
}

export type PosterItemType = {
  pictureUrl: string
  jumpUrl: string
  posterId: number
  sequence: number
  billDate: number
  resourceType: PosterItemResourceType
  resourceId: number
}

export type PosterListRes = {
  posterList: PosterItemType[]
  totalCount: number
  pageId: number
  pageSize: number
}

export type PosterListType =  PosterItemType[]