export interface SelectDrawActivityAwardConfig {
  id: number
  activityId: number
  abKey?: any
  abValue?: any
  traitName?: any
  traitValue?: any
  currencyStart: number
  currencyEnd: number
}

export enum AwardType {
  nothing = 99, // 兜底奖品
  point = 1, // 积分
  materialObject = 2, //实物
  vipCard = 3, // VIP 卡
  jumpProduct = 4, //跳转商品
  monthlyTicket = 5, // 月票
  giftPackage = 6, // 礼包
}

export interface Award {
  id: number
  type: AwardType
  description: string
  title: string
  count: number
  itemId?: any
  logoPic: string
  link?: any
  stock: number
  activityId: number
}

export interface SelectDrawActivityAwardRule {
  id: number
  configId: number
  awardId: number
  probability: number
  drawPeroidCount?: number
  drawPeroid?: number
  award: Award
}

export interface DrawActivity {
  id: number
  title: string
  description: string
  startAt?: any
  endAt?: any
  period: number
  periodList: string
  limitPeriod: number
  limitPeroidCount: number
  currencyType: number
  currencyCount: number
  freePeriod: number
  freePeriodCount: number
  selectDrawActivityAwardConfig: SelectDrawActivityAwardConfig
  selectDrawActivityAwardRules: SelectDrawActivityAwardRule[]
  picUrl: string
  status: number
}

export interface ActivityBaseInfo {
  drawActivity: DrawActivity
  point: number
  remainDrawCount: number
  remainDrawFreeCount: number
  canDraw: boolean
  canTenDraw: boolean
  activityOpen: boolean
  workDayPercentAwardTitle: string
  canReceiveAward: boolean
  percentConfigSumDrawCount: number
  percentDrawCount: number
}
