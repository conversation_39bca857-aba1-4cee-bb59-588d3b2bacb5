import { CashConfig, CashConfigExpGroupId } from './goldCoin'
import { SignInListNewRes } from './signInNew'

export type GrayScaleContextValue = {
  isNew: boolean
  showGoldCoinModule: boolean
  monthlyTicketSignInInfo: SignInListNewRes | null
  showMonthlyTicketGuide: boolean
  isOldTypeMonthlyTicket: boolean
  goldCoinABConfig: Omit<CashConfig, 'taskPackageCashId'> & {
    groupId: CashConfigExpGroupId
  }
}
