export type ActivityInfoRes = {
  code: number
  msg: string | null
  data: WelfareType[]
}

export type WelfareSlotsType = {
  dspDatas: {
    slotId: number
    dspId: number
    dspAdType: number
  }[]
}

export type WelfareType = {
  id: WelfareItemType
  name: string
  activityUrl: string
  imageUrl: string
  slotIds: null | WelfareSlotsType[]
}

export enum WelfareItemType {
  orchard = 0, // 开心果园
  pet, // 养宠物
  fiveBlessings, // 集五福
  turntable, // 福利转盘
  video //看视频
}
