/**
 * ab分组描述 当前命中的ab分组id，1-对照组，2-VIP+领现金，3-VIP碎片+领现金， 4-VIP碎片
 */
export enum CashConfigExpGroupId {
  default = '1', // 对照组
  'VIP&cash' = '2', // VIP+领现金
  'VIP&chip&cash' = '3', // VIP碎片+领现金
  'VIP&chip' = '4', // VIP碎片
}

export type CashConfig = {
  risky: boolean // 当前用户是否被风控
  taskPackageCashId: CashConfigExpGroupId // 当前命中的ab分组id
  changeTasksId: CashConfigChangeTaskId // 当前门槛ab和换一换ab命中的ab分组id，1-对照组，2-实验组
  showTutorial: boolean // 是否展示引导
}

export enum CashConfigChangeTaskId {
  default = '1',
  withAdDPTask = '2',
}
