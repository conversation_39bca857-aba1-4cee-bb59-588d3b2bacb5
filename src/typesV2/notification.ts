export enum EnumNotificationStatus {
  enable = '1',
  disable = '0',
  UNSET = '-1',
}

export type NotificationStatusRequest = {
  msg: string
  ret: number
  value: EnumNotificationStatus
}

export interface LocalPushTimeTableDataMonth {
  content: string
  icon: string
  title: string
  url: string
}

export interface LocalPushTimeTableDataTomorrow {
  content: string
  icon: string
  title: string
  url: string
}

export interface LocalPushTimeTableData {
  month: LocalPushTimeTableDataMonth
  remind: boolean
  tomorrow: LocalPushTimeTableDataTomorrow
}
