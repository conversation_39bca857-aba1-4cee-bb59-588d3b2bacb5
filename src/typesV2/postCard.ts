import {
  width as screenWidth,
  height as screenHeight,
} from '../constantsV2/dimensions'

export enum CardType {
  shareCard = 'SHARE_CARD',
  historyCard = 'HISTORY_CARD',
  screenCard = 'SCREEN_CARD',
  signInModalNew = 'SIGN_IN_MODAL_NEW',
}

const cardWithoutFooterScale = 315 / 370

const cardWithFooterScale = 275 / 420

const cardMaxHeight = 573

export const getPosterWithShareCardSize = (sharePanelHeight: number) => {
  const cardHeight = Math.min(
    (screenHeight - sharePanelHeight) * 0.73,
    cardMaxHeight
  )
  const cardWidth = cardHeight * cardWithFooterScale
  const cardWithoutFooterHeight = cardWidth / cardWithoutFooterScale
  const shouldFontScaled = cardWidth / 275

  return {
    cardHeight,
    cardWidth,
    cardWithoutFooterHeight,
    cardFooterHeight: cardHeight - cardWithoutFooterHeight,
    shouldFontScaled,
  }
}

export const historyCardWidth = screenWidth - 60
const historyCardHeight = (screenWidth - 60) / cardWithoutFooterScale

export const CardTypeData = {
  [CardType.shareCard]: {
    width: 245,
    height: 288,
    dateFont: 13.83,
    lineFont: 9,
    weekFont: 7.25,
    padding: '23px 0 0 18.7px',
    borderRadius: 8,
  },
  [CardType.historyCard]: {
    width: historyCardWidth,
    height: historyCardHeight,
    dateFont: 15.4,
    lineFont: 10,
    weekFont: 9,
    padding: '23px 0 0 25px',
    borderRadius: 8,
  },
  [CardType.screenCard]: {
    width: 212,
    height: 232,
    dateFont: 15.4,
    lineFont: 10,
    weekFont: 9,
    padding: '23px 0 0 22px',
    borderRadius: 0,
  },
  [CardType.signInModalNew]: {
    width: 275,
    height: 230,
    dateFont: 15,
    lineFont: 10,
    weekFont: 8,
    padding: '23px 0 0 22px',
    borderRadius: 0,
  },
}
