import { XMShareChannel, XMShareType } from "constantsV2/share"

export interface RNShareChannelConfig {
  type: XMShareType,
  title: string,
  desc: string,
  link: string,
  dataUrl: string,
  imgUrl: string
}

export type RNShareChannelsConfig = { [ key in XMShareChannel ]?: RNShareChannelConfig }
export type RNShareParamsConfig = { trackId?: number, albumId?: number, trackIds?: string }

export interface RNShareParams {
  channels: RNShareChannelsConfig
  params: RNShareParamsConfig
}

export enum RNShare_EVENT {
  SHOW = 'RNShare_SHOW',
  HIDE = 'RNShare_HIDE',
  ON_SHOW = 'RNShare_ON_SHOW',
  ON_HIDE = 'RNShare_ON_HIDE',
  ON_SHOW_ANIMATION_END = 'RNShare_ON_SHOW_ANIMATION_END',
  ON_HIDE_ANIMATION_END = 'RNShare_ON_HIDE_ANIMATION_END',
  ON_SHARE_SUCCESS = 'RNShare_ON_SHARE_SUCCESS',
  ON_SHARE_FAIL = 'RNShare_ON_SHARE_FAIL',
}


export interface RNShareConfig {
  channels: RNShareChannelsConfig
  params: RNShareParamsConfig
}