import { TaskItemKind } from './taskList'

export enum ExcitationType {
  sign = 'sign',
  welfare = 'welfare',
  sleep = 'sleep'
}
export enum ExcitationTaskListInfoActivityStatus {
  notStart, // 未开始
  active, // 进行中
  end // 已结束
}

export type ExcitationTaskItemAssisters = {
  logo?: string
  nickName?: string
  uid?: number
}[]

export enum ExcitationTaskStatus {
  nonValid = -1, // 非有效期内
  unfinished, // 任务未完成
  finished, // 任务已完成
  received // 奖励已领取
}

export type ExcitationTaskItemType = {
  assisters?: ExcitationTaskItemAssisters // 助力者昵称及头像
  code?: number // 业务自定义字段：1： 三餐任务
  condition?: number // 完成任务条件
  desc?: string // 任务描述
  guideLink?: string // 跳转链接
  guideText: string
  id: number // 任务id
  logo?: string
  order?: number // 任务顺序
  progress?: number // 任务当前进度：收听分钟，或者已邀请几人
  status: ExcitationTaskStatus // 任务状态
  statusText?: string // 任务当前状态显示文案
  stepNo?: number // 当前任务步骤
  tag: string // 任务tag标签如：new（新人）
  taskType: TaskItemKind
  title?: string // 任务title
  worth?: number // 任务奖励价值
  taskLabel?: string
}

export type ExcitationInfoRes = {
  activityStatus?: ExcitationTaskListInfoActivityStatus
  countDownMills?: number
  msg?: string
  taskItems?: ExcitationTaskItemType[]
}
