import { StyleProp, ViewStyle } from 'react-native'
import { AdDPTaskItem } from './adDPTask'
import { UBTMateInfoType } from './common'

/**
 * notStart 未开始
 * active 进行中
 * end 已结束
 */
export enum TaskListInfoActivityStatus {
  notStart,
  active,
  end,
}

export type TaskItemAssisters = {
  logo?: string
  nickName?: string
  uid?: number
}[]

/**
 * (-1, \"非有效期内\")(0, \"任务未完成\")\n (1, \"任务已完成\")\n (2, \"奖励已领取\") "
 */
export enum TaskStatus {
  nonValid = -1, // 非有效期内
  unfinished, // 任务未完成
  finished, // 任务已完成
  received, // 奖励已领取
  tomorrowAwardCanReceive = 7, // 明日可以领奖
}

export enum TaskItemKind {
  realTimeListenTime = 4, // 实时收听时长
  clientBehavior = 102, //客户端行为，不处理用户状态、不存记录、只跳转引流
  thirdpartyTask = 12, // 第三方任务，一般为跳转
  summary = 99, // 汇总任务
  PCListenTask = 104, // pc 收听任务
  newVideoTask = 109, // 新的看激励视频任务
  AdDPSlotTask = 110, // 广告DP 任务插槽
}

export enum StepType {
  Single = 0, // 单步任务
  Multi = 1, // 多步任务
}

// 超级任务包AB实验
export enum ActivityRuleTag {
  false = 'false', // 对照组
  true = 'true', // 实验组
  newAB = 'newAB', // 新实验组
}

export type TaskItemType = {
  UBTMateInfo: UBTMateInfoType
  assisters?: TaskItemAssisters //助力者昵称及头像
  code?: number //业务自定义字段：1： 三餐任务 , 111: 积分换任务
  condition?: number // 完成任务条件
  desc?: string //任务描述
  guideLink?: string // 跳转链接
  guideText?: string
  id: number
  logo?: string
  order?: number // 任务顺序
  progress?: number //任务当前进度：收听分钟，或者已邀请几人
  status: TaskStatus //任务状态：
  statusText?: string // 任务当前状态显示文案
  stepNo?: number //  当前任务步骤
  stepType?: StepType // 是否为多步任务
  tag?: string // 任务tag标签如：new （新人）
  taskType: TaskItemKind
  title?: string //任务title
  worth?: number
  taskLabel?: string
  subTaskType?: number
  activityLogo?: string
  stepInfos?: StepInfo[]
  statusOrderForDisplay: TaskStatusOrderForDisplay // 为了展示的任务顺序，根据任务状态映射 未领取 - 0； 未完成 - 1； 已完成 - 2；
  taskCompleteRate?: number // 进度比例，形如80，表示80%的进度
  taskCompleteCount?: number // 总共多少人完成任务
  adDpItemInfo?: AdDPTaskItem // dp 任务数据
  adDpResponseId?: number // dp 任务数据的 responseId
  contextMap?: Record<string, any> // 任务上下文信息
}

// 从任务投放渠道来 - 0； 未领取 - 1； 未完成 - 2； 已完成 - 3；
export enum TaskStatusOrderForDisplay {
  fromChannel = 0, // 从任务投放渠道来
  canReceive = 1, // 未领取
  notFinish = 2, // 未完成
  finished = 3, // 已完成
  nonValid = -1,
}

export type StepInfoMultiAwardType = {
  productId: number
  worth: number
  name: string
  cover: string
}

export type StepInfo = {
  stepNo: number
  stepStatus: number
  statusText?: string
  awardCover?: string
  condition: number
  worth: number
  desc?: string
  awards?: StepInfoMultiAwardType[]
}

export type TaskListType = TaskItemType[]

export type TaskListInfoRequestResult = {
  activityStatus?: TaskListInfoActivityStatus
  countDownMills?: number
  msg?: string
  summaryTask: TaskItemType
  taskItems?: TaskListType
}

export type TaskItemComponentProps = {
  taskItem: TaskItemType
  isLast: boolean
  containerStyle?: StyleProp<ViewStyle>
  index: number
  withoutScrollAnalyticComp?: boolean
  aid?: number
  onMount?: (item: TaskItemType) => void
  onShow?: (item: TaskItemType, index: number) => void
  onReportTaskPressEvent?: (taskItem: TaskItemType, index: number) => void
  onGetTaskAwardAction?: (options: { taskItem: TaskItemType; aid: number; stepNos?: number[] | undefined }) => Promise<void>
  shouldButtonDisabled?: (taskItem: TaskItemType) => boolean
  onRequestAid?: () => Promise<number>
  isEverydayTask?: boolean
}
