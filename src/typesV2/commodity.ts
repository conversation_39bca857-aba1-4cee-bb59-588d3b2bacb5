export enum ActivityCommodityItemStatus {
  soldOut = 0,
  notStart = 1,
  ing = 2,
  finished = 3,
  UNSET,
}

export type ActivityItem = {
  id?: number
  activityTitle: string
  exchangePrice: number
  marketPrice?: number
  stockNum?: number
  startHour?: number
  startMinute?: number
  endHour?: number
  endMinute?: number
  activityUrl: string
  orderNum: number
  activityImgUrl: string
  activityStatus: number
  activityType: number
  activityId: number
  stockAvailable: number
  startTime: number
  endTime: number
  activityStatusText?: string
  activityTypeText: string
  onlineAt: number
  offlineAt?: number
  autoOnlineAt?: number
  autoOfflineAt?: number
  channels?: any
  channelInfos?: any
  isActivity?: boolean
}

export type MiaoshaInfoActivitiesType = {
  headName: string
  activityItems: ActivityItem[]
}

export type CommodityType = {
  commodityName: string
  exchangePrice: number
  marketPrice: number
  commodityUrl: string
  orderNum: number
  isRelate?: boolean
  commodityId: number
  commodityImgUrl: string
  cornerName: string
  cornerColor: string
  cornerId?: number
  cornerImgUrl?: string
  exchangeMoney: string
  isActivity?: boolean
  startTime?: number
  endTime?: number
}

export type MiaoshaInfoCategoryType = {
  categoryName: string
  orderNum: number
  jumpLink: string
  categoryId: number
  commodities: CommodityType[]
}

export type MiaoshaInfoCategoriesType = MiaoshaInfoCategoryType[]

export type MiaoshaInfoRequestResult = {
  activities: MiaoshaInfoActivitiesType
  categories: MiaoshaInfoCategoriesType
  iconInfos: any[]
  currentTime: number
}

export type CommodityDisplayTabItem = {
  poolId: number
  name: string
  id: number
}

export type CommodityActivityItemType = {
  id: number
  bizType: any
  contentType: any
  elementType: any
  refId: number
  title: string
  cover: string
  summary: any
  preview: any
  landingPage: string
  subTitle: any
  startAt: any
  endAt: any
  activityLabel: any
  anchor: any
  status: any
  count: any
  interact: any
  wrap: any
  ext?: {
    other?: {
      tabId?: number
      exchangePrice: string | number
      exchangeMoney?: string | number
      cornerImgUrl?: string
      isActivity?: boolean
    }
  }
  surElementId: any
  createdAt: any
  updatedAt: any
  subElements: any
  ubt: any
}

export type CommodityDisplayProductItem = {
  id?: number | string
  commodityName: string // 商品名
  commodityCover?: string // 商品封面图
  exchangePrice: string | number // 兑换积分值
  exchangeMoney: string | number // 实际价格 RMB
  isActivity: boolean // 是否限时秒杀
  cornerImgUrl?: string // 商品角标
  tabId?: number // tab id
  commodityUrl: string // 商品详情页链接
  startTime?: number // 秒杀开始时间
  endTime?: number // 秒杀结束时间
}

export type CommodityDisplayProducts = CommodityDisplayProductItem[]

export type CommodityDisplayTabs = CommodityDisplayTabItem[]

export type CommodityBannerItem = {
  cover: string
  link: string
  hidden?: boolean
  order?: number
}
