import { Source } from '@xmly/rn-components'
import { ImageSourcePropType } from 'react-native'

export type Theme = 'light' | 'dark'

export interface ThemeItem {
  light: ThemeStyle
  dark: ThemeStyle
}

export interface Common {
  bg_color: string
  item_bg_color: string
  title_color: string
  title_color_new: string
  sub_title_color: string
  no_more_tip_color: string
  placeholder_bg_color: string
}
export interface Icon {
  icon_circle_arrow: ImageSourcePropType & (number | Source)
  bg_credit_detail: ImageSourcePropType & (number | Source)
  icon_wait_gift: ImageSourcePropType & (number | Source)
}
export interface Container {
  bg_color: string
  bg_image: string
  height: number
}

export interface Header {
  color: string
}
export interface Credit {
  overlay_warp_bg_color: string
  checkbar_overlay_warp_bg_color: string
  bg_color: string
  line_color: string
  number_color: string
  label_color: string
  desc_color: string
  icon_poster_history_entry: ImageSourcePropType & (number | Source)
}
export interface NotificationBar {
  color: string
  bg_color: string
  normalColor: string
}
export interface CheckInBar {
  bg_color: string
  new_bg_color: string
  new_bg_color_checked: string
  sub_title_color: string
  not_received_bg_color: string
  tomorrow_bg_color: string
  common_label_color: string
  notification_switch_label: string
}

export interface GroundList {
  name_color: string
  title_color: string
  btn_left_bg: string
  btn_left_color: string
  btn_left_color_top: string
  btn_left_color_bottom: string
  btn_right_bg: string
  btn_right_color: string
  bg_right_color_top: string
  bg_right_color_bottom: string
}

export interface Categories {
  tabBar: {
    label_color: string
    label_active_color: string
    backgroundColor: string
  }
  commodity: {
    countdown_wrapper_bg: string
  }
}

export interface Task {
  browseTask: {
    wrapper_bg: string
    label_color: string
  }
  taskList: {
    top_border_color: string
  }
  processBar: {
    bg_color: string
  }
  check_more_button: string
  item: {
    title_color: string
    sub_title_color: string
    actionButton: {
      can_receive_bg: string
      not_finish_bg: string
      finished_bg: string
      can_receive_label_color: string
      can_receive_label_color_v2: string
      not_finish_label_color: string
      not_finish_label_color_v2: string
      finished_label_color: string
      finished_label_color_v2: string
      can_receive_border_color: string
      not_finish_border_color: string
      finished_border_color: string

      // new add
      bg_start_color_not_finish: string
      bg_end_color_not_finish: string
      bg_start_color_can_receive: string
      bg_end_color_can_receive: string
      bg_start_color_finished: string
      bg_end_color_finished: string
    }
  }
  multiStepTask: {
    progressBarBG: string
  }
}

export interface CheckInPop {
  gift_wrapper_bg: string
  gift_wrapper_border_color: string
  notification_check_button_label: string
  wrapper_bg_color: string
  left_group_btn_label: string
  left_group_btn_border: string
  title_color: string
}

export interface PosterHistory {
  bg_color: string
  border_color: string
  color: string
}

export interface CreditDetail {
  bg_start_color: string
  bg_end_color: string
}

export interface LoginButton {
  label_color: string
  border_color: string
  button_label_color: string
}

export interface ErrorView {
  label_color: string
  button_label_color: string
  button_border_color: string
  error_icon: ImageSourcePropType & (number | Source)
}

export interface PcDialog {
  bg_img: string
  bg_color: string
  title_color: string
  sit_title: string
  sub_title_color: string
  sub_title_opacity: number
}
export interface ThemeStyle {
  common: Common
  container: Container
  header: Header
  icon: Icon
  credit: Credit
  notificationBar: NotificationBar
  checkInBar: CheckInBar
  groundList: GroundList
  categories: Categories
  task: Task
  posterHistory: PosterHistory
  creditDetail: CreditDetail
  checkInPop: CheckInPop
  loginButton: LoginButton
  errorView: ErrorView
  pcDialog: PcDialog
  everydayChallenge: EverydayChallenge
}

export interface EverydayChallenge {
  taskListModal: {
    bg: string
    closeButtonIconTintColor: string
    sectionItemBg: string
    progress: {
      title: string
      subTitle: string
    }
    // new add
    bg_start_color: string
    bg_end_color: string
    text: string
    bg_list_start_color: string
    bg_list_end_color: string
  }
  progress: {
    trackBG: string
  }
  summaryTaskItem: {
    label: string
  }
  // new add
  summaryTaskList: {
    bg_start_color: string
    bg_end_color: string
    bg_button: string
    text: string
    bg_item: string
    sub_text: string
    bg_progress: string
    bg_progress_bar: string
  }
}
