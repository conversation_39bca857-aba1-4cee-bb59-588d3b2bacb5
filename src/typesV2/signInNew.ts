import { TaskStatus } from './taskList'

export type SignInListNewRes = {
  title: string
  desc: string
  activityId: number
  isTurnOn: boolean
  signInUserInfo: SignInUserInfo
  signInUserAwards: SignInUserAwardType[]
  context: Context
}

export type SignInUserAwardType = {
  day: number
  productId: number
  name: string
  desc: string
  isReal: boolean
  awardStatus: AwardStatus
  context?: CheckInInfo
  logo: string
  unsignedLogo: string
  isPlaceholder?: boolean
}

type CheckInTask = {
  aid: number
  taskId: number
  buttonLabel: string
}

// 今日签到数据（签到面板下的文字和去领取按钮链接）
export type CheckInInfo = Partial<{
  maskText: string
  maskLogo: string
  link: string
  btnText: string
  btnTextForAB?: string
  isGift: boolean
  posterMaskDescTitle?: string
  btnAnimation?: boolean
  awards?: CheckInInfoAwardItem[]
  receiveAwardPop?: BigGiftDescModalInfo
  targetActivityId?: number
  targetTaskId?: number
  chooseAwards?: TodaySignInChooseAwardType[]
  videoTask?: CheckInTask
  videoTaskV2?: CheckInTask
  doubleAwardTask?: CheckInTask
  xmRequestId: string
}>

export type SignInUserInfo = {
  roundId: number
  period: number
  continueDay: number
  totalDay: number
  signInStatus: SignInStatus // 打卡状态
  lastSignInTs: number
  todaySignInDay: number
  manualEndTime: number
  patternType: SignInPatternType
}

export enum SignInPatternType {
  amount = 1, // 累计打卡
  continuous = 0, // 连续打卡
}

export enum SignInStatus {
  isDisabled, // 不能打卡
  isEnabled, // 可以打卡
  isSignedIn, // 已打卡
}

export enum AwardStatus {
  initial, // 初始
  isEnabled, // 可领取
  isDispatched, // 已发放
}

export type CheckInInfoAwardItem = {
  icon: string
  label: string
  doubleAllowed?: boolean
}

export type TodaySignInChooseAwardType = {
  icon: string
  label: string
  productId: number
  type?: number // 如果是24 则是需要去使用的类型，默认没有这个字段
  guideLink?: string // 需要主动领取操作的奖品跳转链接
  guideImg?: string // 需要主动领取操作的说明图片链接
}

// 签到多个奖励配置详情
export type SignInMultiAwardsDetail = {
  targetActivityId: number
  targetTaskId: number
  status: TaskStatus
  targetDay: number // 对应的签到天数
  awards: TodaySignInChooseAwardType[]
}

export type BigGiftDescModalInfo = {
  title: string
  subTitle: string
  buttenText: string
  logo: string
}

// 完成收听任务弹窗信息
export type SuccessPopInfo = Partial<{
  totalTime: number
  totalWorth: number
}>

export type SignInAwardType = {
  day: number
  name: string
  desc: string
  logo: string
  isReal: boolean
  context: CheckInInfo
}

export enum SignInNewResCodeType {
  success = 0,
  notStart = -1,
  isSigned = -2,
  canNotSign = -3,
  finished = -4,
  awardFailed = -5,
  clientError = -400,
  concurrentRequest = -200, // 并发用户请求
  serviceExceptions = -500, // 服务异常
  // 0-> 打卡成功, -1 -> 未开启打卡, -2 -> 已打卡, -3 -> 不满足打卡条件,
  // -4 -> 已打满, -5 -> 奖励下发失败, -200 -> 并发请求, -400 -> 参数错误, -500 -> 服务器异常
}

export type SignInNewRes = {
  code: SignInNewResCodeType
  msg: string
  continueDay: number
  totalDay: number
  roundPeriod: number
  dayAward: SignInAwardType
  orderData?: {
    randomQuantity: number
  }
}

export type Context = {
  activityContext: {
    ruleParseInfo: {
      tag: string
    }
  }
}

export enum EnumRequestGiftPackageReceiveStatus {
  notReceived,
  received,
}

export enum SignInGiftReceiveStatus {
  UNSET = -1,
  notReceived,
  received,
  outOfNumber,
  unavailable,
}

/**
 * 0 新设备， 1召回设备， 2活跃设备
 */
export enum UserIDTag {
  UNSET = '-99',
  newDevice = '0',
  recalledDevice = '1',
  activeDevice = '2',
}

export enum ABRuleTag {
  true = 'true',
  false = 'false',
}

export enum EnumVIPTag {
  noVip = 'noVip',
  vip = 'vip',
}
