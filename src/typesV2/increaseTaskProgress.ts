export type IncreaseTaskProgressRes = {
  status: IncreaseTaskProgressStatus // 任务进度刷新结果
  awardCount: number // 完成任务奖励个数：如20积分，30音符
}

export enum IncreaseTaskProgressStatus {
  RefreshTaskSucceeded = 0, // 刷新任务成功
  TaskFinished = 1, // 任务已完成
  TaskNotValidPeriod = 2, // 任务不在有效时间段内
  ServiceExceptions = 500, // 服务异常
  InvalidParameter = 400, // 无效参数
  HelpIdIsEmpty = -2, // 助力ID为空
  HelpIdIsHelped = -3, // 助力ID已经助力
  TokenFailure = -4 // token失效
}
