export interface ECommerceProduct {
  /**
   * Id
   */
  id: number
  /**
   * 商品Id
   */
  productId: string
  /**
   * 商品池Id
   */
  poolId: number
  /**
   *商品名称
   */
  productName: string
  /**
   *兑换积分
   */
  exchangePoint: number

  /**
   *商品原价 RMB
   */
  payAmount: number
  /**
   * 市场价 (用不上)
   */
  marketPrice: number
  /**
   * 商详页跳转链接
   */
  clickUrl: string
  /**
   *商品封面图
   */
  coverUrl: string
  /**
   *秒杀开始日期
   */
  startDate: string
  /**
   *秒杀结束日期
   */
  endDate: string
  /**
   *秒杀开始时间
   */
  startTime: string
  /**
   *秒杀结束时间
   */
  endTime: string
  /**
   *商品根分类Id
   */
  rootCategoryId: number
  /**
   *排序
   */
  sort: number
  /**
   * 是否是会员专项
   */
  vip: boolean
  /**
   * 前端展示标签
   */
  showTags: string[]
  /**
   * 角标图片链接
   */
  cornerImgUrl?: string
  /**
   * 是否是秒杀
   */
  isSkill?: boolean
}

/**
 *状态 1删除 0关闭 2待上线 3 上线
 *新建 状态为0
 *删除 状态为1
 *修改 关闭状态修改状态为0，上线修改，状态为2
 *上线 状态为3
 */
export enum ECommerceComponentState {
  close,
  deleted,
  readyToGo,
  online,
}

export interface ECommerceComponent {
  /**
   * 组件Id
   */
  id: number
  /**
   * 组件名称
   */
  name: string
  /**
   * 组件类型
   */
  type: string
  /**
   * 多个子table组件信息
   */
  childComponents: ECommerceComponent[]
  /**
   * 商品列表信息
   */
  productVos: ECommerceProduct[]
  /**
   * 页面Id
   */
  pageId: number
  /**
   * 商品池Id
   */
  poolId: number
  /**
   *状态 1删除 0关闭 2待上线 3 上线
   *新建 状态为0
   *删除 状态为1
   *修改 关闭状态修改状态为0，上线修改，状态为2
   *上线 状态为3
   */
  state: ECommerceComponentState
  /**
   *排序
   */
  sort: string

  /**
   * 分页的ID
   */
  pageNo: number

  /**
   * 是否有更多
   */
  hasMore: boolean
}

export type ECommerceComponentVos = {
  id: number
  name: string
  type: string
  childComponents: null
  productVos: null
  componentImgs: null
  secKillStartTimeMillis: null
  secKillEndTimeMillis: null
  isShow: null
  pageId: null
  poolId: number
  state: null
  sort: number
  hasMore: boolean
  pageNo: null
}
