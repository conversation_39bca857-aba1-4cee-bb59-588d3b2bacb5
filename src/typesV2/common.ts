import { ChannelUndertakeModalStatus } from './channelModal'
import { NativeInfo } from './nativeInfo'

export interface Env {
  envType: 'online' | 'uat' | 'test' | 'dev'
}

export enum EnvName {
  test = 'test',
  uat = 'uat',
  prod = 'prod',
}

export type AppRootContentPropsType = NativeInfo & {
  isLogin: boolean
  showNoLoginCheckInPop: boolean
  isDarkMode: boolean
  withMonthlyTicket: boolean
  modalStatus: ChannelUndertakeModalStatus
}

export enum FetchStatus {
  loading,
  success,
  fail,
}

export type UBTMateInfoType = {
  xmRequestId: string
  contentId: string
  contentType: string
}
