export type NativeInfo = {
  channelTaskId?: string
  bundle: string
  tab?: string
  rootTag: number
  initData: NativeInfoInitData
  drawCoin?: string // 来自播放页的领积分入口，进入后需自动领积分，并领取成功弹窗
  srcChannel?: string // 来源
  channelName?: string // 第三方名称
  task?: string // 第三方任务id
  token?: string // 第三方token
  pcTask?: string // pc任务弹窗
  route?: string // 指定路由
  toTaskId?: string // 定位到指定任务
  action?: string // 指定行为
  toCashTask?: boolean // 定位到现金任务
  embed?: string // 是否嵌套
  coin?: string // 是否是金币中心
}

export type NativeInfoInitData = {
  account: { isLogin: boolean; uid?: number; isVip?: boolean }
  apiVersion: string
  bundleVersion: string
  env: NativeInfoInitDataEnv
  isChildProtectOpen?: boolean
  isDarkMode?: boolean // 是否暗黑模式
  isNewInstall?: boolean
  isNewVersion?: boolean
}

export type NativeInfoInitDataEnv = {
  envType: number
  supportFullScreen?: boolean
}
