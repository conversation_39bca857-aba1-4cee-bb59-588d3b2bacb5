export type SignInRecord = {
  awardStatus: AwardStatus // 打卡奖励领取状态
  context: string
  dayNo: number
  logo: string // 奖励logo
  name: string // 奖励名
  productId: number // 奖励商品id
  signInStatus: SignInStatus // 打卡状态
  signInTS: number // 打卡时间戳
}

export type SignInListRes = {
  isSignInToday: boolean // 今天是否打卡
  signInRecords: SignInRecord[]
  todaySignInDay: number // 今天需要打哪天
  uid: number
}

export enum SignInStatus {
  isDisabled, // 不能打卡
  isEnabled, // 可以打卡
  isSignedIn // 已打卡
}

export enum AwardStatus {
  initial, // 初始
  isEnabled, // 可领取
  isDispatched // 已发放
}
