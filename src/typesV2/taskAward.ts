import { TaskStatus } from './taskList'

export enum TaskAwardStatus {
  unfinished = -1, // 任务未完成
  finished = 0, // 成功
  received = 1, // 已领取
  exception = -2, // 领取异常
}

export type TaskAwardRes = {
  taskStatus: TaskStatus // 任务状态
  awardCount: number // 领取奖励数量，如音符数
  awardValue: number // 领取奖励数量，如音符数
  msg: string
  status: TaskAwardStatus // 领取状态
  statusText: string // 任务状态对应文案
  stepNo: number // 任务当前步骤
}
