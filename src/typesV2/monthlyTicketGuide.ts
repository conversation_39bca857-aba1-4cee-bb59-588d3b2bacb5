import { LayoutRectangle } from 'react-native'

export enum MonthlyTicketGuideStep {
  Modal = 'Modal',
  NormalA = 'NormalA',
  NormalB = 'NormalB',
  NormalC = 'NormalC',
  VoteModalA = 'VoteModalA',
  VoteModalB = 'VoteModalB',
  VoteModalC = 'VoteModalC',
}

export enum MonthlyTicketGuideStatus {
  UNSET,
  need,
  noNeed,
}

export type MonthlyTicketGuideContextValue = {
  hasShowMonthlyTicketGuide: boolean
  setHasShowMonthlyTicketGuide: (has: boolean) => void
  layoutInfo: { [key: string]: LayoutRectangle & { radius: number } }
  setLayoutInfo: (
    name: MonthlyTicketGuideStep | MonthlyTicketGuideStep[],
    info: LayoutRectangle & { radius: number }
  ) => void
  setMonthlyTicketIconLayoutInfo: (info: LayoutRectangle) => void
  guideStatus: MonthlyTicketGuideStatus
  monthlyTicketIconLayoutInfo: LayoutRectangle | null
  setGuideStatus: (status: MonthlyTicketGuideStatus) => void
}
