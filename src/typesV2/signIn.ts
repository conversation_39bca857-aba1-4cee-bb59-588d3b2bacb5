export enum AwardOrderType {
  Album = 1, // 专辑
  Audio = 2, // 声音
  Vip = 3, // 会员
  Coupons = 4, // 优惠券
  Xidian = 5, // 喜点
  Score = 6, // 积分
  GiftBag = 7, // 礼包
  PhysicalProduction = 100, // 实物商品
  CustomNote = 10101, // 自定义：音符
  CustomUnlockDressUp = 10102 // 自定义：解锁装扮
}

export enum SignInStatus {
  SignInSucceeded = 0, // 打卡成功
  isDone = 1, // 已打卡
  isFull = 2, // 已打满
  ConfigUndefined = -1, // 打卡轮次配置未找到
  ConcurrentRequest = 100, // 并发用户请求
  ServiceExceptions = 500 // 服务异常
}

export type SignInRes = {
  awardOrderType: AwardOrderType
  customProductCode: number
  desc: string
  msg: string
  productId: number // 奖励关联商品id
  productQuantity: number // 奖励商品数据： eg: 音符个数， 积分个数
  status: SignInStatus
  uid: number
}
