import React, { useEffect } from 'react';
import { View } from 'react-native';
import { getStyles } from './styles';
import Coins from '../../components/CoinDetail/Coins';
import Tabs from '../../components/CoinDetail/Tabs';
import TopBg from '../../components/CoinDetail/TopBg';
import GlobalEventEmitter from 'utilsV2/globalEventEmitter';
import Header from '../../components/CoinDetail/Header';
import { useSetAtom, useAtomValue } from 'jotai';
import { updateCoinDetailAtom } from '../../components/CoinDetail/atom';
import coinDetailThemeAtom from './theme';
import Banner from '../../components/CoinDetail/Transaction';
import { TransactionType } from 'constants/ad';
import { usePageReport } from 'hooks/usePageReport';
import { RootStackParamList } from 'router/type';
import { StackScreenProps } from '@react-navigation/stack';
import log from 'utils/log';

export default function CoinDetail(props: StackScreenProps<RootStackParamList>) {
  const theme = useAtomValue(coinDetailThemeAtom);
  const styles = getStyles(theme);
  const updateCoinDetail = useSetAtom(updateCoinDetailAtom);
  log('debug__CoinDetail_props', props);
  usePageReport({
    pageViewCode: 44982,
    pageExitCode: 44983,
    currPage: 'point_exchange',
    params: {
      from: '福利中心',
    },
    otherProps: props,
  });

  useEffect(() => {
    GlobalEventEmitter.emit('appContentReady');
  }, []);

  useEffect(() => {
    updateCoinDetail({ type: TransactionType.INCOME, refresh: true });
  }, []);

  return (
    <View style={styles.container}>
      <Header />
      <TopBg />
      <Coins />
      <Banner />
      <Tabs />
    </View>
  );
} 