import { StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.bgColor,
  },
  headerBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: px(375),
  },
  content: {
    backgroundColor: theme.contentBgColor,
    paddingVertical: px(16),
  },
}); 