import { atom } from 'jotai';
import { themeAtom } from 'atom/theme';

export const darkTheme = {
  backgroundColor: '#131313',
  bgColor: '#131313',
  contentBgColor: 'rgba(255, 255, 255, .05)',
  gradientColors: [
    '#1F2327',
    'rgba(31, 35, 39, 0.04)',
    'rgba(248, 165, 163, 0.24)',
    'rgba(252, 102, 97, 0.25)'
  ],
  gradientLocations: [0, 0.13, 0.63, 1],
  headerBg: 'https://imagev2.xmcdn.com/storages/be65-audiofreehighqps/A3/19/GKwRIMALsXGzAAAU6gOCGWVb.jpg',
  splitLine: 'https://imagev2.xmcdn.com/storages/e4fa-audiofreehighqps/54/35/GAqh1QQLwpPkAAADLgONWO9t.png'
};

export const lightTheme = {
  backgroundColor: '#F3F7FA',
  bgColor: '#F3F7FA',
  contentBgColor: 'rgba(255, 255, 255, .75)',
  gradientColors: [
    '#F3F7FA',
    'rgba(243, 244, 245, 0.04)',
    'rgba(248, 165, 163, 0.48)',
    'rgba(252, 102, 97, 0.49)'
  ],
  gradientLocations: [0, 0.13, 0.63, 1],
  headerBg: 'https://imagev2.xmcdn.com/storages/521b-audiofreehighqps/C4/10/GAqhfD0LsCbfAAAPTQOBbY9g.png',
  splitLine: 'https://imagev2.xmcdn.com/storages/9117-audiofreehighqps/40/4C/GAqhntALwpPeAAADKQONWOgI.png'
};

export const coinDetailThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default coinDetailThemeAtom; 