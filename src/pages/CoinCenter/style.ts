import { Platform, StyleSheet } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.bgColor,
  },
  headerBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: px(375),
  },
  splitLine: {
    width: '100%',
    aspectRatio: 343 / 9,
    // marginTop: Platform.OS === 'ios' ? px(-1) : 0,
    marginTop: -1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: px(16),
    paddingTop: px(17),
    paddingBottom: px(24),
  },
  content: {
    // borderColor: 'red',
    // borderWidth: 1,
    // borderStyle: 'dashed',
    // borderRadius: px(4),
    // backgroundColor: theme.contentBgColor,
    // paddingVertical: px(16),
  }
});