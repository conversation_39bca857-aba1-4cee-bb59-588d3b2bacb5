import React from "react";
import { View } from "react-native";
import { BetterImage, Text, Touch } from "@xmly/rn-components";
import { getStyles } from "./style";
import { Page } from "@xmly/rn-sdk";
import { themeAtom } from "atom/theme";
import { useAtomValue } from "jotai";

export const themeStyles = {
  light: {
    color: "#131313",
  },
  dark: {
    color: "#DCDCDC",
  },
}

export default function Wallet() {
  const theme = useAtomValue(themeAtom);
  const styles = getStyles(themeStyles[theme]);

  function open() {
    Page.start('iting://open?msg_type=94&bundle=rn_credit_center&srcChannel=wallet')
  }

  return (
    <View>
      <View>
        <Text style={styles.title}>领现金福利</Text>
      </View>
      <Touch onPress={open} style={styles.banner}>
        <BetterImage
          source={{ uri: 'https://imagev2.xmcdn.com/storages/810a-audiofreehighqps/E8/3B/GKwRIJEK0AcwAAPoYgMXzkNQ.png' }}
          imgWidth={343}
          imgHeight={193}
          style={styles.bannerPic}
          resizeMode="cover"
        />
      </Touch>
    </View>
  )
}