import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import { EnumRequestGiftPackageReceiveStatus } from '../typesV2/signInNew'
import request from './request'

export default async function ({
  giftPackageIds,
  dateList,
}: {
  giftPackageIds: number[]
  dateList: string[]
}) {
  const data = {
    giftPackageIds,
    dateList,
  }
  return request<{
    [key: string]: { [key: string]: EnumRequestGiftPackageReceiveStatus }
  }>({
    ...API_DEFAULT,
    url: Api.giftPackageReceiveStatus,
    option: {
      method: 'post',
      data: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
      catchJsonParse: true,
    },
    tip: false,
  })
}
