import { Api, API_OPEN_API } from 'constantsV2/apiConfig'
import { FinishThirdpartyTaskParams } from '../typesV2/finishThirdpartyTaskType'
import request from './request'

// 获取通用配置接口
export default function finishThirdpartyTask(
  data: FinishThirdpartyTaskParams
): Promise<any> {
  return request({
    ...API_OPEN_API,
    url: Api.finishThirdpartyTask,
    option: {
      method: 'post',
      data: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
      catchJsonParse: true,
    },
    tip: false,
  }) as any
}
