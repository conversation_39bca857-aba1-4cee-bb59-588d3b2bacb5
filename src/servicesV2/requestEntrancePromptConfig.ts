import { EntrancePromptConfigType } from '../typesV2/entrancePromptConfigType'
import getCommonConfig from './getCommonConfig'
import { ResDataType } from './request'

// 获取通用配置接口
export default function requestEntrancePromptConfig(): Promise<
  ResDataType<EntrancePromptConfigType>
> {
  return getCommonConfig(
    {
      app: 'web',
      group: 'new-hybrid',
      key: 'entrancePrompt',
    },
    true
  )
}
