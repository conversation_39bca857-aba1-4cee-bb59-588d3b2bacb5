import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import request from './request'

/**
 * 1-普通地址，2-兑吧地址
 */
export enum TopSectionUrlType {
  normal = 1,
  duiba,
  navigationJump,
}

export type TopSectionNotification = {
  text: string
  startTime: number
  endTime: number
  type: TopSectionUrlType
  jumpUrl: string
  btnText?: string
}

export type TopSectionGourds = {
  iconUrl: string
  jumpUrl: string
  title: string
  type: TopSectionUrlType
}
export type IndexSectionGourds = {
  iconUrl: string
  jumpUrl: string
  title: string
  name: string
  button: string
  type: TopSectionUrlType
  navigationUrl?: string
}

export type TopSectionBanners = {
  picUrl: string
  jumpUrl: string
  type: TopSectionUrlType
}

type TopSectionInfoRes = {
  notification: TopSectionNotification[]
  gourds: TopSectionGourds[]
  banners: TopSectionBanners[]
}

//通知 糖葫芦 轮播图
export default function getTopSectionInfo(
  tabName: 'earn' | 'spend' | 'global'
) {
  return request<TopSectionInfoRes>({
    ...API_DEFAULT,
    url: Api.topSectionInfo(tabName),
    tip: false,
  })
}
