import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import request from './request'
import { IncreaseTaskProgressRes } from '../typesV2/increaseTaskProgress'

//  更新任务列表中视频播放的进度 v2
export function increaseTaskListItemProgressV2({
  token,
  sign,
  aid,
  taskId,
}: {
  token: String
  sign: String
  aid: number
  taskId: number
}) {
  return request<IncreaseTaskProgressRes>({
    ...API_DEFAULT,
    url: Api.taskProgressV2,
    option: {
      data: JSON.stringify({ aid, taskId, token, progress: 1, sign }),
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    tip: false,
  })
}
