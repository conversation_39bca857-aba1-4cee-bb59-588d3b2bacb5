import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import request from './request'
import { SignInNewRes } from '../typesV2/signInNew'

// 签到奖励多选一
export default async function requestChooseTaskAward({
  ticket,
  ...rest
}: {
  productId: number[]
  taskId: number
  aid: number
  stepNo?: number
  ticket?: string
}) {
  const headers = {
    'Content-Type': 'application/json',
    'x-tk': ticket,
  }

  return request<SignInNewRes>({
    ...API_DEFAULT,
    url: Api.chooseTaskAward,
    option: {
      method: 'post',
      data: JSON.stringify(rest),
      headers,
    },
    tip: false,
  })
}
