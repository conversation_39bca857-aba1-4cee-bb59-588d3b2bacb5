import { isIOS } from '@xmly/rn-utils'
import { Api, API_ADSE } from 'constantsV2/apiConfig'
import { NativeModules, Platform } from 'react-native'
import userInfoDetail from '../modulesV2/userInfoDetail'
import { AdDPTaskListRequestResult } from '../typesV2/adDPTask'
import adDPTaskCtrl from '../utilsV2/adDPTask'
import request, { ResDataType } from './request'
import uuid from 'utilsV2/uuid'
import { encryptByType, getXuidTicket } from 'utilsV2/native'
import { XUID_ticketConfig } from 'constantsV2'
import { TaskStatus } from 'typesV2/taskList'

interface AdDPTaskListRequestData {
  positionId: number
  version?: string
  xt: string
  name: string
  appid: string
  device: 'android' | 'iphone'
  uid: number
  extInfo?: {
    sourceName?: string
  }
}

export default async function requestAdDPTaskList(sourceName?: string) {
  const deviceInfo = await NativeModules.RNDeviceInfo.getDeviceInfo()
  const ts = Date.now()
  const name = 'points_center_task_banner'
  const data: AdDPTaskListRequestData = {
    positionId: 278,
    version: deviceInfo?.appVersion || undefined,
    xt: String(ts),
    name,
    appid: '0',
    device: Platform.OS === 'android' ? 'android' : 'iphone',
    uid: userInfoDetail.getDetail().uid ?? 0,
  }

  if (sourceName) {
    data.extInfo = { sourceName }
  }

  const defaultHeaders = {
    'Content-Type': 'application/json',
  }

  const iOSCookies = await adDPTaskCtrl.getIosRequestCookies()
  // 这里请求广告接口的时候，需要获取广告特定的 cookie，塞到请求里
  const headers = isIOS && iOSCookies && iOSCookies?.Cookie ? {
    ...iOSCookies,
    ...defaultHeaders,
  } : defaultHeaders

  return request({
    ...API_ADSE,
    url: Api.adDPTaskList({ ts, name }),
    option: {
      method: 'post',
      data: JSON.stringify(data),
      headers,
    },
    tip: false,
  }) as unknown as Promise<AdDPTaskListRequestResult>
}

interface CheckAdAppTaskStatusParams {
  adId: number;
  responseId: number;
  openedTimestamp: number;
}

export const checkAdAppTaskStatus = async (params: CheckAdAppTaskStatusParams): Promise<ResDataType<{ status: TaskStatus }> | undefined> => {

  return request({
    ...API_ADSE,
    url: Api.checkAdAppTaskStatus({ ts: Date.now() }),
    option: {
      method: 'post',
      data: JSON.stringify({ ...params, requestId: uuid() }),
      headers: {
        'Content-Type': 'application/json',
      },
    },
    tip: false,
  })
}

interface AdAppClickReportParams {
  adId: number;
  responseId: number;
  jump3s?: boolean;
  installed?: boolean;
  type: 'click' | 'result';
}

export function adAppClickReport(params: AdAppClickReportParams) {

  return request({
    ...API_ADSE,
    url: Api.highPriceTaskClickReport({ ts: Date.now() }),
    option: {
      method: 'post',
      data: JSON.stringify({ ...params, requestId: uuid() }),
      headers: {
        'Content-Type': 'application/json',
      },
    }
  })
}

export enum AdAppTaskAwardType {
  New = 1,
  Activate = 2,
}

interface RewardAdAppTaskStatusParams {
  adId: string | number;
  responseId: number;
  openedTimestamp?: number; // deprecated
  encryptType: number;
  adxRtbSettlementPrice: string;
  awardType: AdAppTaskAwardType;
}

interface RewardAdAppTaskStatusResult {
  success?: boolean;
  toast?: string;
}

export const rewardAdAppTask = async (params: RewardAdAppTaskStatusParams): Promise<ResDataType<RewardAdAppTaskStatusResult> | undefined> => {
  const {
    adId,
    encryptType,
    adxRtbSettlementPrice,
  } = params;
  const requestId = uuid();
  const ts = Date.now();
  const ticket = await getXuidTicket({
    businessId: XUID_ticketConfig.cashTask.businessId,
    scene: XUID_ticketConfig.cashTask.scene,
    uid: userInfoDetail.getDetail().uid || -1,
  })
  const deviceInfo = await NativeModules.RNDeviceInfo.getDeviceInfo()
  const { checkData: signature } = await encryptByType('md5', {
    checkData: `${requestId}&${adId}&${deviceInfo?.uuid}&${ticket}&${adxRtbSettlementPrice}&${encryptType}&${ts}&0ab3443b71834b33be675af744d76ef0aaa74cd2b60c4b67`
  })
  const data = {
    ...params,
    ticket,
    requestId,
    ts,
    signature,
  }
  return request({
    ...API_ADSE,
    url: Api.rewardAdAppTask({ ts }),
    option: {
      method: 'post',
      data: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
    },
    tip: false,
  })
}
