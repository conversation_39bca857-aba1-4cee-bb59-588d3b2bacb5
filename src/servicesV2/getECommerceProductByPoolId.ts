import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import { ECommerceComponent } from '../typesV2/ECommerce'
import request from './request'

// 获取电商部的
export default function getECommerceProductByPoolId(
  poolId: number,
  componentId: number,
  pageNo?: number
) {
  return request<ECommerceComponent>({
    ...API_DEFAULT,
    url: Api.eCommerceProductListByPoolId(poolId, componentId, pageNo),
    tip: false,
  })
}
