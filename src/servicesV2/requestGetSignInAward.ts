import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import request from './request'

export interface Pop {
  activityId: number
  taskId: number
  deviceTraits: any[]
  limitTime: number
  limitCount: number
  limitTimeUnit: string
  toast: string
  order: number
}

export interface Data {
  code: number
  msg: string
  awardValue: number
  pop: Pop
}

// 签到接口
export default async function requestGetSignInAward(options: { aid: number; day: number; ticket: string }) {
  const domain = API_DEFAULT
  const headers = {
    'Content-Type': 'application/json',
    'x-tk': options.ticket || undefined,
  }

  return request<Data>({
    ...domain,
    url: Api.getSignInAward,
    option: {
      method: 'post',
      data: JSON.stringify(options),
      headers,
    },
    tip: false,
  })
}
