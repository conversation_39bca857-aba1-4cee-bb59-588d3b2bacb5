/**
 * 统一请求接口
 */
import { NativeModules } from 'react-native'
import SentryUtils from 'utilsV2/sentryUtils'
import getDomainEnvSync from '../utilsV2/getEnvSync'
import xmlog from '../utilsV2/xmlog'
import userInfoDetail from '../modulesV2/userInfoDetail'

const Http = NativeModules.Http
export type ResDataType<T> = {
  msg: string
  ret: number
  responseId?: number
  data: T
}
interface Option {
  data?: any
  method?: 'post' | 'get'
  headers?: any
  catchJsonParse?: boolean
  dataType?: 'json' | 'form'
}

const request: <T>(url: string, option: Option) => Promise<ResDataType<T>> = (url: string, option: Option) => {
  const data = option.data || {}
  let body: any = []
  for (let key in data) {
    body.push(`${key}=${data[key]}`)
  }
  body = body.join('&')
  console.log('请求地址', url)
  return new Promise((resolve, reject) => {
    let requestResult = ''
    if (Http) {
      if (option.method && option.method.toLowerCase() === 'post') {
        Http.post({
          url,
          data,
          dataType: option.dataType || 'json',
          header: option.headers || {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        })
          .then((result: any) => {
            requestResult = result
            if (option.catchJsonParse) {
              try {
                resolve(JSON.parse(result))
              } catch (err) {
                console.log('request.post.then', err)
                reject(err)
                SentryUtils.captureException(err, {
                  source: 'request.post.then' + result + data,
                })
              }
            } else {
              resolve(JSON.parse(result))
            }
          })
          .catch((err: any) => {
            SentryUtils.captureException(err, {
              source: 'request.Http.post.catch',
              option,
              requestResult,
            })
            // 任务中心问题排查埋点  其他事件
            xmlog.event(50052, 'others', {
              description: 'request.Http.post.catch' + url,
              Item: requestResult,
            })
            reject(err)
          })
      } else {
        Http.get({ url, header: option.headers || undefined })
          .then((result: any) => {
            requestResult = result
            resolve(JSON.parse(result))
          })
          .catch((err: any) => {
            SentryUtils.captureException(err, {
              source: 'request.Http.get.catch',
              option,
              requestResult,
            })
            // 任务中心问题排查埋点  其他事件
            xmlog.event(50052, 'others', {
              description: 'request.Http.get.catch' + url,
              Item: requestResult,
            })
            reject(err)
          })
      }
    } else {
      let fetchOption = {}
      if (option.method && option.method.toLowerCase() === 'post') {
        fetchOption = {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          ...option,
          method: 'POST',
          body,
        }
      }
      fetch(url, fetchOption)
        .then((response) => {
          return response.json().then((json) => {
            return json
          })
        })
        .then((data) => {
          resolve(data)
        })
        .catch((err) => {
          SentryUtils.captureException(err, {
            source: 'request.fetch.catch',
            option,
          })
          reject(err)
        })
        .finally()
    }
  })
}

interface RequestParam {
  test: string
  uat: string
  prod: string
  protocol?: string
  url: string
  mock?: string
  option?: Option
  run_mock?: boolean
  tip?: boolean
  headers?: any
}
/**
 * 接受对象
 * test 测试域名，也可以添加协议
 * prod 生产域名
 * protocol 协议
 * url 请求地址的pathname
 * mock 如果有mock数据地址，开发阶段会请求，如果mock地址数据请求失败，那么会去尝试请求测试地址
 */
export default async <T>({ test, uat, prod, protocol = 'https', url, mock, option = {}, tip = false, run_mock = false }: RequestParam) => {
  const realTimeLogin = userInfoDetail.getDetail()?.isLogin
  console.log('realTimeLogin ===>', realTimeLogin)
  if (!realTimeLogin && !url.includes('web-config/')) {
    console.log('🚨 🚨 请求被拦截，因为未登录，且不是 web-config 的接口')
    return
  }
  const domain = getDomainEnvSync(test, uat, prod)
  if (/^http/.test(domain)) {
    url = `${domain}${url}`
  } else {
    url = `${protocol}://${domain}${url}`
  }
  try {
    return await new Promise<ResDataType<T>>(async (resolve, reject) => {
      let data: ResDataType<T>
      try {
        if (__DEV__ && (!Http || run_mock)) {
          // 使用 mock数据
          try {
            if (mock && run_mock) {
              data = await request(mock, option)
            } else {
              throw 'mock null'
            }
          } catch (error) {
            // mock数据获取失败，尝试获取开发环境数据
            try {
              data = await request(url, option)
            } catch (error) {
              throw error
            }
          }
        } else {
          try {
            data = await request(url, option)
          } catch (error) {
            throw error
          }
        }
        resolve(data)
      } catch (err) {
        try {
          if (__DEV__) {
            console.log('网络异常>>>>', url)
          }
        } catch (error) {}
        reject(err)
      }
    })
  } catch (error) {
    throw error
  }
}
