import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import { Product } from '../typesV2/product'
import request from './request'

export enum Ret {
  Success = 0, // 请求成功
  NotLogin = 50, // 用户未登录,请用户先登录
  SystemException = -1 // 系统异常,展示错误信息
}

interface AchieveMillstoneRes {
  ret: Ret
  msg: string
  hasAward: boolean // 是否达到里程碑
  product: Product
}

// 达到里程碑
// 文档：http://gitlab.ximalaya.com/x-fm/xfm-integral-mall-activity/wikis/%E6%98%AF%E5%90%A6%E8%BE%BE%E5%88%B0%E9%87%8C%E7%A8%8B%E7%A2%91
export default function achieveMillstone (): Promise<AchieveMillstoneRes> {
  return request({
    ...API_DEFAULT,
    url: Api.achieveMillstone,
    tip: false
  }) as any
}
