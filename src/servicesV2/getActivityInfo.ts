import { Api, API_AD } from 'constantsV2/apiConfig'
import { ActivityInfoRes } from '../typesV2/activityInfo'

import request from './request'

import { isAndroid } from '@xmly/rn-utils/dist/device'
import { XMAppVersionHelper } from '@xmly/rn-sdk'
import { CAN_OPEN_XMLY_SCHEME } from 'constantsV2'

// 开心游戏列表
export default function getActivityInfo (
  idfa: string,
  uid: number,
  deviceId: string,
  appVersion: string
): Promise<ActivityInfoRes> {
  let _appVersion = appVersion
  if (isAndroid) {
    _appVersion = XMAppVersionHelper.notLowerThanSync(appVersion, CAN_OPEN_XMLY_SCHEME) ? appVersion : '' // 如果是安卓且版本号大于等于8.3.12 则传真是的version，否则传字符串
  }
  const url_suffix = `deviceId=${ deviceId }&uid=${ uid }`.concat(
    isAndroid ? `&os=android&osVersion=${ _appVersion }&apiVersion=rn` : `&os=ios&idfa=${ idfa }&osVersion=${ _appVersion }&apiVersion=rn`
  )

  return request({
    ...API_AD,
    url: Api.getActivityInfo(url_suffix),
    tip: false
  }) as any
}
