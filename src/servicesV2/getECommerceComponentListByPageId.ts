import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import { ECommerceComponentVos } from '../typesV2/ECommerce'
import request from './request'

export default function getECommerceComponentListByPageId(pageId: number) {
  return request<{
    id: number
    name: null | string
    type: null
    channel: null
    state: null
    componentVos: ECommerceComponentVos[]
  }>({
    ...API_DEFAULT,
    url: Api.eCommerceComponentListById(pageId),
    tip: false,
  })
}
