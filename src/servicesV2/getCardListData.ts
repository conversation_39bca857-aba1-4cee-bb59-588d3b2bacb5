import {Api, API_DEFAULT} from 'constantsV2/apiConfig';
import request from './request';
import { CardListDataType } from 'typesV2/cardListData';
import userInfoDetail from '../modulesV2/userInfoDetail'

export default function getCardListData() {
  const isLogin = userInfoDetail.getDetail()?.isLogin
  return request({
    ...API_DEFAULT,
    url: isLogin ? Api.getCardListData : Api.getCardListDataNotLogIn,
    tip: false
  }) as unknown as Promise<CardListDataType>;
}