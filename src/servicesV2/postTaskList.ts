import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import { TaskListInfoRequestResult } from 'typesV2/taskList'
import { taskListAid } from '../constantsV2'
import request, { ResDataType } from './request'
import userInfoDetail from '../modulesV2/userInfoDetail'


//任务列表收听任务...
export default async function postTaskList(aid?: number, useV2?: boolean) {
  const isLogin = userInfoDetail.getDetail()?.isLogin
  
  const mAid = typeof aid === 'number' ? aid : taskListAid
  const data = {
    aid: mAid,
  }

  return request<TaskListInfoRequestResult>({
    ...API_DEFAULT,
    url: useV2 ? Api.postTaskListV2 : (isLogin ? Api.postTaskListNew : Api.postTaskListNewNotLogin),
    option: isLogin ? {
      method: 'post',
      data: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
      catchJsonParse: true,
    } : {
      method: 'get'
    },
    tip: false,
  }) as Promise<
    ResDataType<TaskListInfoRequestResult> & {
      context: {
        activityContext: {
          activityBasicInfo: {
            extraInfo: {
              videoTaskInfo: {
                positionId: number
                positionName: string
              }
            }
          }
        }
      }
    }
  >
}
