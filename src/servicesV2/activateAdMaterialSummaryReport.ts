import { Api, API_DEFAULT } from 'constantsV2/apiConfig';
import request from './request';

interface ActivateAdMaterialSummaryReportParams {
  totalCount: number // 从广告获取的素材数量
  filteredCount: number // 过滤后的数量
  filledCount: number // 填充的数量
}

export default function activateAdMaterialSummaryReport(data: ActivateAdMaterialSummaryReportParams) {
  return request({
    ...API_DEFAULT,
    url: Api.activateAdMaterialSummaryReport,
    option: {
      method: 'post',
      data:JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      }
    }
  });
}