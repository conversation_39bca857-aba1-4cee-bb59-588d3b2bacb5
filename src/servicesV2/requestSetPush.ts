import { Api, API_M, mobilePushSettingConfig } from '../constantsV2/apiConfig'
import { EnumNotificationStatus } from '../typesV2/notification'
import generateSig from '../utilsV2/generateSig'
import request from './request'
import requestGenerateNonce from './requestGenerateNonce'

const errorObj = (enable: boolean) => ({
  msg: enable ? '开启失败' : '关闭失败',
  ret: -1,
})

const getCommonParams = (enable: boolean, useEncrypt?: boolean) => ({
  value: enable
    ? EnumNotificationStatus.enable
    : EnumNotificationStatus.disable,
  toId: useEncrypt
    ? mobilePushSettingConfig.toId
    : mobilePushSettingConfig.toId + '',
  businessType: useEncrypt
    ? mobilePushSettingConfig.businessType
    : mobilePushSettingConfig.businessType + '',
})

const getEncryptData = async (enable: boolean) => {
  try {
    const nonceRes = await requestGenerateNonce()

    const originParams = {
      nonce: nonceRes.data,
      ...getCommonParams(enable, true),
    }

    const sig = await generateSig(originParams, {
      needSecurityKey: true,
      needUpperCase: true,
    })

    if (!sig) {
      return errorObj
    }

    const data = {
      ...originParams,
      signature: sig,
    }
    return data
  } catch (err) {
    return null
  }
}

const requestSetPush = async (options: {
  enable: boolean
  useEncrypt?: boolean
}) => {
  const { useEncrypt, enable } = options
  try {
    const url = useEncrypt ? Api.setPushStatus : Api.setPushStatusNoEncrypt

    const data = useEncrypt
      ? await getEncryptData(enable)
      : getCommonParams(enable)

    if (!data) return errorObj(enable)

    return request<any>({
      ...API_M,
      url,
      option: {
        dataType: 'form',
        method: 'post',
        data: useEncrypt ? JSON.stringify(data) : data,
        headers: useEncrypt
          ? {
              'Content-Type': 'application/json',
            }
          : undefined,
        catchJsonParse: true,
      },
      tip: false,
    }) as unknown as Promise<{ meg: string; ret: number }>
  } catch (err) {
    console.log(err)
    return errorObj(enable)
  }
}

export default requestSetPush
