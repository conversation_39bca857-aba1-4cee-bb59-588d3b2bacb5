import { Api, API_DEFAULT } from '../constantsV2/apiConfig'
import request from './request'

let isNewAndRecallUser: boolean

const requestSignUserType = async () => {
  if (typeof isNewAndRecallUser === 'boolean') {
    return isNewAndRecallUser
  }
  try {
    const result = await request<{ newAndRecall: boolean }>({
      ...API_DEFAULT,
      url: Api.getSignUserType,
      tip: false,
    })

    isNewAndRecallUser = result.data.newAndRecall
  } catch (err) {
    isNewAndRecallUser = false
  } finally {
    return isNewAndRecallUser
  }
}

export default requestSignUserType
