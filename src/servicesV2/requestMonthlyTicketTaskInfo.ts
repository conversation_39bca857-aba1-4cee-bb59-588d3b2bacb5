import { Api, API_DEFAULT, monthlyTicketAID } from 'constantsV2/apiConfig'
import { TaskListInfoRequestResult } from 'typesV2/taskList'
import request from './request'

export default function requestMonthlyTicketTaskInfo() {
  const data = {
    aid: monthlyTicketAID(),
  }
  return request<TaskListInfoRequestResult>({
    ...API_DEFAULT,
    url: Api.monthlyTicketTaskInfo,
    option: {
      method: 'post',
      data: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
      },
      catchJsonParse: true,
    },
    tip: false,
  })
}
