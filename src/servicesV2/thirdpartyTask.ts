import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import { taskListAid } from '../constantsV2'
import request from './request'

interface GuideLinkRequest {
  h5Link: string
  schemaLink: string
  taskId: number
}
export default async function getThirdpartyTask(taskId: number, aid: number) {
  const mAid = typeof aid === 'number' ? aid : taskListAid
  return request<GuideLinkRequest>({
    ...API_DEFAULT,
    url: `${Api.thirdpartyTask}?aid=${mAid}&taskId=${taskId}`,
  })
}
