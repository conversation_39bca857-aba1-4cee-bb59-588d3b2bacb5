import { excitationTaskAid, excitationWelfareTaskId } from '../constantsV2'
import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import request from './request'
import { ExcitationInfoRes, ExcitationType } from 'typesV2/excitation'

/**
 *
 * @param ExcitationType type
 * @returns
 * @description 看视频任务进度信息
 */
export default async function getExcitationInfo (type: ExcitationType) {
  const taskId =
    type === ExcitationType.sign ? await excitationTaskAid() : await excitationWelfareTaskId()
  return request<ExcitationInfoRes>({
    ...API_DEFAULT,
    url: Api.excitationInfo(await excitationTaskAid(), taskId),
    tip: false
  })
}
