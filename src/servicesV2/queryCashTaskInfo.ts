import { Api, API_ADSE } from 'constantsV2/apiConfig'
import request from './request'
import { getXuidTicket } from 'utilsV2/native'
import { XUID_ticketConfig } from 'constantsV2'
import userInfoDetail from 'modulesV2/userInfoDetail'
import { TaskInfo } from 'components/CashTask/store/task'
import throttle from 'utilsV2/throttle'

// 获取现金任务信息
export default async function queryCashTaskInfo() {
  const ticket = await getXuidTicket({
    businessId: XUID_ticketConfig.cashTask.businessId,
    scene: XUID_ticketConfig.cashTask.scene,
    uid: userInfoDetail.getDetail().uid || -1,
  })
  console.info('debug_queryCashTaskInfo', {
    businessId: XUID_ticketConfig.cashTask.businessId,
    scene: XUID_ticketConfig.cashTask.scene,
    uid: userInfoDetail.getDetail().uid || -1,
    ticket
  });
  return request<TaskInfo>({
    ...API_ADSE,
    url: Api.queryCashTaskInfo({ ts: Date.now(), ticket }),
  })
}

type CarveUpCashAwardResponse = {
  success: boolean;
  toast: string;
  amount: string;
}

async function carveUpCashAward() {
  return request<CarveUpCashAwardResponse>({
    ...API_ADSE,
    option: {
      method: 'post',
      data: JSON.stringify({}),
      headers: {
        'Content-Type': 'application/json'
      }
    },
    url: Api.carveUpCashAward({ ts: Date.now() }),
  })
}

export const throttleCarveUpCashAward = throttle(carveUpCashAward, 700)