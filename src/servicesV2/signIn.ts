import { signAid } from '../constantsV2'
import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import request from './request'
import { SignInRes } from '../typesV2/signIn'

// 签到接口
export default async function signIn () {
  return request<SignInRes>({
    ...API_DEFAULT,
    url: Api.signIn(await signAid()),
    option: {
      method: 'post',
      data: JSON.stringify({ aid: (await signAid()) }),
      headers: {
        'Content-Type': 'application/json'
      }
    },
    tip: false
  })
}
