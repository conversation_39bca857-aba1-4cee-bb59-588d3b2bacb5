import { taskListAid } from '../constantsV2'
import { Api, API_HYBRID } from 'constantsV2/apiConfig'
import request from './request'

// 领取奖励
export default function refreshClientTaskV2(
  { taskId }: { taskId: number },
  aid: number
) {
  const mAid = typeof aid === 'number' ? aid : taskListAid

  return request<{}>({
    ...API_HYBRID,
    url: Api.refreshClientTaskV2,
    tip: false,
    option: {
      method: 'post',
      data: JSON.stringify({
        aid: mAid,
        taskId,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    },
  })
}
