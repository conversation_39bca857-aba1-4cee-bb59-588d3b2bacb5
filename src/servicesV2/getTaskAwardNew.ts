import { taskListAid } from '../constantsV2'
import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import request from './request'
import { TaskAwardRes } from '../typesV2/taskAward'

//任务列表领取任务奖励
export default function getTaskAwardNew(
  taskInfo: {
    taskId: number
    stepNos?: any[]
  },
  aid: number
) {
  const mAid = typeof aid === 'number' ? aid : taskListAid
  const { taskId, stepNos } = taskInfo
  const data: any = { aid: mAid, taskId }

  if (stepNos) {
    data.stepNos = stepNos
  }
  return request<TaskAwardRes>({
    ...API_DEFAULT,
    url: Api.postAwardNew,
    option: {
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: JSON.stringify(data),
    },
    tip: false,
  })
}
