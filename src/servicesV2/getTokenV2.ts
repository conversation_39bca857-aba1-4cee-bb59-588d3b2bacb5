import { API_DEFAULT, Api } from 'constantsV2/apiConfig'
import { taskListAid } from '../constantsV2'
import { login } from '../utilsV2/native'
import request from './request'

export type TokenRes = {
  token: string
}

// 获取token
export default async function getTokenV2(taskId: number, aid: number) {
  const mAid = typeof aid === 'number' ? aid : taskListAid
  
  return request<TokenRes>({
    ...API_DEFAULT,
    url: Api.excitationTokenV2,
    option: {
      method: 'post',
      data: JSON.stringify({
        aid: mAid,
        taskId,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    },
  }).then((res) => {
    const { ret, data } = res
    if (ret === 0) {
      return data.token
    } else if (ret === 303) {
      login()
      return null
    }
    return null
  })
}
