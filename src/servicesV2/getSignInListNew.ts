import { Api, API_DEFAULT } from 'constantsV2/apiConfig'
import request from './request'
import { SignInListNewRes } from '../typesV2/signInNew'
import userInfoDetail from '../modulesV2/userInfoDetail'

// 签到列表接口
export default async function getSignInListNew(option?: { isMonthlyTicket: boolean; aid?: number; ticket?: string }) {
  const ticketHeader = option?.ticket
    ? {
        'x-tk': option.ticket,
      }
    : undefined

  const domain = API_DEFAULT
  const isLogin = userInfoDetail.getDetail()?.isLogin
  return request<SignInListNewRes>({
    ...domain,
    url: !option?.isMonthlyTicket ? (isLogin ? Api.signInListNew(option?.aid) : Api.signInListNewNotLogin) : Api.queryMonthlyTicketSignInInfo(),
    tip: false,
    option: {
      headers: ticketHeader,
    },
  })
}
