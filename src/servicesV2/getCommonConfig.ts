import { API_DEFAULT, Api } from 'constantsV2/apiConfig'
import request from './request'


// 获取通用配置接口
export default function getCommonConfig (query?: { [ key: string ]: any }, queryAll?: boolean): Promise<any> {
  const queryString = require('query-string')
  return request({
    ...API_DEFAULT,
    url: `${ Api.commonConfig(queryAll) }?${ queryString.stringify(query) }`,
    tip: false
  }) as any
}
