import { API_DEFAULT, Api } from '../constantsV2/apiConfig'
import { PointExchangeRes } from '../typesV2/pointExchange'
import request from './request'

export function pointExchange({
  activityId,
  taskId,
  checkTaskId,
}: {
  activityId: number
  taskId: number
  checkTaskId: number
}) {
  return request<PointExchangeRes>({
    ...API_DEFAULT,
    url: Api.pointExchangeV2,
    option: {
      data: JSON.stringify({ activityId, taskId, checkTaskId }),
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
    },
    tip: false,
  })
}
