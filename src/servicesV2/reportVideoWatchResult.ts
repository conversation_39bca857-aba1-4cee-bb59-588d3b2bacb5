import { Api, API_AD } from 'constantsV2/apiConfig'
import request from './request'

// 看完视频领积分
export default function reportVideoWatchResult ({
  id,
  name,
  deviceId,
  uid
}: {
  id: number
  name: string
  deviceId: string
  uid: number
}) {
  const url_suffix = `id=${id}&name=${name}&deviceId=${deviceId}&uid=${uid}`
  return request({
    ...API_AD,
    url: Api.reportVideoWatchResult(url_suffix),
    tip: true
  })
}
