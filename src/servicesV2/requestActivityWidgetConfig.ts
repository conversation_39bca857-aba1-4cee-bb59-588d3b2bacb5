import { ActivityWidgetConfigType } from '../componentsV2/ActivityWidget/typesv2'
import getCommonConfig from './getCommonConfig'
import { ResDataType } from './request'

// 获取通用配置接口
export default function requestActivityWidgetConfig(): Promise<
  ResDataType<ActivityWidgetConfigType>
> {
  return getCommonConfig(
    {
      app: 'web',
      group: 'new-hybrid',
      key: 'activityWidgetConfig',
    },
    true
  )
}
