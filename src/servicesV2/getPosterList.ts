import { Api, API_M } from 'constantsV2/apiConfig'
import request from './request'
import { PosterListRes } from '../typesV2/posterListType'


// 日签海报列表
export default async function getPosterList ({ page, pageSize, dateType }: { page: number, pageSize: number, dateType: 1 | 2 }) {
  return request<PosterListRes>({
    ...API_M,
    url: `${ Api.posterList }${ Date.now() }?pageId=${ page }&pageSize=${ pageSize }&dateType=${ dateType }`,
    tip: false
  })
}
