import { Api, API_DEFAULT, monthlyTicketAID, newSignInAID } from 'constantsV2/apiConfig'
import request from './request'
import { SignInNewRes } from '../typesV2/signInNew'

// 签到接口
export default async function signInNew(options?: { isMonthlyTicket: boolean; aid?: number }) {
  const isMonthlyTicket = options?.isMonthlyTicket
  const domain = API_DEFAULT
  return request<SignInNewRes>({
    ...domain,
    url: Api.signInNew,
    option: {
      method: 'post',
      data: JSON.stringify({
        aid: typeof options?.aid === 'number' ? options.aid : !isMonthlyTicket ? newSignInAID : monthlyTicketAID(),
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    },
    tip: false,
  })
}
