import { createModel } from '@rematch/core'
import { RootModel } from '.'


export type ThirdpartyTaskModelState = {
  isExist: boolean
}

const defaultThirdpartyTaskModelState: ThirdpartyTaskModelState = {
  isExist: false
}

export const thirdpartyTask = createModel<RootModel>()({
  state: defaultThirdpartyTaskModelState, // initial state
  reducers: {
    reset: () => {
      return defaultThirdpartyTaskModelState
    },
    refresh: (state, payload: Partial<ThirdpartyTaskModelState>) => {
      return {
        ...state,
        ...payload
      }
    }
  },
  effects: dispatch => ({
    async judgeTaskExist ({ channelName, task, token }: { channelName?: string, token?: string, task?: string }) {
      try {
        dispatch.thirdpartyTask.refresh({ isExist: !!(channelName && task && token) })
      } catch (err) {
        dispatch.thirdpartyTask.refresh({ isExist: false })
      }
    }
  })
})
