// 签到
import { createModel } from '@rematch/core'
import { RootModel } from '.'
import requestMonthlyTicketInfo from '../servicesV2/requestMonthlyTicketInfo'
import requestMonthlyTicketTaskInfo from '../servicesV2/requestMonthlyTicketTaskInfo'
import {
  MonthlyTicketActivityContext,
  MonthlyTickInfo,
} from '../typesV2/monthlyTicket'
import { TaskItemType } from '../typesV2/taskList'
import lodashGet from 'lodash.get'

export type MonthlyTicketModelState = {
  monthlyTicketInfo: Partial<MonthlyTickInfo>
  monthlyTicketTaskInfo: TaskItemType | null
  monthlyTicketActivityContext: MonthlyTicketActivityContext
}

const defaultState: MonthlyTicketModelState = {
  monthlyTicketInfo: {},
  monthlyTicketTaskInfo: null,
  monthlyTicketActivityContext: {
    monthlyTicketTitle: '月票',
    monthlyTicketIcon:
      'https://imagev2.xmcdn.com/storages/ddef-audiofreehighqps/90/1C/GKwRIJIHAIlKAAAvoQGkxg_Y.png',
    monthlyTicketModalBtnText: '去给作品投票',
    monthlyTicketModalBtnLink: 'iting://open?msg_type=365',
  },
}

export const monthlyTicket = createModel<RootModel>()({
  state: defaultState,
  reducers: {
    refresh: (state, payload: Partial<MonthlyTicketModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
    reset: () => {
      return defaultState
    },
  },
  effects: (dispatch) => ({
    async addMonthlyTicket(payload: number, rootState) {
      dispatch.monthlyTicket.refresh({
        monthlyTicketInfo: {
          ...rootState.monthlyTicket.monthlyTicketInfo,
          availableTicket:
            (rootState.monthlyTicket.monthlyTicketInfo.availableTicket || 0) +
            payload,
        },
      })
    },
    async getMonthlyTicketInfo() {
      try {
        const res = await requestMonthlyTicketInfo()
        if (res.ret === 0 && res.data) {
          dispatch.monthlyTicket.refresh({ monthlyTicketInfo: res.data })
        }
      } catch (err) {
        console.log(err)
      }
    },
    async getMonthlyTicketProgress() {
      try {
        const taskRes = await requestMonthlyTicketTaskInfo()
        const monthlyTicketActivityContext = lodashGet(
          taskRes,
          'activityContext.activityBasicInfo.extraInfo',
          defaultState.monthlyTicketActivityContext
        )
        const taskItems =
          taskRes.ret === 0 && taskRes.data && taskRes.data.taskItems
            ? taskRes.data.taskItems
            : []
        if (taskItems[0]) {
          dispatch.monthlyTicket.refresh({
            monthlyTicketTaskInfo: taskItems[0],
            monthlyTicketActivityContext,
          })
        }
      } catch (err) {
        console.log(err)
      }
    },
    async refreshMonthlyTicketData({
      withMonthlyTicket,
    }: {
      withMonthlyTicket?: boolean
    }) {
      if (withMonthlyTicket) {
        await dispatch.monthlyTicket.getMonthlyTicketProgress()
      }
      dispatch.monthlyTicket.getMonthlyTicketInfo()
    },
  }),
})
