import { createModel } from '@rematch/core'
import { RootModel } from '.'
import { taskListAid } from '../constantsV2'
import { TaskItemType, TaskStatus } from '../typesV2/taskList'
import getEverydayChallengeAidByAB from '../utilsV2/getEverydayChallengeAidByAB'

export type TaskAwardModalModelState = {
  visible: boolean
  awardWorth: number
  currentTaskId: number
  recommendAwardLabel: string
  recommendTask?: TaskItemType | null
  recommendTaskAid: number
  currentAwardIcon?: string
  currentAwardLabel?: string
}

const defaultTaskAwardModalModelState: TaskAwardModalModelState = {
  visible: false,
  awardWorth: 0,
  currentTaskId: -1,
  recommendAwardLabel: '',
  currentAwardLabel: '',
  currentAwardIcon: '',
  recommendTaskAid: -1,
}

export const taskAwardModal = createModel<RootModel>()({
  state: defaultTaskAwardModalModelState, // initial state
  reducers: {
    reset: () => {
      return defaultTaskAwardModalModelState
    },
    refresh: (state, payload: Partial<TaskAwardModalModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async openModal(
      {
        currentTaskId,
        awardWorth,
        recommendTaskId,
        recommendAwardLabel,
        recommendTaskAid,
        currentAwardIcon,
        currentAwardLabel,
      }: {
        awardWorth: number
        currentTaskId: number
        recommendTaskId: number
        recommendTaskAid: number
        recommendAwardLabel: string
        currentAwardIcon?: string
        currentAwardLabel?: string
      },
      rootState
    ) {
      let taskList = []
      if (recommendTaskAid === taskListAid) {
        taskList = rootState.taskCenter.taskList
      } else if (recommendTaskAid === (await getEverydayChallengeAidByAB())) {
        taskList = rootState.everydayChallenge.taskList
      } else {
        taskList = rootState.taskCenter.taskList
      }

      const taskItem = taskList.find((task) => task.id === recommendTaskId)
      if (taskItem && taskItem.status === TaskStatus.unfinished) {
        console.log({ currentAwardIcon, currentAwardLabel })
        dispatch.taskAwardModal.refresh({
          visible: true,
          awardWorth,
          currentTaskId,
          recommendAwardLabel,
          recommendTask: taskItem,
          recommendTaskAid,
          currentAwardIcon,
          currentAwardLabel,
        })
      }
    },
  }),
})
