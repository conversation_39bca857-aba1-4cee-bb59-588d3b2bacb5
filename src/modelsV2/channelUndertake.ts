import { createModel } from '@rematch/core'
import dayjs from 'dayjs'
import { RootModel } from '.'
import {
  ChannelModalConfigType,
  ChannelUndertakeModalStatus,
} from '../typesV2/channelModal'
import getChannelModalABTest from '../utilsV2/getChannelModalABTest'
import channelModalConfigStorage from '../storageV2/channelModalConfigStorage'
import userChannelModalStatusStorage from '../storageV2/userChannelModalStatusStorage'
import getChannelModalConfig from '../servicesV2/getChannelModalConfig'
import { Toast } from '@xmly/rn-sdk'

export type ChannelUndertakeModelState = {
  /** 弹窗是否需要打开的状态 */
  modalStatus: ChannelUndertakeModalStatus
  /** 需展示的弹窗信息 */
  channelModalConfigInfo: ChannelModalConfigType | null
  modalVisible: boolean
}

const defaultChannelUndertakeModelState: ChannelUndertakeModelState = {
  modalStatus: ChannelUndertakeModalStatus.UNSET,
  channelModalConfigInfo: null,
  modalVisible: false,
}

export const channelUndertake = createModel<RootModel>()({
  state: defaultChannelUndertakeModelState,
  reducers: {
    refresh: (state, payload: Partial<ChannelUndertakeModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async init(srcChannel: string) {
      try {
        // 获取ABtest值
        const channelModalAB = await getChannelModalABTest()
        if (channelModalAB === '1') {
          // 对照组
          __DEV__ && Toast.info('对照组，不展示弹窗')
          dispatch.channelUndertake.refresh({
            modalStatus: ChannelUndertakeModalStatus.noNeed,
          })
        } else {
          // 实验组
          await dispatch.channelUndertake.fetchChannelConfig(srcChannel)
          // 请求配置数据，保存到本地
          dispatch.channelUndertake.fetchAndSaveNewConfig()
        }
      } catch (error) {
        __DEV__ && Toast.info('报错了，不展示弹窗--')
        dispatch.channelUndertake.refresh({
          modalStatus: ChannelUndertakeModalStatus.noNeed,
        })
      }
    },

    async fetchChannelConfig(srcChannel: string) {
      try {
        // 判断本地是否有保存渠道弹窗配置
        let channelModalConfig: ChannelModalConfigType[] = []
        const channelModalConfigLocal = await channelModalConfigStorage.get()

        if (channelModalConfigLocal.length > 0) {
          // 本地有保存渠道配置
          console.log('本地有保存渠道配置')
          channelModalConfig = channelModalConfigLocal
        } else {
          // 本地没有保存渠道弹窗配置
          const { data, ret } = await getChannelModalConfig()
          if (ret === 0 && data.length > 0) {
            channelModalConfig = data
            // 把配置数据存到本地
            channelModalConfigStorage.set(channelModalConfig)
          }
        }

        const channelModalConfigFound = channelModalConfig.find(
          (item) => item.channel === srcChannel
        )
        if (!channelModalConfigFound) {
          __DEV__ && Toast.info('没找到该渠道配置，不展示弹窗')
          dispatch.channelUndertake.refresh({
            modalStatus: ChannelUndertakeModalStatus.noNeed,
          })
        } else {
          // 判断频次是否符合需要弹出的逻辑
          const lastChannelModalShowDate =
            await userChannelModalStatusStorage.get(srcChannel as string)
          const hasFrequencyCtrl = !lastChannelModalShowDate
            ? false // 本地存储时间为空则没有频次控制
            : dayjs().diff(lastChannelModalShowDate, 'day') <
              channelModalConfigFound.expiresDays // 本地时间与当前时间小于配置的天数则处于频次控制
          const visible = !hasFrequencyCtrl
          if (visible) {
            __DEV__ && Toast.info('展示承接弹窗')
            dispatch.channelUndertake.refresh({
              channelModalConfigInfo: channelModalConfigFound,
              modalVisible: true,
              modalStatus: ChannelUndertakeModalStatus.need,
            })
            // 本地存储当次的时间
            userChannelModalStatusStorage.set(srcChannel as string)
          } else {
            __DEV__ &&
              Toast.info(
                `上次：${lastChannelModalShowDate}, diff:${dayjs().diff(
                  lastChannelModalShowDate,
                  'day'
                )}, 频次被控制，不展示弹窗`
              )
            dispatch.channelUndertake.refresh({
              modalStatus: ChannelUndertakeModalStatus.noNeed,
            })
          }
        }
      } catch (error) {
        console.log(error)
        __DEV__ && Toast.info('报错了，不展示弹窗')
        dispatch.channelUndertake.refresh({
          modalStatus: ChannelUndertakeModalStatus.noNeed,
        })
      }
    },

    // 请求配置数据，保存到本地
    async fetchAndSaveNewConfig() {
      const { data, ret } = await getChannelModalConfig()
      if (ret === 0) {
        // 把配置数据存到本地
        channelModalConfigStorage.set(data)
      }
    },
  }),
})
