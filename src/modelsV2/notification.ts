import { createModel } from '@rematch/core'
import { Toast } from '@xmly/rn-sdk'
import dayjs from 'dayjs'
import { RootModel } from '.'
import { checkPushPermission } from '../modulesV2/pushPermission'
import requestPushStatus from '../servicesV2/requestPushStatus'
import requestSetPush from '../servicesV2/requestSetPush'
import notificationDisableConfirmStorage from '../storageV2/notificationDisableConfirmStorage'
import { EnumNotificationStatus } from '../typesV2/notification'
import { TaskItemKind, TaskStatus } from '../typesV2/taskList'
import openPushPermission from '../utilsV2/openPushPermission'
import refreshTaskAndRefreshList from '../utilsV2/refreshTaskAndRefreshList'
import RNAlert from '../componentsV2/Confirm'
import {
  cancelPush,
  fetchLocalPushTimeTable,
  genLocalPushTimeTable,
  setSinglePush,
} from '../modulesV2/localPush'
import { taskListAid } from '../constantsV2'
import xmlog from '../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'

export type NotificationModelState = {
  status: EnumNotificationStatus
}

const defaultNotificationModelState: NotificationModelState = {
  status: EnumNotificationStatus.UNSET,
}

const disableConfirm = () => {
  return new Promise<boolean>((resolve) => {
    RNAlert({
      message: '关闭后，不再为你发送签到提醒，这将增加断签风险',
      actions: [
        {
          text: '取消',
          onPress: () => resolve(false),
          primary: true,
        },
        {
          text: '关闭提醒',
          onPress: () => resolve(true),
        },
      ],
    })
  })
}

export const notification = createModel<RootModel>()({
  state: defaultNotificationModelState, // initial state
  reducers: {
    reset: () => {
      return defaultNotificationModelState
    },
    refresh: (state, payload: Partial<NotificationModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async toggleNotification(
      {
        enable,
        openSettingConfirm,
        onStatusChanged,
        confirmBody,
      }: {
        enable: boolean
        openSettingConfirm?: boolean
        onStatusChanged?: (status: boolean) => void
        confirmBody?: (text: string) => any
      },
      rootState
    ) {
      // 如果是关闭通知操作，展示确认关闭弹窗
      if (!enable) {
        const lastDate = await notificationDisableConfirmStorage.get()
        const isSameDay = dayjs().isSame(lastDate, 'day')
        if (!isSameDay) {
          notificationDisableConfirmStorage.set()
        }
        const confirm = isSameDay ? true : await disableConfirm()
        if (confirm) {
          dispatch.notification.setNotificationStatus({
            enable,
            onStatusChanged,
          })
        }
      } else {
        const { taskList } = rootState.taskCenter
        const openPushTask = taskList.find(
          (task) =>
            task.taskType === TaskItemKind.clientBehavior &&
            task.code === 3 &&
            task.status === TaskStatus.unfinished
        )

        // 获取推送权限
        await openPushPermission({
          onPermissionGranted: () => {
            dispatch.notification.setNotificationStatus({
              enable,
              onStatusChanged,
            })
            if (openPushTask) {
              refreshTaskAndRefreshList(openPushTask.id, taskListAid)
            }
          },
          onPermissionRejected() {
            Toast.info('权限开启失败')
          },
          onPermissionFailed() {
            Toast.info('权限开启失败')
          },
          confirmOpenSetting:
            typeof openSettingConfirm === 'boolean' ? openSettingConfirm : true,
          confirmOpenSettingTitle: '打开消息通知',
          confirmBody:
            typeof confirmBody === 'function'
              ? confirmBody(
                  openPushTask ? '开启通知，可得200积分' : '开启提醒，不怕断签'
                )
              : null,
        })
      }
    },
    async setNotificationStatus({
      enable,
      onStatusChanged,
    }: {
      enable: boolean
      onStatusChanged?: (status: boolean) => void
    }) {
      try {
        const res = await requestSetPush({
          enable,
          useEncrypt: false,
        })

        if (res.ret === 0) {
          Toast.info(enable ? '每日10点提醒签到，连签奖励跑不掉' : '关闭成功')
          dispatch.notification.refresh({
            status: enable
              ? EnumNotificationStatus.enable
              : EnumNotificationStatus.disable,
          })
          if (enable) {
            // 判断是否需要添加本地push
            dispatch.notification.judgeSetLocalPush()
          } else {
            // 取消本地push
            dispatch.notification.cancelLocalPush()
          }
          typeof onStatusChanged === 'function' && onStatusChanged(enable)
        } else {
          Toast.info(enable ? '开启失败' : '关闭失败')
        }
      } catch (err) {
        Toast.info(enable ? '开启失败' : '关闭失败')
      }
    },
    async fetchNotificationStatus() {
      try {
        dispatch.notification.closeLocalPush()
        const [res, pushPermission] = await Promise.all([
          requestPushStatus() as any,
          checkPushPermission(),
        ])
        if (res.ret === 0) {
          const notificationStatus =
            pushPermission === 'granted'
              ? res.value
              : EnumNotificationStatus.disable

          dispatch.notification.refresh({
            status: notificationStatus,
          })
          if (notificationStatus === EnumNotificationStatus.enable) {
            // 判断是否需要添加本地push
            dispatch.notification.judgeSetLocalPush()
          }

          if (notificationStatus === EnumNotificationStatus.disable) {
            // 取消本地push
            dispatch.notification.cancelLocalPush()
          }
        } else {
          throw new Error('')
        }
      } catch (err) {
        dispatch.notification.refresh({
          status: EnumNotificationStatus.disable,
        })
        dispatch.notification.cancelLocalPush()
      }
    },
    async closeLocalPush() {
      try {
        const timeTableInfo = await fetchLocalPushTimeTable()
        if (!timeTableInfo || (timeTableInfo && !timeTableInfo.remind)) {
          await dispatch.notification.cancelLocalPush()
        }
      } catch (err) {
        console.log(err)
      }
    },
    async judgeSetLocalPush() {
      try {
        const timeTableInfo = await fetchLocalPushTimeTable()
        if (timeTableInfo && timeTableInfo.remind) {
          const timeTable = genLocalPushTimeTable(
            timeTableInfo.tomorrow,
            timeTableInfo.month
          )
          await dispatch.notification.cancelLocalPush()
          setTimeout(() => {
            timeTable.forEach((time) => {
              setSinglePush({ ...time })
            })
          }, 2000)
        }
      } catch (err) {
        console.log(err)
      }
    },
    async cancelLocalPush() {
      try {
        await cancelPush()
        // 积分中心 push 排查  其他事件
        xmlog.event(55686, 'others', {
          description: 'push 清除',
        })
      } catch (err) {
        console.log(err)
        // 积分中心 push 排查  其他事件
        xmlog.event(55686, 'others', {
          description: 'push 清除失败' + safetyToString(err),
        })
      }
    },
  }),
})
