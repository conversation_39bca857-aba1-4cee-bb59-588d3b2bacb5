import { createModel } from '@rematch/core'
import { getAppRequestTimeApm } from '@xmly/rn-sdk'
import getCreditRequest from 'servicesV2/getCredit'
import SentryUtils from 'utilsV2/sentryUtils'
import { RootModel } from '.'

export type CreditModelState = {
  creditPoint: number
}

const defaultCreditModelState = {
  creditPoint: 0,
}

export const credit = createModel<RootModel>()({
  state: defaultCreditModelState, // initial state
  reducers: {
    reset: () => {
      return defaultCreditModelState
    },
    refresh: (state, payload: Partial<CreditModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
    increasePoint: (state, payload: number) => {
      return {
        ...state,
        creditPoint: state.creditPoint + payload,
      }
    },
  },
  effects: (dispatch) => ({
    async getCredit(payload?: { init?: boolean }) {
      const isInit = payload?.init
      try {
        let AppRequestTimeApm
        if (isInit) {
          AppRequestTimeApm = getAppRequestTimeApm()
        }
        AppRequestTimeApm?.start()
        const res = await getCreditRequest()
        if (res && typeof res.data !== 'undefined') {
          AppRequestTimeApm?.end()
          dispatch.credit.refresh({
            creditPoint: res.data.point,
          })
        } else {
          dispatch.credit.refresh({
            creditPoint: 0,
          })
          SentryUtils.captureException('接口返回的res 异常', {
            source: 'credit.getCredit',
            res,
          })
        }
      } catch (err) {
        console.log(err)
        dispatch.credit.refresh({
          creditPoint: 0,
        })
        SentryUtils.captureException(err, {
          source: 'credit.getCredit',
        })
      }
    },
  }),
})
