import { createModel } from '@rematch/core'
import { RootModel } from '.'
import goldCoinABValue from '../modulesV2/goldCoinABValue'
import requestCashBalance from '../servicesV2/requestCashBalance'
import { CashConfigExpGroupId } from '../typesV2/goldCoin'

export type GoldCoinModelState = {
  balance: string | number
}

const defaultGoldCoinModelState: GoldCoinModelState = {
  balance: '0',
}

export const goldCoin = createModel<RootModel>()({
  state: defaultGoldCoinModelState, // initial state
  reducers: {
    reset: () => {
      return defaultGoldCoinModelState
    },
    refresh: (state, payload: Partial<GoldCoinModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
    add: (state, payload: number) => {
      return {
        ...state,
        balance: (Number(state.balance) + payload).toFixed(1),
      }
    },
  },
  effects: (dispatch) => ({
    async getBalance() {
      try {
        if (goldCoinABValue.getValue().groupId !== CashConfigExpGroupId.default) {
          const res = await requestCashBalance()
          if (res && typeof res.data !== 'undefined') {
            dispatch.goldCoin.refresh({
              balance: res.data.balance,
            })
          } else {
            throw new Error('')
          }
        }
      } catch (err) {
        console.log(err)
        dispatch.goldCoin.refresh({
          balance: '0',
        })
      }
    },
  }),
})
