import { createModel } from '@rematch/core'
import { Dimensions, Platform } from 'react-native'
import { RootModel } from '.'

export type PageModelState = {
  screenWidth: number
}

const defaultPageModelState: PageModelState = {
  screenWidth: Platform.OS === 'android' ? Dimensions.get('window').width : Dimensions.get('screen').width,
}

export const page = createModel<RootModel>()({
  state: defaultPageModelState, // initial state
  reducers: {
    reset: () => {
      return defaultPageModelState
    },
    refresh: (state, payload: Partial<PageModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async setScreenWidth({ width }: { width: number }) {
      dispatch.page.refresh({
        screenWidth: width,
      })
    },
  }),
})
