import { createModel } from '@rematch/core'
import { RootModel } from '.'
import SentryUtils from 'utilsV2/sentryUtils'
import {
  CommodityBannerItem,
  CommodityDisplayProducts,
  CommodityDisplayTabs,
} from '../typesV2/commodity'
import { Toast } from '@xmly/rn-sdk'
import CommodityManager from '../modulesV2/commodityManager'
import requestCommodityBanner from '../servicesV2/requestCommodityBanner'
import { fillOrigin } from '../utilsV2/image2CustomSize'
import rnEnv from '../../rnEnv'

export type CommodityModelState = {
  currentTab: number
  displayTabs: CommodityDisplayTabs
  displayProducts: CommodityDisplayProducts
  isLoading: boolean
  hasMore: boolean
  commodityBanner: CommodityBannerItem[]
}

const defaultCommodityModelState: CommodityModelState = {
  currentTab: 0,
  displayTabs: [],
  displayProducts: [],
  commodityBanner: [],
  isLoading: true,
  hasMore: true,
}

let fetchMore = false

const commodityManager = new CommodityManager()

export const commodity = createModel<RootModel>()({
  state: defaultCommodityModelState, // initial state
  reducers: {
    reset: () => {
      return defaultCommodityModelState
    },
    refresh: (state, payload: Partial<CommodityModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async fetchCommodityBanner() {
      try {
        const res = await requestCommodityBanner()
        dispatch.commodity.refresh({
          commodityBanner: res.data
            .map((item) => ({
              ...item,
              cover: `https:${fillOrigin(item.cover, 3, rnEnv.isTest())}`,
            }))
            .filter((item) => !item.hidden)
            .sort((a, b) => (b?.order || 0) - (a?.order || 0)),
        })
      } catch (err) {
        console.log(err)
      }
    },
    async setCurrentTabIndex({ index }: { index: number }, rootState) {
      const {
        commodity: { currentTab },
      } = rootState
      if (currentTab === index) return
      dispatch.commodity.refresh({
        currentTab: index,
        isLoading: true,
        hasMore: true,
      })
      dispatch.commodity.changeTab({ index })
    },
    async changeTab({ index }: { index: number }, rootState) {
      try {
        const {
          commodity: { displayTabs },
        } = rootState
        const {
          poolId: targetDisplayTabPoolId,
          id: targetDisplayTabComponentId,
        } = displayTabs[index]

        const eCommerceProductForDisplay =
          commodityManager.getECommerceProductDataForDisplay(
            targetDisplayTabComponentId
          )

        if (eCommerceProductForDisplay.list.length > 0) {
          dispatch.commodity.refresh({
            displayProducts: eCommerceProductForDisplay.list,
            hasMore: eCommerceProductForDisplay.hasMore,
          })
        } else {
          const productsForDisplayInfoNew =
            await commodityManager.getECommerceProductListByPoolId({
              poolId: targetDisplayTabPoolId,
              componentId: targetDisplayTabComponentId,
            })
          dispatch.commodity.refresh({
            displayProducts: productsForDisplayInfoNew.list,
            hasMore: productsForDisplayInfoNew.hasMore,
          })
        }
      } catch (err) {
        console.log(err)
      } finally {
        dispatch.commodity.refresh({
          isLoading: false,
        })
      }
    },
    async initProducts(payload?: { isRefresh: boolean }) {
      fetchMore = false
      try {
        const [result] = await Promise.all([
          commodityManager.initProduct(),
          dispatch.commodity.fetchCommodityBanner(),
        ])
        dispatch.commodity.refresh({
          displayTabs: result.displayTabs,
          isLoading: false,
          displayProducts: result.productForDisplayInfo.list,
          hasMore: result.productForDisplayInfo.hasMore,
        })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'commodity.initProducts',
        })
        dispatch.commodity.refresh({
          isLoading: false,
          hasMore: false,
        })
      }
    },
    async judgeLoadMore({ currentTab }: { currentTab: number }, rootState) {
      if (fetchMore) return
      fetchMore = true
      try {
        const currentTabInfo = rootState.commodity.displayTabs[currentTab]
        if (!currentTabInfo) {
          return
        }
        const currentDisplayTabProductInfo =
          commodityManager.getECommerceProductDataForDisplay(
            rootState.commodity.displayTabs[currentTab].id
          )
        if (currentDisplayTabProductInfo.hasMore) {
          dispatch.commodity.refresh({
            isLoading: true,
          })
          console.log('load more start ～')

          const productsForDisplayInfoNew =
            await commodityManager.getECommerceProductListByPoolId({
              pageNo: currentDisplayTabProductInfo.pageNo + 1,
              poolId: currentTabInfo.poolId,
              componentId: currentTabInfo.id,
            })

          dispatch.commodity.refresh({
            displayProducts: productsForDisplayInfoNew.list,
            hasMore: productsForDisplayInfoNew.hasMore,
          })
        } else {
          return
        }
      } catch (err) {
        Toast.info('加载失败～')
        SentryUtils.captureException(err, {
          source: 'commodity.judgeLoadMore',
        })
      } finally {
        console.log('load more finished ～')
        dispatch.commodity.refresh({
          isLoading: false,
        })
        fetchMore = false
      }
    },
  }),
})
