import { createModel } from '@rematch/core'
import getTopSectionInfo, { TopSectionBanners, TopSectionGourds, TopSectionNotification } from 'servicesV2/getTopSectionInfo'
import SentryUtils from 'utilsV2/sentryUtils'
import { RootModel } from '.'

export type TopSectionModelMateState = {
  bannerList: TopSectionBanners[]
  gourdList: TopSectionGourds[]
  notification: TopSectionNotification[]
}

export type TopSectionModelState = {
  earn: TopSectionModelMateState
  spend: TopSectionModelMateState
  global: TopSectionModelMateState
}

const defaultTopSectionModelState: TopSectionModelState = {
  earn: {
    bannerList: [],
    gourdList: [],
    notification: [],
  },
  spend: {
    bannerList: [],
    gourdList: [],
    notification: [],
  },
  global: {
    bannerList: [],
    gourdList: [],
    notification: [],
  },
}

export const topSection = createModel<RootModel>()({
  state: defaultTopSectionModelState, // initial state
  reducers: {
    reset: () => {
      return defaultTopSectionModelState
    },
    refresh: (state, payload: Partial<TopSectionModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async fetchTopSectionInfo(tabName: 'earn' | 'spend' | 'global') {
      try {
        const res = await getTopSectionInfo(tabName)
        if (res && res.ret === 0) {
          dispatch.topSection.refresh({
            [tabName]: {
              bannerList: res.data.banners,
              gourdList: res.data.gourds,
              notification: res.data.notification,
            },
          })
        } else {
          dispatch.topSection.reset()
        }
      } catch (err) {
        console.log(err)
        dispatch.topSection.reset()
        SentryUtils.captureException(err, {
          source: 'topSection.fetchTopSectionInfo',
        })
      }
    },
  }),
})
