import { ConfigCenter } from '@xmly/rn-sdk'
import dayjs from 'dayjs'
import requestGiftPackageReceiveStatus from '../servicesV2/requestGiftPackageReceiveStatus'
import {
  SignInUserAwardType,
  EnumRequestGiftPackageReceiveStatus,
  SignInGiftReceiveStatus,
} from '../typesV2/signInNew'
import SentryUtils from '../utilsV2/sentryUtils'

type Args = {
  signInRecords: SignInUserAwardType[]
  todaySignInDay: number
}

const getGiftIds = async () => {
  try {
    const res = await ConfigCenter.getConfig('toc', 'credit_center_gift_ids')
    if (typeof res === 'string') {
      return res.split(',').map((item) => parseInt(item))
    }
    return null
  } catch (err) {
    return null
  }
}

let requestPromise: null | Promise<
  | SignInGiftReceiveStatus.notReceived
  | SignInGiftReceiveStatus.received
  | SignInGiftReceiveStatus.unavailable
> = null
let receiveStatus = SignInGiftReceiveStatus.UNSET

const tryGetGiftPackageReceiveStatusByGiftId = async ({
  signInRecords,
  todaySignInDay,
}: Args) => {
  const giftDayDetail = signInRecords.find((record) => record?.context?.isGift)
  if (!giftDayDetail) return SignInGiftReceiveStatus.received // 如果不包含礼包，则不需要请求
  if (todaySignInDay !== giftDayDetail.day) {
    return SignInGiftReceiveStatus.unavailable // 如果当前签到天数不等于礼包在的那天，则不需要请求
  }
  try {
    const giftDayStr = dayjs().format('YYYY-MM-DD')
    const ids = await getGiftIds()
    if (!ids || ids.length === 0) return SignInGiftReceiveStatus.received
    const res = await requestGiftPackageReceiveStatus({
      dateList: [giftDayStr],
      giftPackageIds: ids,
    })
    if (res.ret === 0 && res.data) {
      const isGiftNotReceived = Object.keys(res.data).every(
        (value) =>
          res.data[value][giftDayStr] ===
          EnumRequestGiftPackageReceiveStatus.notReceived
      )

      return isGiftNotReceived
        ? SignInGiftReceiveStatus.notReceived
        : SignInGiftReceiveStatus.received
    } else {
      throw Error('接口异常')
    }
  } catch (err) {
    console.log(err)
    SentryUtils.captureException(err, {
      source: 'CheckInBarItemNew.getGiftPackageReceiveStatus',
    })
    return SignInGiftReceiveStatus.received
  } finally {
    requestPromise = null
  }
}

const initStatus = async (args: Args) => {
  if (requestPromise) {
    return (receiveStatus = await requestPromise)
  } else {
    requestPromise = tryGetGiftPackageReceiveStatusByGiftId(args)
    return (receiveStatus = await requestPromise)
  }
}

const getStatus = async (args: Args) => {
  if (receiveStatus !== SignInGiftReceiveStatus.UNSET) return receiveStatus
  return await initStatus(args)
}

const refreshStatus = async (args: Args) => initStatus(args)

const giftReceiveStatusManager = {
  getStatus,
  refreshStatus,
}

export default giftReceiveStatusManager
