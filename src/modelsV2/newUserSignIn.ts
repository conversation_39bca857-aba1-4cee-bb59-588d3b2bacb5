// 新用户，召回用户签到
import { createModel } from '@rematch/core'
import { Toast } from '@xmly/rn-sdk'
import { RootModel } from '.'
import { getXuidTicket, login } from 'utilsV2/native'
import getSignInListNew from '../servicesV2/getSignInListNew'
import { ABRuleTag, CheckInInfo, EnumVIPTag, SignInListNewRes, SignInStatus, SignInUserAwardType, UserIDTag, SignInMultiAwardsDetail } from '../typesV2/signInNew'
import userInfoDetail from '../modulesV2/userInfoDetail'
import get from 'lodash.get'
import { ResDataType } from '../servicesV2/request'
import customReportError from '../utilsV2/customReportError'
import { APMLocalLog } from '@xmly/rn-utils'
import requestGetSignInAward from '../servicesV2/requestGetSignInAward'
import lodashGet from 'lodash.get'
import { XUID_ticketConfig } from '../constantsV2'
import rnEnv from '../../rnEnv'
const AID = () => (rnEnv.isTest() ? 183 : 192)

export type NewUserSignInModelState = {
  isSignInToday: boolean
  todaySignInCreditValue: string
  vipTag: EnumVIPTag //是否是vip
  title: string // 标题
  desc: string // 副标题
  signInRecords: SignInUserAwardType[] // 签到列表
  todaySignInDay: number // 今天是签到列表中的第几天
  checkInInfo: CheckInInfo
  tomorrowSignInGiftInfo: CheckInInfo
  userIDTag: UserIDTag // 用户id标签
  abRuleTag: ABRuleTag // 是否是A/B测试用户
  randomQuantity: number // 随机积分值
  todaySignInStatus: SignInStatus // 今日签到状态
  todaySignInMultiAwardsConfig: SignInMultiAwardsDetail | null // 签到多个奖励配置详情
}

const newUserSignInDefaultState: NewUserSignInModelState = {
  isSignInToday: false,
  vipTag: EnumVIPTag.noVip,
  title: '',
  desc: '',
  signInRecords: [],
  todaySignInDay: 0,
  checkInInfo: {},
  tomorrowSignInGiftInfo: {},
  abRuleTag: ABRuleTag.true,
  userIDTag: UserIDTag.UNSET,
  randomQuantity: 0,
  todaySignInCreditValue: '0',
  todaySignInStatus: SignInStatus.isDisabled,
  todaySignInMultiAwardsConfig: null,
}

const genTicket = async () => {
  const ticket = await getXuidTicket({
    businessId: XUID_ticketConfig.finishTask.businessId,
    scene: XUID_ticketConfig.finishTask.scene,
    uid: userInfoDetail.getDetail().uid || -1,
  })
  return ticket
}

// 处理当天签到奖励信息
const processSignInUserAwards = (payload: { signInUserAwards: SignInUserAwardType[]; todaySignInDay: number }) => {
  let nextMonthlyTicketAwardDay = -1
  let checkInInfo = {}
  let tomorrowSignInGiftInfo = {}

  try {
    const { signInUserAwards, todaySignInDay } = payload

    if (!signInUserAwards || !Array.isArray(signInUserAwards) || signInUserAwards.length <= 0) {
      return {
        checkInInfo,
        tomorrowSignInGiftInfo,
        nextMonthlyTicketAwardDay,
      }
    }

    signInUserAwards.forEach((dayItem: SignInUserAwardType) => {
      const { day, context } = dayItem
      // 今天
      if (todaySignInDay === day && context) {
        try {
          checkInInfo = context
        } catch (err) {
          console.log(err)
        }
      }
      // 明天
      const tomorrowSignInDay = todaySignInDay !== 7 ? todaySignInDay + 1 : 1
      if (tomorrowSignInDay === day && context) {
        try {
          tomorrowSignInGiftInfo = context
        } catch (err) {
          console.log(err)
        }
      }
    })
  } catch (err) {
  } finally {
    return {
      checkInInfo,
      tomorrowSignInGiftInfo,
      nextMonthlyTicketAwardDay,
    }
  }
}

const getUserIDTagInfo = (signInListRes: ResDataType<SignInListNewRes>) => {
  const ruleParseInfo = get(signInListRes, 'context.activityContext.ruleParseInfo', [
    {
      ruleType: 12,
      tag: 'noVip',
    },
    {
      ruleType: 18,
      tag: '2',
    },
    {
      ruleType: 16,
      tag: 'false',
    },
  ]) as { ruleType: number; tag: string }[]

  let vipTag = EnumVIPTag.noVip // vip标识
  let mABRuleTag = ABRuleTag.true // 是否命中AB规则
  let mUserIDTag = UserIDTag.UNSET // 用户画像标识

  ruleParseInfo.forEach((info) => {
    if (info.ruleType === 12) {
      vipTag = info.tag as EnumVIPTag
    }
    if (info.ruleType === 16) {
      mABRuleTag = info.tag as ABRuleTag
    }

    if (info.ruleType === 18) {
      mUserIDTag = info.tag as UserIDTag
    }
  })

  return {
    vipTag,
    mABRuleTag,
    mUserIDTag,
  }
}

export const newUserSignIn = createModel<RootModel>()({
  state: newUserSignInDefaultState,
  reducers: {
    reset: () => {
      return newUserSignInDefaultState
    },
    refresh: (state, payload: Partial<NewUserSignInModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    // 请求签到列表
    async getSignInInfo() {
      try {
        const xIsLogin = userInfoDetail.getDetail()?.isLogin
        if (!xIsLogin) return
        const ticket = await genTicket()
        if (!ticket) return
        const signInListRes = (await getSignInListNew({ isMonthlyTicket: false, aid: AID(), ticket })) as ResDataType<SignInListNewRes>
        const { ret, data: signInListData } = signInListRes
        const { vipTag, mABRuleTag, mUserIDTag } = getUserIDTagInfo(signInListRes)
        const { signInUserAwards, signInUserInfo, title, desc } = signInListData
        // 当前用户未登录
        if (ret === 303) return login()
        if (ret !== 0) return

        const isSignInToday = signInUserInfo.signInStatus === SignInStatus.isSignedIn // 今天是否签到
        const todaySignInDay = signInUserInfo.todaySignInDay // 今天签到的是第几天

        // 处理当天奖励信息
        const { checkInInfo, tomorrowSignInGiftInfo } = processSignInUserAwards({
          signInUserAwards,
          todaySignInDay,
        })

        console.log({ checkInInfo, tomorrowSignInGiftInfo })

        // 更新签到列表数据
        dispatch.newUserSignIn.refresh({
          signInRecords: signInUserAwards,
          todaySignInDay,
          checkInInfo,
          vipTag,
          title,
          desc,
          userIDTag: mUserIDTag,
          abRuleTag: mABRuleTag,
          todaySignInStatus: signInUserInfo.signInStatus,
          tomorrowSignInGiftInfo,
          isSignInToday,
        })
      } catch (error) {
        console.log({ error })
        customReportError({ source: 'newUserSignIn.getSignInInfo 异常', error })
      }
    },

    // 进入页面自动去签到，打开签到面板
    async getAward(payload: { day: number; awardCheckInInfo?: CheckInInfo }) {
      const { day, awardCheckInInfo } = payload
      console.log('day:', day)
      // 签到
      try {
        const ticket = await genTicket()
        if (!ticket) return

        const signInRes = await requestGetSignInAward({ aid: AID(), day, ticket })

        if (!signInRes) return
        if (signInRes.ret === 0 && signInRes.data.code === 0) {
          // 领取成功
          const awardCount = lodashGet(signInRes, 'data.awardValue', 0)
          const award = lodashGet(awardCheckInInfo, 'awards', [])[0]
          const recommendTaskId = lodashGet(signInRes, 'data.pop.taskId', null)
          const recommendAwardLabel = lodashGet(signInRes, 'data.pop.toast', '')
          const recommendAwardAid = lodashGet(signInRes, 'data.pop.activityId', '')
          if (recommendTaskId) {
            dispatch.taskAwardModal.openModal({
              currentTaskId: -99,
              awardWorth: awardCount,
              recommendTaskId,
              recommendAwardLabel,
              recommendTaskAid: recommendAwardAid,
              currentAwardIcon: award?.icon,
              currentAwardLabel: award?.label,
            })
          } else {
            Toast.info('领取成功')
          }
        } else {
          throw new Error(signInRes?.msg || signInRes.data?.msg || '领取失败，稍后再试')
        }
      } catch (error) {
        Toast.info('领取失败，稍后再试')

        console.log('SentryUtils.captureException error', error)
        APMLocalLog.add(JSON.stringify(error), 'rn_credit_center_signin_error_newUserSignIn')
        customReportError({ source: 'newUserSignIn.handleSignIn 异常', error })
      } finally {
        // 刷新资产数据
        dispatch.credit.getCredit()
        dispatch.goldCoin.getBalance()
        dispatch.chips.getChipsBalance()
        // 刷新签到日历
        dispatch.newUserSignIn.getSignInInfo()
      }
    },
  }),
})
