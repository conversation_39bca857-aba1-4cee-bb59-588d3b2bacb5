// 签到
import { createModel } from '@rematch/core'
import { Toast } from '@xmly/rn-sdk'
import { RootModel } from '.'
import achieveMillstone from 'servicesV2/achieveMillstone'
import { Product } from 'typesV2/product'
import { login } from 'utilsV2/native'
import SentryUtils from '../utilsV2/sentryUtils'
import getSignInListNew from '../servicesV2/getSignInListNew'
import {
  ABRuleTag,
  AwardStatus,
  CheckInInfo,
  BigGiftDescModalInfo,
  EnumVIPTag,
  SignInListNewRes,
  SignInNewResCodeType,
  SignInStatus,
  SignInUserAwardType,
  SuccessPopInfo,
  UserIDTag,
  SignInMultiAwardsDetail,
} from '../typesV2/signInNew'
import signInNew from '../servicesV2/signInNew'
import { PosterListType } from '../typesV2/posterListType'
import getPosterList from '../servicesV2/getPosterList'
import lodashGet from 'lodash.get'
import userInfoDetail from '../modulesV2/userInfoDetail'
import get from 'lodash.get'
import nativeInfoModule from '../modulesV2/nativeInfoModule'
import { defaultSignInRecords } from '../constantsV2/signIn'
import { NativeModules } from 'react-native'
import { RNBroadcastSignSuccessKey } from '../constantsV2'
import request, { ResDataType } from '../servicesV2/request'
import customReportError from '../utilsV2/customReportError'
import requestSignUserType from '../servicesV2/requestSignUserType'
import { TaskListInfoActivityStatus, TaskListInfoRequestResult, TaskStatus } from '../typesV2/taskList'
import { signdays } from '../../src/backData/signdays'
import { APMLocalLog } from '@xmly/rn-utils'
import { API_DEFAULT, Api } from '../constantsV2/apiConfig'
import getXMRequestId from 'utilsV2/getXMRequestId'

export enum PopType {
  MileStonePop = 1, // 里程碑弹窗
  SuccessGetPop = 2, // 完成收听任务弹窗
}

export enum FromType {
  shareA = '日签分享方案A',
  shareB = '日签分享方案B',
  history = '日签历史列表',
}

export type SignInInfoModelState = {
  todaySignInCreditValue: string
  vipTag: EnumVIPTag //是否是vip
  vipTitle: string //签到日历副标题-vip
  noVipTitle: string //签到日历副标题-非vip
  signInRecords: SignInUserAwardType[] // 签到列表
  ifQueryCheck: boolean // 是否请求过签到接口
  todaySignInDay: number // 今天是签到列表中的第几天
  checkInInfo: CheckInInfo
  tomorrowSignInGiftInfo: CheckInInfo
  receiveAwardPop: BigGiftDescModalInfo
  product: Product

  noLoginSigInModalVisible: boolean // 未登录签到弹窗是否显示
  signInSuccessModalVisible: boolean // 签到弹窗是否显示
  signInSuccessModalVisibleAgain?: boolean // 签到翻倍弹窗再次显示
  signInBigGiftPackageModalVisible: boolean // 连签礼包弹窗是否显示

  noLoginAbTestStatus: '1' | '2' //未登录AB测状态
  hasShowOneModalBefore: boolean //已显示任一弹窗

  showRightIcon: boolean // 是否显示右下角悬浮
  showExchangePop: boolean // 里程碑弹窗

  signInPosterShow: boolean // 签到海报弹窗是否显示
  signInPosterWithShare: boolean // 海报签到带分享面板
  signInText: boolean //海报签到成功文案是否显示
  signInDate: boolean //海报上日期是否显示

  successPopShow: boolean // 完成收听任务弹窗
  successPopInfo: SuccessPopInfo // 完成收听任务弹窗信息
  posterListToday: PosterListType //当日海报列表
  posterListHistory: PosterListType //海报历史列表
  currentPoster: PosterListType //当前海报信息
  shareFromType: FromType | string
  userIDTag: UserIDTag // 用户id标签
  abRuleTag: ABRuleTag // 是否是A/B测试用户
  randomQuantity: number // 随机积分值
  todaySignInStatus: SignInStatus // 今日签到状态
  todaySignInMultiAwardsConfig: SignInMultiAwardsDetail | null // 签到多个奖励配置详情
}

const defaultSignInInfoModelState: SignInInfoModelState = {
  vipTag: EnumVIPTag.noVip,
  vipTitle: '',
  noVipTitle: '',
  signInRecords: defaultSignInRecords,
  ifQueryCheck: false,
  todaySignInDay: 0,
  checkInInfo: {},
  product: {},
  tomorrowSignInGiftInfo: {},
  receiveAwardPop: {
    title: '',
    subTitle: '',
    buttenText: '',
    logo: '',
  },
  noLoginSigInModalVisible: false,
  signInSuccessModalVisible: false,
  signInBigGiftPackageModalVisible: false,
  hasShowOneModalBefore: false,
  noLoginAbTestStatus: '1',
  showRightIcon: false,
  showExchangePop: false,

  signInPosterShow: false,
  signInPosterWithShare: false,
  signInText: false,
  signInDate: true,

  successPopShow: false,
  successPopInfo: {},
  posterListToday: [],
  posterListHistory: [],
  currentPoster: [],
  shareFromType: '',
  abRuleTag: ABRuleTag.true,
  userIDTag: UserIDTag.UNSET,
  randomQuantity: 0,
  todaySignInCreditValue: '0',
  todaySignInStatus: SignInStatus.isDisabled,
  todaySignInMultiAwardsConfig: null,
}

// 处理当天签到奖励信息
const processSignInUserAwards = (payload: { signInUserAwards: SignInUserAwardType[]; todaySignInDay: number; isSignInToday: boolean; isFromVoteModal?: boolean }) => {
  let nextMonthlyTicketAwardDay = -1
  let checkInInfo = {}
  let tomorrowSignInGiftInfo = {}
  let bigGiftSignInGiftDescInfo = {
    title: '连签礼包',
    subTitle: '有机会获得1天VIP、3日VIP、大量积分等道具',
    buttenText: '我知道了',
    logo: 'https://placeholder.smart-tools.cn/108x68/?text=ximalaya',
  } // 连签礼包说明信息

  try {
    const { signInUserAwards, todaySignInDay, isFromVoteModal, isSignInToday } = payload

    if (!signInUserAwards || !Array.isArray(signInUserAwards) || signInUserAwards.length <= 0) {
      return {
        checkInInfo,
        tomorrowSignInGiftInfo,
        bigGiftSignInGiftDescInfo,
        nextMonthlyTicketAwardDay,
      }
    }

    signInUserAwards.forEach((dayItem: SignInUserAwardType) => {
      const { day, context } = dayItem
      // 今天
      if (todaySignInDay === day && context) {
        try {
          checkInInfo = context
        } catch (err) {
          console.log(err)
        }
      }
      // 明天
      const tomorrowSignInDay = todaySignInDay !== 7 ? todaySignInDay + 1 : 1
      if (tomorrowSignInDay === day && context) {
        try {
          tomorrowSignInGiftInfo = context
        } catch (err) {
          console.log(err)
        }
      }
      // 连签礼包奖励信息
      if (context?.receiveAwardPop && context.receiveAwardPop.title && context?.awards?.find((award) => award.label.includes('连签礼包'))) {
        bigGiftSignInGiftDescInfo = context.receiveAwardPop
      }

      if (nextMonthlyTicketAwardDay === -1 && dayItem.awardStatus !== AwardStatus.isDispatched && dayItem.context?.awards?.find((award) => award.label.includes('月票'))) {
        nextMonthlyTicketAwardDay = day
      }
    })
    if (isFromVoteModal && isSignInToday) {
      const noticeLabel = nextMonthlyTicketAwardDay !== -1 ? `今日已签到，再签${nextMonthlyTicketAwardDay - todaySignInDay}天可获得月票` : '今日已签到，每天都来签到可获得月票哦'

      Toast.info(noticeLabel)
    }
  } catch (err) {
  } finally {
    return {
      checkInInfo,
      tomorrowSignInGiftInfo,
      bigGiftSignInGiftDescInfo,
      nextMonthlyTicketAwardDay,
    }
  }
}

const getUserIDTagInfo = (signInListRes: ResDataType<SignInListNewRes>) => {
  const ruleParseInfo = get(signInListRes, 'context.activityContext.ruleParseInfo', [
    {
      ruleType: 12,
      tag: 'noVip',
    },
    {
      ruleType: 18,
      tag: '2',
    },
    {
      ruleType: 16,
      tag: 'false',
    },
  ]) as { ruleType: number; tag: string }[]

  let vipTag = EnumVIPTag.noVip // vip标识
  let mABRuleTag = ABRuleTag.true // 是否命中AB规则
  let mUserIDTag = UserIDTag.UNSET // 用户画像标识

  ruleParseInfo.forEach((info) => {
    if (info.ruleType === 12) {
      vipTag = info.tag as EnumVIPTag
    }
    if (info.ruleType === 16) {
      mABRuleTag = info.tag as ABRuleTag
    }

    if (info.ruleType === 18) {
      mUserIDTag = info.tag as UserIDTag
    }
  })

  return {
    vipTag,
    mABRuleTag,
    mUserIDTag,
  }
}

const _calJson = (data: any) => {
  let _o: any = {}
  try {
    _o = JSON.parse(data)
  } catch (err) {
    _o = signdays
  }
  return _o
}

export const signInInfo = createModel<RootModel>()({
  state: defaultSignInInfoModelState,
  reducers: {
    refresh: (state, payload: Partial<SignInInfoModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
    reset: () => {
      return defaultSignInInfoModelState
    },
  },
  effects: (dispatch) => ({
    // 请求签到列表
    async getSignInInfo(payload?: { withMonthlyTicket?: boolean; isFromVoteModal?: boolean }) {
      try {
        const xIsLogin = userInfoDetail.getDetail()?.isLogin
        const _signInListRes = await getSignInListNew()
        const signInListRes = xIsLogin ? _signInListRes : _calJson(_signInListRes?.data?.[0]?.signDays)
        const { ret, data: signInListData } = signInListRes
        const { vipTag, mABRuleTag, mUserIDTag } = getUserIDTagInfo(signInListRes)

        const { signInUserAwards, signInUserInfo, title: vipTitle, desc: noVipTitle } = signInListData

        if (userInfoDetail.getDetail()?.isLogin) {
          if (ret === 303) {
            // 当前用户未登录
            login()
          } else if (ret === 0) {
            const isSignInToday = signInUserInfo.signInStatus === SignInStatus.isSignedIn // 今天是否签到
            const todaySignInDay = signInUserInfo.todaySignInDay // 今天签到的是第几天

            let processSignInUserAwardsTime = Date.now()

            // 处理当天奖励信息
            const { checkInInfo, tomorrowSignInGiftInfo, bigGiftSignInGiftDescInfo } = processSignInUserAwards({
              signInUserAwards,
              isSignInToday,
              todaySignInDay,
              isFromVoteModal: payload?.isFromVoteModal,
            })

            processSignInUserAwardsTime = Date.now() - processSignInUserAwardsTime

            console.log('processSignInUserAwards 💡💡💡💡💡💡', processSignInUserAwardsTime, checkInInfo)

            // 设置当天多个奖励详情（如果有的话）
            dispatch.signInInfo.setTodayMultiAwards({
              todaySignInContext: checkInInfo,
              targetDay: todaySignInDay,
            })

            // 如果今天没签到  处理签到
            if (!isSignInToday) {
              const isTomorrowCanReceive = await dispatch.tomorrowAward.judgeTomorrowCanReceive()
              dispatch.signInInfo.handleSignIn({
                signInUserAwards,
                todaySignInDay,
                withMonthlyTicket: payload?.withMonthlyTicket,
                checkInInfo,
                isTomorrowCanReceive,
              })
            } else {
              // 尝试刷新一下多选一奖励任务状态
              dispatch.signInInfo.refreshMultiAwardsTaskStatus(checkInInfo)
            }

            // 更新签到列表数据
            dispatch.signInInfo.refresh({
              ifQueryCheck: true,
              signInRecords: signInUserAwards,
              todaySignInDay,
              checkInInfo,
              vipTitle,
              noVipTitle,
              vipTag,
              userIDTag: mUserIDTag,
              abRuleTag: mABRuleTag,
              todaySignInStatus: signInUserInfo.signInStatus,
              tomorrowSignInGiftInfo,
              receiveAwardPop: bigGiftSignInGiftDescInfo,
            })
          }
        } else {
          // 处理用户未登录
          dispatch.signInInfo.refresh({
            ifQueryCheck: true,
            signInRecords: signInUserAwards,
            todaySignInDay: -1,
            checkInInfo: {},
            vipTitle,
            noVipTitle,
            vipTag,
            userIDTag: mUserIDTag,
            abRuleTag: mABRuleTag,
            tomorrowSignInGiftInfo: {},
            receiveAwardPop: {
              title: '',
              subTitle: '',
              buttenText: '',
              logo: '',
            },
          })
        }
      } catch (error) {
        console.log({ error })
        Toast.info('数据获取失败，请稍后重试')
        // NetInfo.fetch().then((state) => {
        //   if (state.isConnected) {
        //   } else {
        //     Toast.info('当前无网络，请检查网络后重试')
        //   }
        // })

        SentryUtils.captureException(error, {
          source: 'signInInfo.getSignInInfo',
        })
        customReportError({ source: 'signInInfo.getSignInInfo 异常', error })
      }
    },

    async refreshMultiAwardsTaskStatus(checkInInfo: CheckInInfo, rootState) {
      try {
        // 查一下多选奖励的任务状态
        if (checkInInfo.targetActivityId && checkInInfo.targetTaskId) {
          const data = {
            aid: checkInInfo.targetActivityId,
            taskId: checkInInfo.targetTaskId,
          }
          const multiAwardsTaskListRes = await request<TaskListInfoRequestResult>({
            ...API_DEFAULT,
            url: Api.postTaskListV2,
            option: {
              method: 'post',
              data: JSON.stringify(data),
              headers: {
                'Content-Type': 'application/json',
              },
              catchJsonParse: true,
            },
            tip: false,
          })

          const taskItems = lodashGet(multiAwardsTaskListRes, 'data.taskItems', [])

          if (taskItems.length > 0 && multiAwardsTaskListRes?.data?.activityStatus === TaskListInfoActivityStatus.active) {
            const targetTaskStatus = taskItems[0].status
            console.log({ multiAwardsTaskListRes, targetTaskStatus })
            if (rootState.signInInfo.todaySignInMultiAwardsConfig)
              dispatch.signInInfo.refresh({
                todaySignInMultiAwardsConfig: {
                  ...rootState.signInInfo.todaySignInMultiAwardsConfig,
                  status: targetTaskStatus,
                },
              })
          } else {
            throw new Error('active error')
          }
        }
      } catch (err) {
        dispatch.signInInfo.refresh({
          todaySignInMultiAwardsConfig: null,
        })
      }
    },
    async markCheckInRequestId(payload, rootState) {
      const xmRequestId = await getXMRequestId()
      dispatch.signInInfo.refresh({
        ...rootState.signInInfo,
        checkInInfo: {
          ...rootState.signInInfo.checkInInfo,
          xmRequestId
        }
      })
    },
    // 进入页面自动去签到，打开签到面板
    async handleSignIn(payload: {
      signInUserAwards: SignInUserAwardType[]
      todaySignInDay: number
      withMonthlyTicket?: boolean
      checkInInfo: CheckInInfo
      isTomorrowCanReceive: boolean
    }) {
      const { signInUserAwards, todaySignInDay, withMonthlyTicket, checkInInfo, isTomorrowCanReceive } = payload
      console.log('todaySignInDay:', todaySignInDay)
      console.log('withMonthlyTicket:', withMonthlyTicket)

      // 签到
      try {
        await requestSignUserType()
        const [signInRes] = await Promise.all([signInNew(), withMonthlyTicket ? signInNew({ isMonthlyTicket: true }) : Promise.resolve({})])

        const ret = signInRes?.ret;
        const signInData = signInRes?.data;

        if (ret === 0 && signInData?.code === SignInNewResCodeType.success) {
          await Promise.all([
            // 更新签到请求的xmRequestId (埋点用)
            dispatch.signInInfo.markCheckInRequestId(null),
            // 查一下多选奖励的任务状态
            dispatch.signInInfo.refreshMultiAwardsTaskStatus(checkInInfo)
          ])
          const { orderData = { randomQuantity: 0 }, dayAward = { name: '' } } = signInData
          console.log('======nativeInfoModule.getInfo()?.pcTask===:', nativeInfoModule.getInfo()?.pcTask)
          const isPushPc = nativeInfoModule.getInfo()?.pcTask === '1'
          let showBigGiftModal = false
          if (signInUserAwards) {
            signInUserAwards.forEach((dayItem: SignInUserAwardType) => {
              const { day, context } = dayItem
              // 今天
              if (todaySignInDay === day && context) {
                if (dayItem.context?.awards?.find((award) => award.label.includes('连签礼包'))) {
                  showBigGiftModal = true
                }
              }
            })
          }
          console.log('======showBigGiftModal===:', showBigGiftModal)
          // 签到成功后刷新月票相关信息
          await dispatch.monthlyTicket.refreshMonthlyTicketData({
            withMonthlyTicket,
          })

          dispatch.signInInfo.refresh({
            signInBigGiftPackageModalVisible: showBigGiftModal,
            signInSuccessModalVisible: isTomorrowCanReceive ? false : !isPushPc,
            randomQuantity: orderData.randomQuantity,
            todaySignInCreditValue: dayAward.name,
            todaySignInStatus: SignInStatus.isSignedIn,
          })

          NativeModules.RNBroadcast.sendBroadcast(RNBroadcastSignSuccessKey, {}) // 给native 发消息签到成功
          dispatch.credit.getCredit() // 拉取积分值
          dispatch.signInInfo.displayMillstoneIcon({ todaySignInDay, isPushPc })
        } else {
          Toast.info('签到失败，请重进')
          APMLocalLog.add(JSON.stringify({ signInData, ret }), 'rn_credit_center_signin_error_1')
        }
      } catch (error) {
        console.log('SentryUtils.captureException error', error)
        Toast.info(error)
        APMLocalLog.add(JSON.stringify(error), 'rn_credit_center_signin_error_2')
        customReportError({ source: 'signInInfo.handleSignIn 异常', error })
      }
    },
    async setTodayMultiAwards(payload: { todaySignInContext: CheckInInfo; targetDay: number }) {
      const { targetActivityId, targetTaskId, chooseAwards } = payload.todaySignInContext
      if (targetActivityId && targetTaskId && chooseAwards && chooseAwards?.length > 0) {
        // 判断成当天配置了多选奖励
        dispatch.signInInfo.refresh({
          todaySignInMultiAwardsConfig: {
            status: TaskStatus.unfinished,
            targetDay: payload.targetDay,
            targetActivityId,
            targetTaskId,
            awards: chooseAwards,
          },
        })
      } else {
        dispatch.signInInfo.refresh({
          todaySignInMultiAwardsConfig: null,
        })
      }
    },

    async displayMillstoneIcon({ todaySignInDay, isPushPc }: { todaySignInDay: number; isPushPc: boolean }) {
      if (todaySignInDay !== 7 && !isPushPc) {
        try {
          const res = await achieveMillstone()
          const { ret, hasAward, product } = res
          if (ret === 0 && hasAward) {
            await dispatch.signInInfo.refresh({ product })
            // 展示右下角悬浮窗
            dispatch.signInInfo.refresh({ showRightIcon: true })
          }
        } catch (error) { }
      }
    },
    // 获取今日海报
    async getPosterListToday() {
      try {
        const res = await getPosterList({
          page: 1,
          pageSize: 200,
          dateType: 1,
        })
        console.log('--------------------getPosterList---------------------------')

        console.log(res)
        const posterListToday = lodashGet(res, 'data.posterList', [])
        const currentPoster = lodashGet(res, 'data.posterList', [])
        dispatch.signInInfo.refresh({
          posterListToday,
          currentPoster,
        })
      } catch (err) {
        console.log(err)
        dispatch.signInInfo.refresh({ posterListToday: [], currentPoster: [] })
        SentryUtils.captureException(err, {
          source: 'signInInfo.getPosterListToday',
        })
      }
    },
    // 获取历史海报列表
    async getPosterListHistory(payload, rootState) {
      try {
        const res = await getPosterList({
          page: 1,
          pageSize: 200,
          dateType: 2,
        })
        console.log({ res })
        console.log({ res: res.data.posterList })
        dispatch.signInInfo.refresh({
          posterListHistory: [...rootState.signInInfo.posterListHistory, ...res.data.posterList],
        })
      } catch (err) {
        console.log(err)
        dispatch.signInInfo.refresh({
          posterListHistory: rootState.signInInfo.posterListHistory,
        })
        SentryUtils.captureException(err, {
          source: 'signInInfo.getPosterListHistory',
        })
      }
    },
  }),
})
