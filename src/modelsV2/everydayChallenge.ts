import { createModel } from '@rematch/core'
import getTaskAwardRequestNew from 'servicesV2/getTaskAwardNew'
import { ActivityRuleTag, StepInfoMultiAwardType, StepType, TaskItemKind, TaskItemType, TaskListType, TaskStatus, TaskStatusOrderForDisplay } from 'typesV2/taskList'
import postTaskList from 'servicesV2/postTaskList'
import { RootModel } from '.'
import { Toast } from '@xmly/rn-sdk'
import SentryUtils from 'utilsV2/sentryUtils'

import lodashGet from 'lodash.get'
import UserConfig from '../utilsV2/getIsNewUser'
import getTaskWhitelist from '../utilsV2/getTaskWhitelist'
import shouldFilterVideoTask from '../utilsV2/shouldFilterVideoTask'
import processTaskListData from '../utilsV2/processTaskListData'
import getEverydayChallengeAidByAB from '../utilsV2/getEverydayChallengeAidByAB'
import requestAdDPTaskList from '../servicesV2/requestAdDPTaskList'
import adDPTaskCtrl from '../utilsV2/adDPTask'
import partition from '../utilsV2/partition'
import everydayChallengeTaskListStorage from '../storageV2/everydayChallengeTaskListStorage'
import adDPTaskListStorage from '../storageV2/adDPTaskListStorage'
import todayHasShownTaskListStorage from '../storageV2/todayHasShownTaskListStorage'
import { AdDPTaskItem } from '../typesV2/adDPTask'
import requestRefreshTaskList from '../servicesV2/requestRefreshTaskList'
import { APMLocalLog } from '@xmly/rn-utils'
import isMultiStepTaskFinished from '../utilsV2/isMultiStepTaskFinished'
import rnEnv from '../../rnEnv'
import goldCoinABValue from '../modulesV2/goldCoinABValue'
import { CashConfigChangeTaskId, CashConfigExpGroupId } from '../typesV2/goldCoin'
import { LayoutRectangle } from 'react-native'
import { ChannelUndertakeModalStatus } from '../typesV2/channelModal'
import activateAdMaterialSummaryReport from 'servicesV2/activateAdMaterialSummaryReport'
import log from 'utils/log'

const customSetTaskItemTaskStatusOrderForDisplay = (item: TaskItemType, channelTaskId?: number) => {
  if (item.id === channelTaskId) {
    item.statusOrderForDisplay = TaskStatusOrderForDisplay.fromChannel
    return
  }
  if (item.stepType === StepType.Multi) {
    if (isMultiStepTaskFinished(item)) {
      item.statusOrderForDisplay = TaskStatusOrderForDisplay.finished
      return
    }
    if (item.stepInfos!.some((step) => step.stepStatus === TaskStatus.finished)) {
      item.statusOrderForDisplay = TaskStatusOrderForDisplay.canReceive
      return
    }
    item.statusOrderForDisplay = TaskStatusOrderForDisplay.notFinish
  } else {
    switch (item.status) {
      case TaskStatus.finished:
        item.statusOrderForDisplay = TaskStatusOrderForDisplay.finished
        break
      case TaskStatus.received:
        item.statusOrderForDisplay = TaskStatusOrderForDisplay.finished
        break
      case TaskStatus.unfinished:
        item.statusOrderForDisplay = TaskStatusOrderForDisplay.notFinish
        break
      case TaskStatus.nonValid:
        item.statusOrderForDisplay = TaskStatusOrderForDisplay.nonValid
        break
    }
  }
}

// 默认任务包列表总数
const defaultListTotalCount = 6

export enum EverydayChallengeGuideStep {
  A = 'A',
  B = 'B',
}

function trimTaskList(taskList: TaskListType) {
  const compareKeys: (keyof TaskItemType)[] = ['id', 'title', 'desc'];
  const newTaskList = taskList?.map((task: any) => {
    let newTask: any = {};
    compareKeys.forEach(key => {
      if (key in task) {
        newTask[key] = task[key];
      }
    });
    return newTask;
  });
  return newTaskList;
}

export type EverydayChallengeModelState = {
  showNewUserGuide: boolean
  taskList: TaskListType
  summaryTask: TaskItemType
  activityContext: object
  progress: number
  totalCondition: number
  activityTitle: string
  activityRuleTag: ActivityRuleTag
  multiAwardModalVisible: boolean
  currentMultiAwardsInfo: {
    taskItem: TaskItemType
    stepNos?: number[]
    aid: number
    awards: StepInfoMultiAwardType[]
  } | null
  pointExchangeTask: TaskItemType | null
  showTaskCount: number
  refreshConfig: {
    supportRefresh: boolean
    refreshPoint: number
  }
  layoutInfo: { [key in EverydayChallengeGuideStep]: (LayoutRectangle & { radius: number }) | null } | {}
}

const defaultEverydayChallengeModelState: EverydayChallengeModelState = {
  showNewUserGuide: false,
  currentMultiAwardsInfo: null,
  pointExchangeTask: null,
  taskList: [],
  activityTitle: '每日挑战',
  activityRuleTag: ActivityRuleTag.false,
  progress: 0,
  totalCondition: 0,
  multiAwardModalVisible: false,
  showTaskCount: defaultListTotalCount,
  refreshConfig: {
    supportRefresh: false,
    refreshPoint: 0,
  },
  layoutInfo: {},
  summaryTask: {
    id: -1,
    title: '',
    desc: '',
    status: TaskStatus.unfinished,
    statusText: '',
    taskType: TaskItemKind.summary,
    statusOrderForDisplay: TaskStatusOrderForDisplay.notFinish,
    guideText: '领VIP',
  },
  activityContext: {
    activityBasicInfo: {
      extraInfo: {
        videoTaskInfo: {
          positionId: 254,
          positionName: 'integral_center_inspire_video',
        },
      },
    },
  },
}

export const everydayChallenge = createModel<RootModel>()({
  state: defaultEverydayChallengeModelState, // initial state
  reducers: {
    reset: () => {
      return defaultEverydayChallengeModelState
    },
    refresh: (state, payload: Partial<EverydayChallengeModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async refreshEffect(payload: Partial<EverydayChallengeModelState>) {
      await dispatch.everydayChallenge.refresh(payload)
    },
    async getAdDPTaskList(payload: { displayedAdDPTaskIds: number[] }) {
      let originListLength: number = -1;
      try {
        let finalList: AdDPTaskItem[] = []
        let isFromCache = false
        let responseId = 0
        const { list: storageList, responseId: storageResponseId } = await adDPTaskListStorage.get()
        console.log('storageList', storageList.length)
        if (storageList.length > 0) {
          originListLength = storageList.length;
          // 有缓存数据，直接用缓存数据
          console.log('🥺🥺🥺🥺🥺 有缓存数据，直接用缓存数据: ')
          finalList = storageList
          isFromCache = true
          responseId = storageResponseId
        } else {
          console.log('🔑🔑🔑🔑🔑 没有缓存或者缓存过期，重新请求数据')
          const res = await requestAdDPTaskList('cashAppTask')
          const list = res?.data || []
          const newResponseId = res.responseId
          console.log('请求到的广告物料的数量', list.length)
          if (list.length > 0) {
            originListLength = list.length;
            finalList = list
            isFromCache = false
            responseId = newResponseId

            // 缓存列表
            await adDPTaskListStorage.set({
              list,
              responseId: newResponseId,
            })
          } else {
            console.log('获取数据为空')
            throw new Error('获取数据为空')
          }
        }
        finalList = await adDPTaskCtrl.filterAvailableAdDPTaskList({ originList: finalList })
        const result: Record<string, any> = { list: finalList, isFromCache, responseId };
        if (originListLength > 0) {
          result.originListLength = originListLength;
        }
        return result;
      } catch (err) {
        console.log('getAdDPTaskList error: ', err)
        return { list: [], isFromCache: false, responseId: 0 }
      }
    },
    async processAdDPTask({
      todayHasShownTaskIds,
      aid,
      taskList,
      showTaskCount,
      totalCondition,
      summaryTaskProgress,
    }: {
      aid: number
      todayHasShownTaskIds?: { normal: number[]; adDP: number[] }
      taskList: TaskListType
      showTaskCount: number
      totalCondition: number
      summaryTaskProgress: number
    }) {
      const videoTask = taskList.find((item) => item.taskType === TaskItemKind.newVideoTask && item.status === TaskStatus.unfinished);
      try {
        console.log({ todayHasShownTaskIds })

        if (todayHasShownTaskIds) {
          console.log('🧲🧲🧲🧲🧲 换一换～')

          const withoutFinishedTaskList = taskList.filter((task) => task.status !== TaskStatus.received && task.taskType !== TaskItemKind.newVideoTask)

          const { true: _AdDPTaskSlots, false: _normalTaskList } = partition(withoutFinishedTaskList, (item) => item.taskType === TaskItemKind.AdDPSlotTask)

          // 如果是换一换逻辑，把展示过的任务放在最后面
          const { true: hasShownNormalTasks, false: notHasShownNormalTasks } = partition(_normalTaskList, (item) => todayHasShownTaskIds.normal.includes(item.id))

          const { true: hasShownAdDPTasks, false: notHasShownAdDPTasks } = partition(_AdDPTaskSlots, (item) => todayHasShownTaskIds.adDP.includes(item.id))

          const sortedNormalTasks = [...notHasShownNormalTasks, ...hasShownNormalTasks]

          const sortedAdDPTasks = [...notHasShownAdDPTasks, ...hasShownAdDPTasks]

          if (notHasShownNormalTasks.length === 0 || notHasShownAdDPTasks.length === 0) {
            rnEnv.isTest() && notHasShownNormalTasks.length === 0 && Toast.info('普通任务换完一轮')
            rnEnv.isTest() && notHasShownAdDPTasks.length === 0 && Toast.info('商业化任务换完一轮')
            await todayHasShownTaskListStorage.removeData({ aid, normalTaskList: notHasShownNormalTasks, AdDPTaskList: notHasShownAdDPTasks })
          }

          console.log(
            JSON.stringify(
              {
                hasShownNormalTasks: hasShownNormalTasks.length,
                notHasShownNormalTasks: notHasShownNormalTasks.length,
                hasShownAdDPTasks: hasShownAdDPTasks.length,
                notHasShownAdDPTasks: notHasShownAdDPTasks.length,
              },
              null,
              2
            )
          )

          taskList = [...sortedAdDPTasks, ...sortedNormalTasks]
        } else {
          console.log('🧨🧨🧨🧨🧨 没有换一换～')
        }

        // 当前用户已展示的任务列表-----没有换一换之前，使用同一个列表
        const { normalTaskIds, adDpTaskIds, normalTaskIdToAdDPTaskIdMap } = await everydayChallengeTaskListStorage.get({
          aid,
        })

        console.log('normalTaskIdToAdDPTaskIdMap = >>>>>>>>> ', normalTaskIdToAdDPTaskIdMap)

        // 拿到广告的插槽
        let { true: AdDPTaskSlots, false: normalTaskList } = partition(taskList, (item) => item.taskType === TaskItemKind.AdDPSlotTask)

        // 拿到展示过的普通任务和未展示的
        const { true: lastDisplayedNormalTaskList, false: lastNotDisplayedNormalTaskList } = partition(normalTaskList.slice(), (item) => normalTaskIds.includes(item.id))

        // 拿到展示过的广告DP任务和未展示的
        const { true: lastDisplayedAdDPTaskList, false: lastNotDisplayedAdDPTaskList } = partition(AdDPTaskSlots.slice(), (item) => adDpTaskIds.includes(item.id))

        AdDPTaskSlots = [...lastDisplayedAdDPTaskList, ...lastNotDisplayedAdDPTaskList]
        normalTaskList = [...lastDisplayedNormalTaskList, ...lastNotDisplayedNormalTaskList].filter(item => item.taskType !== TaskItemKind.newVideoTask) // 激励视频任务不算做普通任务

        // 可用的广告任务列表
        const { list: adDPTaskList, originListLength = 0, responseId } = await dispatch.everydayChallenge.getAdDPTaskList({
          displayedAdDPTaskIds: adDpTaskIds,
        })

        console.log(
          '处理好顺序的AdDPTaskSlots：',
          AdDPTaskSlots.map((slot) => slot.id)
        )
        // 完成的 DP 广告任务数量
        const finishedAdDPTaskLength = AdDPTaskSlots.filter((item) => item.status === TaskStatus.received).length

        // 需要出现的商业化任务的个数 = 任务列表展示的任务总数 - ( 最终奖励所需完成任务数 - 已完成的任务数 - 已经完成的商业化任务数 ) + 2
        console.log({ totalCondition, showTaskCount, summaryTaskProgress, finishedAdDPTaskLength, 'adDpTaskIds.length': adDpTaskIds.length, adDpTaskIds })
        const tempShouldDisplayAdDPTaskCount = adDpTaskIds.length > 1 ? adDpTaskIds.length : showTaskCount - (totalCondition - summaryTaskProgress - finishedAdDPTaskLength) + 2
        const shouldDisplayAdDPTaskCount = tempShouldDisplayAdDPTaskCount >= showTaskCount ? showTaskCount : tempShouldDisplayAdDPTaskCount
        // console.info('debug_tempShouldDisplayAdDPTaskCount', { adDpTaskIds, showTaskCount, totalCondition, summaryTaskProgress, finishedAdDPTaskLength, shouldDisplayAdDPTaskCount });

        // 用广告服务端数据填充插槽
        const replacedAdDPTaskList = AdDPTaskSlots.map((item, index) => {
          let currentAdDPTaskId;
          // 根据上一次映射关系拿到的 AdDPTaskId
          if (normalTaskIdToAdDPTaskIdMap && normalTaskIdToAdDPTaskIdMap[item.id]) {
            currentAdDPTaskId = normalTaskIdToAdDPTaskIdMap[item.id]
          } else {
            // 防止adp任务重复
            const filteredAdDPTaskList = adDPTaskList.filter((item: any) => !Object.values(normalTaskIdToAdDPTaskIdMap ?? {}).includes(item?.adid));
            currentAdDPTaskId = filteredAdDPTaskList?.[index]?.adid
          }

          const targetAdDPTask = currentAdDPTaskId ? adDPTaskList.find((ad) => ad.adid === currentAdDPTaskId) : null

          console.log({ currentAdDPTaskId, targetAdDPTask: targetAdDPTask?.name })

          if (targetAdDPTask) {
            console.log('ad dp task name: ' + targetAdDPTask.name + ' ' + targetAdDPTask.adid, '=>>>>', item.id)
            return {
              ...item,
              title: `${targetAdDPTask.name}`,
              desc: `${rnEnv.isTest() ? `only test env: ${item.id}; ` : ''}${targetAdDPTask.description}`,
              statusText: item.status === TaskStatus.unfinished && targetAdDPTask.buttonText ? targetAdDPTask.buttonText : item.statusText,
              // worth: targetAdDPTask.bonusPoints,
              adDpItemInfo: targetAdDPTask,
              adDpResponseId: responseId,
            }
          } else {
            return null
          }
        }).filter((item) => item !== null) as TaskItemType[]

        // 实际展示的 DP 广告任务数量
        let displayedAdDpTaskList = replacedAdDPTaskList.slice(0, shouldDisplayAdDPTaskCount)
        // console.info('debug_displayedAdDpTaskList', { replacedAdDPTaskList, shouldDisplayAdDPTaskCount, displayedAdDpTaskList });
        let newNormalTaskIdToAdDPTaskIdMap = {}

        displayedAdDpTaskList.forEach((task) => {
          if (task.taskType === TaskItemKind.AdDPSlotTask) {
            newNormalTaskIdToAdDPTaskIdMap[task.id] = task.adDpItemInfo!.adid
          }
        })
        if (!displayedAdDpTaskList.find(item => item.taskType === TaskItemKind.newVideoTask) && videoTask) {
          displayedAdDpTaskList.unshift(videoTask);
          if (displayedAdDpTaskList.length > Math.max(shouldDisplayAdDPTaskCount, 1)) {
            const removeTask = displayedAdDpTaskList.pop();
            if (removeTask) {
              displayedAdDpTaskList = displayedAdDpTaskList.filter(task => task.id !== removeTask.id);
            }
          }
        }
        activateAdMaterialSummaryReport({
          filledCount: displayedAdDpTaskList.filter(item => item?.taskType !== TaskItemKind.newVideoTask).length, // 填充的数量排除激励视频任务
          filteredCount: adDPTaskList.length,
          totalCount: originListLength
        });
        const displayedNormalTaskList = normalTaskList.slice(0, showTaskCount - displayedAdDpTaskList.length)
        console.log({ displayedAdDpTaskList: displayedAdDpTaskList.length, displayedNormalTaskList: displayedNormalTaskList.length })
        // 根据任务状态排序
        const finalTaskList = [...displayedAdDpTaskList, ...displayedNormalTaskList].slice().sort((taskA, taskB) => {
          return taskA.statusOrderForDisplay - taskB.statusOrderForDisplay
        })

        // 存储当前展示的任务
        everydayChallengeTaskListStorage.set({
          aid,
          displayedAdDpTaskList,
          displayedNormalTaskList,
          normalTaskIdToAdDPTaskIdMap: Object.keys(newNormalTaskIdToAdDPTaskIdMap).length > 0 ? newNormalTaskIdToAdDPTaskIdMap : null,
        })

        // 存储当天所有展示过的任务
        todayHasShownTaskListStorage.setHasShownTaskList({
          aid,
          normalTaskList: displayedNormalTaskList,
          AdDPTaskList: displayedAdDpTaskList,
        })
        return finalTaskList
      } catch (err) {
        return taskList
      }
    },
    async updateEveryDayTaskRefreshConfig(payload, rootState) {
      const aidFromFootball = await getEverydayChallengeAidByAB();
      const targetAid = aidFromFootball;
      const response = await postTaskList(targetAid, true);
      const supportRefresh = rootState.everydayChallenge?.refreshConfig?.supportRefresh;
      const _activityContext = lodashGet(response, 'activityContext', {
        activityBasicInfo: {
          refreshPoint: defaultEverydayChallengeModelState.refreshConfig.refreshPoint
        },
      })
      const { refreshPoint } = _activityContext.activityBasicInfo;
      dispatch.everydayChallenge.refresh({
        refreshConfig: {
          refreshPoint,
          supportRefresh
        }
      });
    },
    async getTaskList(payload?: { aid: number; todayHasShownTaskIds?: { normal: number[]; adDP: number[] } }) {
      let finalTaskList: TaskListType = []
      try {
        const goldCoinABValueValue = goldCoinABValue.getValue()
        if (goldCoinABValueValue.groupId === CashConfigExpGroupId.default) return
        const changeTaskABConfig = goldCoinABValueValue.changeTasksId // 拉活任务 AB
        const aidFromFootball = await getEverydayChallengeAidByAB()
        const useV2 = true
        const targetAid = aidFromFootball
        const skipVideoTask = shouldFilterVideoTask()
        const [originRes, whitelist, isPCNewUser] = await Promise.all([postTaskList(targetAid, useV2), getTaskWhitelist(), UserConfig.getIsNewUser()])
        const originTaskListData = originRes.data
        const summaryTask = originTaskListData.summaryTask
        const activityContext = lodashGet(originRes.context, 'activityContext', defaultEverydayChallengeModelState.activityContext)
        const _activityContext = lodashGet(originRes, 'activityContext', {
          ruleParseInfo: [{ tag: defaultEverydayChallengeModelState.activityRuleTag }],
          activityBasicInfo: {
            title: defaultEverydayChallengeModelState.activityTitle,
            refreshPoint: defaultEverydayChallengeModelState.refreshConfig.refreshPoint,
            supportRefresh: defaultEverydayChallengeModelState.refreshConfig.supportRefresh,
            showTaskCount: defaultListTotalCount,
          },
        })

        const activityRuleTag = _activityContext.ruleParseInfo[0].tag
        const { title: activityTitle, refreshPoint, showTaskCount: tempShowTaskCount, supportRefresh } = _activityContext.activityBasicInfo
        const showTaskCount = tempShowTaskCount || defaultListTotalCount
        const totalCondition = summaryTask?.condition ?? 0 // 最终大奖的总条件
        const summaryTaskProgress = (summaryTask?.progress || 0) > totalCondition ? totalCondition : summaryTask?.progress || 0 // 最终大奖的当前进度

        let pointExchangeTask = null
        // 完整的任务列表
        finalTaskList = (
          await processTaskListData({
            originTaskListData,
            skipVideoTask,
            isPCNewUser,
            whitelist,
            customDisplayOrder: customSetTaskItemTaskStatusOrderForDisplay,
            onSpecialTargetCapture: (item) => {
              // code === 111 为积分跳过任务
              if (item.code === 111) {
                pointExchangeTask = item
                return true
              }
              return false
            },
          })
        ).slice()

        if (changeTaskABConfig === CashConfigChangeTaskId.withAdDPTask) {
          //  如果支持换一换则判断成是有拉活广告任务
          finalTaskList = await dispatch.everydayChallenge.processAdDPTask({
            showTaskCount,
            taskList: finalTaskList,
            aid: targetAid,
            todayHasShownTaskIds: payload?.todayHasShownTaskIds,
            summaryTaskProgress,
            totalCondition,
          })
        }

        dispatch.everydayChallenge.refresh({
          activityContext,
          taskList: finalTaskList,
          summaryTask,
          totalCondition,
          activityTitle,
          activityRuleTag,
          progress: summaryTaskProgress,
          pointExchangeTask,
          refreshConfig: {
            supportRefresh: supportRefresh && summaryTaskProgress !== totalCondition && changeTaskABConfig === CashConfigChangeTaskId.withAdDPTask,
            refreshPoint,
          },
          showTaskCount,
        })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'everydayChallenge.getTaskList',
        })
      } finally {
        dispatch.chips.getChipsBalance()
        return finalTaskList;
      }
    },
    async getTaskAward(payload: { taskItem: TaskItemType; stepNos?: number[]; aid: number }) {
      try {
        const { taskItem, stepNos, aid: aidFromPayload } = payload
        const aid = (await getEverydayChallengeAidByAB()) || aidFromPayload
        const res = await getTaskAwardRequestNew(
          {
            taskId: taskItem.id,
            stepNos,
          },
          aid
        )
        const getTaskAwardStatus = lodashGet(res, 'data.status', -2)
        if (getTaskAwardStatus === 0) {
          const awardCount = lodashGet(res, 'data.awardCount', 0)
          const recommendTaskId = lodashGet(res, 'data.pop.taskId', null)
          const recommendAwardLabel = lodashGet(res, 'data.pop.toast', '')
          dispatch.credit.increasePoint(awardCount)
          await dispatch.everydayChallenge.getTaskList({ aid })
          dispatch.everydayChallenge.setTaskReceived({ taskId: taskItem.id })

          if (recommendTaskId) {
            dispatch.taskAwardModal.openModal({
              currentTaskId: taskItem.id,
              awardWorth: awardCount,
              recommendTaskId,
              recommendAwardLabel,
              recommendTaskAid: aid,
            })
          } else {
            Toast.info('领取成功')
          }
          return res
        } else {
          throw new Error('领取失败')
        }
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'everydayChallenge.getTaskAward',
        })
        Toast.info('积分领取失败')
        return null
      }
    },
    async setTaskReceived({ taskId }: { taskId: number }, rootState) {
      try {
        const taskList = rootState.everydayChallenge.taskList
        const newTaskList = taskList.map((task) => {
          if (task.id !== taskId) {
            return { ...task }
          } else {
            return {
              ...task,
              status: TaskStatus.received,
            }
          }
        })
        dispatch.everydayChallenge.refresh({ taskList: [...newTaskList] })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'everydayChallenge.setTaskReceived',
        })
      }
    },
    async hideOneTask({ taskId, aid }: { taskId: number; aid: number }, rootState) {
      try {
        // 如果 aid 不等于配置的任务包 aid 就不处理
        if (aid !== (await getEverydayChallengeAidByAB())) return
        const taskList = rootState.everydayChallenge.taskList
        const newTaskList = taskList.filter((task) => task.id !== taskId)
        dispatch.everydayChallenge.refresh({ taskList: [...newTaskList] })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'everydayChallenge.hideOneTask',
        })
      }
    },
    async toggleMultiAwardsModal({ visible }: { visible: boolean }) {
      dispatch.everydayChallenge.refresh({
        multiAwardModalVisible: visible,
      })
    },
    async handleMultiAwardsSelect(payload: { taskItem: TaskItemType; stepNos?: number[]; aid: number; awards: StepInfoMultiAwardType[] }) {
      try {
        dispatch.everydayChallenge.toggleMultiAwardsModal({ visible: true })
        dispatch.everydayChallenge.refresh({
          currentMultiAwardsInfo: payload,
        })
      } catch (err) {
        console.log(err)
      }
    },
    async multiAwardsReceivedSuccess() {
      try {
        dispatch.everydayChallenge.getTaskList()
        dispatch.credit.getCredit()
        dispatch.goldCoin.getBalance()
      } catch (err) {
        console.log(err)
      }
    },
    async changeTaskList(payload, rootState) {
      const lastTaskList = trimTaskList(rootState.everydayChallenge.taskList);
      try {
        const aidFromFootball = await getEverydayChallengeAidByAB()
        await Promise.all([
          adDPTaskListStorage.set({
            list: [],
            responseId: 0,
            clearAll: true,
          }),
          everydayChallengeTaskListStorage.set({
            aid: aidFromFootball,
            displayedAdDpTaskList: [],
            displayedNormalTaskList: [],
            normalTaskIdToAdDPTaskIdMap: null,
          }),
        ])
        const { hasShownNormalTaskIds: todayHasShownNormalTaskIds, hasShownAdDPTaskIds: todayHasShownAdDPTaskIds } = await todayHasShownTaskListStorage.getHasShownTaskList({
          aid: aidFromFootball,
        })
        const result = await dispatch.everydayChallenge.getTaskList({
          aid: aidFromFootball,
          todayHasShownTaskIds: {
            normal: todayHasShownNormalTaskIds,
            adDP: todayHasShownAdDPTaskIds,
          },
        })
        const newTaskList = trimTaskList(result as TaskListType);
        const same = JSON.stringify(newTaskList) === JSON.stringify(lastTaskList);
        log('debug__exchange_result:', {
          condition: JSON.stringify(newTaskList) === JSON.stringify(newTaskList),
          lastTaskList: lastTaskList,
        })
        if (same) {
          Toast.info('没有更多任务了')
        } else {
          const res = await requestRefreshTaskList(aidFromFootball)
          if (res?.ret !== 0) {
            const toastMsg = res?.msg || '操作失败，请重试'
            Toast.info(toastMsg)
            APMLocalLog.add('换一换请求失败', 'rn_credit_center')
          }
          dispatch.everydayChallenge.updateEveryDayTaskRefreshConfig(null);
        }
      } catch (err) {
        console.log(err)
        Toast.info('换一换失败')
        APMLocalLog.add('换一换失败', 'rn_credit_center' + err)
      } finally {
        dispatch.credit.getCredit()
      }
    },
    async setLayoutInfo(payload: { name: EverydayChallengeGuideStep; info: LayoutRectangle & { radius: number } }, rootState) {
      const { name, info } = payload
      const layoutInfo = {}
      layoutInfo[name] = info

      dispatch.everydayChallenge.refresh({
        layoutInfo: {
          ...rootState.everydayChallenge.layoutInfo,
          ...layoutInfo,
        },
      })
    },
    async judgeShowGuide({ }, rootState) {
      console.log({
        'hasShowOneModalBefore 取反': !rootState.signInInfo.hasShowOneModalBefore,
        'channelUndertake.modalStatus === no need': rootState.channelUndertake.modalStatus === ChannelUndertakeModalStatus.noNeed,
        showTutorial: goldCoinABValue.getValue().showTutorial,
        'showNewUserGuide 取反': !rootState.everydayChallenge.showNewUserGuide,
      })
      if (
        !rootState.signInInfo.hasShowOneModalBefore &&
        rootState.channelUndertake.modalStatus === ChannelUndertakeModalStatus.noNeed &&
        goldCoinABValue.getValue().showTutorial &&
        !rootState.everydayChallenge.showNewUserGuide
      ) {
        console.log('🔐🔐🔐判断要展示引导!!')

        dispatch.everydayChallenge.refresh({
          showNewUserGuide: true,
        })
      } else {
        console.log('📟📟📟判断不要展示引导~~')
      }
    },
    async closeGuide() {
      goldCoinABValue.setValue({
        ...goldCoinABValue.getValue(),
        showTutorial: false,
      })
      dispatch.everydayChallenge.refresh({
        showNewUserGuide: false,
      })
    },
  }),
})
