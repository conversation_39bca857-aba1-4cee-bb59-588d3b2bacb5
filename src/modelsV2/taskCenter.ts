import { createModel } from '@rematch/core'
import getTaskAwardRequestNew from 'servicesV2/getTaskAwardNew'
import {
  TaskItemKind,
  TaskItemType,
  TaskListType,
  TaskStatus,
  TaskStatusOrderForDisplay,
} from 'typesV2/taskList'
import postTaskList from 'servicesV2/postTaskList'
import { RootModel } from '.'
import { Toast } from '@xmly/rn-sdk'
import SentryUtils from 'utilsV2/sentryUtils'

import lodashGet from 'lodash.get'
import UserConfig from '../utilsV2/getIsNewUser'
import { taskListAid } from '../constantsV2'
import getTaskWhitelist from '../utilsV2/getTaskWhitelist'
import shouldFilterVideoTask from '../utilsV2/shouldFilterVideoTask'
import processTaskListData from '../utilsV2/processTaskListData'
import userInfoDetail from '../modulesV2/userInfoDetail'
import { tasks } from '../backData/tasks.js'

export type TaskCenterModelState = {
  taskList: TaskListType
  summaryTask: TaskItemType
  activityContext: object
}

const defaultTaskCenterModelState: TaskCenterModelState = {
  taskList: [],
  summaryTask: {
    id: -1,
    title: '',
    desc: '',
    status: TaskStatus.unfinished,
    statusText: '',
    taskType: TaskItemKind.summary,
    statusOrderForDisplay: TaskStatusOrderForDisplay.notFinish,
  },
  activityContext: {
    activityBasicInfo: {
      extraInfo: {
        videoTaskInfo: {
          positionId: 254,
          positionName: 'integral_center_inspire_video',
        },
      },
    },
  },
}

const _calJson = (serverTasks: any) => {
  let _o: any = {}
  try {
    _o = JSON.parse(serverTasks)
  } catch (err) {
    _o = tasks
  }
  return _o
}

export const taskCenter = createModel<RootModel>()({
  state: defaultTaskCenterModelState, // initial state
  reducers: {
    reset: () => {
      return defaultTaskCenterModelState
    },
    refresh: (state, payload: Partial<TaskCenterModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async refreshEffect(payload: Partial<TaskCenterModelState>) {
      await dispatch.taskCenter.refresh(payload)
    },
    async setTaskList({ fullList }: { fullList: TaskListType }) {
      dispatch.taskCenter.refresh({
        taskList: fullList,
      })
    },
    async setSummaryTask({ summaryTask }: { summaryTask: TaskItemType }) {
      dispatch.taskCenter.refresh({
        summaryTask,
      })
    },
    async getTaskList(payload?: { aid: number }) {
      const xIsLogin = userInfoDetail.getDetail()?.isLogin
      try {
        const targetAid = payload?.aid || taskListAid
        const skipVideoTask = shouldFilterVideoTask()
        const [normalRse, whitelist, isPCNewUser] = await Promise.all([
          postTaskList(targetAid),
          getTaskWhitelist(),
          UserConfig.getIsNewUser(),
        ])
        // const _normalRse = xIsLogin ? normalRse : JSON.parse(normalRse?.data?.[0]?.taskCenter || '{}')
        const _normalRse = xIsLogin
          ? normalRse
          : _calJson(normalRse?.data?.[0]?.taskCenter)

        const originTaskListData = _normalRse.data
        const summaryTask = originTaskListData.summaryTask
        const activityContext = lodashGet(
          _normalRse.context,
          'activityContext',
          defaultTaskCenterModelState.activityContext
        )
        const taskList = await processTaskListData({
          originTaskListData,
          skipVideoTask,
          isPCNewUser,
          whitelist,
        })
        dispatch.taskCenter.refresh({
          taskList,
          activityContext,
          summaryTask,
        })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'taskCenter.getTaskList',
        })
      } finally {
        dispatch.chips.getChipsBalance()
      }
    },
    async getTaskAward(payload: {
      taskItem: TaskItemType
      stepNos?: number[]
      aid: number
    }) {
      try {
        const { taskItem, stepNos, aid } = payload
        const res = await getTaskAwardRequestNew(
          {
            taskId: taskItem.id,
            stepNos,
          },
          aid
        )
        const getTaskAwardStatus = lodashGet(res, 'data.status', -2)
        if (getTaskAwardStatus === 0) {
          const awardCount = lodashGet(res, 'data.awardCount', 0)
          const recommendTaskId = lodashGet(res, 'data.pop.taskId', null)
          const recommendAwardLabel = lodashGet(res, 'data.pop.toast', '')
          dispatch.credit.increasePoint(awardCount)
          await dispatch.taskCenter.getTaskList({ aid })
          dispatch.taskCenter.setTaskReceived({ taskId: taskItem.id })

          if (recommendTaskId) {
            dispatch.taskAwardModal.openModal({
              currentTaskId: taskItem.id,
              awardWorth: awardCount,
              recommendTaskId,
              recommendAwardLabel,
              recommendTaskAid: aid,
            })
          } else {
            Toast.info('领取成功')
          }
          return res
        } else {
          throw new Error('领取失败')
        }
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'taskCenter.getTaskAward',
        })
        Toast.info('积分领取失败')
        return null
      }
    },
    async setTaskReceived({ taskId }: { taskId: number }, rootState) {
      try {
        const taskList = rootState.taskCenter.taskList
        const newTaskList = taskList.map((task) => {
          if (task.id !== taskId) {
            return { ...task }
          } else {
            return {
              ...task,
              status: TaskStatus.received,
            }
          }
        })
        dispatch.taskCenter.setTaskList({ fullList: [...newTaskList] })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'taskCenter.setTaskReceived',
        })
      }
    },
    async hideOneTask({ taskId }: { taskId: number }, rootState) {
      try {
        const taskList = rootState.taskCenter.taskList
        const newTaskList = taskList.filter((task) => task.id !== taskId)
        dispatch.taskCenter.setTaskList({ fullList: [...newTaskList] })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, {
          source: 'taskCenter.hideOneTask',
        })
      }
    },
  }),
})
