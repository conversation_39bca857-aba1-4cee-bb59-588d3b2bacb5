import { createModel } from '@rematch/core'
import { RootModel } from '.'
import rnEnv from '../../rnEnv'
import { API_DEFAULT, Api } from '../constantsV2/apiConfig'
import request, { ResDataType } from '../servicesV2/request'
import requestLotteryActivityAwardInfo from '../servicesV2/requestLotteryActivityAwardInfo'
import { TaskAwardRes } from '../typesV2/taskAward'
import lodashGet from 'lodash.get'
import { TomorrowAwardList, TomorrowAwardStatus } from '../componentsV2/TomorrowAward/types'
import { TaskItemType, TaskListInfoRequestResult, TaskStatus } from '../typesV2/taskList'
import { Toast } from '@xmly/rn-sdk'
import { SelectDrawActivityAwardRule } from '../typesV2/lotteryActivity'
import { tomorrowAwardTaskAID } from '../constantsV2'

export type TomorrowAwardModelState = {
  awardList: TomorrowAwardList
  tomorrowAwardStatus: TomorrowAwardStatus
  todayAwardValue: number
  modalVisible: boolean
  lotteryAwardList: SelectDrawActivityAwardRule[]
  activityType: 'new' | 'old'
  nextTomorrowAwardStatus: TomorrowAwardStatus
}

const defaultTomorrowAwardModelState: TomorrowAwardModelState = {
  awardList: [],
  tomorrowAwardStatus: TomorrowAwardStatus.UNSET,
  todayAwardValue: 0,
  modalVisible: false,
  lotteryAwardList: [],
  activityType: 'old',
  nextTomorrowAwardStatus: TomorrowAwardStatus.UNSET,
}

let tomorrowAwardTaskInfoRef: TaskItemType | null = null
let tempTomorrowAwardStatus: TomorrowAwardStatus = TomorrowAwardStatus.UNSET

export const tomorrowAward = createModel<RootModel>()({
  state: defaultTomorrowAwardModelState, // initial state
  reducers: {
    reset: () => {
      return defaultTomorrowAwardModelState
    },
    refresh: (state, payload: Partial<TomorrowAwardModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
  },
  effects: (dispatch) => ({
    async fetchLotteryActivityAwardInfo() {
      // 获取奖品列表
      const res = await requestLotteryActivityAwardInfo({
        activityId: rnEnv.isOnline() ? 3 : 1,
      })
      if (res) {
        dispatch.tomorrowAward.refresh({
          lotteryAwardList: res.data.drawActivity.selectDrawActivityAwardRules,
        })
      }
    },
    async drawAward({ taskId }: { taskId?: number }) {
      // 抽奖
      if (!taskId) return false
      try {
        const data: any = {
          aid: tomorrowAwardTaskAID(),
          taskId: taskId,
        }

        const res = await request<TaskAwardRes>({
          ...API_DEFAULT,
          url: Api.postAwardNew,
          option: {
            method: 'post',
            headers: {
              'Content-Type': 'application/json',
            },
            data: JSON.stringify(data),
          },
          tip: false,
        })
        const getTaskAwardStatus = lodashGet(res, 'data.status', -2)
        if (res && res.ret === 0 && getTaskAwardStatus === 0) {
          const { tomorrowTaskStatus: refreshedTaskStatus } = await dispatch.tomorrowAward.fetchTomorrowAwardTaskInfo()
          tempTomorrowAwardStatus = refreshedTaskStatus
          // 取awardContext 看是否是新的抽奖礼物
          const newAwardDetailStr = lodashGet(res, 'data.awardContext', '')

          // 解析抽奖礼物的数据
          const awardContext = newAwardDetailStr ? JSON.parse(newAwardDetailStr) : null

          const currentAward =
            awardContext && awardContext.logoPic && awardContext.title
              ? {
                  icon: awardContext.logoPic,
                  label: awardContext.title,
                }
              : {
                  icon: 'https://imagev2.xmcdn.com/storages/fe10-audiofreehighqps/AD/91/GKwRINsGdLeTAAAbvAFmYFP1.png',
                  label: res.data.awardValue + '积分',
                }

          const awardList =
            refreshedTaskStatus === TomorrowAwardStatus.tomorrowCanReceive
              ? [
                  currentAward,
                  {
                    icon: 'https://imagev2.xmcdn.com/storages/3f45-audiofreehighqps/58/F1/GMCoOSQIMRH6AAA4EwIaKm72.png',
                    label: '明日可领',
                  },
                ]
              : [currentAward]

          dispatch.tomorrowAward.refresh({
            awardList,
            todayAwardValue: res.data.awardValue,
            nextTomorrowAwardStatus: refreshedTaskStatus,
          })
          return true
        } else {
          throw new Error('')
        }
      } catch (err) {
        return false
      }
    },
    async fetchTomorrowAwardTaskInfo() {
      let tomorrowTaskStatus = TomorrowAwardStatus.UNSET
      let modalStyle: 'old' | 'new' = 'old'
      try {
        const data = {
          aid: tomorrowAwardTaskAID(),
        }
        const res = await request<TaskListInfoRequestResult>({
          ...API_DEFAULT,
          url: Api.postTaskListV2,
          option: {
            method: 'post',
            data: JSON.stringify(data),
            headers: {
              'Content-Type': 'application/json',
            },
            catchJsonParse: true,
          },
          tip: false,
        })

        const tasks = lodashGet(res, 'data.taskItems', [])
        const tomorrowAwardTaskInfo: TaskItemType | null = tasks[0] || null
        if (tomorrowAwardTaskInfo) {
          switch (tomorrowAwardTaskInfo.status) {
            case TaskStatus.finished:
              tomorrowAwardTaskInfoRef = tomorrowAwardTaskInfo
              tomorrowTaskStatus = TomorrowAwardStatus.canReceive

              break
            case TaskStatus.tomorrowAwardCanReceive:
              tomorrowAwardTaskInfoRef = tomorrowAwardTaskInfo
              tomorrowTaskStatus = TomorrowAwardStatus.tomorrowCanReceive

              break
            default:
              tomorrowAwardTaskInfoRef = null
              tomorrowTaskStatus = TomorrowAwardStatus.nonValid
              break
          }

          if (tomorrowAwardTaskInfo.code === 101) {
            modalStyle = 'new'
          } else {
            modalStyle = 'old'
          }
        } else {
          throw Error('')
        }
      } catch (err) {
        console.log('🥺 why', err)
        tomorrowTaskStatus = TomorrowAwardStatus.nonValid
      } finally {
        return { tomorrowTaskStatus, modalStyle }
      }
    },
    async initTaskStatus() {
      try {
        tomorrowAwardTaskInfoRef = null
        tempTomorrowAwardStatus = TomorrowAwardStatus.UNSET
        const { tomorrowTaskStatus: status, modalStyle } = await dispatch.tomorrowAward.fetchTomorrowAwardTaskInfo()
        dispatch.tomorrowAward.refresh({
          tomorrowAwardStatus: status,
          activityType: modalStyle,
        })
      } catch (err) {}
    },
    async closeModal() {
      try {
        dispatch.tomorrowAward.refresh({
          todayAwardValue: 0,
          modalVisible: false,
          awardList: [],
          tomorrowAwardStatus: tempTomorrowAwardStatus,
        })
      } catch (err) {
        dispatch.tomorrowAward.refresh({
          todayAwardValue: 0,
          modalVisible: false,
          awardList: [],
          tomorrowAwardStatus: TomorrowAwardStatus.UNSET,
        })
      }
    },
    async openModal({ tomorrowAwardStatus }: { tomorrowAwardStatus: TomorrowAwardStatus }) {
      try {
        if (tomorrowAwardStatus === TomorrowAwardStatus.canReceive) {
          const drawAwardStatus = await dispatch.tomorrowAward.drawAward({
            taskId: tomorrowAwardTaskInfoRef?.id,
          })
          if (drawAwardStatus) {
            dispatch.tomorrowAward.refresh({ modalVisible: true })
            // 刷新资产数据
            dispatch.credit.getCredit()
            dispatch.goldCoin.getBalance()
            dispatch.chips.getChipsBalance()
          } else {
            Toast.info('开奖失败')
          }
        } else {
          tempTomorrowAwardStatus = TomorrowAwardStatus.tomorrowCanReceive
          dispatch.tomorrowAward.refresh({ modalVisible: true })
        }
      } catch (err) {
        console.log(err)
      }
    },
    async judgeTomorrowCanReceive() {
      try {
        const { tomorrowTaskStatus, modalStyle } = await dispatch.tomorrowAward.fetchTomorrowAwardTaskInfo()

        if (tomorrowTaskStatus === TomorrowAwardStatus.tomorrowCanReceive && modalStyle === 'new') {
          dispatch.tomorrowAward.openModal({
            tomorrowAwardStatus: tomorrowTaskStatus,
          })
          return true
        } else {
          return false
        }
      } catch (err) {
        return false
      }
    },
  }),
})
