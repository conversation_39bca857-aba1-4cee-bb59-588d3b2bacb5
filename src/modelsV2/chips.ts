import { createModel } from '@rematch/core'
import SentryUtils from 'utilsV2/sentryUtils'
import { RootModel } from '.'
import { ChipsAID } from '../constantsV2'
import requestChipsBalance from '../servicesV2/requestChipsBalance'

export type ChipsModelState = {
  chipsBalance: number
}

const defaultChipsModelState = {
  chipsBalance: 0,
}

export const chips = createModel<RootModel>()({
  state: defaultChipsModelState, // initial state
  reducers: {
    reset: () => {
      return defaultChipsModelState
    },
    refresh: (state, payload: Partial<ChipsModelState>) => {
      return {
        ...state,
        ...payload,
      }
    },
    // add: (state, payload: number) => {
    //   return {
    //     ...state,
    //     chipsBalance: Number(state.chipsBalance) + payload,
    //   }
    // },
  },
  effects: (dispatch) => ({
    async getChipsBalance(payload?: { init?: boolean }) {
      try {
        // if (goldCoinABValue.getValue().groupId !== CashConfigExpGroupId.default) {
        const res = await requestChipsBalance({ id: ChipsAID() })
        if (res?.ret === 0) {
          dispatch.chips.refresh({
            chipsBalance: Number(res.data.accumulatedTotalDays),
          })
        } else {
          dispatch.chips.refresh({
            chipsBalance: 0,
          })
        }
        // }
      } catch (err) {
        console.log(err)
        dispatch.chips.refresh({
          chipsBalance: 0,
        })
        SentryUtils.captureException(err, {
          source: 'chips.getChipsBalance',
        })
      }
    },
  }),
})
