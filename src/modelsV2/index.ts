import { Models } from '@rematch/core'
import { signInInfo } from './signInInfo'
import { credit } from './credit'
import { taskCenter } from './taskCenter'
import { commodity } from './commodity'
import { topSection } from './topSection'
import { monthlyTicket } from './monthlyTicket'
import { thirdpartyTask } from './thirdpartyTask'
import { page } from './page'
import { notification } from './notification'
import { taskAwardModal } from './taskAwardModal'
import { everydayChallenge } from './everydayChallenge'
import { tomorrowAward } from './tomorrowAward'
import { channelUndertake } from './channelUndertake'
import { goldCoin } from './goldCoin'
import { chips } from './chips'
import { newUserSignIn } from './newUserSignIn'

export interface RootModel extends Models<RootModel> {
  everydayChallenge: typeof everydayChallenge
  taskCenter: typeof taskCenter
  credit: typeof credit
  signInInfo: typeof signInInfo
  commodity: typeof commodity
  topSection: typeof topSection
  thirdpartyTask: typeof thirdpartyTask
  page: typeof page
  monthlyTicket: typeof monthlyTicket
  notification: typeof notification
  taskAwardModal: typeof taskAwardModal
  tomorrowAward: typeof tomorrowAward
  channelUndertake: typeof channelUndertake
  goldCoin: typeof goldCoin
  chips: typeof chips
  newUserSignIn: typeof newUserSignIn
}

export const models: RootModel = {
  newUserSignIn,
  taskCenter,
  goldCoin,
  everydayChallenge,
  credit,
  signInInfo,
  commodity,
  topSection,
  thirdpartyTask,
  page,
  monthlyTicket,
  tomorrowAward,
  notification,
  taskAwardModal,
  channelUndertake,
  chips,
}
