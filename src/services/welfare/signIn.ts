import { API_ADSE } from 'constantsV2/apiConfig';
import { encryptByType, getXuidTicket } from 'utilsV2/native';
import { FallbackReqType } from 'constants/ad';
import { XUID_ticketConfig } from 'constantsV2';
import request, { ResDataType } from 'servicesV2/request';
import userInfoDetail from 'modulesV2/userInfoDetail'
import uuid from 'utilsV2/uuid';

// 0-未完成 1-普通签到 2-广告签到 3-开奖 4-已补签
export enum SignInStatus {
  INCOMPLETE = 0,  // 未完成
  COMPLETED = 1,   // 已完成
  AD_COMPLETED = 2, // 广告签到
  LOTTERY = 3, // 已开奖
  BACKDATED = 4, // 已补签
}

interface SignInAwardInfo {
  day: number;
  today: boolean;
  status: SignInStatus;
  award: number;        // 普通奖励
  upgradeAward: number; // 升级奖励
  alreadyAward?: number;// 已领取金额
  hasLottery?: boolean; // 是否有抽奖
}

interface SignInInfo {
  success: boolean;
  code: number;
  title: string;
  enableResignIn: boolean; // 是否可以续签
  enableWakeUp: boolean; // 是否可以开启唤端
  awardInfo: SignInAwardInfo[]; // 七天的奖励信息
}

interface ReSignInParams {
  adId?: number;
  adResponseId?: number;
  ecpm?: string;
  encryptType?: string;
  fallbackReq: FallbackReqType;
  reSignInDay: number;
  retry?: number;
}

interface ReSignInResult {
  success: boolean;
  code: number;
  retry: boolean;
  toast: string;
}

const MAX_RETRY_COUNT = 3;

/**
 * 查询签到信息
 */
export const querySignInInfo = async (): Promise<ResDataType<SignInInfo> | undefined> => {
  return request<SignInInfo>({
    ...API_ADSE,
    url: `incentive/ting/welfare/querySignInInfo/ts-${Date.now()}`,
  });
};

/**
 * 补签
 */
export const reSignIn = async (params: ReSignInParams, retryCount = 0): Promise<ResDataType<ReSignInResult>> => {
  const { ecpm = '', encryptType = '', reSignInDay = 0 } = params;
  const uid = userInfoDetail.getDetail().uid || -1;
  const requestId = uuid();
  const ts = Date.now();
  const ticket = await getXuidTicket({
    businessId: XUID_ticketConfig.coinTask.businessId,
    scene: XUID_ticketConfig.coinTask.scene,
    uid,
  });

  const { checkData: signature } = await encryptByType('md5', {
    checkData: `${requestId}&${uid}&${reSignInDay}&${ecpm}&${encryptType}&${ts}&${retryCount}&${XUID_ticketConfig.coinTask.salt}`
  });

  const data = {
    requestId,
    ts,
    ticket,
    signature,
    retry: retryCount,
    fallbackReq: params.fallbackReq,
    reSignInDay
  };

  // 添加可选参数
  const assignProps = ['adId', 'adResponseId', 'ecpm', 'encryptType'];
  Object.assign(data,
    Object.fromEntries(
      assignProps
        .filter(key => params[key as keyof ReSignInParams] !== undefined)
        .map(key => [key, params[key as keyof ReSignInParams]])
    )
  );

  try {
    const response = await request<ReSignInResult>({
      ...API_ADSE,
      url: `incentive/ting/welfare/reSignIn/ts-${ts}`,
      option: {
        method: 'post',
        data: JSON.stringify(data),
        headers: {
          'Content-Type': 'application/json',
        },
      }
    });

    if (response?.data?.retry && retryCount < MAX_RETRY_COUNT) {
      return reSignIn(params, retryCount + 1);
    }

    return response as ResDataType<ReSignInResult>;
  } catch (error) {
    if (retryCount < MAX_RETRY_COUNT) {
      return reSignIn(params, retryCount + 1);
    }
    throw error;
  }
};

