import request, { ResDataType } from '../../servicesV2/request';
import { API_ADSE } from 'constantsV2/apiConfig';

interface AdTaskItem {
  coins: number;
  status: number; // 0-未领取, 1-已领取
}

interface AdTaskResponse {
  success: boolean;
  code: number;
  list: AdTaskItem[];
}

/**
 * 查询广告任务列表
 */
export const queryAdTask = async (): Promise<ResDataType<AdTaskResponse> | undefined> => {
  return request<AdTaskResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryAdTask/ts-${Date.now()}`,
  });
}; 