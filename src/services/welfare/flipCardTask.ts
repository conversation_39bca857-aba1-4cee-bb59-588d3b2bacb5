import request, { ResDataType } from '../../servicesV2/request';
import { API_ADSE } from 'constantsV2/apiConfig';
import userInfoDetail from 'modulesV2/userInfoDetail';
import { encryptByType, getXuidTicket } from 'utilsV2/native';
import { XUID_ticketConfig } from 'constantsV2';
import uuid from 'utilsV2/uuid';
import { assignFields } from 'utils/assign';
import { FallbackReqType } from 'constants/ad';

export enum FlipCardStatus {
  INIT = 0,    // 初始状态
  AVAILABLE = 1, // 可领取
  COMPLETED = 2, // 已完成
  RECEIVED = 3, // 已领取
}

interface FlipCardInfo {
  success: boolean;
  code: number;
  status: FlipCardStatus;
  title: string;
  subTitle: string;
  currentCoins: number;
}

interface FlipCardParams {
  position: number;
  adId?: number;
  adResponseId?: number;
  ecpm?: string;
  encryptType?: string;
  fallbackReq?: FallbackReqType;
}

interface FlipCardResult {
  success: boolean;
  code: number;
  toast: string;
  retry: boolean;
  curCoins: number;
  msg?: string;
}

const MAX_RETRY_COUNT = 3;

/**
 * 查询翻卡任务信息
 */
export const queryFlipCardInfo = async (): Promise<ResDataType<FlipCardInfo> | undefined> => {
  return request<FlipCardInfo>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryCardFlipInfo/ts-${Date.now()}`,
  });
};

export const flipCard = async (params: FlipCardParams, retryCount = 0): Promise<ResDataType<FlipCardResult>> => {
  const { position, ecpm = '', encryptType = '' } = params;
  const uid = userInfoDetail.getDetail().uid || -1;
  const requestId = uuid();
  const ts = Date.now();
  const ticket = await getXuidTicket({
    businessId: XUID_ticketConfig.coinTask.businessId,
    scene: XUID_ticketConfig.coinTask.scene,
    uid,
  });

  const { checkData: signature } = await encryptByType('md5', {
    checkData: `${requestId}&${uid}&${position}&${ecpm}&${encryptType}&${retryCount}&${ts}&${XUID_ticketConfig.coinTask.salt}`
  });

  const data = {
    requestId,
    position,
    ts,
    ticket,
    signature,
    retry: retryCount,
    fallbackReq: params.fallbackReq,
  };
  assignFields(data, params, ['adId', 'adResponseId', 'ecpm', 'encryptType']);

  try {
    const response = await request<FlipCardResult>({
      ...API_ADSE,
      url: `incentive/ting/welfare/flipCard/ts-${ts}`,
      option: {
        method: 'post',
        data: JSON.stringify(data),
        headers: {
          'Content-Type': 'application/json',
        },
      }
    });

    if (response?.data?.retry && retryCount < MAX_RETRY_COUNT) {
      return flipCard(params, retryCount + 1);
    }

    return response as ResDataType<FlipCardResult>;
  } catch (error) {
    if (retryCount < MAX_RETRY_COUNT) {
      return flipCard(params, retryCount + 1);
    }
    throw error;
  }
}; 