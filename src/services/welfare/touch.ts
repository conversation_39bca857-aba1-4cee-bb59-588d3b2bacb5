import request, { ResDataType } from '../../servicesV2/request';
import { API_ADSE } from 'constantsV2/apiConfig';
import { DailyTaskType } from './dailyTask';

export interface TouchTaskInfo {
  success: boolean;
  code: number;
  calmSeconds: number;
  type: DailyTaskType;
  title: string;
  icon: string;
  dynamicIcon: string;
}


/**
 * 查询悬浮touch接口
 */
export const queryTouchTask = async (): Promise<ResDataType<TouchTaskInfo> | undefined> => {
  return request<TouchTaskInfo>({
    ...API_ADSE,
    url: `incentive/ting/welfare/querySuspendTouchInfo/ts-${Date.now()}`,
  });
}; 