import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from 'servicesV2/request';

interface AnnoncementResult {
    success: boolean;
    code: number;
    valid: boolean;
    content: string;
    msg: string;
    link?: string;
}

export const queryAnnouncement = async (uid: number): Promise<ResDataType<AnnoncementResult> | undefined> => {
    return request<AnnoncementResult>({
        ...API_ADSE,
        url: `incentive/ting/welfare/queryAnnouncement/ts-${Date.now()}?uid=${uid}`,
    });
};