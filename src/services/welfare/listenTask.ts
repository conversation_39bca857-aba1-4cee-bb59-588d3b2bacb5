import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from 'servicesV2/request';

// export type TaskStatus = 0 | 1 | 2; // 0: 未完成, 1: 待领取, 2: 已领取

export enum TaskStatus {
  UNFINISHED = 0, // 未完成
  PENDING = 1,  // 待领取
  CLAIMED = 2  // 已领取
} 

export interface ListenTaskInfo {
  success: boolean,
  currentStep: number;
  stepInfo: {
    condition: string;
    amount: number;
    status: TaskStatus;
  }[];
  totalCoins: number;
  listenDuration: number; // 当前已听时长（秒）
  nextRewardTime: number; // 下一个奖励所需时长（秒）
  title: string;
  btnText: string;
  status: TaskStatus; // 添加任务整体状态
}

export const queryListenTaskInfo = async (): Promise<ResDataType<ListenTaskInfo> | undefined> => {
  return request<ListenTaskInfo>({
      ...API_ADSE,
      url: `incentive/ting/welfare/queryListenTaskInfo/ts-${Date.now()}`,
    });
};
