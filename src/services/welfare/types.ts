export interface BaseRecord {
  title: string;
  createTime: string;
  coins?: string;
  amount?: string;
}

export interface GoldCoinHistoryItem extends BaseRecord {
  coins: string;
}

// 0-初始化 1-处理中 2-成功 3-失败
export enum WithdrawStatus {
  INIT = 0,
  PROCESSING = 1,
  SUCCESS = 2,
  FAILED = 3,
}

export interface WithdrawRecord extends BaseRecord {
  amount: string;
  status: WithdrawStatus;
}

export interface BannerCard {
  title: string;
  subTitle: string;
  coins: number;
  consumeType: number; // 1-金币换现金 2-金币换时长
}

export interface GoldCoinHistoryResponse {
  success: boolean;
  code: number;
  hasMore: boolean;
  curIndex: string;
  transactionType: number;
  list: GoldCoinHistoryItem[];
}

export interface QueryGoldCoinPageResponse {
  success: boolean;
  code: number;
  coins: number;
  enableWithDraw: boolean;
  bannerCards: BannerCard[];
  goldCoinHistory: GoldCoinHistoryResponse;
}

export interface ExchangeResponse {
  success: boolean;
  code: number;
  retry: boolean;
  toast: string;
}

export interface CashFlowItem {
  id: number;
  type: number;
  date: string;
  comment: string;
  delta: string;
  createdAt: string;
  updatedAt: string;
  withdrawStatus: number;
}

export interface CashFlowResponse {
  ret: number;
  msg: string | null;
  data: CashFlowItem[];
}