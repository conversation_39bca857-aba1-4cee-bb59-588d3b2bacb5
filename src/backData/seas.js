export const seas = {
  "cardList": [
      {
          "body": [
              {
                  "cover": "https://imagev2.xmcdn.com/storages/f0d3-audiofreehighqps/32/5F/GKwRIasIeF3YAAAOSgIyspkD.png",
                  "landingPage": "https://m.ximalaya.com/gatekeeper/h5-credit-center-lottery/everyday?_full_with_transparent_bar=1",
                  "title": "抽大奖"
              },
              {
                  "cover": "https://imagev2.xmcdn.com/storages/956b-audiofreehighqps/B6/08/GMCoOSMIeF1zAAASQwIysm4W.png",
                  "landingPage": "https://m.ximalaya.com/gatekeeper/flip-game-h5?theme=2&_full_with_transparent_bar=1",
                  "title": "免费听"
              },
              {
                  "cover": "https://imagev2.xmcdn.com/storages/a8c5-audiofreehighqps/7C/41/GKwRIMAIeF10AAANIQIysm77.png",
                  "landingPage": "iting://open?msg_type=94&bundle=rn_benefits_conversion&goodsShelfName=换会员&goodsShelfId=2",
                  "title": "换会员"
              },
              {
                  "cover": "https://imagev2.xmcdn.com/storages/ca12-audiofreehighqps/92/A6/GKwRIDoIjE9tAAAVhwI6A4bL.png",
                  "landingPage": "rn://CommodityList?pageId=21&pageTitle=0元购",
                  "title": "0元购"
              },
              {
                  "cover": "https://imagev2.xmcdn.com/storages/0e4c-audiofreehighqps/22/1F/GKwRIDoIgnw-AABZHAI2b84K.png",
                  "landingPage": "iting://open?msg_type=94&bundle=rn_benefits_conversion&goodsShelfName=换道具&goodsShelfId=3",
                  "title": "换道具"
              },
              {
                  "cover": "https://imagev2.xmcdn.com/storages/ffda-audiofreehighqps/F0/AA/GKwRIW4IeF11AAAIAAIysm9Y.png",
                  "landingPage": "rn://CommodityList?pageId=1&pageTitle=换实物",
                  "title": "换实物"
              },
              {
                  "cover": "https://imagev2.xmcdn.com/storages/a6cb-audiofreehighqps/4F/1C/GKwRIaIIgnvqAABfOgI2b54G.png",
                  "landingPage": "iting://open?msg_type=94&bundle=rn_benefits_conversion&goodsShelfName=学英语&goodsShelfId=1",
                  "title": "学英语"
              }
          ],
          "cardType": "square",
          "hasMore": false,
          "header": {
              "title": "积分当钱花"
          }
      }
  ],
  "code": 1,
  "message": "ok"
}