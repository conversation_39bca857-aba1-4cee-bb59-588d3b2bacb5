export const signdays = {
  "ret": 0,
  "msg": null,
  "data": {
      "activityId": 87,
      "title": "连签7天得560积分、33点成长值及免费会员",
      "desc": "连签7天得560积分、限免热门专辑及免费会员",
      "tag": null,
      "guideLink": null,
      "isTurnOn": true,
      "signInUserInfo": {
          "roundId": 15,
          "period": 7,
          "continueDay": 1,
          "totalDay": 47,
          "signInStatus": 2,
          "lastSignInTs": 1692598513866,
          "todaySignInDay": 1
      },
      "signInUserAwards": [
          {
              "day": 1,
              "name": "20",
              "desc": null,
              "logo": "https://imagev2.xmcdn.com/storages/6b60-audiofreehighqps/F1/76/GMCoOSAIN8NRAAAP4gIcJpXm.png",
              "unsignedLogo": "https://imagev2.xmcdn.com/storages/6b60-audiofreehighqps/F1/76/GMCoOSAIN8NRAAAP4gIcJpXm.png",
              "context": {
                  "widgetIcon": "https://imagev2.xmcdn.com/storages/4697-audiofreehighqps/DB/B7/GMCoOSQIOPq_AAAKTAIckKFF.png",
                  "widgetLabel": "20积分",
                  "maskText": "",
                  "link": "https://m.ximalaya.com/gatekeeper/h5-credit-center-lottery/everyday?_full_with_transparent_bar=1",
                  "btnText": "去抽奖赢积分",
                  "btnTextForAB": "去抽奖赢积分",
                  "linkForAB": "https://m.ximalaya.com/gatekeeper/h5-credit-center-lottery/everyday?_full_with_transparent_bar=1",
                  "maskLogo": "",
                  "posterMaskDescTitle": "",
                  "isGift": false,
                  "awards": [
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/fe10-audiofreehighqps/AD/91/GKwRINsGdLeTAAAbvAFmYFP1.png",
                          "label": "20积分"
                      }
                  ]
              },
              "isReal": false,
              "awardStatus": 2
          },
          {
              "day": 2,
              "name": "月票",
              "desc": null,
              "logo": "https://imagev2.xmcdn.com/storages/c5ad-audiofreehighqps/39/BB/GMCoOSQIN8NTAAAKigIcJpbE.png",
              "unsignedLogo": "https://imagev2.xmcdn.com/storages/c5ad-audiofreehighqps/39/BB/GMCoOSQIN8NTAAAKigIcJpbE.png",
              "context": {
                  "widgetIcon": "https://imagev2.xmcdn.com/storages/39c1-audiofreehighqps/43/20/GKwRIasIOPrFAAAIyQIckKON.png",
                  "widgetLabel": "月票",
                  "maskText": "",
                  "link": "iting://open?msg_type=365",
                  "btnText": "去给作品投月票",
                  "btnTextForAB": "去给作品投月票",
                  "linkForAB": "iting://open?msg_type=365",
                  "maskLogo": "",
                  "posterMaskDescTitle": "",
                  "isGift": false,
                  "awards": [
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/0684-audiofreehighqps/F4/30/GMCoOSMHQ7w8AAA31wHIfPkV.png",
                          "label": "月票"
                      }
                  ]
              },
              "isReal": false,
              "awardStatus": 0
          },
          {
              "day": 3,
              "name": "50",
              "desc": null,
              "logo": "https://imagev2.xmcdn.com/storages/fab0-audiofreehighqps/46/27/GKwRIDoGd2V1AAAQiAFn0NaB.png",
              "unsignedLogo": "https://imagev2.xmcdn.com/storages/fab0-audiofreehighqps/46/27/GKwRIDoGd2V1AAAQiAFn0NaB.png",
              "context": {
                  "widgetIcon": "https://imagev2.xmcdn.com/storages/54ba-audiofreehighqps/82/D5/GMCoOSQIOPraAAANUQIckKqn.png",
                  "widgetLabel": "限免专辑",
                  "link": "https://pages.ximalaya.com/mkt/act/0ca832f4ebc7b042",
                  "btnText": "立即领取",
                  "isGift": true,
                  "btnTextForAB": "马上领取",
                  "linkForAB": "https://pages.ximalaya.com/mkt/act/0ca832f4ebc7b042",
                  "awards": [
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/fe10-audiofreehighqps/AD/91/GKwRINsGdLeTAAAbvAFmYFP1.png",
                          "label": "积分"
                      },
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/b106-audiofreehighqps/22/9E/GKwRIaIGdLeTAAAe8gFmYFQu.png",
                          "label": "限免专辑"
                      }
                  ]
              },
              "isReal": false,
              "awardStatus": 0
          },
          {
              "day": 4,
              "name": null,
              "desc": null,
              "logo": "https://imagev2.xmcdn.com/storages/f1ac-audiofreehighqps/2C/8D/GKwRIRwGx-rIAAATtgGQLKOW.png",
              "unsignedLogo": "https://imagev2.xmcdn.com/storages/4207-audiofreehighqps/79/88/GKwRIUEFqcPyAAAMugEJ7v4z.png",
              "context": {
                  "widgetIcon": "https://imagev2.xmcdn.com/storages/3126-audiofreehighqps/FE/3E/GKwRIJEIOPrSAAAUkwIckKeV.png",
                  "widgetLabel": "直播道具",
                  "maskText": "直播道具奖励",
                  "link": "iting://open?msg_type=354&type=1&id=4790",
                  "btnText": "去直播间赠送小心心",
                  "btnTextForAB": "去直播间赠送小心心",
                  "linkForAB": "iting://open?msg_type=354&type=1&id=4790",
                  "maskLogo": "https://imagev2.xmcdn.com/storages/9666-audiofreehighqps/67/E7/GMCoOSIGSuNfAAACkgFMZxAd.png",
                  "isGift": false,
                  "awards": [
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/fe10-audiofreehighqps/AD/91/GKwRINsGdLeTAAAbvAFmYFP1.png",
                          "label": "积分"
                      },
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/f1ac-audiofreehighqps/2C/8D/GKwRIRwGx-rIAAATtgGQLKOW.png",
                          "label": "小心心"
                      }
                  ]
              },
              "isReal": false,
              "awardStatus": 0
          },
          {
              "day": 5,
              "name": "月票",
              "desc": null,
              "logo": "https://imagev2.xmcdn.com/storages/c5ad-audiofreehighqps/39/BB/GMCoOSQIN8NTAAAKigIcJpbE.png",
              "unsignedLogo": "https://imagev2.xmcdn.com/storages/c5ad-audiofreehighqps/39/BB/GMCoOSQIN8NTAAAKigIcJpbE.png",
              "context": {
                  "widgetIcon": "https://imagev2.xmcdn.com/storages/39c1-audiofreehighqps/43/20/GKwRIasIOPrFAAAIyQIckKON.png",
                  "widgetLabel": "月票",
                  "maskText": "",
                  "link": "iting://open?msg_type=365",
                  "btnText": "去给作品投月票",
                  "btnTextForAB": "去给作品投月票",
                  "linkForAB": "iting://open?msg_type=365",
                  "maskLogo": "",
                  "posterMaskDescTitle": "",
                  "isGift": false,
                  "awards": [
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/0684-audiofreehighqps/F4/30/GMCoOSMHQ7w8AAA31wHIfPkV.png",
                          "label": "月票"
                      }
                  ]
              },
              "isReal": false,
              "awardStatus": 0
          },
          {
              "day": 6,
              "name": "100",
              "desc": null,
              "logo": "https://imagev2.xmcdn.com/storages/d15f-audiofreehighqps/7A/48/GMCoOSMIN8NSAAAQQgIcJpaS.png",
              "unsignedLogo": "https://imagev2.xmcdn.com/storages/d15f-audiofreehighqps/7A/48/GMCoOSMIN8NSAAAQQgIcJpaS.png",
              "context": {
                  "widgetIcon": "https://imagev2.xmcdn.com/storages/4697-audiofreehighqps/DB/B7/GMCoOSQIOPq_AAAKTAIckKFF.png",
                  "widgetLabel": "100积分",
                  "maskText": "",
                  "link": "https://m.ximalaya.com/gatekeeper/h5-credit-center-lottery/everyday?_full_with_transparent_bar=1",
                  "btnText": "去抽奖赢积分",
                  "btnTextForAB": "去抽奖赢积分",
                  "linkForAB": "https://m.ximalaya.com/gatekeeper/h5-credit-center-lottery/everyday?_full_with_transparent_bar=1",
                  "maskLogo": "",
                  "posterMaskDescTitle": "",
                  "isGift": false,
                  "awards": [
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/fe10-audiofreehighqps/AD/91/GKwRINsGdLeTAAAbvAFmYFP1.png",
                          "label": "100积分"
                      }
                  ]
              },
              "isReal": false,
              "awardStatus": 0
          },
          {
              "day": 7,
              "name": "1天VIP",
              "desc": "1天VIP",
              "logo": "https://imagev2.xmcdn.com/storages/19c1-audiofreehighqps/1C/E6/GMCoOSQGd2V2AAAKGwFn0NdM.png",
              "unsignedLogo": "https://imagev2.xmcdn.com/storages/19c1-audiofreehighqps/1C/E6/GMCoOSQGd2V2AAAKGwFn0NdM.png",
              "context": {
                  "widgetIcon": "https://imagev2.xmcdn.com/storages/7540-audiofreehighqps/4B/91/GMCoOSEIOQN5AAAI0QIclEYU.png",
                  "widgetLabel": "VIP",
                  "maskText": "",
                  "link": "https://m.ximalaya.com/gatekeeper/invite-friend-v3?_full_with_transparent_bar=1&channelSrc=sign_in_sevendays",
                  "btnText": "邀请好友赚VIP",
                  "btnTextForAB": "邀请好友赚VIP",
                  "linkForAB": "https://m.ximalaya.com/gatekeeper/invite-friend-v3?_full_with_transparent_bar=1&channelSrc=sign_in_sevendays",
                  "isGift": false,
                  "maskLogo": "",
                  "posterMaskDescTitle": "",
                  "awards": [
                      {
                          "icon": "https://imagev2.xmcdn.com/storages/1743-audiofreehighqps/46/E2/GKwRIW4GfeKsAAAZJwFqt5-h.png",
                          "label": "1天VIP"
                      }
                  ]
              },
              "isReal": false,
              "awardStatus": 0
          }
      ],
      "manualEndTime": 0,
      "currentRoundPeriod": 7,
      "patternType": 0,
      "guideStatus": 2
  },
  "context": {
      "currentUser": null,
      "basicRequestContext": null,
      "activityContext": {
          "activityBasicInfo": null,
          "ruleParseInfo": [
              {
                  "ruleType": 12,
                  "tag": "noVip"
              },
              {
                  "ruleType": 18,
                  "tag": "2"
              },
              {
                  "ruleType": 3,
                  "tag": "new"
              },
              {
                  "ruleType": 23,
                  "tag": "1"
              }
          ]
      }
  }
} 