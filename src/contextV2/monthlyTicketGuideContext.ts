import React from 'react'
import { LayoutRectangle } from 'react-native'
import { MonthlyTicketGuideContextValue, MonthlyTicketGuideStatus, MonthlyTicketGuideStep } from '../typesV2/monthlyTicketGuide'

export default React.createContext<MonthlyTicketGuideContextValue>({
  layoutInfo: {},
  hasShowMonthlyTicketGuide: false,
  setHasShowMonthlyTicketGuide: (has: boolean) => {},
  setLayoutInfo: (name: MonthlyTicketGuideStep | MonthlyTicketGuideStep[], info: LayoutRectangle & { radius: number }) => {},
  setMonthlyTicketIconLayoutInfo: (info: LayoutRectangle) => {},
  guideStatus: MonthlyTicketGuideStatus.noNeed,
  monthlyTicketIconLayoutInfo: null,
  setGuideStatus: (status: MonthlyTicketGuideStatus) => {},
})
