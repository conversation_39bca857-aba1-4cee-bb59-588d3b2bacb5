import React from 'react'
import { defaultGoldCoinABConfig } from '../constantsV2'

import { GrayScaleContextValue } from '../typesV2/grayScaleContext'

const GrayScaleContext = React.createContext<GrayScaleContextValue>({
  isNew: true,
  monthlyTicketSignInInfo: null,
  showMonthlyTicketGuide: false,
  isOldTypeMonthlyTicket: false,
  goldCoinABConfig: defaultGoldCoinABConfig,
  showGoldCoinModule: false,
})

export default GrayScaleContext
