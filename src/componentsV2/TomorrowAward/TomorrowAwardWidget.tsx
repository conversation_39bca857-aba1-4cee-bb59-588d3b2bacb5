import React, { useEffect } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { View, StyleSheet, Image } from 'react-native'
import isEqual from 'lodash.isequal'
import { TomorrowAwardStatus } from './types'
import xmlog from '../../utilsV2/xmlog'
import { textAlignMiddleStyle } from '../../constantsV2/layout'
import Movable from '@xmly/rn-movable'
import { width } from '../../constantsV2/dimensions'

type Props = {
  onPress: () => void
  tomorrowAwardStatus: TomorrowAwardStatus
}

const styles = StyleSheet.create({
  wrapper: {
    width: 52,
    height: 56,
  },
  image: {
    width: 52,
    height: 52,
  },
  labelWrapper: {
    backgroundColor: '#ff4444',
    width: 52,
    height: 18,
    borderRadius: 241,
    alignItems: 'center',
    justifyContent: 'center',
    bottom: 0,
    position: 'absolute',
  },
  labelText: {
    fontSize: 10,
    color: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    ...textAlignMiddleStyle,
    fontWeight: 'bold',
  },
})

const source = {
  uri: 'https://imagev2.xmcdn.com/storages/fdb8-audiofreehighqps/3C/25/GKwRIJIIXfS2AAIITQIpD46N.gif',
}

const TomorrowAwardWidget: React.FC<Props> = ({
  onPress,
  tomorrowAwardStatus,
}) => {
  const label =
    tomorrowAwardStatus === TomorrowAwardStatus.tomorrowCanReceive
      ? '明日开奖'
      : tomorrowAwardStatus === TomorrowAwardStatus.canReceive
      ? '立即开奖'
      : ''

  useEffect(() => {
    // 任务中心-隔日领奖活动入口  控件曝光
    xmlog.event(53955, 'slipPage', {
      currPage: '任务中心',
      status: label,
    })
  }, [])

  if (label) {
    return (
      <Movable initPostion={{ left: width - 82, top: 280 }} padding={10}>
        <Touch style={styles.wrapper} onPress={onPress}>
          <Image
            source={source}
            style={styles.image}
            resizeMethod='resize'
            resizeMode='cover'
          />
          <View style={styles.labelWrapper}>
            <Text style={styles.labelText}>{label}</Text>
          </View>
        </Touch>
      </Movable>
    )
  }
  return null
}

export default React.memo(TomorrowAwardWidget, isEqual)
