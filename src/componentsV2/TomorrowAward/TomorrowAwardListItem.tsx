import React from 'react'
import { Text } from '@xmly/rn-components'
import { View, StyleSheet, Image } from 'react-native'
import isEqual from 'lodash.isequal'
import { SelectDrawActivityAwardRule } from '../../typesV2/lotteryActivity'

type Props = {
  item: SelectDrawActivityAwardRule
  index: number
  small: boolean
}

const styles = StyleSheet.create({
  wrapper: {
    width: 72,
    height: 84,
    borderRadius: 6,
    backgroundColor: 'rgba(255,255,255,0.80)',
    marginRight: 6,
    alignItems: 'center',
  },
  awardIconImage: {
    width: 55,
    height: 55,
  },
  label: {
    fontSize: 12,
    color: '#871D09',
  },
})

const TomorrowAwardListItem: React.FC<Props> = ({ item, index, small }) => {
  const wrapperStyle = [
    styles.wrapper,
    index === 0 ? { marginLeft: 15 } : undefined,
    small
      ? {
          width: 56,
          height: 66,
        }
      : undefined,
  ]

  const awardIconImageStyle = [
    styles.awardIconImage,
    small
      ? {
          width: 45,
          height: 45,
        }
      : undefined,
  ]

  const labelStyle = [
    styles.label,
    small
      ? {
          fontSize: 10,
        }
      : undefined,
  ]

  return (
    <View style={wrapperStyle}>
      <Image source={{ uri: item.award.logoPic }} style={awardIconImageStyle} />
      <Text style={labelStyle}>{item.award.title}</Text>
    </View>
  )
}

export default React.memo(TomorrowAwardListItem, isEqual)
