import React, { Suspense, useEffect } from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import { TomorrowAwardStatus } from './types'
import xmlog from '../../utilsV2/xmlog'

const TomorrowAwardWidgetLazy = React.lazy(() => import('./TomorrowAwardWidget'))

// 新样式弹窗
const TomorrowAwardModalNewLazy = React.lazy(() => import('./TomorrowAwardModalNew'))

type Props = {}

const TomorrowAward: React.FC<Props> = () => {
  const { tomorrowAwardStatus, todayAwardValue, modalVisible, awardList } = useSelector((state: RootState) => state.tomorrowAward)

  const thirdpartyTaskIsExist = useSelector((state: RootState) => state.thirdpartyTask.isExist)

  const initTaskStatus = async () => {
    store.dispatch.tomorrowAward.initTaskStatus()
  }

  useEffect(() => {
    initTaskStatus()
  }, [])

  const handleOpenModal = async () => {
    store.dispatch.tomorrowAward.openModal({ tomorrowAwardStatus })
    xmlog.click(53954, undefined, {
      currPage: '任务中心',
      status: tomorrowAwardStatus === TomorrowAwardStatus.canReceive ? '领奖' : '明日开奖',
    })
  }

  const handleCloseModal = () => {
    store.dispatch.tomorrowAward.closeModal()
  }

  const fetchLotteryActivityAwardInfo = async () => {
    store.dispatch.tomorrowAward.fetchLotteryActivityAwardInfo()
  }

  useEffect(() => {
    fetchLotteryActivityAwardInfo()
  }, [])

  if (tomorrowAwardStatus !== TomorrowAwardStatus.UNSET && tomorrowAwardStatus !== TomorrowAwardStatus.nonValid)
    return (
      <>
        {modalVisible ? (
          <Suspense fallback={null}>
            <TomorrowAwardModalNewLazy
              onClose={handleCloseModal}
              tomorrowAwardStatus={tomorrowAwardStatus}
              awardList={awardList}
              todayAwardValue={todayAwardValue}
            />
          </Suspense>
        ) : null}
        {!thirdpartyTaskIsExist ? (
          <Suspense fallback={null}>
            <TomorrowAwardWidgetLazy
              onPress={handleOpenModal}
              tomorrowAwardStatus={tomorrowAwardStatus}
            />
          </Suspense>
        ) : null}
      </>
    )

  return null
}

export default React.memo(TomorrowAward, isEqual)
