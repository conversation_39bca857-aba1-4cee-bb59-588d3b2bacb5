import React, { useCallback, useEffect, useRef, useState } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { StyleSheet, Image, ScrollView, View, AccessibilityInfo, findNodeHandle } from 'react-native'
import isEqual from 'lodash.isequal'
import Pop from '../common/Pop'
import { ActionButton, ActionButtonLabel, SubTitle } from '../CheckInPop/styles'
import notificationGuideStorage from '../../storageV2/notificationGuideStorage'
import { EnumNotificationStatus } from '../../typesV2/notification'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import dayjs from 'dayjs'
import NotificationCheckButton from '../CheckInPop/NotificationCheckButton'
import { TomorrowAwardList, TomorrowAwardStatus } from './types'
import xmlog from '../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import { Page } from '@xmly/rn-sdk'
import getUrlToOpen from '../../utilsV2/getUrlToOpen'
import LinearGradient from 'react-native-linear-gradient'
import TomorrowAwardListItem from './TomorrowAwardListItem'
import { textAlignMiddleStyle } from '../../constantsV2/layout'
import pop_close from '../../appImagesV2/pop_close'

type Props = {
  tomorrowAwardStatus: TomorrowAwardStatus
  onClose: () => void
  awardList: TomorrowAwardList
  todayAwardValue: number
}

const styles = StyleSheet.create({
  wrapper: {},
  awardListWrapper: {
    marginTop: 16,
    width: '100%',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#821905',
    textAlign: 'center',
  },
  contentWrapper: {
    width: 275,
    borderRadius: 18,
    paddingBottom: 30,
    paddingTop: 26,
    alignItems: 'center',
  },
  beforeReceiveRewardIconWrapper: {
    width: 108,
    height: 81,
    marginTop: 16,
    marginBottom: 24,
  },
  ruleButtonWrapper: {
    marginTop: 15,
  },
  subTitle: {
    marginTop: 4,
    fontSize: 15,
    fontWeight: 'normal',
    marginBottom: 0,
  },
  todayAwardWrapper: {
    alignItems: 'center',
    marginTop: 26,
  },
  todayAwardLabelWrapper: {
    position: 'absolute',
    height: 30,
    width: 83,
    paddingTop: 2.5,
  },
  todayAwardLabelBG: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
  },
  todayAwardLabel: {
    fontSize: 16,
    textAlign: 'center',
    ...textAlignMiddleStyle,
    fontWeight: 'bold',
    color: '#871D09',
  },
  todayAwardIcon: {
    width: 131,
    height: 131,
    marginTop: 14,
  },
  smallAwardListTitleWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 15,
  },
  smallTitleLine: {
    width: 24,
    height: 1,
    marginHorizontal: 10,
  },
  modalSloganWrapper: {
    width: 294,
    height: 125,
    marginBottom: -11,
    zIndex: 1,
    position: 'relative',
    left: 1.5,
  },
  modalSloganIcon: {
    width: '100%',
    height: '100%',
  },
})

const TomorrowAwardModal: React.FC<Props> = ({
  onClose,
  tomorrowAwardStatus,
  // 今天抽奖获得的礼物，是个数组。如果数组长度是1，则只有今天的礼物，明日不可领。如果数组长度为2，则今日领完之后，明日还可以领。此时需要展示抽奖的礼品列表
  awardList,
  todayAwardValue,
}) => {
  const contentRef = useRef<View>(null)
  const tomorrowCanReceiveLabelRef = useRef<View>(null)
  const lotteryAwardList = useSelector((state: RootState) => state.tomorrowAward.lotteryAwardList)

  const notificationStatus = useSelector((state: RootState) => state.notification.status)
  const userIDTag = useSelector((state: RootState) => state.signInInfo.userIDTag)

  const [notificationChecked, setNotificationChecked] = useState(true)
  const [showNotificationGuide, setShowNotificationGuide] = useState(false)

  const judgeShowNotificationGuide = useCallback(async () => {
    if (notificationStatus === EnumNotificationStatus.UNSET) return
    try {
      const lastGuideDate = await notificationGuideStorage.get()
      const hasFrequencyCtrl = !lastGuideDate
        ? false // 本地存储时间为空则没有频次控制
        : dayjs().diff(lastGuideDate, 'day') < 30 // 本地时间与当前时间小于30天则处于频次控制

      const visible = !hasFrequencyCtrl && notificationStatus === EnumNotificationStatus.disable

      if (visible) {
        // 如果可见，则存储当前日期作为30天频控起始日期
        notificationGuideStorage.set()
      }

      setShowNotificationGuide(visible)
    } catch (err) {
      setShowNotificationGuide(false)
    }
  }, [userIDTag, notificationStatus])

  useEffect(() => {
    judgeShowNotificationGuide()
  }, [judgeShowNotificationGuide])

  useEffect(() => {
    const status = tomorrowAwardStatus === TomorrowAwardStatus.tomorrowCanReceive ? '明日开奖' : awardList.length > 1 ? '领奖+明日开奖' : '领奖'

    // 任务中心-隔日领奖活动弹窗  控件曝光
    xmlog.event(53957, 'slipPage', {
      currPage: '任务中心',
      status: status,
    })
  }, [])

  const handleClose = () => {
    if (notificationChecked && showNotificationGuide) {
      store.dispatch.notification.toggleNotification({
        enable: true,
        openSettingConfirm: false,
      })
    }

    onClose()
  }

  const trySetAccessibilityFocus = () => {
    try {
      const target = contentRef.current || tomorrowCanReceiveLabelRef.current || null
      if (target) {
        const nodeTag = findNodeHandle(target)
        nodeTag && AccessibilityInfo.setAccessibilityFocus(nodeTag)
      }
    } catch (err) {
      console.log(err)
    }
  }
  useEffect(() => {
    const timer = setTimeout(() => {
      trySetAccessibilityFocus()
    }, 500)

    return () => {
      clearTimeout(timer)
    }
  }, [])

  const modalSloganIconSource = {
    uri:
      tomorrowAwardStatus === TomorrowAwardStatus.tomorrowCanReceive
        ? 'https://imagev2.xmcdn.com/storages/da40-audiofreehighqps/2B/DE/GMCoOSYIiqBGAABwDQI5VXj7.png'
        : 'https://imagev2.xmcdn.com/storages/e515-audiofreehighqps/6F/6B/GKwRIJIIiqBHAABwrQI5VXlc.png',
  }

  const buttonLabel = tomorrowAwardStatus === TomorrowAwardStatus.tomorrowCanReceive ? '明天来开奖' : awardList.length > 1 ? '明天继续开奖' : '开心收下'

  const titleContent = todayAwardValue > 0 ? `恭喜获得${todayAwardValue}积分` : '明天0点开奖 有机会获得'

  const handlePressAction = () => {
    // 任务中心-隔日领奖活动弹窗  点击事件
    xmlog.click(53956, undefined, {
      currPage: '任务中心',
      status: tomorrowAwardStatus === TomorrowAwardStatus.tomorrowCanReceive ? '明日开奖' : '立即开奖',
      Item: safetyToString(buttonLabel),
    })
    handleClose()
  }

  const handleNotificationCheckedPress = () => {
    setNotificationChecked(!notificationChecked)
  }

  const handleCheckRule = () => {
    Page.start(getUrlToOpen('https://pages.ximalaya.com/mkt/act/eead617afb8bb3ac'))
  }

  const renderReceiveAwardContent = () => {
    const todayAward = awardList[0]
    return (
      <>
        <View
          style={[styles.todayAwardWrapper, awardList.length === 1 ? { marginBottom: 39 } : undefined]}
          accessible
          ref={contentRef}
          collapsable={false}
          accessibilityLabel={`恭喜获得${todayAward.label}`}
        >
          <Image
            source={{ uri: todayAward.icon }}
            style={styles.todayAwardIcon}
            resizeMethod="resize"
            resizeMode="contain"
          />
          <View style={styles.todayAwardLabelWrapper}>
            <Image
              style={styles.todayAwardLabelBG}
              resizeMethod="resize"
              resizeMode="contain"
              source={{
                uri: 'https://imagev2.xmcdn.com/storages/e3cd-audiofreehighqps/6F/FD/GMCoOSQIicQgAAAQDgI5Elr4.png',
              }}
            />
            <Text style={styles.todayAwardLabel}>{todayAward.label}</Text>
          </View>
        </View>

        {awardList.length > 1 ? renderTomorrowCanReceiveContent({ small: true }) : null}
      </>
    )
  }

  const renderTomorrowCanReceiveContent = (options?: { small?: boolean }) => {
    const accessibilityLabel =
      lotteryAwardList && lotteryAwardList.length > 0 ? `明日签到可以获得以下奖励，${lotteryAwardList.map((award) => award.award.title).join('，')}` : '明日签到可以获得额外奖励'
    // 当拿到明日抽奖的列表时才展示
    if (lotteryAwardList.length > 0) {
      return (
        <>
          {options?.small ? (
            <View style={styles.smallAwardListTitleWrapper}>
              <LinearGradient
                style={styles.smallTitleLine}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                colors={['transparent', '#821905']}
              />

              <Text style={[styles.title, { marginTop: 0, fontSize: 12 }]}>{titleContent}</Text>
              <LinearGradient
                style={styles.smallTitleLine}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 0 }}
                colors={['#821905', 'transparent']}
              />
            </View>
          ) : (
            <View
              collapsable={false}
              ref={tomorrowCanReceiveLabelRef}
              accessibilityLabel={accessibilityLabel}
              accessible
            >
              <Text style={styles.title}>{titleContent}</Text>
            </View>
          )}
          {/* 明日可开奖 */}
          {lotteryAwardList.length > 0 ? (
            <ScrollView
              style={styles.awardListWrapper}
              horizontal
              showsHorizontalScrollIndicator={false}
            >
              {lotteryAwardList.map((item, index) => (
                <TomorrowAwardListItem
                  key={index}
                  item={item}
                  index={index}
                  small={options?.small || false}
                />
              ))}
            </ScrollView>
          ) : null}
        </>
      )
    }
    return null
  }

  return (
    <Pop handleClose={handleClose}>
      <View style={styles.modalSloganWrapper}>
        <Image
          source={modalSloganIconSource}
          style={styles.modalSloganIcon}
          resizeMethod="resize"
          resizeMode="contain"
        />
      </View>
      <LinearGradient
        style={styles.contentWrapper}
        colors={['#FFF2F2', '#FFF7F7']}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      >
        {/* 此时展示的是明天可以领取的奖励列表 */}
        {tomorrowAwardStatus === TomorrowAwardStatus.tomorrowCanReceive ? renderTomorrowCanReceiveContent() : null}

        {/* 此时展示的是今日获得的奖励 */}
        {tomorrowAwardStatus === TomorrowAwardStatus.canReceive ? renderReceiveAwardContent() : null}

        {showNotificationGuide ? (
          <NotificationCheckButton
            checked={notificationChecked}
            onPress={handleNotificationCheckedPress}
          />
        ) : null}
      </LinearGradient>

      <ActionButton onPress={handlePressAction} style={{ marginTop: 24 }}>
        <ActionButtonLabel>{buttonLabel}</ActionButtonLabel>
      </ActionButton>

      <Touch
        onPress={handleCheckRule}
        style={styles.ruleButtonWrapper}
      >
        <SubTitle style={{ color: 'rgba(255,255,255,0.6)', fontSize: 12, marginTop: 0 }}>点击查看活动规则</SubTitle>
      </Touch>
      <Touch style={{ marginTop: 20 }} onPress={handleClose}>
        <Image style={{ width: 24, height: 24 }} width={24} height={24} source={pop_close}></Image>
      </Touch>
    </Pop>
  )
}

export default React.memo(TomorrowAwardModal, isEqual)
