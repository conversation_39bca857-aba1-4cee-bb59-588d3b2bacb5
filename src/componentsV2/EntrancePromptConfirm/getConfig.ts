import requestEntrancePromptConfig from '../../servicesV2/requestEntrancePromptConfig'
import { EntrancePromptConfigType } from '../../typesV2/entrancePromptConfigType'

let entrancePromptConfig: EntrancePromptConfigType

export const getConfig = async () => {
  if (entrancePromptConfig) {
    return entrancePromptConfig
  }
  try {
    const res = await requestEntrancePromptConfig()
    entrancePromptConfig = res.data[0]
  } catch (err) {
  } finally {
    return entrancePromptConfig
  }
}
