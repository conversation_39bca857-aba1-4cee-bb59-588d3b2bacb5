import React, { useContext, useEffect, useRef } from 'react'
import { Text } from '@xmly/rn-components'
import { Image, StyleSheet, View } from 'react-native'
import isEqual from 'lodash.isequal'
import RNAlert from '../Confirm'
import { DarkModeContext } from '../../contextV2/darkModeContext'
import NotificationCheckButton from '../CheckInPop/NotificationCheckButton'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import { EnumNotificationStatus } from '../../typesV2/notification'
import styled from 'styled-components'
import { ThemeStyle } from '../../typesV2/themeInfo'
import entrancePromptConfirmStorage from '../../storageV2/entrancePromptConfirmStorage'
import dayjs from 'dayjs'
import { getConfig } from './getConfig'
import { EntrancePromptConfigType } from '../../typesV2/entrancePromptConfigType'
import rnEnv from '../../../rnEnv'
import { fillOrigin } from '../../utilsV2/image2CustomSize'
import requestSignUserType from '../../servicesV2/requestSignUserType'
import xmlog from '../../utilsV2/xmlog'

type Props = {
  config: EntrancePromptConfigType
}

const ConfirmBodyLabel = styled(Text)`
  font-size: 11px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.checkInBar.notification_switch_label};
  margin-top: 8px;
`

const styles = StyleSheet.create({
  body: {
    width: '100%',
    alignItems: 'center',
  },
  image: {
    width: 270,
    height: 121,
  },
})

const EntrancePromptConfirmBody: React.FC<Props> = React.memo(({ config }) => {
  const notificationStatus = useSelector((state: RootState) => state.notification.status)
  const notificationStatusRef = useRef(notificationStatus)
  const { isDarkMode } = useContext(DarkModeContext)
  const bg = {
    uri: `https:${fillOrigin(isDarkMode ? config.entranceImageDark : config.entranceImage, 3, rnEnv.isTest())}`,
  }

  useEffect(() => {
    // 任务中心-签到教育弹窗  控件曝光
    xmlog.event(51798, 'slipPage', {
      currPage: '任务中心',
      description: notificationStatus === EnumNotificationStatus.disable ? 'true' : 'false',
    })
  }, [])

  const handleOpenNotification = () => {
    // 任务中心-签到教育弹窗  点击事件
    xmlog.click(51797, undefined, {
      currPage: '任务中心',
      Item: notificationStatus === EnumNotificationStatus.disable ? '打开' : '关闭',
    })
    store.dispatch.notification.toggleNotification({
      confirmBody: (text: string) => <ConfirmBodyLabel>{text}</ConfirmBodyLabel>,
      enable: notificationStatus === EnumNotificationStatus.disable,
    })
  }

  return (
    <View style={styles.body}>
      <Image source={bg} style={styles.image} />
      {notificationStatusRef.current === EnumNotificationStatus.disable ? (
        <NotificationCheckButton checked={notificationStatus === EnumNotificationStatus.enable} onPress={handleOpenNotification} />
      ) : null}
    </View>
  )
}, isEqual)

const judgeShouldShowConfirm = async (srcChannel: string) => {
  try {
    const [lastPromptDate, entrancePromptConfig, signUserType] = await Promise.all([entrancePromptConfirmStorage.get(), getConfig(), requestSignUserType()])

    const notSignInSevenDay = signUserType

    const hasFrequencyCtrl = !lastPromptDate
      ? false // 本地存储时间为空则没有频次控制
      : dayjs().diff(lastPromptDate, 'day') < 30 // 本地时间与当前时间小于30天则处于频次控制

    if (srcChannel && entrancePromptConfig.needConfirmChannel?.includes(srcChannel) && notSignInSevenDay && !hasFrequencyCtrl) {
      return { shouldConfirm: true, entrancePromptConfig }
    } else {
      return { shouldConfirm: false, entrancePromptConfig }
    }
  } catch (err) {
    return { shouldConfirm: false, entrancePromptConfig: null }
  }
}

const EntrancePromptConfirm = async (srcChannel: string) => {
  const { shouldConfirm, entrancePromptConfig } = await judgeShouldShowConfirm(srcChannel)

  if (shouldConfirm && entrancePromptConfig) {
    entrancePromptConfirmStorage.set()

    return new Promise((resolve, reject) => {
      RNAlert({
        title: typeof entrancePromptConfig.confirmTitle === 'string' && entrancePromptConfig.confirmTitle ? entrancePromptConfig.confirmTitle : '通过“首页-右上角”进入签到',
        body: <EntrancePromptConfirmBody config={entrancePromptConfig} />,
        closable: true,
        contentFullWidth: true,
        titleStyle: {
          marginBottom: 0,
          fontWeight: 'bold',
        },
        actions: [
          {
            text: '再逛逛',
            onPress: () => {
              // 任务中心-签到教育弹窗  点击事件
              xmlog.click(51797, undefined, {
                currPage: '任务中心',
                Item: '再逛逛',
              })
              reject()
            },
          },
          {
            text: '知道了',
            onPress: () => {
              // 任务中心-签到教育弹窗  点击事件
              xmlog.click(51797, undefined, {
                currPage: '任务中心',
                Item: '知道了',
              })
              resolve(true)
            },
            primary: true,
          },
        ],
      })
    })
  }

  return Promise.resolve(true)
}

export { getConfig } from './getConfig'

export default EntrancePromptConfirm
