import React, { useRef, useEffect } from 'react'
import { View, Animated, Easing, EasingFunction, StyleSheet } from 'react-native'

type Props = {
  backSlot: React.ReactElement
  delay?: number
  duration: number
  easing?: EasingFunction
  fire: boolean
  frontSlot: React.ReactElement
}

const styles = StyleSheet.create({
  card: {
    backfaceVisibility: 'hidden',
  },
  back: {
    position: 'absolute'
  }
})

export default function FlipCard(props: Props) {
  const {
    backSlot,
    delay = 0,
    duration = 600,
    easing = Easing.linear,
    fire,
    frontSlot,
  } = props
  const flipAnimation = useRef(new Animated.Value(0)).current
  const frontTransformStyle = {
    transform: [{
      rotateY: flipAnimation.interpolate({
        inputRange: [0, 180],
        outputRange: ["0deg", "180deg"]
      })
    }],
  }
  const backTransformStyle = {
    transform: [{
      rotateY: flipAnimation.interpolate({
        inputRange: [0, 180],
        outputRange: ["180deg", "360deg"]
      })
    }],
  }

  function flip() {
    Animated.timing(flipAnimation, {
      toValue: 180,
      duration,
      delay,
      easing,
      useNativeDriver: true,
    }).start()
  }

  useEffect(() => {
    if (fire) {
      flip()
    }
  }, [fire])

  return (
    <View style={{
      position: 'relative'
    }}>
      <Animated.View style={[frontTransformStyle, styles.card]}>
        {frontSlot}
      </Animated.View>
      <Animated.View style={[backTransformStyle, styles.card, styles.back]}>
        {backSlot}
      </Animated.View>
    </View>
  )
}
