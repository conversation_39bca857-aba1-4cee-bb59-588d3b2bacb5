import React, { useEffect, useRef, useState } from 'react'
import { Touch } from '@xmly/rn-components'
import { Image, View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import icon_suspense_close from '../../appImagesV2/icon_suspense_close'
import { ActivityWidgetConfigType, ActivityWidgetItemType } from './types'
import ActivityWidgetItem from './ActivityWidgetItem'
import { Page } from '@xmly/rn-sdk'
import getUrlToOpen from '../../utilsV2/getUrlToOpen'
import GlobalEventEmitter from '../../utilsV2/globalEventEmitter'
import { HomeScrollViewEventName } from '../../constantsV2'
import Animated, {
  Clock,
  cond,
  set,
  useCode,
  useValue,
} from 'react-native-reanimated'
import { runTiming } from '../../utilsV2/animations/runTiming'

type Props = {
  onClose: () => void
  styleConfig: Omit<ActivityWidgetConfigType, 'activities'>
  activities: ActivityWidgetItemType[]
}

const IconSize = 12

const Container = styled(Animated.View)`
  position: absolute;
  z-index: 1;
  align-items: flex-start;
  justify-content: flex-end;
`

const CloseButton = styled(Touch)`
  width: ${IconSize}px;
  height: ${IconSize}px;
  position: absolute;
  right: 0;
  top: 0;
`

const CloseButtonIcon = styled(Image)`
  width: 100%;
  height: 100%;
`

const ActivityWidget: React.FC<Props> = ({
  onClose,
  styleConfig,
  activities,
}) => {
  const hideTimer = useRef<NodeJS.Timer | null>(null)
  const isHidden = useRef(false)
  const [isScrolling, setIsScrolling] = useState(false)

  const hiddenAnimation = useValue<number>(0)
  const hiddenState = useValue<0 | 1>(0)
  const hiddenClock = useRef(new Clock()).current

  const width = styleConfig.containerWidth
  const height = styleConfig.containerHeight

  const widgetPosition = {
    bottom: styleConfig.positionBottom,
    right: styleConfig.positionRight,
  }

  const widgetSize = {
    width: width + IconSize,
    height: height + IconSize,
  }

  const animationDuration = 230

  useCode(() => {
    return [
      cond(
        hiddenState,
        [
          set(
            hiddenAnimation,
            runTiming({
              duration: animationDuration,
              from: hiddenAnimation,
              to: 1,
              clock: hiddenClock,
            })
          ),
        ],
        [
          set(
            hiddenAnimation,
            runTiming({
              duration: animationDuration,
              from: hiddenAnimation,
              to: 0,
              clock: hiddenClock,
            })
          ),
        ]
      ),
    ]
  }, [])

  const handleHomeScrollViewOnScroll = () => {
    if (!isHidden.current) {
      setIsScrolling(true)
      isHidden.current = true
      hiddenState.setValue(1)
      clearHideTimer()
    }
  }

  const handleHomeScrollViewOnMomentumScrollEnd = () => {
    if (isHidden.current) {
      clearHideTimer()
      setIsScrolling(false)
      hideTimer.current = setTimeout(() => {
        isHidden.current = false
        hiddenState.setValue(0)
      }, 3000)
    }
  }

  const clearHideTimer = () => {
    hideTimer.current && clearTimeout(hideTimer.current)
  }

  useEffect(() => {
    return () => {
      clearHideTimer()
    }
  }, [])

  useEffect(() => {
    const onScrollListener = GlobalEventEmitter.addListener(
      HomeScrollViewEventName.onScroll,
      handleHomeScrollViewOnScroll
    )
    const onMomentumScrollEndListener = GlobalEventEmitter.addListener(
      HomeScrollViewEventName.onMomentumScrollEnd,
      handleHomeScrollViewOnMomentumScrollEnd
    )
    return () => {
      onScrollListener.remove()
      onMomentumScrollEndListener.remove()
    }
  }, [])

  const handleClose = () => {
    onClose()
  }

  const handleActivityPress = (jumpLink?: string) => {
    if (isHidden.current) {
      return
    }
    if (jumpLink) {
      Page.start(getUrlToOpen(jumpLink))
    }
  }

  const translateX = hiddenAnimation.interpolate({
    outputRange: [0, widgetSize.width + widgetPosition.right / 2],
    inputRange: [0, 1],
  })

  const opacity = hiddenAnimation.interpolate({
    outputRange: [1, 0.3],
    inputRange: [0, 1],
  })

  return (
    <Container
      style={[
        widgetPosition,
        widgetSize,
        { transform: [{ translateX }] },
        { opacity },
      ]}
    >
      <CloseButton onPress={handleClose}>
        <CloseButtonIcon source={icon_suspense_close} />
      </CloseButton>
      {activities.slice(0, 1).map((activity, index) => (
        <ActivityWidgetItem
          key={index}
          onPress={handleActivityPress}
          jumpLink={activity.jumpLink || ''}
          activityCover={activity.activityCover || ''}
          width={width}
          height={height}
        />
      ))}
    </Container>
  )
}

export default React.memo(ActivityWidget, isEqual)
