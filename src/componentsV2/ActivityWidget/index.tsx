import React, { Suspense, useEffect, useState } from 'react'
import isEqual from 'lodash.isequal'
import requestActivityWidgetConfig from '../../servicesV2/requestActivityWidgetConfig'
import { ActivityWidgetConfigType, ActivityWidgetItemType } from './types'
import {
  defaultBottom,
  defaultHeight,
  defaultRight,
  defaultWidth,
} from './constants'
import { useSelector } from 'react-redux'
import { RootState } from '../../store'

const ActivityWidgetLazy = React.lazy(() => import('./ActivityWidget'))

type Props = {}

const index: React.FC<Props> = (props) => {
  const thirdpartyTaskExist = useSelector(
    (state: RootState) => state.thirdpartyTask.isExist
  )
  const [dataFetched, setDataFetched] = useState(false)
  const [visible, setVisible] = useState(false)
  const [activities, setActivities] = useState<ActivityWidgetItemType[]>([])
  const [displayDelay, setDisplayDelay] = useState(0)
  const [styleConfig, setStyleConfig] = useState<
    Omit<ActivityWidgetConfigType, 'activities'>
  >({
    containerHeight: defaultHeight,
    containerWidth: defaultWidth,
    positionBottom: defaultBottom,
    positionRight: defaultRight,
  })

  const fetchActivityConfig = async () => {
    try {
      const res = await requestActivityWidgetConfig()
      const data = res.data[0] || null
      if (data && data?.activities.length > 0) {
        const { activities, displayDelay, ...rest } = data
        setActivities(activities)
        setStyleConfig(rest)
        if (typeof displayDelay === 'number') {
          setDisplayDelay(displayDelay)
        }
      }
    } catch (err) {
      setVisible(false)
    } finally {
      setDataFetched(true)
    }
  }

  useEffect(() => {
    let timer: NodeJS.Timeout
    if (dataFetched && activities.length > 0) {
      timer = setTimeout(() => {
        setVisible(true)
      }, displayDelay * 1000)
    }
    return () => {
      timer && clearTimeout(timer)
    }
  }, [dataFetched, activities])

  useEffect(() => {
    fetchActivityConfig()
  }, [])

  const handleClose = () => {
    setVisible(false)
  }

  if (visible && !thirdpartyTaskExist) {
    return (
      <Suspense fallback={null}>
        <ActivityWidgetLazy
          onClose={handleClose}
          styleConfig={styleConfig}
          activities={activities}
        />
      </Suspense>
    )
  } else {
    return null
  }
}

export default React.memo(index, isEqual)
