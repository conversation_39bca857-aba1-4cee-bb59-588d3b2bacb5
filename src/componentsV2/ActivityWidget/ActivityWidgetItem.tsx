import React from 'react'
import { BetterImage, Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import getImageUri from '../../utilsV2/getImageUri'
import rnEnv from '../../../rnEnv'
import { defaultHeight, defaultWidth } from './constants'

type Props = {
  onPress?: (jumpLink?: string) => void
  jumpLink?: string
  activityCover?: string
  width?: number
  height?: number
}

const Wrapper = styled(Touch)``

const ActivityWidgetItem: React.FC<Props> = ({
  onPress,
  jumpLink,
  activityCover,
  width,
  height,
}) => {
  const mWidth = typeof width === 'number' ? width : defaultWidth
  const mHeight = typeof height === 'number' ? height : defaultHeight

  const handlePress = () => {
    if (typeof onPress === 'function') {
      onPress(jumpLink)
    } else {
    }
  }

  const coverUri = activityCover
    ? getImageUri(activityCover, {
        width: mWidth * 3,
        height: mHeight * 3,
        test: rnEnv.isTest(),
      })
    : ''

  const cover = { uri: coverUri }

  return (
    <Wrapper
      onPress={handlePress}
      style={{ width: mWidth, height: mHeight }}
      activeOpacity={1}
    >
      <BetterImage
        source={cover}
        style={{ width: '100%', height: '100%' }}
        resizeMethod='resize'
        resizeMode='contain'
      />
    </Wrapper>
  )
}

export default React.memo(ActivityWidgetItem, isEqual)
