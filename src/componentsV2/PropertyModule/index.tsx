import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { View, Text, LayoutChangeEvent, Image, Platform, UIManager, LayoutAnimation } from 'react-native'
import styled from 'styled-components'
import { DarkModeContext } from '../../contextV2/darkModeContext'
import { ThemeStyle } from '../../typesV2/themeInfo'
import Credit from '../Credit'
import CheckInBar from '../EarnCredit/CheckInBar'
import icon_down_arrow_light from '../../appImagesV2/icon_down_arrow_light'
import { useSelector } from 'react-redux'
import { Touch } from '@xmly/rn-components'
import xmlog from 'utilsV2/xmlog'
import monthlyTicketGuideContext from 'contextV2/monthlyTicketGuideContext'
import { NativeInfoContext } from 'contextV2/nativeInfoContext'
import { MonthlyTicketVoteModalSrcName } from '../../constantsV2'
import { MonthlyTicketGuideStatus } from 'typesV2/monthlyTicketGuide'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { RootState, store } from '../../store'
import { TaskStatus } from '../../typesV2/taskList'
import isEqual from 'lodash.isequal'
import { UserInfoContext } from 'contextV2/userInfoContext'
import UserLoginStatus from 'typesV2/userLoginStatus'
import { width } from '../../constantsV2/dimensions'
import useDoubleAwardTask from '../../hooks/useDoubleAwardTask'

const Wrapper = styled(View)`
  background: ${({ theme }: { theme: ThemeStyle }) => theme.credit.bg_color};
  border-radius: 8px;
  margin-top: 11px;
  margin-bottom: 12px;
  /* border: 1px red solid; */
`

interface ToggleWrapperProps {
  expanded: boolean
}
const ToggleWrapper = styled(Touch) <ToggleWrapperProps>`
  width: 100%;
  padding-top: ${(props) => (props.expanded ? '8px' : '0px')};
  padding-bottom: 8px;
  flex-direction: row;
  justify-content: center;
  align-items: center;
`
const ToggleText = styled(Text)`
  font-family: PingFangSC-Regular;
  font-size: 11px;
  font-weight: normal;
  line-height: 13px;
  color: #999999;
`
interface IconProps {
  expanded: boolean
}

const ToggleIcon = styled(Image).attrs({
  source: icon_down_arrow_light,
}) <IconProps>`
  width: 16px;
  height: 16px;
  transform: rotate(${(props) => (props.expanded ? '180deg' : '0deg')});
`
interface Props {
  creditHeight: React.MutableRefObject<number>
  handleShowBigGiftModal: () => void
}

if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true)
}

const PropertyBtnItemKey = 'PropertyBtnItemKey'

const PropertyModule: React.FC<Props> = ({ creditHeight, handleShowBigGiftModal }) => {
  const todaySignInMultiAwardsConfig = useSelector((state: RootState) => state.signInInfo.todaySignInMultiAwardsConfig)
  const signInSuccessModalVisible = useSelector((state: RootState) => state.signInInfo.signInSuccessModalVisible)

  const { loginStatus } = useContext(UserInfoContext)
  const { noLoginAbTestStatus } = useSelector((state: RootState) => state.signInInfo)

  const [containerWidth, setContainerWidth] = useState(width - 60)
  const { isDarkMode } = useContext(DarkModeContext)
  const { srcChannel, toTaskId } = useContext(NativeInfoContext)
  const isFromVoteModal = srcChannel === MonthlyTicketVoteModalSrcName
  const { guideStatus } = useContext(monthlyTicketGuideContext)
  const [expanded, setExpanded] = useState(signInSuccessModalVisible)
  const firstUpdate = useRef(true)
  const [currentStatus, setCurrentStatus] = useState(false)
  const isByPressAction = useRef(false)
  const doubleAwardInfo = useDoubleAwardTask();
  const toggleButtonLabel = doubleAwardInfo.foldText ?
    doubleAwardInfo.foldText :
    todaySignInMultiAwardsConfig && todaySignInMultiAwardsConfig.status === TaskStatus.finished ?
      '有奖励待领取'
      :
      `${expanded ? '收起' : '展开'}签到`

  const updateCreditSectionLayout = (event: LayoutChangeEvent) => {
    creditHeight.current = Math.round(event.nativeEvent.layout.height)
    setContainerWidth(event.nativeEvent.layout.width)
  }

  const handlerPress = () => {
    isByPressAction.current = true
    toggleOpen({ isByPressButton: true })
  }

  useEffect(() => {
    if (loginStatus === UserLoginStatus.notLogin && noLoginAbTestStatus === '2') {
      setExpanded(true)
      setCurrentStatus(true)
    } else {
      if (firstUpdate.current) {
        if (guideStatus === MonthlyTicketGuideStatus.need && isFromVoteModal) {
          setExpanded(true)
        }
        firstUpdate.current = false
        setCurrentStatus(true)
        return
      } else {
        if (!(srcChannel && toTaskId)) {
          const timer = setTimeout(() => {
            !isByPressAction.current && toggleOpen()
            setCurrentStatus(true)
          }, 1000)
          return () => {
            clearTimeout(timer)
          }
        }
      }
    }
  }, [srcChannel, toTaskId, signInSuccessModalVisible, noLoginAbTestStatus])

  const toggleOpen = useCallback(
    (options?: { isByPressButton?: boolean }) => {
      // 任务中心-签到收缩/展开条  点击事件
      xmlog.click(52161, undefined, {
        currPage: '任务中心',
        status: toggleButtonLabel.replace('签到', ''),
        Item: expanded ? '展开' : '收缩',
      })
      LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut)
      setExpanded(!expanded)
    },
    [expanded],
  )

  const onShowHandler = () => {
    if (currentStatus) {
      console.log('控件曝光====')
      // 任务中心-签到收缩/展开条  控件曝光
      xmlog.event(52162, 'slipPage', {
        currPage: '任务中心',
        status: toggleButtonLabel.replace('签到', ''),
        Item: expanded ? '展开' : '收缩',
      })
    }
  }

  useEffect(() => {
    store.dispatch.notification.fetchNotificationStatus()
  }, [])

  return (
    <Wrapper>
      <View onLayout={updateCreditSectionLayout}>
        <Credit />
      </View>
      {expanded && (
        <CheckInBar
          showGiftModal={handleShowBigGiftModal}
          containerWidth={containerWidth}
        />
      )}
      {currentStatus && (
        <ScrollAnalyticComp
          itemKey={PropertyBtnItemKey}
          onShow={onShowHandler}
        >
          <ToggleWrapper
            expanded={expanded}
            onPress={handlerPress}
            accessibilityLabel={`${toggleButtonLabel}日历`}
          >
            <ToggleText>{toggleButtonLabel}</ToggleText>
            <ToggleIcon
              expanded={expanded}
              style={{ tintColor: isDarkMode ? '#66666B' : '#999' }}
            />
          </ToggleWrapper>
        </ScrollAnalyticComp>
      )}
    </Wrapper>
  )
}

export default React.memo(PropertyModule, isEqual)
