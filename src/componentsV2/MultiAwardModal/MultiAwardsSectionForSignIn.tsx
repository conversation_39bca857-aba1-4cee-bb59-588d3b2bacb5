import React, { useEffect, useState } from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import requestChooseTaskAward from '../../servicesV2/requestChooseTaskAward'
import { TodaySignInChooseAwardType } from '../../typesV2/signInNew'
import { Page, Toast } from '@xmly/rn-sdk'
import getUrlToOpen from '../../utilsV2/getUrlToOpen'
import xmlog from '../../utilsV2/xmlog'
import ModalContent from './ModalContent'
import Pop from '../common/Pop'
import { safetyToString } from '@xmly/rn-utils'

type Props = {}

// 签到使用的奖励多选一弹窗
const MultiAwardsSectionForSignIn: React.FC<Props> = ({}) => {
  const [withGuideAwardInfo, setWithGuideAwardInfo] =
    useState<TodaySignInChooseAwardType | null>(null)

  const todaySignInMultiAwardsConfig = useSelector(
    (state: RootState) => state.signInInfo.todaySignInMultiAwardsConfig
  )

  const checkInInfo = useSelector(
    (state: RootState) => state.signInInfo.checkInInfo
  )
  const todaySignInCreditValue = useSelector(
    (state: RootState) => state.signInInfo.todaySignInCreditValue
  )
  const randomQuantity = useSelector(
    (state: RootState) => state.signInInfo.randomQuantity
  )

  const basicAwards = (checkInInfo.awards || []).filter(
    (a) => !a.label.includes('连签礼包')
  )

  const awards = todaySignInMultiAwardsConfig?.awards || []

  const eventReportProps = {
    description:
      awards.length > 0 ? awards.map((award) => award.label).join('+') : '',
    dialogTitle: '签到成功恭喜获得',
    id:
      awards.length > 0 ? awards.map((award) => award.productId).join(',') : '',
  }

  useEffect(() => {
    if (!todaySignInMultiAwardsConfig) return
    // 任务中心-签到成功弹窗(多选奖励)  控件曝光
    xmlog.event(55163, 'slipPage', {
      currPage: '任务中心',
      dialogTitle: eventReportProps.dialogTitle,
      description: eventReportProps.description,
      id: eventReportProps.id,
    })
  }, [])

  useEffect(() => {
    if (withGuideAwardInfo) {
      // 任务中心-签到多选奖励-领取成功  控件曝光
      xmlog.event(55165, 'slipPage', {
        currPage: '任务中心',
        dialogTitle: `领取成功 ${withGuideAwardInfo.label}`,
      })
    }
  }, [withGuideAwardInfo])

  const handleClose = () => {
    store.dispatch.signInInfo.refresh({ signInSuccessModalVisible: false })
    store.dispatch.signInInfo.refreshMultiAwardsTaskStatus(
      store.getState().signInInfo.checkInInfo
    )
  }

  const chooseTaskAward = async ({
    productId,
    targetActivityId,
    targetTaskId,
    onFinish,
  }: {
    productId: number
    targetTaskId: number
    targetActivityId: number
    onFinish?: () => void
  }) => {
    try {
      const res = await requestChooseTaskAward({
        productId: [productId],
        aid: targetActivityId,
        taskId: targetTaskId,
      })
      if (res && res.ret !== 0) {
        if (res.msg) {
          throw new Error(res.msg)
        } else {
          throw new Error('领取失败')
        }
      } else {
        typeof onFinish === 'function' && onFinish()
      }
    } catch (err) {
      console.log(err)
      Toast.info(err)
      handleClose()
    } finally {
      store.dispatch.signInInfo.refreshMultiAwardsTaskStatus(
        store.getState().signInInfo.checkInInfo
      )
    }
  }

  const closeModal = () => {
    handleClose()
  }

  const showGuide = (selectedAward: TodaySignInChooseAwardType) => {
    setWithGuideAwardInfo(selectedAward)
  }

  const handleAwardSelect = (award: TodaySignInChooseAwardType) => {
    if (!todaySignInMultiAwardsConfig) return

    // 任务中心-签到成功弹窗(多选奖励)  点击事件
    xmlog.click(55162, undefined, {
      currPage: '任务中心',
      dialogTitle: eventReportProps.dialogTitle,
      description: eventReportProps.description,
      Item: award.label,
      id: safetyToString(award.productId),
    })

    chooseTaskAward({
      targetTaskId: todaySignInMultiAwardsConfig.targetTaskId,
      targetActivityId: todaySignInMultiAwardsConfig.targetActivityId,
      productId: award.productId,
      onFinish: () => {
        if (award.type === 24) {
          showGuide(award)
        } else {
          closeModal()
          Toast.info('领取成功')
        }
      },
    })
  }
  const handlePressGuideLink = (guideLink?: string) => {
    // 任务中心-签到多选奖励-领取成功  点击事件
    xmlog.click(55164, undefined, {
      currPage: '任务中心',
      dialogTitle: `领取成功 ${withGuideAwardInfo?.label}`,
      Item: '去使用',
    })
    if (guideLink) {
      Page.start(getUrlToOpen(guideLink))
    }
    handleClose()
  }

  if (todaySignInMultiAwardsConfig) {
    return (
      <Pop handleClose={handleClose}>
        <ModalContent
          basicAwards={basicAwards}
          todaySignInCreditValue={todaySignInCreditValue}
          multiAwards={todaySignInMultiAwardsConfig.awards || []}
          onAwardSelect={handleAwardSelect}
          onPressGuideLink={handlePressGuideLink}
          randomQuantity={randomQuantity}
          withGuideAwardInfo={withGuideAwardInfo}
        />
      </Pop>
    )
  }
  return null
}

export default React.memo(MultiAwardsSectionForSignIn, isEqual)
