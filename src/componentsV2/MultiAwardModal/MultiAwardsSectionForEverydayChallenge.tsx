import React, { useEffect } from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import requestChooseTaskAward from '../../servicesV2/requestChooseTaskAward'
import { Toast } from '@xmly/rn-sdk'
import xmlog from '../../utilsV2/xmlog'
import ModalContent from './ModalContent'
import Pop from '../common/Pop'
import { StepInfoMultiAwardType } from '../../typesV2/taskList'
import { getXuidTicket } from '../../utilsV2/native'
import { XUID_ticketConfig } from '../../constantsV2'
import userInfoDetail from '../../modulesV2/userInfoDetail'
import { safetyToString } from '@xmly/rn-utils'

type Props = {}

// 任务包使用的多选一奖励弹窗
const MultiAwardsSectionForEverydayChallenge: React.FC<Props> = () => {
  const currentMultiAwardsInfo = useSelector(
    (state: RootState) => state.everydayChallenge.currentMultiAwardsInfo
  )

  const awards = currentMultiAwardsInfo?.awards || []

  const eventReportProps = {
    description:
      awards.length > 0 ? awards.map((award) => award.name).join('+') : '',
    dialogTitle: '任务完成恭喜获得',
    id:
      awards.length > 0 ? awards.map((award) => award.productId).join(',') : '',
  }

  useEffect(() => {
    if (!currentMultiAwardsInfo) return
    // 任务中心-签到成功弹窗(多选奖励)  控件曝光
    xmlog.event(55163, 'slipPage', {
      currPage: '任务中心',
      dialogTitle: eventReportProps.dialogTitle,
      id: eventReportProps.id,
      description: eventReportProps.description,
    })
  }, [])

  const chooseTaskAward = async ({
    productId,
    targetActivityId,
    targetTaskId,
    onFinish,
  }: {
    productId: number
    targetTaskId: number
    targetActivityId: number
    onFinish?: () => void
  }) => {
    if (!currentMultiAwardsInfo) return
    try {
      const ticket = await getXuidTicket({
        businessId: XUID_ticketConfig.finishTask.businessId,
        scene: XUID_ticketConfig.finishTask.scene,
        uid: userInfoDetail.getDetail().uid || -1,
      })
      const res = await requestChooseTaskAward({
        productId: [productId],
        aid: targetActivityId,
        taskId: targetTaskId,
        ticket,
        stepNo: currentMultiAwardsInfo.stepNos
          ? currentMultiAwardsInfo.stepNos[0]
          : 3,
      })
      if (res && res.ret !== 0) {
        if (res.msg) {
          throw new Error(res.msg)
        } else {
          throw new Error('领取失败')
        }
      } else {
        typeof onFinish === 'function' && onFinish()
      }
    } catch (err) {
      console.log(err)
      Toast.info(err)
      handleClose()
    } finally {
      store.dispatch.signInInfo.refreshMultiAwardsTaskStatus(
        store.getState().signInInfo.checkInInfo
      )
    }
  }

  const closeModal = () => {
    handleClose()
  }

  const handleAwardSelect = (award: StepInfoMultiAwardType) => {
    if (!currentMultiAwardsInfo) return

    // 任务中心-签到成功弹窗(多选奖励)  点击事件
    xmlog.click(55162, undefined, {
      currPage: '任务中心',
      dialogTitle: eventReportProps.dialogTitle,
      description: eventReportProps.description,
      Item: award.name,
      id: safetyToString(award.productId),
    })

    chooseTaskAward({
      targetTaskId: currentMultiAwardsInfo.taskItem.id,
      targetActivityId: currentMultiAwardsInfo.aid,
      productId: award.productId,
      onFinish: () => {
        closeModal()
        Toast.info('领取成功')
        store.dispatch.everydayChallenge.multiAwardsReceivedSuccess()
      },
    })
  }

  const handleClose = () => {
    store.dispatch.everydayChallenge.toggleMultiAwardsModal({ visible: false })
  }

  if (currentMultiAwardsInfo) {
    return (
      <Pop handleClose={handleClose}>
        <ModalContent
          multiAwards={awards}
          onAwardSelect={handleAwardSelect}
          title='任务完成 恭喜获得'
        />
      </Pop>
    )
  }
  return null
}

export default React.memo(MultiAwardsSectionForEverydayChallenge, isEqual)
