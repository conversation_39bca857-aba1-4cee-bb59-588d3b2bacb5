import React, { Suspense } from 'react'
import isEqual from 'lodash.isequal'

// 签到用的多选一
const MultiAwardsSectionForSignInLazy = React.lazy(
  () => import('./MultiAwardsSectionForSignIn')
)

// 任务包用的多选一
const MultiAwardsSectionForEverydayChallengeLazy = React.lazy(
  () => import('./MultiAwardsSectionForEverydayChallenge')
)

type Props = {
  forEverydayChallenge?: boolean
}

const MultiAwardModal: React.FC<Props> = ({ forEverydayChallenge }) => {
  if (forEverydayChallenge) {
    return (
      <Suspense fallback={null}>
        <MultiAwardsSectionForEverydayChallengeLazy />
      </Suspense>
    )
  }
  return (
    <Suspense fallback={null}>
      <MultiAwardsSectionForSignInLazy />
    </Suspense>
  )
}

export default React.memo(MultiAwardModal, isEqual)
