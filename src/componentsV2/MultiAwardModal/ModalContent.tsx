import React, { useState } from 'react'
import { BetterImage, Text, Touch } from '@xmly/rn-components'
import { View, StyleSheet, Image, LayoutChangeEvent } from 'react-native'
import isEqual from 'lodash.isequal'
import {
  CheckInInfoAwardItem,
  TodaySignInChooseAwardType,
} from '../../typesV2/signInNew'
import AwardItem from './AwardItem'
import { StepInfoMultiAwardType } from '../../typesV2/taskList'

const styles = StyleSheet.create({
  wrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    position: 'relative',
    zIndex: 1,
    marginBottom: 35,
  },
  awardWrapper: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'center',
  },

  labelText: {
    fontSize: 32,
    color: '#FFF7E3',
    fontWeight: 'bold',
    marginBottom: 12,
  },
  awardSectionLabelWrapper: {
    flexDirection: 'row',
    marginBottom: 18,
    height: 15,
    alignItems: 'center',
  },
  awardSectionLabel: {
    fontSize: 15,
    color: '#FFF7E2',
    marginHorizontal: 8.5,
  },
  awardSectionLabelLeftLine: {
    width: 26,
    height: 1,
  },
  awardSectionLabelRightLine: {
    width: 26,
    height: 1,
    transform: [{ rotate: '180deg' }],
  },
  awardGuideImg: {
    width: 275,
    height: 323,
  },
  todayBasicAwardWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    position: 'relative',
    height: 28,
  },
  todayBasicAwardBG: {
    width: 204,
    height: 28,
    position: 'absolute',
    top: 0,
  },
  basicAwardItemWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4.5,
  },
  basicAwardItemIcon: {
    width: 19,
    height: 19,
    marginRight: 4,
  },
  basicAwardItemLabel: {
    fontSize: 12,
    color: '#FFF9EA',
  },
})

type Props = {
  multiAwards: (StepInfoMultiAwardType | TodaySignInChooseAwardType)[] // 多选一奖励
  onAwardSelect: (
    award: TodaySignInChooseAwardType | StepInfoMultiAwardType
  ) => void // 领奖
  onPressGuideLink?: (link?: string) => void // 奖品有引导链接时展示引导弹窗
  withGuideAwardInfo?: null | TodaySignInChooseAwardType // 是否有引导弹窗
  basicAwards?: CheckInInfoAwardItem[] // 当天的基础奖励
  randomQuantity?: number // 随机的积分，为了展示当天的基础奖励
  todaySignInCreditValue?: string // 当天的几分，为了展示当天的基础奖励
  title?: string // 弹窗标题
}

const ModalContent: React.FC<Props> = ({
  basicAwards,
  randomQuantity,
  todaySignInCreditValue,
  multiAwards,
  onAwardSelect,
  onPressGuideLink,
  withGuideAwardInfo,
  title,
}) => {
  const [containerWidth, setContainerWidth] = useState(0)

  const handleLayout = (event: LayoutChangeEvent) => {
    setContainerWidth(event.nativeEvent.layout.width)
  }

  const handleOpenGuideLink = () => {
    typeof onPressGuideLink === 'function' &&
      onPressGuideLink(withGuideAwardInfo?.guideLink)
  }

  const handleSelectAward = (award: TodaySignInChooseAwardType) => {
    onAwardSelect(award)
  }

  //渲染基础奖励，可能为空
  const renderBasicAwards = () => {
    if (basicAwards && basicAwards.length > 0) {
      return (
        <View style={styles.todayBasicAwardWrapper}>
          <Image
            source={{
              uri: 'https://imagev2.xmcdn.com/storages/6bd2-audiofreehighqps/2F/9F/GKwRIJIIcHsoAAAn4AIvo-tk.png',
            }}
            style={styles.todayBasicAwardBG}
            resizeMode='contain'
            resizeMethod='resize'
          />
          {basicAwards.map((award, index) => {
            const giftItemLabel =
              award.label.includes('积分') && typeof randomQuantity === 'number'
                ? `${
                    randomQuantity > 0
                      ? randomQuantity
                      : todaySignInCreditValue || ''
                  }积分`
                : award.label
            return (
              <View key={index} style={styles.basicAwardItemWrapper}>
                <Image
                  source={{ uri: award.icon }}
                  style={styles.basicAwardItemIcon}
                />
                <Text style={styles.basicAwardItemLabel}>{giftItemLabel}</Text>
              </View>
            )
          })}
        </View>
      )
    }
    return null
  }

  return (
    <View style={styles.wrapper} onLayout={handleLayout}>
      {!withGuideAwardInfo && containerWidth > 0 ? (
        <>
          <Text style={styles.labelText}>
            {title ? title : '签到成功 恭喜获得'}
          </Text>
          {/* 渲染基础奖励，可能为空 */}
          {renderBasicAwards()}
          {/* ”以下奖励2选1“ 标语 */}
          <View style={styles.awardSectionLabelWrapper}>
            <Image
              resizeMethod='resize'
              resizeMode='contain'
              source={{
                uri: 'https://imagev2.xmcdn.com/storages/43d0-audiofreehighqps/2E/1D/GMCoOR4IcHvfAAABHwIvpELm.png',
              }}
              style={styles.awardSectionLabelLeftLine}
            />

            <Text style={styles.awardSectionLabel}>以下奖励2选1</Text>
            <Image
              resizeMethod='resize'
              resizeMode='contain'
              source={{
                uri: 'https://imagev2.xmcdn.com/storages/43d0-audiofreehighqps/2E/1D/GMCoOR4IcHvfAAABHwIvpELm.png',
              }}
              style={styles.awardSectionLabelRightLine}
            />
          </View>

          <View style={[styles.awardWrapper]} collapsable={false}>
            {multiAwards.length > 0
              ? multiAwards.map((award) => {
                  return (
                    <AwardItem
                      key={award.productId}
                      award={award}
                      onPress={handleSelectAward}
                      containerWidth={containerWidth}
                    />
                  )
                })
              : null}
          </View>
        </>
      ) : withGuideAwardInfo ? (
        <>
          <Text style={{ ...styles.labelText, fontSize: 24 }}>
            领取成功 {withGuideAwardInfo.label}
          </Text>
          <Touch onPress={handleOpenGuideLink}>
            <BetterImage
              style={styles.awardGuideImg}
              source={{ uri: withGuideAwardInfo.guideImg }}
              resizeMethod='resize'
              resizeMode='contain'
            />
          </Touch>
        </>
      ) : null}
    </View>
  )
}

export default React.memo(ModalContent, isEqual)
