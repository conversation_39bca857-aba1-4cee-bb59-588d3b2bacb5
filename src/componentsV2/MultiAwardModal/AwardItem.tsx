import React from 'react'
import { BetterImage, Touch } from '@xmly/rn-components'
import { StyleSheet } from 'react-native'
import isEqual from 'lodash.isequal'
import { TodaySignInChooseAwardType } from '../../typesV2/signInNew'
import { StepInfoMultiAwardType } from '../../typesV2/taskList'

type Props = {
  containerWidth: number
  onPress: (award: TodaySignInChooseAwardType | StepInfoMultiAwardType) => void
  award: TodaySignInChooseAwardType | StepInfoMultiAwardType
}

const styles = StyleSheet.create({
  itemWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 6,
  },
  icon: {
    width: '100%',
    height: '100%',
  },
})

const AwardItem: React.FC<Props> = ({ containerWidth, award, onPress }) => {
  const handlePress = () => {
    onPress(award)
  }

  const imageWidth = Math.floor(Math.min(157, containerWidth * 0.41))
  const imageHeigh = Math.floor(imageWidth / 0.616)

  const cover =
    (award as TodaySignInChooseAwardType).icon ||
    (award as StepInfoMultiAwardType).cover ||
    ''

  return (
    <Touch
      onPress={handlePress}
      style={[styles.itemWrapper, { width: imageWidth, height: imageHeigh }]}
      activeOpacity={1}
    >
      {cover ? (
        <BetterImage
          style={[styles.icon]}
          source={{ uri: cover }}
          resizeMethod='resize'
          resizeMode='contain'
        />
      ) : null}
    </Touch>
  )
}

export default React.memo(AwardItem, isEqual)
