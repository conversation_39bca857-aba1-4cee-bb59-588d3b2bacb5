import React, { useContext } from 'react'
import { View, Image as RNImage } from 'react-native'
import styled from 'styled-components'
import Pop from '../../common/Pop'
import { Text, Touch, BetterImage } from '@xmly/rn-components'
import Clipboard from '@react-native-community/clipboard'
import { Toast } from '@xmly/rn-sdk'
import { hitSlop } from '@xmly/rn-utils'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import { ThemeContext } from '../../../contextV2/themeContext'
import { DarkModeContext } from '../../../contextV2/darkModeContext'
import icon_pc_close_light from '../../../appImagesV2/icon_pc_close_light'
import isEqual from 'lodash.isequal'

const Content = styled(View)`
  width: 275px;
  background: ${({ theme }: { theme: ThemeStyle }) => theme.pcDialog.bg_color};
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  overflow: hidden;
`
const TextWrapper = styled(View)`
  flex-direction: row;
  margin-bottom: 8px;
  padding: 0 20px;
`
const Order = styled(Text)`
  margin-right: 5px;
  width: 15px;
  font-size: 13px;
  text-align: left;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.pcDialog.title_color};
  line-height: 18px;
`
const Title = styled(Text)`
  font-size: 13px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.pcDialog.title_color};
  line-height: 18px;
  flex: 1;
`
const SitTitle = styled(Text)`
  font-size: 13px;
  font-family: PingFangSC-Regular;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.pcDialog.sit_title};
  line-height: 18px;
  font-weight: bold;
`
const Image = styled(BetterImage)`
  width: 100%;
  height: 160px;
  margin-bottom: 10px;
`
const SubTitle = styled(Text)`
  margin-top: 4px;
  text-align: center;
  font-size: 12px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.pcDialog.sub_title_color};
  opacity: ${({ theme }: { theme: ThemeStyle }) =>
    theme.pcDialog.sub_title_opacity};
  line-height: 17px;
`
const CopyBtn = styled(Touch)`
  width: 200px;
  height: 40px;
  background: #ff4646;
  border-radius: 29px;

  margin: 24px 0 28px 0;
`
const CopyText = styled(Text)`
  line-height: 40px;
  text-align: center;
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: #ffffff;
`
const CloseBtn = styled(Touch)`
  position: absolute;
  right: 12px;
  top: 12px;
  width: 13px;
  height: 13px;
`
const CloseIcon = styled(RNImage)`
  width: 100%;
  height: 100%;
`
const TaskPcDialog = ({
  onClose,
  isPCListenTask,
}: {
  onClose: () => void
  isPCListenTask: boolean
}) => {
  const theme = useContext(ThemeContext)
  const { isDarkMode } = useContext(DarkModeContext)

  const handleClose = () => {
    onClose()
  }

  /** 复制链接 */
  const copyShareLink = async () => {
    try {
      const shareLink = 'pc.ximalaya.com'
      await Clipboard.setString(shareLink)
      Toast.info('复制成功')
    } catch (error) {
      console.log(error)
    }
  }

  return (
    <Pop
      handleClose={handleClose}
      maskStyle={{ backgroundColor: 'rgba(0,0,0,0.75)' }}
    >
      <Content>
        <Image source={{ uri: theme.pcDialog.bg_img }} resizeMode='cover' />
        <TextWrapper>
          <Order>1.</Order>
          <Title>
            在电脑端输入
            <SitTitle> pc.ximalaya.com </SitTitle>
            选择需要下载的版本
          </Title>
        </TextWrapper>
        <TextWrapper>
          <Order>2.</Order>
          <Title>打开并登录喜马账号使用即可完成本任务</Title>
        </TextWrapper>
        {isPCListenTask ? null : (
          <SubTitle>*本任务仅限首次使用PC客户端用户完成</SubTitle>
        )}
        <CopyBtn onPress={copyShareLink} activeOpacity={1}>
          <CopyText>一键复制地址</CopyText>
        </CopyBtn>
        <CloseBtn onPress={handleClose} hitSlop={hitSlop(20)}>
          <CloseIcon
            source={icon_pc_close_light}
            style={{ tintColor: isDarkMode ? '#888' : undefined }}
          />
        </CloseBtn>
      </Content>
    </Pop>
  )
}

export default React.memo(TaskPcDialog, isEqual)
