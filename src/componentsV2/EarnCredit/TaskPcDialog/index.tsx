import React, { useState, useEffect, useContext, Suspense } from 'react'
import GlobalEventEmitter from '../../../utilsV2/globalEventEmitter'
import { NativeInfoContext } from '../../../contextV2/nativeInfoContext'
import { UserInfoContext } from '../../../contextV2/userInfoContext'
import UserLoginStatus from '../../../typesV2/userLoginStatus'
import monthlyTicketGuideContext from '../../../contextV2/monthlyTicketGuideContext'
import { MonthlyTicketGuideStatus } from '../../../typesV2/monthlyTicketGuide'
import isEqual from 'lodash.isequal'

const TaskPcDialogLazy = React.lazy(() => import('./TaskPcDialog.lazy'))

const TaskPcDialog = () => {
  const [show, setShow] = useState(false)
  const nativeInfo = useContext(NativeInfoContext)
  const { loginStatus } = useContext(UserInfoContext)
  const { guideStatus } = useContext(monthlyTicketGuideContext)
  const [isPCListenTask, setIsPCListen] = useState(false)

  useEffect(() => {
    if (
      nativeInfo.pcTask === '1' &&
      loginStatus === UserLoginStatus.login &&
      guideStatus === MonthlyTicketGuideStatus.noNeed
    ) {
      setShow(true)
    }
  }, [])

  useEffect(() => {
    const listener = GlobalEventEmitter.addListener(
      'changeDialog',
      ({ visible, isPCListen }: { visible: boolean; isPCListen?: boolean }) => {
        setShow(visible)
        typeof isPCListen === 'boolean' && setIsPCListen(isPCListen)
      }
    )
    return () => {
      listener.remove()
    }
  }, [])

  const handleClose = () => {
    setShow(false)
  }

  if (show) {
    return (
      <Suspense fallback={null}>
        <TaskPcDialogLazy
          onClose={handleClose}
          isPCListenTask={isPCListenTask}
        />
      </Suspense>
    )
  } else {
    return null
  }
}

export default React.memo(TaskPcDialog, isEqual)
