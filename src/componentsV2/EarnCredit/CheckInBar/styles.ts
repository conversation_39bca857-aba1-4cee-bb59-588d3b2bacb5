import { BetterImage, Text, Touch } from '@xmly/rn-components'
import { Image, View, Text as RNText } from 'react-native'
import styled from 'styled-components'
import { width } from '../../../constantsV2/dimensions'
import { ThemeStyle } from '../../../typesV2/themeInfo'

const checkInItemWidth = (width - 60) / 7
const checkInItemInnerHeight = checkInItemWidth * (53 / 44)

const getDayItemInnerBGColor = ({ theme, checked, isGiftNotReceived, isTomorrow }: { theme?: ThemeStyle; checked: boolean; isGiftNotReceived?: boolean; isTomorrow?: boolean }) => {
  if (isTomorrow) {
    return theme?.checkInBar.tomorrow_bg_color
  }
  return isGiftNotReceived ? theme?.checkInBar.not_received_bg_color : checked ? theme?.checkInBar.new_bg_color_checked : theme?.checkInBar.new_bg_color
}

export const DayItem = styled(Touch)`
  align-items: center;
`

export const DayItemNew = styled(Touch)`
  align-items: center;
  flex-shrink: 0;
  margin: 0 1px;
`

export const DayItemInnerNew = styled(View)`
  width: 100%;
  height: ${checkInItemInnerHeight}px;
  background-color: ${({ theme, checked, isGiftNotReceived, isTomorrow }: { theme?: ThemeStyle; checked: boolean; isGiftNotReceived?: boolean; isTomorrow?: boolean }) =>
    getDayItemInnerBGColor({ theme, checked, isGiftNotReceived, isTomorrow })};
  border-radius: 4px;
`
export const LogoImg = styled(Image)`
  width: 100%;
  margin-top: 2px;
  height: 32px;
`

export const LogoImgNew = styled(Image)`
  width: 100%;
  height: 100%;
`

export const NameText = styled(RNText)`
  position: absolute;
  top: 35px;
  width: 100%;
  font-size: 13px;
  text-align: center;
  color: #ff4646;
  font-family: XmlyNumber;
  line-height: 17px;
`

export const DayText = styled(Text)`
  font-size: 11px;
  font-family: PingFangSC-Regular;
  width: 100%;
  font-weight: 400;
  text-align: center;
  line-height: 16px;
  margin-top: 6px;
`

export const DayTextNew = styled(Text)`
  font-size: 11px;
  font-family: PingFangSC-Regular;
  width: 100%;
  font-weight: 400;
  text-align: center;
  margin-top: 4px;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.checkInBar?.common_label_color};
`
export const TipImage = styled(BetterImage).attrs({
  source: ({ theme }: { theme: ThemeStyle }) => theme.icon.icon_wait_gift,
})`
  position: absolute;
  top: -5px;
  width: 40px;
  height: 18px;
`

export const Container = styled(View)`
  width: 100%;
  align-items: flex-start;
`
export const Title = styled(Text)`
  font-size: 17px;
  font-family: PingFangSC-Semibold;
  font-weight: bold;
  text-align: left;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  line-height: 24px;
  margin-bottom: 4px;
  margin-left: 16px;
`
export const SubTitle = styled(Text)`
  font-size: 12px;
  font-family: PingFangSC-Regular;
  text-align: left;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.checkInBar.sub_title_color};
  line-height: 17px;
  margin-left: 16px;
`
export const DayItemsWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  justify-content: center;
  padding-top: 16px;
`

export const DayItemCheckIcon = styled(Image)`
  width: 16px;
  height: 16px;
  position: absolute;
  right: 6px;
  bottom: 13px;
`

export const TomorrowTip = styled(Image)`
  position: absolute;
  width: 49px;
  height: 18px;
  top: -20px;
`

export const MultiAwardsReceiveTag = styled(View)`
  width: 45px;
  height: 17px;
  border-radius: 8.5px;
  background: #ff4444;
  margin-top: 3px;
  align-items: center;
  justify-content: center;
`

export const MultiAwardsReceiveTagLabel = styled(Text)`
  font-size: 9px;
  font-weight: bold;
  text-align: center;
  color: #ffffff;
`
