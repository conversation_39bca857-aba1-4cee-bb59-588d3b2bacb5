import React, { useEffect, useState } from 'react'
import isEqual from 'lodash.isequal'
import { Title } from './styles'
import { View } from 'react-native'
import { textAlignMiddleStyle } from '../../../constantsV2/layout'
import styled from 'styled-components'
import SignInNotificationSwitch from '../../SignInNotification/SignInNotificationSwitch'
import { RootState } from '../../../store'
import { useSelector } from 'react-redux'
import { ConfigCenter } from '@xmly/rn-sdk'

type Props = {}

const TitleWrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  margin-bottom: 4px;
  width: 100%;
`

const defaultHeaderTitle = '签到领月票'
const CheckInBarHeader: React.FC<Props> = (props) => {
  const [headerTitle, setHeaderTitle] = useState(defaultHeaderTitle)

  const notificationStatus = useSelector(
    (state: RootState) => state.notification.status
  )

  const fetchHeaderTitle = async () => {
    try {
      const res = await ConfigCenter.getConfig(
        'toc',
        'credit_center_check_in_bar_title'
      )
      if (res) {
        setHeaderTitle(res)
      } else {
        setHeaderTitle(defaultHeaderTitle)
      }
    } catch (err) {
      console.log(err)
      setHeaderTitle(defaultHeaderTitle)
    }
  }

  useEffect(() => {
    fetchHeaderTitle()
  }, [])

  return (
    <>
      <TitleWrapper>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'center',
          }}
        >
          <Title
            style={[textAlignMiddleStyle, { marginLeft: 0, marginBottom: 0 }]}
          >
            {headerTitle}
          </Title>
        </View>

        <SignInNotificationSwitch notificationStatus={notificationStatus} />
      </TitleWrapper>
    </>
  )
}

export default React.memo(CheckInBarHeader, isEqual)
