import React, { useContext, useRef } from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState } from '../../../store'
import {
  EnumVIPTag,
  SignInGiftReceiveStatus,
  SignInStatus,
} from '../../../typesV2/signInNew'
import { DayItemsWrapper } from './styles'
import monthlyTicketGuideContext from '../../../contextV2/monthlyTicketGuideContext'
import { View } from 'react-native'
import { MonthlyTicketGuideStep } from '../../../typesV2/monthlyTicketGuide'
import CheckInBarItemNew from './CheckInBarItemNew'

type Props = {
  giftReceiveStatus: SignInGiftReceiveStatus
  giftReceiveStatusNew: SignInGiftReceiveStatus
  showGiftModal: () => void
  containerWidth: number
}

const CheckInBarList: React.FC<Props> = ({
  giftReceiveStatusNew,
  showGiftModal,
  containerWidth,
}) => {
  const containerRef = useRef<View>(null)
  const signInRecords = useSelector(
    (state: RootState) => state.signInInfo.signInRecords
  )
  const todaySignInDay = useSelector(
    (state: RootState) => state.signInInfo.todaySignInDay
  )
  const isVip = useSelector(
    (state: RootState) => state.signInInfo.vipTag === EnumVIPTag.vip
  )
  const isSignInToday = useSelector(
    (state: RootState) =>
      state.signInInfo.todaySignInStatus === SignInStatus.isSignedIn
  )

  const { setLayoutInfo } = useContext(monthlyTicketGuideContext)

  const handleLayout = () => {
    if (containerRef.current) {
      containerRef.current.measure((x, y, width, height, pageX, pageY) => {
        setLayoutInfo(MonthlyTicketGuideStep.VoteModalB, {
          x: pageX,
          y: pageY - 7,
          width: width,
          height: height + 24,
          radius: 10,
        })
      })
    }
  }

  if (signInRecords && signInRecords.length > 0) {
    return (
      <View
        ref={containerRef}
        onLayout={handleLayout}
        style={{ width: '100%' }}
      >
        <DayItemsWrapper>
          {signInRecords.map((signInRecord) => (
            <CheckInBarItemNew
              key={signInRecord.day}
              containerWidth={containerWidth}
              giftReceiveStatus={giftReceiveStatusNew}
              signInRecord={signInRecord}
              todaySignInDay={todaySignInDay}
              isVip={isVip}
              isSignInToday={isSignInToday}
              showGiftModal={showGiftModal}
            />
          ))}
        </DayItemsWrapper>
      </View>
    )
  } else {
    return null
  }
}

export default React.memo(CheckInBarList, isEqual)
