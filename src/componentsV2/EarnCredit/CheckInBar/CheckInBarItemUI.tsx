import React from 'react'
import isEqual from 'lodash.isequal'
import icon_sign_in_checked from '../../../appImagesV2/icon_sign_in_checked'
import icon_tomorrow_tip from '../../../appImagesV2/icon_tomorrow_tip'
import { textAlignMiddleStyle } from '../../../constantsV2/layout'
import { DayItemNew, DayItemInnerNew, LogoImgNew, DayItemCheckIcon, MultiAwardsReceiveTag, MultiAwardsReceiveTagLabel, DayTextNew, TomorrowTip } from './styles'
import { AwardStatus, SignInStatus, SignInUserAwardType } from '../../../typesV2/signInNew'
import { fillOrigin } from '../../../utilsV2/image2CustomSize'
import rnEnv from '../../../../rnEnv'
import useDoubleAwardTask from '../../../hooks/useDoubleAwardTask'

type Props = {
  showUnSignLogo?: boolean
  containerWidth: number
  onPress: (item: SignInUserAwardType) => void
  item: SignInUserAwardType
  isTodayMultiAwardsNotReceive?: boolean
  withoutTodayLabel?: boolean
  isGiftNotReceived?: boolean
  todaySignInDay: number
  todaySignInStatus: SignInStatus
  showCanReceiveLabel?: boolean
  noTomorrowTip?: boolean
  judgeIsChecked?: (item: SignInUserAwardType) => boolean
}

const CheckInBarItemUI: React.FC<Props> = ({
  showUnSignLogo,
  item,
  containerWidth,
  todaySignInDay,
  todaySignInStatus,
  isTodayMultiAwardsNotReceive,
  onPress,
  noTomorrowTip,
  judgeIsChecked,
  isGiftNotReceived,
  showCanReceiveLabel,
  withoutTodayLabel,
}) => {
  const { day, awardStatus, logo, unsignedLogo } = item
  const checkInItemWidth = (containerWidth - 36) / 7
  const checkInItemInnerHeight = checkInItemWidth * (53 / 44)
  const isToday = todaySignInDay === day
  const isTomorrow = noTomorrowTip ? false : todaySignInDay !== 0 && todaySignInDay + 1 === day
  const isChecked =
    typeof judgeIsChecked === 'function' ? judgeIsChecked(item) : awardStatus === AwardStatus.isDispatched || (isToday && todaySignInStatus === SignInStatus.isSignedIn)

  const logoUri = fillOrigin(showUnSignLogo && unsignedLogo ? (isChecked ? logo : unsignedLogo) : logo, 3, rnEnv.isTest())

  const icon = { uri: logoUri.includes('http') || logoUri.includes('https') ? logoUri : `https:${logoUri}` }

  const dayItemInnerNewStyle = {
    height: checkInItemInnerHeight,
  }
  const doubleAwardTaskInfo = useDoubleAwardTask(!!item?.context?.doubleAwardTask);
  const doubleAwardTaskUncompleted = doubleAwardTaskInfo.uncompleted && isToday;
  const handlePress = () => {
    if (doubleAwardTaskUncompleted) {
      return doubleAwardTaskInfo.onBtnClick();
    }
    onPress(item)
  }
  const dayLabel = doubleAwardTaskUncompleted ?
    doubleAwardTaskInfo.btnText
    :
    showCanReceiveLabel ?
      '待领取'
      :
      isToday && !withoutTodayLabel ?
        '今天'
        :
        `第${day}天`
  const highlightTextColor = showCanReceiveLabel ? { color: '#FF4444' } : undefined

  return (
    <DayItemNew
      key={day}
      activeOpacity={1}
      onPress={handlePress}
      style={{ width: checkInItemWidth }}
      accessibilityLabel={`${dayLabel} ${isChecked ? '已签到' : '未签到'}`}
    >
      <DayItemInnerNew
        isGiftNotReceived={isGiftNotReceived}
        style={dayItemInnerNewStyle}
        checked={isChecked}
        isTomorrow={isTomorrow}
      >
        <LogoImgNew
          resizeMode="contain"
          source={icon}
        />
        {isChecked ? <DayItemCheckIcon source={icon_sign_in_checked} /> : null}
      </DayItemInnerNew>
      {doubleAwardTaskInfo.uncompleted && isToday ?
        <MultiAwardsReceiveTag>
          <MultiAwardsReceiveTagLabel style={textAlignMiddleStyle}>{dayLabel}</MultiAwardsReceiveTagLabel>
        </MultiAwardsReceiveTag>
        :
        isTodayMultiAwardsNotReceive ? (
          <MultiAwardsReceiveTag>
            <MultiAwardsReceiveTagLabel style={textAlignMiddleStyle}>待领取</MultiAwardsReceiveTagLabel>
          </MultiAwardsReceiveTag>
        ) : (
          <DayTextNew style={highlightTextColor}>{dayLabel}</DayTextNew>
        )}
      {/* 展示明天待领取Tip */}
      {isTomorrow ? <TomorrowTip source={icon_tomorrow_tip} /> : null}
    </DayItemNew>
  )
}

export default React.memo(CheckInBarItemUI, isEqual)
