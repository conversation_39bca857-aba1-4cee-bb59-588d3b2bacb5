import React, { useContext } from 'react'
import isEqual from 'lodash.isequal'
import { CheckInBarItemProps } from './types'
import { Toast, Page } from '@xmly/rn-sdk'
import getUrlToOpen from '@xmly/rn-sdk/dist/Navigate'
import { safetyToString } from '@xmly/rn-utils'
import { AwardStatus, SignInGiftReceiveStatus, SignInStatus } from '../../../typesV2/signInNew'
import xmlog from '../../../utilsV2/xmlog'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../../store'
import { TaskStatus } from '../../../typesV2/taskList'
import monthlyTicketGuideContext from '../../../contextV2/monthlyTicketGuideContext'
import CheckInBarItemUI from './CheckInBarItemUI'
import useDoubleAwardTask from '../../../hooks/useDoubleAwardTask'

type Props = CheckInBarItemProps & {
  showGiftModal: () => void
}

const CheckInBarItemNew: React.FC<Props> = ({ todaySignInDay, signInRecord, giftReceiveStatus, showGiftModal, containerWidth }) => {
  const { setHasShowMonthlyTicketGuide } = useContext(monthlyTicketGuideContext)
  const todaySignInMultiAwardsConfig = useSelector((state: RootState) => state.signInInfo.todaySignInMultiAwardsConfig)
  const todaySignInStatus = useSelector((state: RootState) => state.signInInfo.todaySignInStatus)
  const { day, awardStatus, context = {} } = signInRecord

  const isTodayHasMultiAwards = todaySignInMultiAwardsConfig?.targetDay === day

  const isTodayMultiAwardsNotReceive = isTodayHasMultiAwards && todaySignInMultiAwardsConfig.status === TaskStatus.finished

  const isToday = todaySignInDay === day

  const isChecked = awardStatus === AwardStatus.isDispatched || (isToday && todaySignInStatus === SignInStatus.isSignedIn)

  const doubleAwardTaskInfo = useDoubleAwardTask(!!signInRecord?.context?.doubleAwardTask);
  const doubleAwardTaskUncompleted = doubleAwardTaskInfo.uncompleted && isToday;

  const handlePress = async () => {
    // 任务中心-点击签到  点击事件
    xmlog.click(40150, undefined, {
      currPage: '任务中心',
      isFinish: isChecked ? '1' : '0',
      getStatus: context.isGift ? safetyToString(giftReceiveStatus) : '',
      item: safetyToString(day),
      status: doubleAwardTaskUncompleted ?
        doubleAwardTaskInfo?.btnText
        :
        isTodayMultiAwardsNotReceive ?
          '未领取'
          :
          isChecked ?
            '已签到'
            :
            '未签到',
    })

    if (doubleAwardTaskUncompleted) {
      return doubleAwardTaskInfo.onBtnClick();
    }

    if (isChecked) {
      if (!isTodayMultiAwardsNotReceive) {
        // 原有逻辑，当配置了跳转链接则跳转到具体页面
        if (context.link) {
          Page.start(getUrlToOpen(context.link))
          return
        }

        // 点击判断当前是连签礼包，则展示礼包弹窗，通过判断context 内 awards 数组中对象的label字段是否含有 “连签礼包” 来判断
        if (context.awards && context.awards.length > 0) {
          const showBigGiftDescModal = context.awards.some((item) => item.label && item.label.includes('连签礼包'))
          if (showBigGiftDescModal) {
            showGiftModal()
            Toast.info('今日已领取礼包')
          } else {
            Toast.info('已领取奖励')
          }
        } else {
          Toast.info('已领取奖励')
        }
      } else {
        // 处理领取多选一的奖励
        setHasShowMonthlyTicketGuide(false) // 去掉月票新手引导的互斥标识
        await store.dispatch.tomorrowAward.refresh({ modalVisible: false }) // 设置隔日领奖弹窗的展示为false，防止出现两个弹窗重叠的情况
        store.dispatch.signInInfo.refresh({ signInSuccessModalVisible: true })
      }
    } else {
      if (context.awards && context.awards.length > 0) {
        const showBigGiftDescModal = context.awards.some((item) => item.label && item.label.includes('连签礼包'))
        if (showBigGiftDescModal) {
          showGiftModal()
        } else {
          Toast.info('当日签到后可领取奖励')
        }
      } else {
        Toast.info('当日签到后可领取奖励')
      }
    }
  }

  const isGiftNotReceived = context?.isGift && giftReceiveStatus === SignInGiftReceiveStatus.notReceived

  return (
    <CheckInBarItemUI
      isGiftNotReceived={isGiftNotReceived}
      onPress={handlePress}
      item={signInRecord}
      containerWidth={containerWidth}
      todaySignInDay={todaySignInDay}
      todaySignInStatus={todaySignInStatus}
      isTodayMultiAwardsNotReceive={isTodayMultiAwardsNotReceive}
    />
  )
}

export default React.memo(CheckInBarItemNew, isEqual)
