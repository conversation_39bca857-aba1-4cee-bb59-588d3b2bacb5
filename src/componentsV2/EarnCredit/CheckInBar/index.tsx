import React, { useContext, useEffect, useState } from 'react'
import { connect, useSelector } from 'react-redux'
import { RootState } from '../../../store'
import {
  ABRuleTag,
  EnumVIPTag,
  SignInGiftReceiveStatus,
  SignInUserAwardType,
} from '../../../typesV2/signInNew'
import isEqual from 'lodash.isequal'
import { ConfigCenter } from '@xmly/rn-sdk'
import getGiftReceiveStatus from '../../../servicesV2/getGiftReceiveStatus'
import lodashGet from 'lodash.get'
import { Container } from './styles'
import CheckInBarHeader from './CheckInBarHeader'
import CheckInBarList from './CheckInBarList'
import giftReceiveStatusManager from '../../../modelsV2/giftReceiveStatusManager'
import { usePageOnResume } from '@xmly/rn-utils'
import styled from 'styled-components'
import { TouchableWithoutFeedback, View } from 'react-native'
import { ThemeStyle } from 'typesV2/themeInfo'
import { UserInfoContext } from 'contextV2/userInfoContext'
import UserLoginStatus from 'typesV2/userLoginStatus'
import goToLoginForAbTest from 'utilsV2/goToLoginForAbTest'

interface CheckInBarProps {
  signInRecords: SignInUserAwardType[]
  vipTag: EnumVIPTag
  todaySignInDay: number
  containerWidth: number
  abRuleTag: ABRuleTag
  showGiftModal: () => void
}

const MaskWrap = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
`

const Overlay = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.credit.checkbar_overlay_warp_bg_color};
  z-index: 1;
`

const getGiftKey = async () => {
  try {
    const res = await ConfigCenter.getConfig('toc', 'credit_center_gift_key')
    if (typeof res === 'string') {
      return res
    }
    return null
  } catch (err) {
    return null
  }
}

const CheckInBar: React.FC<CheckInBarProps> = (props) => {
  const isVip = props?.vipTag === EnumVIPTag.vip ? true : false
  const [giftReceiveStatus, setGiftReceiveStatus] =
    useState<SignInGiftReceiveStatus>(SignInGiftReceiveStatus.received)

  const [giftReceiveStatusNew, setGiftReceiveStatusNew] =
    useState<SignInGiftReceiveStatus>(SignInGiftReceiveStatus.UNSET)

  const { loginStatus } = useContext(UserInfoContext)
  const { noLoginAbTestStatus } = useSelector(
    (state: RootState) => state.signInInfo
  )

  const tryGetGiftPackageReceiveStatusByGiftId = async ({
    refresh,
  }: {
    refresh?: boolean
  }) => {
    if (
      props.abRuleTag === ABRuleTag.true &&
      props.signInRecords &&
      props.signInRecords.length > 0 &&
      props.signInRecords.every((record) => record.context)
    ) {
      const status = refresh
        ? await giftReceiveStatusManager.refreshStatus({
            signInRecords: props.signInRecords,
            todaySignInDay: props.todaySignInDay,
          })
        : await giftReceiveStatusManager.getStatus({
            signInRecords: props.signInRecords,
            todaySignInDay: props.todaySignInDay,
          })
      setGiftReceiveStatusNew(status)
    }
  }

  useEffect(() => {
    tryGetGiftPackageReceiveStatusByGiftId({ refresh: false })
  }, [props.signInRecords, props.abRuleTag])

  const tryGetGiftReceiveStatus = async () => {
    if (isVip) {
      setGiftReceiveStatus(SignInGiftReceiveStatus.received)
      return
    }

    if (props.signInRecords && props.signInRecords.length > 0) {
      if (props.signInRecords.some((record) => record?.context?.isGift)) {
        try {
          const giftKey = await getGiftKey()
          if (giftKey) {
            const giftReceiveStatusRes = await getGiftReceiveStatus(giftKey)
            if (giftReceiveStatusRes && giftReceiveStatusRes.ret === 0) {
              const data = lodashGet(
                giftReceiveStatusRes,
                'data',
                SignInGiftReceiveStatus.unavailable
              )
              setGiftReceiveStatus(data)
            } else {
              setGiftReceiveStatus(SignInGiftReceiveStatus.unavailable)
            }
          }
        } catch (err) {
          setGiftReceiveStatus(SignInGiftReceiveStatus.unavailable)
        }
      }
    }
  }

  const handlePageResume = () => {
    tryGetGiftReceiveStatus()
    tryGetGiftPackageReceiveStatusByGiftId({ refresh: true })
  }

  usePageOnResume(handlePageResume)

  useEffect(() => {
    tryGetGiftReceiveStatus()
  }, [props.signInRecords])

  const checkCredit = () => {
    goToLoginForAbTest(true)
  }

  if (props.signInRecords && props.signInRecords.length > 0) {
    return (
      <Container>
        {loginStatus === UserLoginStatus.notLogin &&
          noLoginAbTestStatus === '2' && (
            <TouchableWithoutFeedback onPress={checkCredit}>
              <MaskWrap>
                <Overlay />
              </MaskWrap>
            </TouchableWithoutFeedback>
          )}
        <CheckInBarHeader />
        <CheckInBarList
          containerWidth={props.containerWidth}
          giftReceiveStatus={giftReceiveStatus}
          giftReceiveStatusNew={giftReceiveStatusNew}
          showGiftModal={props.showGiftModal}
        />
      </Container>
    )
  } else return null
}

export default connect(
  ({
    signInInfo: { signInRecords, vipTag, todaySignInDay, abRuleTag },
  }: RootState) => ({
    signInRecords,
    vipTag,
    todaySignInDay,
    abRuleTag,
  })
)(React.memo(CheckInBar, isEqual))
