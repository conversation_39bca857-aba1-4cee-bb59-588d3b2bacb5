import React, { useContext, useEffect, useRef, useState } from 'react'
import {
  StyleSheet,
  ScrollView,
  View,
  Platform,
  NativeModules,
} from 'react-native'
import {
  ScrollAnalyticWapper,
  ScrollEventSender,
} from '@xmly/react-native-page-analytics'
import { useSelector } from 'react-redux'
import { RootState } from '../../../store'
import HeaderPlain from '../../common/Headers/HeaderPlain'
import { useNavigation } from '@react-navigation/native'
import { ThemeContext } from '../../../contextV2/themeContext'
import RenderTaskList from './RenderTaskList'
import displayTaskListFilter from '../../../utilsV2/displayTaskListFilter'
import isEqual from 'lodash.isequal'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { usePageOnResume } from '@xmly/rn-utils'
import { TaskStatus } from '../../../typesV2/taskList'
import TaskItemEmpty from './TaskItemEmpty'
import styled from 'styled-components'
import StaticListFooter from '../../common/StaticListFooter'

type Props = {}

const styles = StyleSheet.create({
  listWrapper: {
    paddingLeft: 16,
    borderTopWidth: 0.5,
    borderTopColor: '#EEEEEE',
  },
})

const ContentWrapper = styled(View)`
  padding-right: 16px;
`

const TaskListId = 'TaskListId'

const TaskListScreen: React.FC<Props> = () => {
  const navigation = useNavigation()
  const safeAreaInsets = useSafeAreaInsets()
  const scrollViewRef = useRef<ScrollView>(null)
  const handleScroll = () => {
    ScrollEventSender.send(TaskListId, 'scroll')
  }
  const { taskList } = useSelector((state: RootState) => state.taskCenter)
  const thirdpartyTaskExist = useSelector(
    (state: RootState) => state.thirdpartyTask.isExist
  )
  const theme = useContext(ThemeContext)
  const filteredTaskList = displayTaskListFilter(taskList, thirdpartyTaskExist)
  const completedTaskCount = useRef(0)
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true)
    }, 10)

    return () => {
      clearTimeout(timer)
    }
  }, [])

  useEffect(() => {
    if (filteredTaskList.length > 0) {
      const mCompletedTaskCount =
        filteredTaskList.filter((task) => task.status === TaskStatus.finished)
          ?.length || 0
      if (
        mCompletedTaskCount > completedTaskCount.current &&
        scrollViewRef.current
      ) {
        scrollViewRef.current.scrollTo(0, 0, true)
      }
      completedTaskCount.current = mCompletedTaskCount
    }
  }, [filteredTaskList])

  const handlePressBack = () => {
    navigation.goBack()
  }

  usePageOnResume(() => {
    if (Platform.OS == 'ios') {
      NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
    }
  })

  return (
    <View style={{ flex: 1, backgroundColor: theme.common.bg_color }}>
      <HeaderPlain label='全部任务' onPressBack={handlePressBack} />

      <ScrollAnalyticWapper id={TaskListId} viewStyle={{ flex: 1 }}>
        <ScrollView
          ref={scrollViewRef}
          onScroll={handleScroll}
          style={[
            styles.listWrapper,
            {
              borderTopColor: theme.task.taskList.top_border_color,
            },
          ]}
          contentContainerStyle={{ paddingBottom: safeAreaInsets.bottom }}
          showsVerticalScrollIndicator={false}
        >
          {visible ? (
            <RenderTaskList
              taskList={filteredTaskList}
              judgeItemIsLast={(index) => index + 1 === filteredTaskList.length}
              itemContainerStyle={{ paddingVertical: 16, paddingRight: 16 }}
            />
          ) : (
            <ContentWrapper>
              <TaskItemEmpty />
              <TaskItemEmpty />
              <TaskItemEmpty />
              <TaskItemEmpty />
              <TaskItemEmpty />
              <TaskItemEmpty isLast />
            </ContentWrapper>
          )}
          <StaticListFooter
            containerStyle={{ paddingRight: 32 }}
            onShow={() => {}}
            inViewPortGroupName='task_list_screen_footer'
            hasMore={!visible}
          />
        </ScrollView>
      </ScrollAnalyticWapper>
    </View>
  )
}

export default React.memo(TaskListScreen, isEqual)
