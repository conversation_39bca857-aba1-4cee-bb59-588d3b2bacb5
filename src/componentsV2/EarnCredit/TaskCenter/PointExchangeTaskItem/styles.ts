import { BetterImage, Text } from '@xmly/rn-components'
import { FlatList, View } from 'react-native'
import styled from 'styled-components'
import { ThemeStyle } from '../../../../typesV2/themeInfo'
import LinearGradient from 'react-native-linear-gradient'

export const Container = styled(LinearGradient).attrs({})`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin: 0 -10px;
  border-radius: 10px;
  height: 68px;
  padding: 0 10px;
`
export const StrongTitle = styled(Text)`
  font-size: 16px;
  font-weight: 700;
  text-align: center;
  color: #fe4444;
`
export const TaskItemLeft = styled(View)`
  flex: 1;
  margin-right: 5px;
  overflow: hidden;
`
export const TaskTitleWrap = styled(View)`
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  overflow: hidden;
`
export const TaskTitle = styled(Text)`
  font-size: 16px;
  font-family: PingFangSC-Regular;
  text-align: left;
  color: ${({ theme }: { theme?: ThemeStyle }) =>
    theme?.everydayChallenge.summaryTaskList.text};
  letter-spacing: 0px;
  flex-shrink: 0;
  font-weight: 500;
`
export const TaskTag = styled(BetterImage)`
  width: 46px;
  height: 16px;
  margin-right: 4px;
`

export const StepsWrapper = styled(View)`
  margin-top: 4px;
`

export const StepsProgressWrapper = styled(FlatList)`
  overflow: hidden;
  width: 100%;
  flex: 1;
`

export const ProgressBarWrapper = styled(View)`
  position: absolute;
  width: 100%;
  align-items: center;
  justify-content: center;
  bottom: 21px;
`
