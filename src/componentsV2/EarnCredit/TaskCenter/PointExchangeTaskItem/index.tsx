import React, { memo, useEffect, useRef, useCallback } from 'react'
import { TaskItemComponentProps, TaskStatus } from 'typesV2/taskList'
import performTask from 'modulesV2/performTask'
import xmlog from '../../../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import { store } from '../../../../store'
import getImageUri from '../../../../utilsV2/getImageUri'
import rnEnv from '../../../../../rnEnv'
import TaskItemActionButton from './TaskItemActionButton'
import isEqual from 'lodash.isequal'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import {
  Container,
  TaskItemLeft,
  TaskTitleWrap,
  TaskTag,
  TaskTitle,
  StrongTitle,
} from './styles'
import { taskListAid } from '../../../../constantsV2'
import { Toast } from '@xmly/rn-sdk'
import RNAlert from '../../../Confirm'

type Props = TaskItemComponentProps & {}

const getTitle = (title: string, condition?: number) => {
  const titles = title.split('$point')
  return {
    title_left: titles[0],
    title_right: titles[1],
    point: String(condition ?? 0),
  }
}

const PointExchangeTaskItem: React.FC<Props> = ({
  taskItem,
  containerStyle,
  index,
  onMount,
  withoutScrollAnalyticComp,
  aid,
  onShow,
  onReportTaskPressEvent,
  onGetTaskAwardAction,
  shouldButtonDisabled,
  onRequestAid,
}) => {
  const clickBtnEnable = useRef<boolean>(true)
  const isReceiveAward = useRef(false)
  const mAid = typeof aid === 'number' ? aid : taskListAid

  useEffect(() => {
    if (withoutScrollAnalyticComp) {
      typeof onMount === 'function' && onMount(taskItem)
    }
  }, [withoutScrollAnalyticComp])

  const isButtonDisabled =
    typeof shouldButtonDisabled === 'function' && shouldButtonDisabled(taskItem)

  const handleTaskClick = async () => {
    try {
      const confirmed = await confirm()
      if(!confirmed) return
      if (isButtonDisabled) return
      if (isReceiveAward.current) return
      const aid =
        typeof onRequestAid === 'function' ? await onRequestAid() : mAid

      if (taskItem.status === TaskStatus.finished) {
        isReceiveAward.current = true
        if (typeof onGetTaskAwardAction === 'function') {
          await onGetTaskAwardAction({ taskItem, aid })
        } else {
          await store.dispatch.taskCenter.getTaskAward({
            taskItem,
            aid,
          })
        }

        isReceiveAward.current = false
      } else {
        if (clickBtnEnable.current) {
          clickBtnEnable.current = false
          setTimeout(() => {
            clickBtnEnable.current = true
          }, 1000)
          performTask(taskItem, aid)
        }
      }
      if (typeof onReportTaskPressEvent === 'function') {
        onReportTaskPressEvent(taskItem, index)
      } else {
        xmlog.click(40143, undefined, {
          taskId: safetyToString(taskItem.id),
          taskTitle: safetyToString(taskItem.title),
          currPage: '任务中心',
          Item: safetyToString(
            taskItem.statusText ||
              (taskItem.status === TaskStatus.finished ? '去领取' : '去完成')
          ),
          rewardName: safetyToString(taskItem.desc),
          position: safetyToString(index + 1),
        })
      }
    } catch (err) {
      console.log(err)
      Toast.info('执行任务失败~')
    }
  }

  const { title = '', id, condition } = taskItem

  const titles = getTitle(title, condition)

  const onShowHandler = () => {
    if (typeof onShow === 'function') {
      onShow(taskItem, index)
    } else {
      xmlog.event(42772, 'slipPage', {
        taskId: id + '',
        taskTitle: title as string,
        currPage: '任务中心',
      })
    }
  }

  const confirm = useCallback(function confirm() {
    return new Promise((resolve, reject) => {
      RNAlert({
        message: `消费${condition}积分跳过？`,
        actions: [
          {
            text: '取消',
            onPress: () => resolve(false),
          },
          {
            text: '确认',
            onPress: () => resolve(true),
            primary: true,
          },
        ],
      })
    });
  }, [condition]);

  const renderContent = () => {
    return (
      <Container
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        colors={['rgba(255, 101, 82, 0.06)', 'rgba(255, 65, 112, 0.06)']}
        style={[containerStyle]}
      >
        <TaskItemLeft>
          <TaskTitleWrap>
            {taskItem.activityLogo ? (
              <TaskTag
                source={{
                  uri: getImageUri(taskItem.activityLogo, {
                    width: 46 * 3,
                    height: 16 * 3,
                    test: rnEnv.isTest(),
                  }),
                }}
              />
            ) : null}
            <TaskTitle
              numberOfLines={1}
              style={{
                maxWidth: taskItem.code !== 111 ? '60%' : undefined,
                flex: taskItem.code !== 111 ? undefined : 1,
              }}
            >
              {titles.title_left}
              <StrongTitle>{titles.point}</StrongTitle>
              {titles.title_right}
            </TaskTitle>
          </TaskTitleWrap>
        </TaskItemLeft>
        <TaskItemActionButton
          item={taskItem}
          onPress={handleTaskClick}
          disabled={isButtonDisabled}
        />
      </Container>
    )
  }

  if (withoutScrollAnalyticComp) {
    return renderContent()
  }

  return (
    // 普通任务
    <ScrollAnalyticComp itemKey={id + ''} onShow={onShowHandler}>
      {renderContent()}
    </ScrollAnalyticComp>
  )
}

export default memo(PointExchangeTaskItem, isEqual)
