import React, { useContext } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import {
  StepType,
  TaskItemType,
  TaskStatus,
} from '../../../../typesV2/taskList'
import styled from 'styled-components'
import isEqual from 'lodash.isequal'
import { ThemeStyle } from '../../../../typesV2/themeInfo'
import { textAlignMiddleStyle } from '../../../../constantsV2/layout'
import isMultiStepTaskFinished from '../../../../utilsV2/isMultiStepTaskFinished'
import LinearGradient from 'react-native-linear-gradient'
import { ThemeContext } from '../../../../contextV2/themeContext'

type Props = {
  item: TaskItemType
  onPress: () => void
  disabled?: boolean
}

const TaskButton = styled(LinearGradient).attrs({
  start: { x: 0, y: 0 },
  end: { x: 1, y: 0 },
})`
  padding: 0 12px;
  height: 28px;
  border-radius: 14px;
  justify-content: center;
  align-items: center;
  border-width: 0.5px;
`

const TaskButtonCanReceive = styled(TaskButton)`
  border-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.can_receive_border_color};
`

const TaskButtonNotFinish = styled(TaskButton)`
  border-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.not_finish_border_color};
`

const TaskButtonFinished = styled(TaskButton)`
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.finished_bg};
  border-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.finished_border_color};
`

const TaskButtonText = styled(Text)`
  font-size: 12px;
  font-family: PingFangSC-Regular;
  font-weight: bold;
  text-align: center;
`

const TaskButtonCanReceiveText = styled(TaskButtonText)`
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.can_receive_label_color_v2};
`

const TaskButtonNotFinishText = styled(TaskButtonText)`
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.not_finish_label_color_v2};
`

const TaskButtonFinishedText = styled(TaskButtonText)`
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.finished_label_color_v2};
`

const FinishedButton = () => {
  const theme = useContext(ThemeContext)
  return (
    <TaskButtonFinished
      colors={[
        theme.task.item.actionButton.bg_start_color_finished,
        theme.task.item.actionButton.bg_end_color_finished,
      ]}
    >
      <TaskButtonFinishedText style={textAlignMiddleStyle}>
        已完成
      </TaskButtonFinishedText>
    </TaskButtonFinished>
  )
}

const CanReceiveButton = ({ statusText }: { statusText?: string }) => {
  const theme = useContext(ThemeContext)
  return (
    <TaskButtonCanReceive
      colors={[
        theme.task.item.actionButton.bg_start_color_can_receive,
        theme.task.item.actionButton.bg_end_color_can_receive,
      ]}
    >
      <TaskButtonCanReceiveText style={textAlignMiddleStyle}>
        {statusText || '已跳过'}
      </TaskButtonCanReceiveText>
    </TaskButtonCanReceive>
  )
}

const NotFinishButton = ({ statusText }: { statusText?: string }) => {
  const theme = useContext(ThemeContext)
  return (
    <TaskButtonNotFinish
      colors={[
        theme.task.item.actionButton.bg_start_color_not_finish,
        theme.task.item.actionButton.bg_end_color_not_finish,
      ]}
    >
      <TaskButtonNotFinishText style={textAlignMiddleStyle}>
        {statusText || '立即跳过'}
      </TaskButtonNotFinishText>
    </TaskButtonNotFinish>
  )
}

const TaskItemActionButton: React.FC<Props> = ({ item, onPress, disabled }) => {
  const { status, stepType, statusText } = item

  const taskDisabled =
    disabled ||
    (item.stepType === StepType.Single &&
      (status === TaskStatus.nonValid || status === TaskStatus.received)) ||
    isMultiStepTaskFinished(item) // 多步任务，所有子任务状态都是已领取时判断成已完成

  const handleTaskClick = () => {
    onPress()
  }

  const renderButtonContent = () => {
    if (typeof disabled !== 'undefined' && disabled) {
      return <CanReceiveButton statusText={statusText} />
    }

    if (stepType === StepType.Multi) {
      // 分步任务
      if (isMultiStepTaskFinished(item)) {
        return <FinishedButton />
      }
      const canReceiveStepItem = item.stepInfos!.find(
        (step) => step.stepStatus === TaskStatus.finished
      )
      if (canReceiveStepItem) {
        return <CanReceiveButton statusText={canReceiveStepItem.statusText} />
      } else {
        return <NotFinishButton statusText={statusText} />
      }
    }

    switch (status) {
      case TaskStatus.unfinished:
        return <NotFinishButton statusText={statusText} />
      case TaskStatus.finished:
        return <CanReceiveButton statusText={statusText} />
      default:
        return <FinishedButton />
    }
  }

  return (
    <Touch onPress={handleTaskClick} disabled={taskDisabled}>
      {renderButtonContent()}
    </Touch>
  )
}

export default React.memo(TaskItemActionButton, isEqual)
