import React from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { View } from 'react-native'
import { StepType, TaskItemType, TaskStatus } from '../../../typesV2/taskList'
import styled from 'styled-components'
import isEqual from 'lodash.isequal'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import { textAlignMiddleStyle } from '../../../constantsV2/layout'
import isMultiStepTaskFinished from '../../../utilsV2/isMultiStepTaskFinished'

type Props = {
  item: TaskItemType
  onPress: () => void
  disabled?: boolean
}

const TaskButton = styled(View)`
  width: 58px;
  height: 28px;
  border-radius: 14px;
  justify-content: center;
  align-items: center;
  border-width: 0.5px;
`

const TaskButtonCanReceive = styled(TaskButton)`
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.can_receive_bg};
  border-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.can_receive_border_color};
`

const TaskButtonNotFinish = styled(TaskButton)`
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.not_finish_bg};
  border-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.not_finish_border_color};
`

const TaskButtonFinished = styled(TaskButton)`
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.finished_bg};
  border-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.finished_border_color};
`

const TaskButtonText = styled(Text)`
  font-size: 12px;
  font-family: PingFangSC-Regular;
  font-weight: bold;
  text-align: center;
`

const TaskButtonCanReceiveText = styled(TaskButtonText)`
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.can_receive_label_color};
`

const TaskButtonNotFinishText = styled(TaskButtonText)`
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.not_finish_label_color};
`

const TaskButtonFinishedText = styled(TaskButtonText)`
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.actionButton.finished_label_color};
`

const FinishedButton = () => {
  return (
    <TaskButtonFinished>
      <TaskButtonFinishedText style={textAlignMiddleStyle}>
        已完成
      </TaskButtonFinishedText>
    </TaskButtonFinished>
  )
}

const CanReceiveButton = ({ statusText }: { statusText?: string }) => {
  return (
    <TaskButtonCanReceive>
      <TaskButtonCanReceiveText style={textAlignMiddleStyle}>
        {statusText || '领取奖励'}
      </TaskButtonCanReceiveText>
    </TaskButtonCanReceive>
  )
}

const NotFinishButton = ({ statusText }: { statusText?: string }) => {
  return (
    <TaskButtonNotFinish>
      <TaskButtonNotFinishText style={textAlignMiddleStyle}>
        {statusText || '去完成'}
      </TaskButtonNotFinishText>
    </TaskButtonNotFinish>
  )
}

const TaskItemActionButton: React.FC<Props> = ({ item, onPress, disabled }) => {
  const { status, stepType, statusText } = item

  const taskDisabled =
    disabled ||
    (item.stepType === StepType.Single &&
      (status === TaskStatus.nonValid || status === TaskStatus.received)) ||
    isMultiStepTaskFinished(item) // 多步任务，所有子任务状态都是已领取时判断成已完成

  const handleTaskClick = () => {
    onPress()
  }

  const renderButtonContent = () => {
    if (typeof disabled !== 'undefined' && disabled) {
      return <FinishedButton />
    }

    if (stepType === StepType.Multi) {
      // 分步任务
      if (isMultiStepTaskFinished(item)) {
        return <FinishedButton />
      }
      const canReceiveStepItem = item.stepInfos!.find(
        (step) => step.stepStatus === TaskStatus.finished
      )
      if (canReceiveStepItem) {
        return <CanReceiveButton statusText={canReceiveStepItem.statusText} />
      } else {
        return <NotFinishButton statusText={statusText} />
      }
    }

    switch (status) {
      case TaskStatus.unfinished:
        return <NotFinishButton statusText={statusText} />
      case TaskStatus.finished:
        return <CanReceiveButton statusText={statusText} />
      default:
        return <FinishedButton />
    }
  }

  return (
    <Touch onPress={handleTaskClick} disabled={taskDisabled}>
      {renderButtonContent()}
    </Touch>
  )
}

export default React.memo(TaskItemActionButton, isEqual)
