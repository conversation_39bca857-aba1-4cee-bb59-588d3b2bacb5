import React from 'react'
import { View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { ThemeStyle } from 'typesV2/themeInfo'

type Props = {
  progress: number
}

const Wrapper = styled(View)`
  height: 2px;
  background: ${({ theme }: { theme: ThemeStyle }) => theme.task.processBar.bg_color};;
  width: 100%;
  position: absolute;
  overflow: hidden;
`

const BarActivePart = styled(View)`
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #ff4646;
`

const ProgressBar: React.FC<Props> = ({ progress }) => {
  return (
    <Wrapper>
      <BarActivePart
        style={{
          width: `${progress * 100}%`,
        }}
      />
    </Wrapper>
  )
}

export default React.memo(ProgressBar, isEqual)
