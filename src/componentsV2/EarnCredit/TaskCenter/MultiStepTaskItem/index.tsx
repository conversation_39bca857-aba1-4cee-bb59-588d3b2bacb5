import React, { useEffect, useMemo, useState, useContext } from 'react'
import isEqual from 'lodash.isequal'
import { StepInfo, TaskItemComponentProps, TaskStatus } from '../../../../typesV2/taskList'
import { Container, TaskItemLeft, StepsWrapper, TaskTag, TaskTitle, TaskTitleWrap, ProgressBarWrapper } from '../styles'
import TaskItemActionButton from '../TaskItemActionButton'
import rnEnv from '../../../../../rnEnv'
import getImageUri from '../../../../utilsV2/getImageUri'
import StepItem from './StepItem'
import performTask from '../../../../modulesV2/performTask'
import { store } from '../../../../store'
import { FlatList, LayoutChangeEvent, View } from 'react-native'
import ProgressBar from './ProgressBar'
import xmlog from '../../../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { taskListAid } from '../../../../constantsV2'
import UserLoginStatus from '../../../../typesV2/userLoginStatus'
// import goToLogin from '../../../../utilsV2/goToLogin'
import goToLoginForAbTest from '../../../../utilsV2/goToLoginForAbTest'

import { UserInfoContext } from '../../../../contextV2/userInfoContext'
import { Toast } from '@xmly/rn-sdk'
import useLocatedTask from '../../../../hooksV2/useLocatedTask'

type Props = TaskItemComponentProps & {}

const keyExtractor = (item: any) => {
  return item?.desc?.toString()
}

const MultiStepTaskItem: React.FC<Props> = ({
  isLast,
  taskItem,
  containerStyle,
  index,
  onMount,
  withoutScrollAnalyticComp,
  aid,
  onShow,
  onReportTaskPressEvent,
  onGetTaskAwardAction,
  shouldButtonDisabled,
  onRequestAid,
}) => {
  const [progressContainerWidth, setProgressContainerWidth] = useState(0)
  const [initIndex, setInitIndex] = useState(0)
  const mAid = typeof aid === 'number' ? aid : taskListAid
  const { loginStatus } = useContext(UserInfoContext)
  const { onLayoutProperty, extraReportParams } = useLocatedTask(taskItem.id);

  useEffect(() => {
    if (withoutScrollAnalyticComp) {
      typeof onMount === 'function' && onMount(taskItem)
    }
  }, [withoutScrollAnalyticComp])

  const handleActionPress = async () => {
    try {
      if (loginStatus !== UserLoginStatus.login) {
        // goToLogin()
        goToLoginForAbTest()
        return
      }
      if (typeof shouldButtonDisabled === 'function' && shouldButtonDisabled(taskItem)) return
      if (typeof onReportTaskPressEvent === 'function') {
        onReportTaskPressEvent(taskItem, index)
      } else {
        xmlog.click(40143, undefined, {
          taskId: safetyToString(taskItem.id),
          taskTitle: safetyToString(taskItem.title),
          currPage: '任务中心',
          Item: safetyToString(taskItem.statusText || (taskItem.status === TaskStatus.finished ? '去领取' : '去完成')),
          rewardName: safetyToString(taskItem.desc),
          position: safetyToString(index + 1),
          ...extraReportParams
        })
      }
      const canReceiveSteps = taskItem.stepInfos!.filter((task) => task.stepStatus === TaskStatus.finished)
      const aid = typeof onRequestAid === 'function' ? await onRequestAid() : mAid
      if (canReceiveSteps.length > 0) {
        if (typeof onGetTaskAwardAction === 'function') {
          onGetTaskAwardAction({
            taskItem,
            aid,
            stepNos: canReceiveSteps.map((task) => task.stepNo),
          })
        } else {
          store.dispatch.taskCenter.getTaskAward({
            taskItem,
            stepNos: canReceiveSteps.map((task) => task.stepNo),
            aid,
          })
        }
      } else {
        performTask(taskItem, aid)
      }
    } catch (err) {
      console.log(err)
      Toast.info('执行任务失败~')
    }
  }

  const handleLayout = (event: LayoutChangeEvent) => {
    const listWidth = event.nativeEvent.layout.width
    setProgressContainerWidth(listWidth)
  }

  useEffect(() => {
    const stepItems = taskItem.stepInfos
    const stepItemsLength = stepItems ? stepItems.length : 0
    if (stepItems && stepItemsLength > 0 && progressContainerWidth > 0) {
      const notFinishedIndex = stepItems.findIndex((item: StepInfo) => {
        return item.stepStatus === TaskStatus.unfinished
      })
      if (notFinishedIndex > 1) {
        setInitIndex(notFinishedIndex)
      }
    }
  }, [taskItem.stepInfos, progressContainerWidth])

  const onShowHandler = () => {
    if (typeof onShow === 'function') {
      onShow(taskItem, index)
    } else {
      xmlog.event(42772, 'slipPage', {
        taskId: taskItem.id + '',
        taskTitle: taskItem.title as string,
        currPage: '任务中心',
      })
    }
  }

  const itemWidth = useMemo(() => {
    return progressContainerWidth > 0 && taskItem.stepInfos?.length
      ? 60 * taskItem.stepInfos?.length > progressContainerWidth
        ? 60
        : Math.floor(progressContainerWidth / taskItem.stepInfos?.length)
      : 0
  }, [progressContainerWidth, taskItem.stepInfos?.length])

  const renderStepItem = ({ item: stepInfo, index }: { item: any; index: number }) => {
    return (
      <View
        style={{
          width: itemWidth,
          height: 41.5,
          alignContent: 'center',
          justifyContent: 'center',
          flexDirection: 'row',
        }}
      >
        <StepItem
          stepInfo={stepInfo}
          index={index}
          taskItem={taskItem}
        />
        <ProgressBarWrapper>
          <ProgressBar progress={(taskItem?.progress || 0) >= stepInfo.condition ? 1 : 0} />
        </ProgressBarWrapper>
      </View>
    )
  }

  const RenderContent = () => {
    return (
      <Container
        isLast={isLast}
        style={[containerStyle]}
        {...onLayoutProperty}
      >
        <TaskItemLeft style={{ marginRight: 20, overflow: 'visible' }}>
          <TaskTitleWrap>
            {taskItem.activityLogo ? (
              <TaskTag
                source={{
                  uri: getImageUri(taskItem.activityLogo, {
                    width: 46 * 3,
                    height: 16 * 3,
                    test: rnEnv.isTest(),
                  }),
                }}
              />
            ) : null}
            <TaskTitle
              numberOfLines={1}
              style={{ maxWidth: '100%' }}
            >
              {taskItem.title}
            </TaskTitle>
          </TaskTitleWrap>
          {taskItem.stepInfos ? (
            <StepsWrapper>
              <FlatList
                initialScrollIndex={initIndex}
                style={{ flex: 1 }}
                onLayout={handleLayout}
                data={taskItem.stepInfos}
                renderItem={renderStepItem}
                horizontal
                keyExtractor={keyExtractor}
                showsHorizontalScrollIndicator={false}
              />
            </StepsWrapper>
          ) : null}
        </TaskItemLeft>
        <TaskItemActionButton
          item={taskItem}
          onPress={handleActionPress}
        />
      </Container>
    )
  }

  if (withoutScrollAnalyticComp) {
    return RenderContent()
  }

  return (
    // 普通任务
    <ScrollAnalyticComp
      itemKey={taskItem.id + ''}
      onShow={onShowHandler}
    >
      {RenderContent()}
    </ScrollAnalyticComp>
  )
}

export default React.memo(MultiStepTaskItem, isEqual)
