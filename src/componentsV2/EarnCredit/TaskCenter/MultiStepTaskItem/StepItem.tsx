import React from 'react'
import { Text } from '@xmly/rn-components'
import { Image, View, Text as RNText } from 'react-native'
import isEqual from 'lodash.isequal'
import { StepInfo, TaskItemType, TaskStatus } from '../../../../typesV2/taskList'
import styled from 'styled-components'
import icon_money from '../../../../appImagesV2/icon_money'
import icon_sign_in_checked from '../../../../appImagesV2/icon_sign_in_checked'
import { ThemeStyle } from '../../../../typesV2/themeInfo'

type Props = {
  stepInfo: StepInfo
  index: number
  taskItem: TaskItemType
}

const Wrapper = styled(View)`
  align-items: center;
  width: 100%;
`
const LabelWrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
`

const AwardReceivedIcon = styled(Image)`
  width: 15.5px;
  height: 15.5px;
  position: absolute;
  right: -16px;
`

const Label = styled(RNText)`
  font-size: 13px;
  font-weight: bold;
  color: #ff4646;
`

const Dot = styled(View)`
  width: 6px;
  height: 6px;
  background: ${({ isFinished, theme }: { isFinished: boolean; theme: ThemeStyle }) => (isFinished ? '#ff4646' : theme.task.processBar.bg_color)};
  border-radius: 3px;
  margin: 2px 0;
`

const Icon = styled(Image)`
  width: 15.5px;
  height: 15.5px;
`

const StepItemDesc = styled(Text)`
  font-size: 11px;
  font-weight: 400;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.task.item.sub_title_color};
  line-height: 16px;
`

const StepItem: React.FC<Props> = ({ stepInfo, taskItem }) => {
  const isFinished = (taskItem?.progress || 0) >= stepInfo.condition
  const accessibilityLabel =
    stepInfo.stepStatus === TaskStatus.received
      ? `已领取${stepInfo.worth}积分奖励`
      : isFinished
        ? `有${stepInfo.worth}积分奖励待领取`
        : `收听${stepInfo.desc}可获得${stepInfo.worth}积分奖励`
  return (
    <Wrapper
      accessible
      accessibilityLabel={accessibilityLabel}
    >
      <LabelWrapper>
        <Icon source={icon_money} />
        <Label
          style={[
            {
              textAlignVertical: 'center',
              includeFontPadding: false,
              fontFamily: 'XmlyNumber',
            },
          ]}
        >
          +{stepInfo.worth}
        </Label>
        <AwardReceivedIcon
          source={icon_sign_in_checked}
          style={{
            opacity: stepInfo.stepStatus === TaskStatus.received ? 1 : 0,
          }}
        />
      </LabelWrapper>
      {/* @ts-ignore */}
      <Dot isFinished={isFinished} />
      <StepItemDesc>{stepInfo.desc}</StepItemDesc>
    </Wrapper>
  )
}

export default React.memo(StepItem, isEqual)
