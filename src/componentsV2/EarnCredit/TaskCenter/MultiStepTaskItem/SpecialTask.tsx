import React, { useContext } from 'react'
import { Image, Text, View } from 'react-native'
import styled from 'styled-components'
import isEqual from 'lodash.isequal'
import { TaskItemType, TaskStatus } from 'typesV2/taskList'
import { ThemeStyle } from 'typesV2/themeInfo'
import icon_money from 'appImagesV2/icon_money'
import icon_vip_tag from 'appImagesV2/icon_vip_tag'
import { store } from '../../../../store'
import { Touch } from '@xmly/rn-components'
import performTask from 'modulesV2/performTask'
import { DarkModeContext } from 'contextV2/darkModeContext'
import { taskListAid } from '../../../../constantsV2'

type Props = {
  taskItem: TaskItemType
}

const Wrapper = styled(View)`
  width: 100%;
  margin-top: 6px;
`

const SectionSubTitle = styled(Text)`
  font-family: PingFangSC-Regular;
  font-size: 12px;
  font-weight: normal;
  text-align: justify;
  letter-spacing: 0px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.sub_title_color};
`

const Container = styled(View)`
  margin-top: 43px;
  margin-bottom: 10px;
  height: 6px;
  width: 100%;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.processBar.bg_color};
  border-radius: 4px;
  position: relative;
`

interface ProgressBarProps {
  percent: number
}

const ProgressBar = styled(View)<ProgressBarProps>`
  height: 6px;
  width: ${(props) => props.percent}%;
  background-color: #ff4444;
  border-radius: 4px;
`

interface StepProps {
  stepStatus: number
}

const StepWrap = styled(View)`
  position: relative;
  height: 0;
  width: 100%;
`
const StepItem = styled(View)`
  position: absolute;
  top: -34px;
  width: 38px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`

const StepInfo = styled(View)`
  padding: 2px 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`

const IconMoney = styled(Image)`
  width: 12px;
  height: 12px;
  margin-right: 2px;
`

const IconVip = styled(Image)`
  width: 14px;
  height: 9.5px;
  margin-right: 2px;
`

const StepInfoText = styled(Text)`
  font-size: 13px;
  color: #ff4444;
`

const Step = styled(View)<StepProps>`
  height: 18px;
  width: 37px;
  border-radius: 20px;
  background-color: ${(props) =>
    props.stepStatus === 0 ? '#F6F7F8' : '#ff4444'};
  border: ${(props) =>
    props.stepStatus === 0 ? '2px solid transparent' : '2px solid #ff4444'};
`

const StepDark = styled(View)<StepProps>`
  height: 18px;
  width: 37px;
  border-radius: 20px;
  background-color: ${(props) => (props.stepStatus === 0 ? '#000' : '#ff4444')};
  border: ${(props) =>
    props.stepStatus === 0 ? '2px solid transparent' : '2px solid #ff4444'};
`

const StepText = styled(Text)<StepProps>`
  font-size: 9px;
  text-align: center;
  line-height: 13px;
  color: ${(props) => (props.stepStatus === 0 ? '#8F8F8F' : '#fff')};
`

const SpecialTask: React.FC<Props> = ({ taskItem }) => {
  const { isDarkMode } = useContext(DarkModeContext)
  const percent = Math.floor((taskItem.progress! / taskItem.condition!) * 100)
  const len = taskItem.stepInfos!.length
  const lastIndex = len - 1

  const handleTaskClick = () => {
    const canReceiveSteps = taskItem.stepInfos!.filter(
      (task) => task.stepStatus === TaskStatus.finished
    )
    if (canReceiveSteps.length > 0) {
      store.dispatch.taskCenter.getTaskAward({
        taskItem,
        stepNos: canReceiveSteps.map((task) => task.stepNo),
        aid: taskListAid,
      })
    } else {
      performTask(taskItem, taskListAid)
    }
  }

  return (
    <Wrapper>
      <SectionSubTitle>{taskItem.title}</SectionSubTitle>
      <Container>
        <ProgressBar percent={percent} />
        <StepWrap>
          {taskItem?.stepInfos &&
            taskItem?.stepInfos.map(
              (
                { condition: stepCondition, worth, stepStatus, desc },
                index
              ) => (
                <StepItem
                  key={index}
                  style={{
                    left: `${Math.floor(
                      (stepCondition / taskItem.condition!) * 100
                    )}%`,
                    transform: [
                      { translateX: (index === lastIndex ? -1 : -0.5) * 36 },
                    ],
                  }}
                >
                  <Touch onPress={handleTaskClick}>
                    <StepInfo>
                      {desc === 'vip' ? (
                        <IconVip source={icon_vip_tag} />
                      ) : (
                        <IconMoney source={icon_money} />
                      )}
                      <StepInfoText>+{worth}</StepInfoText>
                    </StepInfo>
                    {isDarkMode ? (
                      <StepDark stepStatus={stepStatus}>
                        <StepText stepStatus={stepStatus}>
                          {stepStatus === 0
                            ? `${stepCondition}次`
                            : stepStatus === 1
                            ? '领取'
                            : '已领取'}
                        </StepText>
                      </StepDark>
                    ) : (
                      <Step stepStatus={stepStatus}>
                        <StepText stepStatus={stepStatus}>
                          {stepStatus === 0
                            ? `${stepCondition}次`
                            : stepStatus === 1
                            ? '领取'
                            : '已领取'}
                        </StepText>
                      </Step>
                    )}
                  </Touch>
                </StepItem>
              )
            )}
        </StepWrap>
      </Container>
    </Wrapper>
  )
}

export default React.memo(SpecialTask, isEqual)
