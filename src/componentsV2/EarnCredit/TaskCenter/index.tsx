import React, { Suspense, useContext, useEffect, useMemo, useState } from 'react'
import { View } from 'react-native'
import { useSelector } from 'react-redux'
import styled from 'styled-components'
import { TaskItemKind, TaskItemType, TaskStatus } from 'typesV2/taskList'
import { RootState, store } from '../../../store'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import xmlog from '../../../utilsV2/xmlog'
import { UserInfoContext } from '../../../contextV2/userInfoContext'
import UserLoginStatus from '../../../typesV2/userLoginStatus'
import LoginButton from '../../LoginButton'
import SectionHeader from './SectionHeader'
import RenderTaskList from './RenderTaskList'
import displayTaskListFilter from '../../../utilsV2/displayTaskListFilter'
import isEqual from 'lodash.isequal'
import SpecialTask from './MultiStepTaskItem/SpecialTask'
import { NativeInfoContext } from '../../../contextV2/nativeInfoContext'
import GlobalEventEmitter from '../../../utilsV2/globalEventEmitter'
import refreshTaskAndRefreshList from '../../../utilsV2/refreshTaskAndRefreshList'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import TaskListSkeleton from './TaskListSkeleton'
import { taskListAid } from '../../../constantsV2'
import AdAppTask from 'components/AdAppTask'
import AdVideoTask from 'components/AdVideoTask'

const Container = styled(View)`
  width: 100%;
  background-color: ${({ theme }: { theme: ThemeStyle }) => theme.common.item_bg_color};
  border-radius: 8px;
  margin-bottom: 12px;
  padding: 16px 11px 4px 15px;
  overflow: hidden;
`

const ContentWrapper = styled(View)`
  margin-top: 8px;
`

const handleTaskMount = (task: TaskItemType) => {
  const UBTMateInfo = task.UBTMateInfo ? task.UBTMateInfo : {}
  xmlog.event(42772, 'slipPage', {
    taskId: task.id + '',
    taskTitle: task.title as string,
    currPage: '任务中心',
    ...UBTMateInfo,
  })
}

const TaskCenter = () => {
  const safeAreaInsets = useSafeAreaInsets()
  const nativeInfo = useContext(NativeInfoContext)
  const { taskList, summaryTask } = useSelector((state: RootState) => state.taskCenter)
  const { noLoginAbTestStatus } = useSelector((state: RootState) => state.signInInfo)
  const [totalTaskWorth, setTotalTaskWorth] = useState(0)
  const thirdpartyTaskExist = useSelector((state: RootState) => state.thirdpartyTask.isExist)
  const useInfo = useContext(UserInfoContext)
  const filteredTaskList = displayTaskListFilter(taskList, thirdpartyTaskExist)
  const newVideoTask = taskList.find((task) => task.taskType === TaskItemKind.newVideoTask && task?.contextMap?.style === 'new')
  // 根据iting 的srcChannel 参数来判断是否完成添加widget 任务
  const finishAddWidgetTask = async (isFromEvent?: boolean) => {
    try {
      if ((filteredTaskList.length > 0 && nativeInfo.srcChannel === 'widget') || (filteredTaskList.length > 0 && isFromEvent)) {
        // 判断是通过挂件进入的积分中心
        const addWidgetTaskNotFinish = filteredTaskList.find((task) => task.taskType === TaskItemKind.clientBehavior && task.code === 777 && task.status === TaskStatus.unfinished)
        console.log('addWidgetTaskNotFinish ⌚️⌚️⌚️⌚️⌚️⌚️', addWidgetTaskNotFinish)
        if (addWidgetTaskNotFinish) {
          // 主动完成widget 任务并且刷新任务列表
          refreshTaskAndRefreshList(addWidgetTaskNotFinish.id, taskListAid)
        }
      }
    } catch (err) { }
  }

  const handleFinishAddWidgetTaskEvent = () => {
    finishAddWidgetTask(true)
  }

  useEffect(() => {
    finishAddWidgetTask()

    const finishAddWidgetTaskEventListener = GlobalEventEmitter.addListener('finishAddWidgetTaskEvent', handleFinishAddWidgetTaskEvent)

    return () => {
      finishAddWidgetTaskEventListener.remove()
    }
  }, [filteredTaskList, nativeInfo.srcChannel])

  useEffect(() => {
    initData()
  }, [taskList.length])

  useEffect(() => {
    calcTotalTaskWorth()
  }, [taskList])

  const calcTotalTaskWorth = () => {
    let count = 0
    taskList.forEach((item) => {
      count += item.worth!
    })
    setTotalTaskWorth(count)
  }

  const initData = async () => {
    try {
      store.dispatch.taskCenter.getTaskList({ aid: taskListAid })
    } catch (err) { }
  }

  const isLogin = useInfo.loginStatus === UserLoginStatus.login

  const renderContent = useMemo(() => {
    if (taskList.length > 0) {
      GlobalEventEmitter.emit('taskCenterLoaded')
      if (!isLogin && noLoginAbTestStatus !== '2') {
        return <LoginButton />
      }
      return (
        <>
          {/* 下面一行TODO 跳转登录页 */}
          <SectionHeader totalTaskWorth={totalTaskWorth} />
          <Suspense fallback={null}>
            <AdAppTask />
          </Suspense>
          {/* SpecialTask 是进度条 */}
          {summaryTask?.id > 0 ? <SpecialTask taskItem={summaryTask} /> : null}
          <ContentWrapper>
            <RenderTaskList
              taskList={filteredTaskList}
              withoutScrollAnalyticComp
              itemContainerStyle={{ paddingVertical: 12 }}
              judgeItemIsLast={(index) => index + 1 === filteredTaskList.length}
              onTaskMount={handleTaskMount}
            />
          </ContentWrapper>
        </>
      )
    } else {
      return <TaskListSkeleton />
    }
  }, [taskList.length, isLogin, filteredTaskList, summaryTask, totalTaskWorth])

  return <>
    {newVideoTask ? <AdVideoTask task={newVideoTask} /> : null}
    <Container>{renderContent}</Container>
  </>
}

export default React.memo(TaskCenter, isEqual)
