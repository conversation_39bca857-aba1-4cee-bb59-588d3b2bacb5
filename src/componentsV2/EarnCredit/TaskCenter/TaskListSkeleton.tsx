import React from 'react'
import { StyleProp, View, ViewStyle } from 'react-native'
import styled from 'styled-components'
import TaskItemEmpty from './TaskItemEmpty'

type Props = {
  containerStyle?: StyleProp<ViewStyle>
}

const ContentWrapper = styled(View)`
`
const InnerBlock = styled(View)`
  border-radius: 2px;
  opacity: 0.15;
  /* Normal🌞/ThinTextColor */
  background: #aaaaaa;
`
const TaskListSkeleton: React.FC<Props> = ({ containerStyle }) => {
  return (
    <ContentWrapper style={containerStyle}>
      <InnerBlock style={{ width: 83, height: 18, marginBottom: 32 }} />
      <TaskItemEmpty />
      <TaskItemEmpty />
      <TaskItemEmpty />
      <TaskItemEmpty />
      <TaskItemEmpty isLast />
    </ContentWrapper>
  )
}

export default React.memo(TaskListSkeleton, () => true)
