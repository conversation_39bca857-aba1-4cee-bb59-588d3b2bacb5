import React from 'react'
import { View } from 'react-native'
import styled from 'styled-components'

type Props = {
  isLast?: boolean
}

const Container = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 36px;
  height: 43px;
`
const TaskItemLeft = styled(View)`
  flex: 1;
  margin-right: 32px;
`
const TaskTitleWrap = styled(View)`
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  overflow: hidden;
`
const TaskTitle = styled(View)`
  flex-shrink: 0;
  height: 14px;
  opacity: 0.15;
  /* Normal🌞/ThinTextColor */
  background: #aaaaaa;
  border-radius: 2px;
  width: 100%;
`
const TaskSubTitle = styled(View)`
  flex-shrink: 0;
  height: 14px;
  border-radius: 2px;
  width: 103px;
  opacity: 0.15;

  background: #aaaaaa;
  margin-top: 15px;
`

const TaskItemActionButton = styled(View)`
  width: 80px;
  height: 28px;
  border-radius: 362px;
  justify-content: center;
  opacity: 0.15;
  align-items: center;
  /* Normal🌞/ThinTextColor */
  background: #aaaaaa;
`

const TaskItemEmpty: React.FC<Props> = ({ isLast }) => {
  return (
    <Container>
      <TaskItemLeft>
        <TaskTitleWrap>
          <TaskTitle />
        </TaskTitleWrap>
        <TaskSubTitle />
      </TaskItemLeft>
      <TaskItemActionButton />
    </Container>
  )
}

export default TaskItemEmpty
