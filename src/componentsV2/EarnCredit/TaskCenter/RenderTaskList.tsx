import React from 'react'
import isEqual from 'lodash.isequal'
import { StepType, TaskItemKind, TaskItemType } from '../../../typesV2/taskList'
import MultiStepTaskItem from './MultiStepTaskItem'
import TaskItem from './TaskItem'
import { StyleProp, ViewStyle } from 'react-native'

type Props = {
  taskList: TaskItemType[]
  onTaskMount?: (taskItem: TaskItemType) => void
  onTaskShow?: (taskItem: TaskItemType, index: number) => void
  onReportTaskPressEvent?: (taskItem: TaskItemType, index: number) => void
  withoutScrollAnalyticComp?: boolean
  itemContainerStyle?: StyleProp<ViewStyle>
  judgeItemIsLast?: (index: number) => boolean
  aid?: number
  onGetTaskAwardAction?: (options: {
    taskItem: TaskItemType
    aid: number
    stepNos?: number[] | undefined
  }) => Promise<void>

  shouldButtonDisabled?: (taskItem: TaskItemType) => boolean
}

const RenderTaskList: React.FC<Props> = ({
  taskList,
  onTaskMount,
  onTaskShow,
  withoutScrollAnalyticComp,
  itemContainerStyle,
  judgeItemIsLast,
  onReportTaskPressEvent,
  aid,
  onGetTaskAwardAction,
  shouldButtonDisabled,
}) => {
  if (taskList.length > 0) {
    return (
      <>
        {taskList
          .filter((item) => item.code !== 111)
          .map((item, index) => {
            const isLast =
              typeof judgeItemIsLast === 'function'
                ? judgeItemIsLast(index)
                : false

            return item.stepType === StepType.Single ||
              (item.code === 1 &&
                [TaskItemKind.clientBehavior, TaskItemKind.newVideoTask].includes(item.taskType)) ? (
              <TaskItem
                shouldButtonDisabled={shouldButtonDisabled}
                withoutScrollAnalyticComp={withoutScrollAnalyticComp}
                onMount={onTaskMount}
                containerStyle={itemContainerStyle}
                taskItem={item}
                isLast={isLast}
                key={item.id}
                onShow={onTaskShow}
                index={index}
                onReportTaskPressEvent={onReportTaskPressEvent}
                aid={aid}
                onGetTaskAwardAction={onGetTaskAwardAction}
              />
            ) : (
              <MultiStepTaskItem
                shouldButtonDisabled={shouldButtonDisabled}
                aid={aid}
                taskItem={item}
                key={item.id}
                onShow={onTaskShow}
                index={index}
                isLast={isLast}
                withoutScrollAnalyticComp={withoutScrollAnalyticComp}
                onMount={onTaskMount}
                onReportTaskPressEvent={onReportTaskPressEvent}
                containerStyle={itemContainerStyle}
                onGetTaskAwardAction={onGetTaskAwardAction}
              />
            )
          })}
      </>
    )
  }
  return null
}

export default React.memo(RenderTaskList, isEqual)
