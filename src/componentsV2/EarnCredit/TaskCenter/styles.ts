import { BetterImage, Text } from '@xmly/rn-components'
import { FlatList, Image, View } from 'react-native'
import styled from 'styled-components'
import { ThemeStyle } from '../../../typesV2/themeInfo'

export const Container = styled(View)`
  border-bottom-width: ${(props: { isLast: boolean }) =>
    props.isLast ? 0 : 0.5}px;
  border-bottom-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.credit.line_color};
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`
export const TaskItemLeft = styled(View)`
  flex: 1;
  margin-right: 5px;
  overflow: hidden;
`
export const TaskTitleWrap = styled(View)`
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  overflow: hidden;
`
export const TaskTitle = styled(Text)`
  font-size: 14px;
  font-family: PingFangSC-Regular;
  text-align: left;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.task.item.title_color};
  letter-spacing: 0px;
  flex-shrink: 1;
`
export const TaskTag = styled(BetterImage)`
  width: 46px;
  height: 16px;
  margin-right: 4px;
`
export const TaskSubTitle = styled(Text)`
  font-size: 11px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  text-align: left;
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.task.item.sub_title_color};
  margin-top: 4px;
  flex: 1;
`

export const TaskAwardWrapper = styled(View)`
  flex-direction: row;
  margin-left: 6px;
  align-items: center;
  flex-shrink: 0;
  flex-grow: 1;
`

export const TaskAwardIcon = styled(Image)`
  width: 16px;
  height: 16px;
`

export const TaskAwardText = styled(Text)`
  font-size: 13px;
  color: #ff4646;
  font-weight: 600;
`

export const StepsWrapper = styled(View)`
  margin-top: 4px;
  height: 41.5px;
`

export const StepsProgressWrapper = styled(FlatList)`
  overflow: hidden;
  width: 100%;
  flex: 1;
`

export const ProgressBarWrapper = styled(View)`
  position: absolute;
  width: 100%;
  align-items: center;
  justify-content: center;
  bottom: 21px;
`
