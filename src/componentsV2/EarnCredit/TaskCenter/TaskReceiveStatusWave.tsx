import React, { useEffect, useRef } from 'react'
import { StyleSheet } from 'react-native'
import Animated, { cond, interpolate, useValue } from 'react-native-reanimated'
import { loopTiming } from '../../../utilsV2/animations/loopTiming'

type Props = {}

const TaskReceiveStatusWave: React.FC<Props> = (props) => {
  const loopAnimation = useRef(
    loopTiming({
      duration: 5500,
    })
  ).current

  const shouldDelayRun = useValue<0 | 1>(0)

  const loopAnimationDelay = useRef(
    cond(
      shouldDelayRun,
      loopTiming({
        duration: 5500,
      })
    )
  ).current

  useEffect(() => {
    const time = setTimeout(() => {
      shouldDelayRun.setValue(1)
    }, 5500 * 0.2)
    return () => {
      time && clearTimeout(time)
    }
  })

  return (
    <>
      <Animated.View
        style={[
          styles.wrapper,
          {
            opacity: interpolate(loopAnimation, {
              inputRange: [0, 0.2, 1],
              outputRange: [1, 0, 0],
            }),
            transform: [
              {
                scaleX: interpolate(loopAnimation, {
                  inputRange: [0, 0.2, 1],
                  outputRange: [1, 1.35, 1.35],
                }),
              },
              {
                scaleY: interpolate(loopAnimation, {
                  inputRange: [0, 0.2, 1],
                  outputRange: [1, 1.6, 1.6],
                }),
              },
            ],
          },
        ]}
      />
      <Animated.View
        style={[
          styles.wrapper,
          {
            opacity: interpolate(loopAnimationDelay, {
              inputRange: [0, 0.2, 1],
              outputRange: [1, 0, 0],
            }),
            transform: [
              {
                scaleX: interpolate(loopAnimationDelay, {
                  inputRange: [0, 0.2, 1],
                  outputRange: [1, 1.35, 1.35],
                }),
              },
              {
                scaleY: interpolate(loopAnimationDelay, {
                  inputRange: [0, 0.2, 1],
                  outputRange: [1, 1.6, 1.6],
                }),
              },
            ],
          },
        ]}
      />
    </>
  )
}

const styles = StyleSheet.create({
  wrapper: {
    width: 58,
    height: 24,
    borderRadius: 14,
    position: 'absolute',
    backgroundColor: 'rgba(255,98,71,0.25)',
  },
})

export default React.memo(TaskReceiveStatusWave, () => true)
