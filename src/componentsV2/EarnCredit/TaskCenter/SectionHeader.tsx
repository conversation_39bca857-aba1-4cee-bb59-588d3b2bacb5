import React from 'react'
import { Text } from '@xmly/rn-components'
import { Image, View } from 'react-native'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import icon_money from '../../../appImagesV2/icon_money'
import styled from 'styled-components'
import isEqual from 'lodash.isequal'

type Props = {
  totalTaskWorth: number
}

const Wrapper = styled(View)`
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: baseline;
`

const SectionTitle = styled(Text)`
  font-size: 17px;
  font-family: PingFangSC-Semibold;
  font-weight: bold;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
`

const SectionDesc = styled(View)`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 5px;
`

const SectionDescInnerText = styled(Text)`
  font-family: PingFangSC-Regular;
  font-size: 12px;
  font-weight: normal;
  text-align: justify;
  letter-spacing: 0px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.sub_title_color};
`

const TaskAwardIcon = styled(Image)`
  width: 16px;
  height: 16px;
  margin: 0 2px;
`

const SectionHeader: React.FC<Props> = ({ totalTaskWorth }) => {
  return (
    <Wrapper>
      <SectionTitle
        style={[{ textAlignVertical: 'center', includeFontPadding: false }]}
      >
        赚积分
      </SectionTitle>
      <SectionDesc>
        <SectionDescInnerText>今日最多可赚</SectionDescInnerText>
        <TaskAwardIcon source={icon_money} />
        <SectionDescInnerText>{totalTaskWorth}</SectionDescInnerText>
      </SectionDesc>
    </Wrapper>
  )
}

export default React.memo(SectionHeader, isEqual)
