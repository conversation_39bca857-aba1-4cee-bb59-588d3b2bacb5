import React, { useEffect, useRef, useContext } from 'react'
import { TaskItemComponentProps, TaskItemKind, TaskStatus } from 'typesV2/taskList'
import performTask from 'modulesV2/performTask'
import xmlog from '../../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import { store } from '../../../store'
import icon_money from '../../../appImagesV2/icon_money'
import getImageUri from '../../../utilsV2/getImageUri'
import rnEnv from '../../../../rnEnv'
import TaskItemActionButton from './TaskItemActionButton'
import isEqual from 'lodash.isequal'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { Container, TaskItemLeft, TaskTitleWrap, TaskTag, TaskTitle, TaskAwardWrapper, TaskAwardIcon, Task<PERSON><PERSON>Text, TaskSubTitle } from './styles'
import { taskListAid } from '../../../constantsV2'
import UserLoginStatus from '../../../typesV2/userLoginStatus'
// import goToLogin from '../../../utilsV2/goToLogin'
import goToLoginForAbTest from '../../../utilsV2/goToLoginForAbTest'
import { UserInfoContext } from '../../../contextV2/userInfoContext'
import { Toast } from '@xmly/rn-sdk'
import useLocatedTask from '../../../hooksV2/useLocatedTask'

const TaskItem = ({
  taskItem,
  isLast,
  containerStyle,
  index,
  onMount,
  withoutScrollAnalyticComp,
  aid,
  onShow,
  onReportTaskPressEvent,
  onGetTaskAwardAction,
  shouldButtonDisabled,
  onRequestAid,
  isEverydayTask
}: TaskItemComponentProps) => {
  const clickBtnEnable = useRef<boolean>(true)
  const isReceiveAward = useRef(false)
  const mAid = typeof aid === 'number' ? aid : taskListAid
  const { loginStatus } = useContext(UserInfoContext)
  const { onLayoutProperty, extraReportParams } = useLocatedTask(taskItem.id);

  useEffect(() => {
    if (withoutScrollAnalyticComp) {
      typeof onMount === 'function' && onMount(taskItem)
    }
  }, [withoutScrollAnalyticComp])

  const isButtonDisabled = typeof shouldButtonDisabled === 'function' && shouldButtonDisabled(taskItem)

  const handleTaskClick = async () => {
    try {
      if (loginStatus !== UserLoginStatus.login) {
        // goToLogin()
        goToLoginForAbTest()
        return
      }
      if (isButtonDisabled) return
      if (isReceiveAward.current) return
      const aid = typeof onRequestAid === 'function' ? await onRequestAid() : mAid

      if (taskItem.status === TaskStatus.finished) {
        isReceiveAward.current = true
        if (typeof onGetTaskAwardAction === 'function') {
          await onGetTaskAwardAction({ taskItem, aid })
        } else {
          await store.dispatch.taskCenter.getTaskAward({
            taskItem,
            aid,
          })
        }

        isReceiveAward.current = false
      } else {
        if (clickBtnEnable.current) {
          clickBtnEnable.current = false
          setTimeout(() => {
            clickBtnEnable.current = true
          }, 1000)
          let extInfo
          if (taskItem.taskType === TaskItemKind.newVideoTask) {
            if (isEverydayTask) {
              extInfo = { sourceName: 'cashVideoTask' }
            } else {
              extInfo = { sourceName: 'videoTask' }
            }
          }
          performTask(taskItem, aid, extInfo)
        }
      }
      if (typeof onReportTaskPressEvent === 'function') {
        onReportTaskPressEvent(taskItem, index)
      } else {
        const commonProps = {
          taskId: safetyToString(taskItem.id),
          taskTitle: safetyToString(taskItem.title),
          currPage: '任务中心',
          Item: safetyToString(taskItem.statusText || (taskItem.status === TaskStatus.finished ? '去领取' : '去完成')),
          rewardName: safetyToString(taskItem.desc),
          position: safetyToString(index + 1),
          ...extraReportParams
        }
        const eventReportProps =
          taskItem.UBTMateInfo && taskItem.UBTMateInfo.xmRequestId
            ? {
              ...commonProps,
              xmRequestId: taskItem.UBTMateInfo.xmRequestId,
            }
            : commonProps
        xmlog.click(40143, undefined, eventReportProps)
      }
    } catch (err) {
      console.log(err)
      Toast.info('执行任务失败~')
    }
  }

  const { title, desc, id } = taskItem

  const onShowHandler = () => {
    if (typeof onShow === 'function') {
      onShow(taskItem, index)
    } else {
      xmlog.event(42772, 'slipPage', {
        taskId: id + '',
        taskTitle: title as string,
        currPage: '任务中心',
      })
    }
  }

  const accessibilityLabel = `${title}${taskItem.worth ? `, 完成此任务获得${taskItem.worth}积分` : ''}。${desc ? `任务描述：${desc}` : ''}`

  const renderContent = () => {
    return (
      <Container
        isLast={isLast}
        style={[containerStyle]}
        {...onLayoutProperty}
      >
        <TaskItemLeft
          accessible
          accessibilityLabel={accessibilityLabel}
        >
          <TaskTitleWrap>
            {taskItem.activityLogo ? (
              <TaskTag
                source={{
                  uri: getImageUri(taskItem.activityLogo, {
                    width: 46 * 3,
                    height: 16 * 3,
                    test: rnEnv.isTest(),
                  }),
                }}
              />
            ) : null}
            <TaskTitle numberOfLines={1}>{title}</TaskTitle>
            {taskItem.worth ? (
              <TaskAwardWrapper>
                <TaskAwardIcon source={icon_money} />
                <TaskAwardText style={[{ includeFontPadding: false, textAlignVertical: 'center' }]}>
                  {'+'}
                  {taskItem.worth}
                </TaskAwardText>
              </TaskAwardWrapper>
            ) : null}
          </TaskTitleWrap>
          {desc ? <TaskSubTitle numberOfLines={1}>{desc}</TaskSubTitle> : null}
        </TaskItemLeft>

        <TaskItemActionButton
          item={taskItem}
          onPress={handleTaskClick}
          disabled={isButtonDisabled}
        />
      </Container>
    )
  }

  if (withoutScrollAnalyticComp) {
    return renderContent()
  }

  return (
    // 普通任务
    <ScrollAnalyticComp
      itemKey={id + ''}
      onShow={onShowHandler}
    >
      {renderContent()}
    </ScrollAnalyticComp>
  )
}

export default React.memo(TaskItem, isEqual)
