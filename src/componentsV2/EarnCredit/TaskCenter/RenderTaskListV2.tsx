import React from 'react'
import isEqual from 'lodash.isequal'
import { StepType, TaskItemKind, TaskItemType } from '../../../typesV2/taskList'
import MultiStepTaskItem from './MultiStepTaskItem'
import TaskItem from './TaskItem'
import { StyleProp, ViewStyle } from 'react-native'
import PointExchangeTaskItem from './PointExchangeTaskItem'

type Props = {
  pointExchangeTask?: TaskItemType | null
  taskList: TaskItemType[]
  onTaskMount?: (taskItem: TaskItemType) => void
  onTaskShow?: (taskItem: TaskItemType, index: number) => void
  onReportTaskPressEvent?: (taskItem: TaskItemType, index: number) => void
  withoutScrollAnalyticComp?: boolean
  itemContainerStyle?: StyleProp<ViewStyle>
  judgeItemIsLast?: (index: number) => boolean
  aid?: number
  onGetTaskAwardAction?: (options: { taskItem: TaskItemType; aid: number; stepNos?: number[] | undefined }) => Promise<void>
  onRequestAid?: () => Promise<number>
  shouldButtonDisabled?: (taskItem: TaskItemType) => boolean
}

const RenderTaskList: React.FC<Props> = ({
  taskList,
  onTaskMount,
  onTaskShow,
  withoutScrollAnalyticComp,
  itemContainerStyle,
  judgeItemIsLast,
  onReportTaskPressEvent,
  aid,
  onGetTaskAwardAction,
  shouldButtonDisabled,
  onRequestAid,
  pointExchangeTask,
}) => {
  if (taskList.length > 0) {
    return (
      <>
        {pointExchangeTask ? (
          <PointExchangeTaskItem
            shouldButtonDisabled={shouldButtonDisabled}
            withoutScrollAnalyticComp={withoutScrollAnalyticComp}
            onMount={onTaskMount}
            containerStyle={itemContainerStyle}
            taskItem={pointExchangeTask}
            isLast={false}
            key={pointExchangeTask.id}
            onShow={onTaskShow}
            index={-1}
            onReportTaskPressEvent={onReportTaskPressEvent}
            aid={aid}
            onRequestAid={onRequestAid}
            onGetTaskAwardAction={onGetTaskAwardAction}
          />
        ) : null}
        {taskList
          .filter((item) => item.code !== 111)
          .map((item, index) => {
            return item.stepType === StepType.Single || (item.code === 1 && [item.taskType === TaskItemKind.newVideoTask, TaskItemKind.clientBehavior].includes(item.taskType)) ? (
              <TaskItem
                shouldButtonDisabled={shouldButtonDisabled}
                withoutScrollAnalyticComp={withoutScrollAnalyticComp}
                onMount={onTaskMount}
                containerStyle={itemContainerStyle}
                taskItem={item}
                isLast={true}
                key={item.id}
                onShow={onTaskShow}
                index={index}
                onReportTaskPressEvent={onReportTaskPressEvent}
                aid={aid}
                onRequestAid={onRequestAid}
                onGetTaskAwardAction={onGetTaskAwardAction}
                isEverydayTask
              />
            ) : (
              <MultiStepTaskItem
                shouldButtonDisabled={shouldButtonDisabled}
                aid={aid}
                onRequestAid={onRequestAid}
                taskItem={item}
                key={item.id}
                onShow={onTaskShow}
                index={index}
                isLast={true}
                withoutScrollAnalyticComp={withoutScrollAnalyticComp}
                onMount={onTaskMount}
                onReportTaskPressEvent={onReportTaskPressEvent}
                containerStyle={itemContainerStyle}
                onGetTaskAwardAction={onGetTaskAwardAction}
              />
            )
          })}
      </>
    )
  }
  return null
}

export default React.memo(RenderTaskList, isEqual)
