import React, { Suspense } from 'react'
const MambaConsoleEjector: React.FC = () => {
  try {
    if (require('../../package.json').mambaId) {
      const MambaConsoleLazy = React.lazy(() => import('@xmly/mamba-console'))
      return (
        <Suspense fallback={null}>
          <MambaConsoleLazy showInDevelopment keepLogNumber={500} />
        </Suspense>
      )
    } else {
      return null
    }
  } catch (err) {
    console.log(err)
    return null
  }
}

export default React.memo(MambaConsoleEjector, () => true)
