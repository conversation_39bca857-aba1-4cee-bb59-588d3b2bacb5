import { Text, Touch } from '@xmly/rn-components'
import { Image, View } from 'react-native'
import styled from 'styled-components'
import icon_nologin_award from '../../appImagesV2/icon_nologin_award'
import { ThemeStyle } from '../../typesV2/themeInfo'

export const ModalContentWrapper = styled(View)`
  width: 276px;
  background: ${({ theme }: { theme: ThemeStyle }) => theme.checkInPop.wrapper_bg_color};
  border-radius: 10px;
  padding: 32px 38px 24px 38px;
  align-items: center;
`

export const ActionButton = styled(Touch)`
  margin-top: 24px;
  background: #ff4444;
  border-radius: 29px;
  width: 200px;
  height: 40px;
  align-items: center;
  justify-content: center;
`

export const ActionButtonLabel = styled(Text)`
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
`

export const CloseBtnWrapper = styled(Touch)`
  justify-content: center;
  align-items: center;
  width: 37px;
  height: 37px;
  position: absolute;
  top: 0;
  right: 0;
`

export const TitleNew = styled(Text)`
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: bold;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color_new};
`

export const GiftItemWrapper = styled(View)`
  width: 74px;
  align-items: center;
  margin-top: 24px;
`

export const GiftItemCover = styled(Image)`
  width: 74px;
  height: 74px;
  border-radius: 6px;
`

export const GiftItemLabel = styled(Text)`
  font-size: 11px;
  font-weight: 400;
  color: #ff4444;
  text-align: center;
  margin-top: 8px;
`

