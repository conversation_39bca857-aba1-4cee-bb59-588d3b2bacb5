import isEqual from 'lodash.isequal'
import React from 'react'
import { useSelector } from 'react-redux'
import { RootState } from 'store'


const NoLoginCheckPop = React.lazy(() => import('./NoLoginCheckPop'))


const CheckInPop = () => {
  const noLoginSigInModalVisible = useSelector(
    (state: RootState) => state.signInInfo.noLoginSigInModalVisible)

    if (noLoginSigInModalVisible) {
      return (
        <React.Suspense fallback={null}>
          <NoLoginCheckPop />
        </React.Suspense>
      )
    } 
  return null
}

export default React.memo(CheckInPop, isEqual)
