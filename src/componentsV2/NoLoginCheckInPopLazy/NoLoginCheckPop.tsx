import React, {
	useCallback,
	useContext,
	useEffect,
	useRef,
	useState,
} from 'react'
import isEqual from 'lodash.isequal'
import { RootState, store } from '../../store'
import xmlog from '../../utilsV2/xmlog'
import Pop from '../common/Pop'
import {
	ActionButton,
	ActionButtonLabel,
	GiftItemWrapper,
	GiftItemCover,
	GiftItemLabel,
	CloseBtnWrapper,
	ModalContentWrapper,
	TitleNew,
} from './styles'
import CloseIcon from '../common/CloseIcon'
import { Toast } from '@xmly/rn-sdk'
import goToLoginForAbTest from 'utilsV2/goToLoginForAbTest'
import { connect } from 'react-redux'
import { SignInUserAwardType } from 'typesV2/signInNew'

type Props = {
	noLoginAward: SignInUserAwardType
}


const NoLoginCheckPop: React.FC<Props> = ({ noLoginAward }) => {

	useEffect(() => {
		init()
	}, [])

	const init = async () => {
		// 任务中心-未登录签到弹窗  控件曝光
		xmlog.event(56451, 'slipPage', { currPage: '任务中心' });
	}

	// 关闭弹窗
	const handleClose = () => {
		store.dispatch.signInInfo.refresh({ noLoginSigInModalVisible: false })
	}

	const handleCloseBtnPress = () => {
		handleClose()
	}

	const handleBtnClick = () => {
		// 任务中心-未登录签到弹窗  点击事件
		xmlog.click(56450, undefined, { currPage: '任务中心' });
		handleClose()
		// 去登录
		goToLoginForAbTest(true)
	}

	return (
		<Pop handleClose={handleClose}>
			<ModalContentWrapper style={{ paddingLeft: 0, paddingRight: 0 }}>
				{noLoginAward.context?.awards && noLoginAward.context?.awards.length > 0 && <TitleNew>签到成功,获得{noLoginAward.context?.awards[0].label}</TitleNew>}
				<GiftItemWrapper>
					{noLoginAward.context?.awards && noLoginAward.context?.awards.length > 0 && <GiftItemCover source={{ uri: noLoginAward.context?.awards[0].icon }} />}
					{noLoginAward.context?.awards && noLoginAward.context?.awards.length > 0 && <GiftItemLabel>{noLoginAward.context?.awards[0].label}</GiftItemLabel>}
				</GiftItemWrapper>
				<ActionButton
					onPress={handleBtnClick}
				>
					<ActionButtonLabel>
						登录领取奖励
					</ActionButtonLabel>
				</ActionButton>
				<CloseBtnWrapper onPress={handleCloseBtnPress}>
					<CloseIcon size={13} color='#4c3c3c' />
				</CloseBtnWrapper>
			</ModalContentWrapper>
		</Pop>
	)
}
const mapStateToProps = (state: RootState) => ({
	noLoginAward: state.signInInfo.signInRecords[0],
})

export default connect(mapStateToProps)(React.memo(NoLoginCheckPop, isEqual))
