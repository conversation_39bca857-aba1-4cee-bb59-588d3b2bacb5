import { XMAppVersionHelper } from "@xmly/rn-sdk";
import execVideoTask from "modulesV2/performTask/execVideoTask";
import execVideoTaskNew from "modulesV2/performTask/execVideoTaskNew";
import { useEffect, useState } from "react";
import { CheckInInfo } from "typesV2/signInNew";

export default function useVideoTask(checkInInfo: CheckInInfo) { // 签到弹窗看视频任务播放激励视频hook
  const [isValidVersion, setIsValidVersion] = useState(false);
  const videoTask = isValidVersion ? checkInInfo?.videoTaskV2 ?? checkInInfo?.videoTask : checkInInfo?.videoTask;
  const hasVideoTask = typeof videoTask?.taskId === 'number' && typeof videoTask.aid === 'number'
  // 版本高于9.1.78，且有新的视频任务，使用新的激励视频播放方式，即客户端完成任务
  const nativeCompleteTask = isValidVersion && !!checkInInfo?.videoTaskV2;

  function execVideo() {
    console.info('debug_execVideo');
    execVideoTaskNew({ id: videoTask!.taskId }, videoTask!.aid, undefined, { sourceName: 'adTask' })
  }
  function execVideoOld() {
    execVideoTask({ id: videoTask!.taskId }, videoTask!.aid, { sourceName: 'adTask' })
  }

  useEffect(() => {
    async function checkVersion() {
      const isHigherVersion = await XMAppVersionHelper.notLowerThan('9.1.78');
      setIsValidVersion(isHigherVersion);
    }
    checkVersion();
  }, [setIsValidVersion])

  return {
    videoTask,
    hasVideoTask,
    execVideo: nativeCompleteTask ? execVideo : execVideoOld,
  };
}