import { Touch } from '@xmly/rn-components'
import { Animated, Image, StyleSheet, View, Text } from 'react-native'
import styled from 'styled-components'
import icon_monthly_ticket_plus from '../../appImagesV2/icon_monthly_ticket_plus'
import { ThemeStyle } from '../../typesV2/themeInfo'

export const ModalContentWrapper = styled(View)`
  width: 276px;
  background: ${({ theme }: { theme?: ThemeStyle }) =>
    theme?.checkInPop.wrapper_bg_color};
  border-radius: 10px;
  padding: 28px 38px 24px 38px;
  align-items: center;
`
export const Title = styled(Text)`
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: bold;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
`
export const SubTitle = styled(Text)`
  font-size: 14px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  letter-spacing: 0px;
  margin-top: 8px;
`
export const GiftWrapper = styled(View)`
  margin-top: 13px;
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
  height: 88px;
`

export const GiftWrapperNew = styled(View)`
  margin-top: 20px;
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
`

export const ActionButtonAnimationWrapper = Animated.View

export const ActionButton = styled(Touch)`
  background: #ff4444;
  border-radius: 29px;
  width: 200px;
  height: 40px;
  align-items: center;
  justify-content: center;
`

export const ActionButtonLabel = styled(Text)`
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
`

export const BtnGroup = styled(View)`
  display: flex;
  align-items: center;
  width: 100%;
  flex-direction: row;
  position: absolute;
  bottom: -.5px;
`
export const GroupBtn = styled(Touch)`
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 45px;
`

export const LeftGroupBtn = styled(GroupBtn)`
  border-bottom-left-radius: 10px;
  border-top-width: .5px;
  border-top-color: ${({ theme }: { theme?: ThemeStyle }) =>
    theme?.checkInPop.left_group_btn_border};
`
export const LeftGroupBtnLabel = styled(ActionButtonLabel)`
  color: ${({ theme }: { theme?: ThemeStyle }) =>
    theme?.checkInPop.left_group_btn_label};
`
export const RightGroupBtn = styled(GroupBtn)`
  border-bottom-right-radius: 10px;
  background-color: #FF4444;
`

export const CloseBtnWrapper = styled(Touch)`
  justify-content: center;
  align-items: center;
  width: 37px;
  height: 37px;
  position: absolute;
  top: 0;
  right: 0;
`

export const TitleNew = styled(Text)`
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: bold;
  text-align: center;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.checkInPop.title_color};
`

export const GiftItemWrapper = styled(View)`
  width: 72px;
  height: 82px;
  background-color: rgba(255, 68, 68, 0.04);
  border: .5px solid rgba(255, 68, 68, 0.1533);
  border-radius: 4.5px;
  align-items: center;
  margin: 0 4px;
`

export const GiftItemCover = styled(Image)`
  width: 64px;
  height: 64px;
  border-radius: 6px;
`

export const GiftItemLabelWrapper = styled(View)`
  align-items: center;
  flex-wrap: nowrap;
  height: 16px;
  position: relative;
  top: -6px;
`

export const GiftItemLabel = styled(Text)`
  font-size: 11px;
  font-weight: 400;
  color: #ff4444;
  text-align: center;
  flex-wrap: wrap;
  position: absolute;
  width: 99px;
`

export const GiftItemToolTipWrapper = styled(View)`
  background-color: #ffb32c;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  padding: 1px 4px;
  position: absolute;
  z-index: 1;
`
export const GiftItemToolTipArrow = styled(View)`
  border-color: transparent;
  width: 0;
  height: 0;
  border-top-width: 3px;
  border-right-width: 3px;
  border-left-width: 3px;
  border-bottom-width: 0px;
  border-top-color: #ffb32c;
  position: absolute;
  bottom: -2.9px;
`
export const GiftItemToolTipLabel = styled(Text)`
  color: #ffffff;
  font-size: 9px;
  text-align: center;
`

export const NoticeLabelWrapper = styled(View)`
  margin-top: 16;
  align-items: center;
`

export const NoticeLabel = styled(Text)`
  color: ${({ theme }: { theme?: ThemeStyle }) =>
    theme?.pcDialog.sub_title_color};
  font-size: 11px;
  padding: 0 18px;
  text-align: center;
`

export const IconPlus = styled(Image).attrs({
  source: icon_monthly_ticket_plus,
})`
  width: 12px;
  height: 12px;
  margin-bottom: 16px;
`

export const CornerTagStyles = StyleSheet.create({
  container: {
    width: 21,
    height: 12,
    borderRadius: 6,
    borderBottomLeftRadius: 1,
    backgroundColor: '#FF4444',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 4,
    right: 4
  },
  text: {
    fontSize: 11,
    lineHeight: 11,
    color: '#FFF',
    position: 'absolute',
    top: 1,
  }
});

export const NotificationWrapStyle = StyleSheet.create({
  wrap: {
    marginTop: 20,
    marginBottom: 16,
  }
});
