import React, { useCallback, useEffect, useRef, useState } from 'react'
import isEqual from 'lodash.isequal'
import { RootState, store } from '../../store'
import xmlog from '../../utilsV2/xmlog'
import Pop from '../common/Pop'
import {
  ActionButton,
  ActionButtonAnimationWrapper,
  ActionButtonLabel,
  CloseBtnWrapper,
  GiftWrapperNew,
  IconPlus,
  ModalContentWrapper,
  NoticeLabel,
  NoticeLabelWrapper,
  TitleNew,
  BtnGroup,
  LeftGroupBtn,
  LeftGroupBtnLabel,
  RightGroupBtn,
  NotificationWrapStyle,
} from './styles'
import CloseIcon from '../common/CloseIcon'
import { connect } from 'react-redux'
import { CheckInInfo, EnumVIPTag, UserIDTag } from '../../typesV2/signInNew'
import GiftItem from './GiftItem'
import FlipCard from '../FlipCard'
import { signInUserIDLabel } from '../../constantsV2/signIn'
import { Page, Toast, XMAppVersionHelper } from '@xmly/rn-sdk'
import getUrlToOpen from '../../utilsV2/getUrlToOpen'
import { safetyToString } from '@xmly/rn-utils'
import SentryUtils from '../../utilsV2/sentryUtils'
import getTomorrowSignInNoticeLabel from '../../utilsV2/getTomorrowSignInNoticeLabel'
import { AccessibilityInfo, Animated, NativeEventEmitter, NativeModules, StyleProp, View, ViewStyle } from 'react-native'
import useMonthlyTicketProgress from '../../hooksV2/useMonthlyTicketProgress'
import { MonthlyTicketActivityContext } from '../../typesV2/monthlyTicket'
import NotificationCheckButton from './NotificationCheckButton'
import { EnumNotificationStatus } from '../../typesV2/notification'
import notificationGuideStorage from '../../storageV2/notificationGuideStorage'
import dayjs from 'dayjs'
import { CheckInPopProps } from './types'
import execVideoTaskNew from '../../modulesV2/performTask/execVideoTaskNew'
import customReportError from '../../utilsV2/customReportError'
import TaskListModalControl from '../EverydayChallengeV2/TaskListModal/TaskListModalControl'
import useVideoTask from './hooks/useVideoTask'
import useDoubleAwardTask from '../../hooks/useDoubleAwardTask'

type Props = {
  userIDTag: UserIDTag
  randomQuantity: number
  tomorrowSignInGiftInfo: CheckInInfo
  monthlyTicketActivityContext: MonthlyTicketActivityContext
  notificationStatus: EnumNotificationStatus
}

const CheckInPopNew: React.FC<CheckInPopProps & Props> = ({
  todaySignInDay,
  checkInInfo,
  userIDTag,
  srcChannel,
  randomQuantity,
  isVip,
  tomorrowSignInGiftInfo,
  monthlyTicketActivityContext,
  notificationStatus,
}) => {
  const titleWrapperRef = useRef<View>(null)
  const [showNotificationGuide, setShowNotificationGuide] = useState(false)
  const animatedValue = useRef(new Animated.Value(1)).current
  const popExposeReported = useRef<boolean>(false)
  const xmRequestId = checkInInfo?.xmRequestId
  const { videoTask, hasVideoTask, execVideo } = useVideoTask(checkInInfo);
  const doubleTask = checkInInfo?.doubleAwardTask
  const [doubleTaskFinished, setDoubleTaskFinished] = useState(false)
  const hasDoubleTask = typeof doubleTask?.taskId === 'number' && typeof doubleTask.aid === 'number'
  const showDoubleTaskBtn = hasDoubleTask && !doubleTaskFinished
  const modalContentWrapperStyle = showDoubleTaskBtn ? {
    paddingLeft: 0,
    paddingRight: 0,
    paddingBottom: 45,
  } : { paddingLeft: 0, paddingRight: 0 }
  const [doubleAdViewed, setDoubleAdViewed] = useState<boolean | 'fallback'>(false)
  const [pageResumedFromDoubleAd, setPageResumedFromDoubleAd] = useState(false)
  const doubleAwardTaskInfo = useDoubleAwardTask();

  useEffect(() => {
    if (checkInInfo.btnAnimation) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 1.2,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
        ])
      ).start()
    }
  }, [checkInInfo.btnAnimation, animatedValue])

  const judgeShowNotificationGuide = useCallback(async () => {
    if (notificationStatus === EnumNotificationStatus.UNSET) return
    try {
      const lastGuideDate = await notificationGuideStorage.get()
      const hasFrequencyCtrl = !lastGuideDate
        ? false // 本地存储时间为空则没有频次控制
        : dayjs().diff(lastGuideDate, 'day') < 30 // 本地时间与当前时间小于30天则处于频次控制

      const visible = !hasFrequencyCtrl && notificationStatus === EnumNotificationStatus.disable

      if (visible) {
        // 如果可见，则存储当前日期作为30天频控起始日期
        notificationGuideStorage.set()
      }

      setShowNotificationGuide(visible)
    } catch (err) {
      setShowNotificationGuide(false)
    }
  }, [userIDTag, notificationStatus])

  useEffect(() => {
    judgeShowNotificationGuide()
  }, [judgeShowNotificationGuide])

  const [notificationChecked, setNotificationChecked] = useState(true)
  const isOldTypeMonthlyTicket = false
  const monthlyTicketProgressInfo = useMonthlyTicketProgress()

  const titleContent = doubleTaskFinished ? '任务完成，奖励翻倍' : todaySignInDay === 1 ? '签到成功' : `连续签到${todaySignInDay}天` + '，恭喜获得'

  const dynamicButtonLabelKey = isOldTypeMonthlyTicket ? 'btnTextForAB' : 'btnText'
  const dynamicButtonLinkKey = isOldTypeMonthlyTicket ? 'linkForAB' : 'link'
  const dynamicButtonLabel = checkInInfo[dynamicButtonLabelKey] || ''

  const actionButtonLabelContent =
    hasVideoTask && videoTask?.buttonLabel
      ? videoTask?.buttonLabel
      : monthlyTicketProgressInfo.isDone
        ? monthlyTicketActivityContext.monthlyTicketModalBtnText
        : dynamicButtonLabel
          ? dynamicButtonLabel
          : checkInInfo[dynamicButtonLinkKey]
            ? '去领取'
            : '知道了'

  const actionButtonLink = (monthlyTicketProgressInfo.isDone ? monthlyTicketActivityContext.monthlyTicketModalBtnLink : checkInInfo[dynamicButtonLinkKey]) || ''
  const notificationGuideButtonLabel = showDoubleTaskBtn ? '提醒我明天签到' : '知道了，提醒我明天签到'
  const actionButtonLabel = showNotificationGuide ? notificationGuideButtonLabel : actionButtonLabelContent

  const signDays = safetyToString(todaySignInDay)
  const eventReportCommonProps = {
    currPage: '任务中心',
    signDays,
    isVIP: isVip ? 'true' : 'false',
    groupName: safetyToString(signInUserIDLabel[userIDTag]),
    status: notificationChecked ? '选择' : '未选择',
  }

  const init = useCallback(() => {
    handleSetAccessibilityFocus()
    if (showDoubleTaskBtn && signDays && typeof xmRequestId === 'string') {
      xmlog.event(60209, 'slipPage', { currPage: '任务中心', signDays, xmRequestId })
    } else {
      xmlog.event(30540, 'dialogView', {
        sourceType: safetyToString(srcChannel),
        ...eventReportCommonProps,
      })
    }
  }, [showDoubleTaskBtn, signDays, xmRequestId])

  // 关闭弹窗
  const handleClose = (config?: { NoEventReport?: boolean }) => {
    // 同时关闭签到面板组件
    store.dispatch.signInInfo.refresh({ signInSuccessModalVisible: false })
    doubleAwardTaskInfo?.update?.();
    if (config?.NoEventReport) return
    xmlog.event(38589, 'dialogClick', eventReportCommonProps)
  }

  const handleCloseBtnPress = () => {
    handleClose()
  }

  const goToLink = (link?: string) => {
    link && Page.start(getUrlToOpen(link))
  }

  // 点击去领取
  const handleJumpLink = async () => {
    try {
      // if (isGift) {
      //   const receiveStatus = await giftReceiveStatusManager.getStatus({
      //     signInRecords: store.getState().signInInfo.signInRecords,
      //     todaySignInDay,
      //   })
      //   if (receiveStatus === SignInGiftReceiveStatus.notReceived) {
      //     goToLink(link)
      //   } else {
      //     Toast.info('奖励已领取')
      //   }
      // } else {
      //   goToLink(link)
      // }

      if (actionButtonLink.includes('action=taskListModal') && actionButtonLink.includes('bundle=rn_credit_center')) {
        // 打开任务包弹窗
        TaskListModalControl.open()
        return
      }

      if (actionButtonLink.includes('route=TaskList')) {
        // GlobalEventEmitter.emit('openTaskListPageFromSignInModal')
      } else {
        goToLink(actionButtonLink)
      }
    } catch (err) {
      console.log(err)
      SentryUtils.captureException(err, {
        source: 'CheckInPopNew.handleJumpLink',
      })
    }
  }

  function actionBtnClickReport() {
    if (hasDoubleTask) {
      if (doubleTaskFinished) {
        // 任务中心-签到奖励翻倍弹窗  点击事件
        xmlog.click(60220, '', { currPage: '任务中心', signDays, Item: actionButtonLabel, xmRequestId: xmRequestId ?? '' })
      } else {
        // 任务中心-签到成功弹窗V2  点击事件
        xmlog.click(60208, 'actionBtn', { currPage: '任务中心', signDays, Item: actionButtonLabel, xmRequestId: xmRequestId ?? '' })
      }
    } else {
      xmlog.event(30541, 'dialogClick', {
        item: actionButtonLabelContent,
        ...eventReportCommonProps,
      })
    }
  }

  const handlePressAction = () => {
    actionBtnClickReport()
    if (hasVideoTask) {
      execVideo();
    } else if (actionButtonLink) {
      handleJumpLink()
    }
    handleClose({ NoEventReport: true })
  }

  function onDoubleAdViewed() {
    setDoubleTaskFinished(true)
    setDoubleAdViewed(false)
    setPageResumedFromDoubleAd(false)
    // 任务中心-签到奖励翻倍弹窗  控件曝光
    xmlog.event(60221, 'slipPage', { currPage: '任务中心', signDays, Item: actionButtonLabelContent, xmRequestId: xmRequestId ?? '' })
  }

  useEffect(() => {
    if (doubleAdViewed && pageResumedFromDoubleAd || doubleAdViewed === 'fallback') { // 兜底fallback直接完成任务
      onDoubleAdViewed()
    }
  }, [doubleAdViewed, pageResumedFromDoubleAd])

  async function onDoubleBtnClick() {
    try {
      // 任务中心-签到成功弹窗V2  点击事件
      xmlog.click(60208, 'doubleBtn', { currPage: '任务中心', signDays, Item: doubleTask?.buttonLabel ?? '', xmRequestId: xmRequestId ?? '' })
      try {
        const lowerVersion = await XMAppVersionHelper.isLowerThan('9.1.78')
        if (lowerVersion) {
          return Toast.info('当前版本不支持该功能，请更新APP~')
        }
      } catch (error) {
        customReportError({ source: 'onDoubleBtnClick.checkLowVersion', error })
      }
      try {
        const PageEventEmitter = new NativeEventEmitter(NativeModules.Page)
        const onResumeListener = PageEventEmitter.addListener("onResume", () => {
          onResumeListener.remove()
          setPageResumedFromDoubleAd(true)
        })
      } catch (error) {
        customReportError({ source: 'onDoubleBtnClick.addPageResumeListener', error })
      }
      const viewResult = await execVideoTaskNew({ id: checkInInfo.doubleAwardTask!.taskId }, checkInInfo.doubleAwardTask!.aid, undefined, { sourceName: 'signInPopup' })
      if (viewResult) {
        setDoubleAdViewed(viewResult)
      }
    } catch (e) {
      customReportError({ source: 'onDoubleBtnClick.error', error: e })
    }
  }

  const RenderGift = () => {
    const { awards } = checkInInfo
    const filterPackageAwards = awards?.filter((a) => !a.label.includes('连签礼包')) || []
    if (filterPackageAwards && filterPackageAwards.length > 0) {
      const duration = 300
      const itemAnimationGap = 0
      const doubleGift = filterPackageAwards.filter((gift) => gift.doubleAllowed)
      return filterPackageAwards.map((gift, index) => {
        const giftItem = <GiftItem
          key={index}
          gift={gift}
          isFirst={index === 0}
          randomQuantity={randomQuantity}
        />
        const doubledGiftItem = <GiftItem
          key={index}
          gift={gift}
          isFirst={index === 0}
          randomQuantity={randomQuantity}
          doubled={true}
        />
        const animateIndex = doubleGift.findIndex(item => item.label === gift.label) //动画执行序号
        return (
          hasDoubleTask && gift.doubleAllowed ?
            <FlipCard
              backSlot={doubledGiftItem}
              delay={animateIndex * (duration + itemAnimationGap)}
              duration={duration}
              fire={doubleTaskFinished}
              frontSlot={giftItem}
              key={index}
            />
            :
            giftItem
        )
      })
    }

    return null
  }

  const renderNoticeLabel = () => {
    const { awards } = tomorrowSignInGiftInfo
    return <NoticeLabelWrapper>{awards && awards.length > 0 ? <NoticeLabel>明日签到，可得{getTomorrowSignInNoticeLabel(awards)}</NoticeLabel> : null}</NoticeLabelWrapper>
  }

  const RenderMonthlyTicketGift = () => {
    if (!monthlyTicketProgressInfo.isDone) return
    const { awards } = checkInInfo
    const containerWrapStyle: StyleProp<ViewStyle> =
      awards && awards?.length > 2
        ? {
          width: '100%',
          alignItems: 'center',
          marginBottom: 17,
        }
        : {
          flexDirection: 'row',
          alignItems: 'center',
        }
    return (
      <View style={containerWrapStyle}>
        <GiftItem
          gift={{
            icon: monthlyTicketActivityContext.monthlyTicketIcon,
            label: monthlyTicketActivityContext.monthlyTicketTitle,
          }}
        />
        <IconPlus />
      </View>
    )
  }

  const handleNotificationCheckedPress = () => {
    setNotificationChecked(!notificationChecked)
  }

  const handleOpenNotification = () => {
    actionBtnClickReport()

    if (notificationChecked) {
      store.dispatch.notification.toggleNotification({
        enable: true,
        openSettingConfirm: false,
      })
    }
    handleClose({ NoEventReport: true })
  }

  const actionButtonStyle: Animated.WithAnimatedObject<ViewStyle> = {
    transform: [{ scale: animatedValue }],
  }

  const handleSetAccessibilityFocus = () => {
    try {
      let label = '签到成功，展示签到成功弹窗'
      const filterPackageAwards = checkInInfo.awards?.filter((a) => !a.label.includes('连签礼包')) || []
      if (filterPackageAwards && filterPackageAwards.length > 0) {
        const awardsLabels = filterPackageAwards.map((award) => award.label).join('，')
        label = `${label}，恭喜获得${awardsLabels} 奖励`
      }
      AccessibilityInfo.announceForAccessibility(label)
    } catch (err) {
      console.log(err)
    }
  }

  useEffect(() => {
    if (!popExposeReported.current) {
      popExposeReported.current = true
      init()
    }
  }, [showDoubleTaskBtn, signDays, xmRequestId])

  return (
    <Pop handleClose={handleClose}>
      <ModalContentWrapper style={modalContentWrapperStyle}>
        <View ref={titleWrapperRef}>
          <TitleNew>{titleContent}</TitleNew>
        </View>
        <GiftWrapperNew>
          {RenderMonthlyTicketGift()}
          {RenderGift()}
        </GiftWrapperNew>
        {/* {renderNoticeLabel()} */}
        <View style={NotificationWrapStyle.wrap}>
          {showNotificationGuide ? (
            <NotificationCheckButton
              checked={notificationChecked}
              onPress={handleNotificationCheckedPress}
            />
          ) : null}
        </View>
        {showDoubleTaskBtn ? (
          <BtnGroup>
            <LeftGroupBtn onPress={showNotificationGuide ? handleOpenNotification : handlePressAction}>
              <LeftGroupBtnLabel>{actionButtonLabel}</LeftGroupBtnLabel>
            </LeftGroupBtn>
            <RightGroupBtn onPress={onDoubleBtnClick}>
              <ActionButtonLabel>{doubleTask?.buttonLabel}</ActionButtonLabel>
            </RightGroupBtn>
          </BtnGroup>
        ) : (
          <ActionButtonAnimationWrapper style={actionButtonStyle}>
            <ActionButton onPress={showNotificationGuide ? handleOpenNotification : handlePressAction}>
              <ActionButtonLabel>{actionButtonLabel}</ActionButtonLabel>
            </ActionButton>
          </ActionButtonAnimationWrapper>
        )}
        <CloseBtnWrapper
          onPress={handleCloseBtnPress}
          accessibilityLabel="关闭弹窗"
        >
          <CloseIcon
            size={13}
            color="#666"
          />
        </CloseBtnWrapper>
      </ModalContentWrapper>
    </Pop>
  )
}

export default connect(
  ({
    signInInfo: { todaySignInDay, checkInInfo, vipTag, userIDTag, randomQuantity, tomorrowSignInGiftInfo },
    monthlyTicket: { monthlyTicketActivityContext },
    notification: { status },
  }: RootState) => ({
    monthlyTicketActivityContext,
    todaySignInDay,
    checkInInfo,
    isVip: vipTag === EnumVIPTag.vip,
    userIDTag,
    randomQuantity,
    tomorrowSignInGiftInfo,
    notificationStatus: status,
  })
)(React.memo(CheckInPopNew, isEqual))
