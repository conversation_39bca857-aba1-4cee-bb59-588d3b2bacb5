import isEqual from 'lodash.isequal'
import React, { useContext } from 'react'
import { connect, useSelector } from 'react-redux'
import { NativeInfoContext } from '../../contextV2/nativeInfoContext'
import { RootState } from '../../store'
import { SignInMultiAwardsDetail } from '../../typesV2/signInNew'
import { ChannelUndertakeModalStatus } from '../../typesV2/channelModal'
import { cashTaskTouchChannel } from 'constantsV2'
import taskAtom from 'components/CashTask/store/task'
import { useAtomValue } from 'jotai'

const CheckInPopNewUI = React.lazy(() => import('./CheckInPopNew'))
const MultiAwardModalLazy = React.lazy(() => import('../MultiAwardModal'))

type Props = {
  hasShowOneModalBefore: boolean /** 之前未登录时是否显示过弹窗（承接｜未登录签到） */
  signInSuccessModalVisible: boolean /** 是否显示签到弹窗 */
  signInSuccessModalVisibleAgain?: boolean /** 是否再次显示签到（翻倍）弹窗 */
  signInBigGiftPackageModalVisible: boolean /** 是否显示大礼包弹窗 */
  todaySignInMultiAwardsConfig: null | SignInMultiAwardsDetail
}

const CheckInPop = ({ hasShowOneModalBefore, signInSuccessModalVisible, signInSuccessModalVisibleAgain, signInBigGiftPackageModalVisible, todaySignInMultiAwardsConfig }: Props) => {
  const { srcChannel = '', channelName = '' } = useContext(NativeInfoContext)
  const { modalStatus: channelModalStatus } = useSelector((state: RootState) => state.channelUndertake)
  const cashTask = useAtomValue(taskAtom);

  if (hasShowOneModalBefore) return null
  if (signInSuccessModalVisible && !signInBigGiftPackageModalVisible && channelModalStatus === ChannelUndertakeModalStatus.noNeed) {
    const mSrcChannel = channelName || srcChannel || ''
    // cashTask loading过程中不显示弹窗 && 有领现金任务不展示弹窗 && 用户主动触发翻倍弹窗可再次显示
    if ((cashTask === undefined || cashTask) && !signInSuccessModalVisibleAgain) {
      return null
    }
    if (!todaySignInMultiAwardsConfig) {
      return (
        <React.Suspense fallback={null}>
          <CheckInPopNewUI srcChannel={mSrcChannel} />
        </React.Suspense>
      )
    } else {
      return (
        <React.Suspense fallback={null}>
          <MultiAwardModalLazy srcChannel={mSrcChannel} />
        </React.Suspense>
      )
    }
  }

  return null
}

export default connect(({ signInInfo: { signInSuccessModalVisible, signInSuccessModalVisibleAgain, signInBigGiftPackageModalVisible, todaySignInMultiAwardsConfig, hasShowOneModalBefore } }: RootState) => ({
  signInSuccessModalVisible,
  signInSuccessModalVisibleAgain,
  signInBigGiftPackageModalVisible,
  todaySignInMultiAwardsConfig,
  hasShowOneModalBefore,
  // abRuleTag,
}))(React.memo(CheckInPop, isEqual))
