import React from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { Image, View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { textAlignMiddleStyle } from '../../constantsV2/layout'
import icon_monthly_ticket_checked from '../../appImagesV2/icon_monthly_ticket_checked'
import { hitSlop } from '@xmly/rn-utils'
import { ThemeStyle } from '../../typesV2/themeInfo'

type Props = {
  checked: boolean
  onPress: () => void
  label?: string
}

const Wrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  paddingTop: 4px;
`

const IconWrapper = styled(Touch)`
  margin-right: 2px;
  border: 1px solid rgba(153, 153, 153, 0.4);
  width: 12px;
  height: 12px;
  border-radius: 6px;
`

const Icon = styled(Image)`
  width: 12px;
  height: 12px;
`

const Label = styled(Text)`
  font-size: 12px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.checkInPop.notification_check_button_label};
`

const NotificationCheckButton: React.FC<Props> = ({ checked, onPress, label }) => {
  const accessibilityLabel = checked ? '单选按钮，订阅签到提醒已开启' : '单选按钮，订阅签到提醒已关闭'
  return (
    <Wrapper>
      <IconWrapper
        style={{ borderWidth: checked ? 0 : 1 }}
        onPress={onPress}
        activeOpacity={1}
        hitSlop={hitSlop(45)}
        accessibilityLabel={accessibilityLabel}
      >
        {checked ? (
          <Icon
            fadeDuration={0}
            source={icon_monthly_ticket_checked}
          />
        ) : null}
      </IconWrapper>
      <Label style={textAlignMiddleStyle}>{label || '订阅签到提醒，每日10点推送提醒消息'}</Label>
    </Wrapper>
  )
}

export default React.memo(NotificationCheckButton, isEqual)
