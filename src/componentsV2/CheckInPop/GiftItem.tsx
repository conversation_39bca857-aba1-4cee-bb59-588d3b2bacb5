import React from 'react'
import { View, Text } from 'react-native'
import isEqual from 'lodash.isequal'
import {
  GiftItemCover,
  GiftItemLabel,
  GiftItemLabelWrapper,
  GiftItemToolTipArrow,
  Gift<PERSON>temToolTipLabel,
  GiftItemToolTipWrapper,
  GiftItemWrapper,
  CornerTagStyles
} from './styles'
import { CheckInInfoAwardItem } from '../../typesV2/signInNew'
import { useSelector } from 'react-redux'
import { RootState } from '../../store'

type Props = {
  gift: CheckInInfoAwardItem
  isFirst?: boolean
  giftTooltip?: string
  randomQuantity?: number
  isMonthlyTicket?: boolean
  doubled?: boolean
}

const GiftItem: React.FC<Props> = ({
  gift,
  isFirst,
  giftTooltip,
  randomQuantity,
  isMonthlyTicket,
  doubled,
}) => {
  const todaySignInDay = useSelector(
    (state: RootState) => state.signInInfo.todaySignInDay
  )
  const isCreditAwardType = isFirst && todaySignInDay !== 7

  const todaySignInCreditValue = useSelector(
    (state: RootState) => state.signInInfo.todaySignInCreditValue
  )
  const giftItemLabel = gift.label.includes('积分') && typeof randomQuantity === 'number' ?
    randomQuantity > 0 ?
      `${randomQuantity}积分`
      :
      Number(todaySignInCreditValue) > 0 ?
        `${todaySignInCreditValue}积分`
        :
        gift.label
    :
    gift.label

  return (
    <GiftItemWrapper>
      {isCreditAwardType && giftTooltip ? (
        <GiftItemToolTipWrapper style={{ transform: [{ translateY: -8 }] }}>
          <GiftItemToolTipLabel>{giftTooltip}</GiftItemToolTipLabel>
          <GiftItemToolTipArrow />
        </GiftItemToolTipWrapper>
      ) : null}
      <GiftItemCover source={{ uri: gift.icon }} />
      <GiftItemLabelWrapper>
        <GiftItemLabel>{giftItemLabel}</GiftItemLabel>
      </GiftItemLabelWrapper>
      {doubled && <View style={CornerTagStyles.container}>
        <Text style={CornerTagStyles.text}>x2</Text>
      </View>}
    </GiftItemWrapper>
  )
}

export default React.memo(GiftItem, isEqual)
