import React, { Children, useEffect, useState } from 'react'
import { NativeModules } from 'react-native'
import { XMABTestConfig, XMABTestValueType } from './typesv2'
import XMABTest from './XMABTest'

type Props = {
  config: XMABTestConfig[]
  children: JSX.Element
}

const getSingleConfig = (configItem: XMABTestConfig) => {
  try {
    switch (configItem.type) {
      case XMABTestValueType.boolean:
        return NativeModules.XMAbtest.getBoolean(
          configItem.key,
          configItem.defaultValue
        )
      case XMABTestValueType.string:
        return NativeModules.XMAbtest.getString(
          configItem.key,
          configItem.defaultValue
        )
      case XMABTestValueType.number:
        return NativeModules.XMAbtest.getInt(
          configItem.key,
          configItem.defaultValue
        )
    }
  } catch (err) {
    return Promise.resolve(configItem.defaultValue)
  }
}

const XMABTestProvider: React.FC<Props> = ({ config, children }) => {
  const [ready, setReady] = useState(false)

  useEffect(() => {
    getConfig()
  }, [])

  const getConfig = async () => {
    let result = {}
    let resultArr: any[] = []
    try {
      if (
        typeof NativeModules?.XMAbtest !== 'undefined' &&
        typeof NativeModules?.XMAbtest?.getBoolean === 'function' &&
        typeof NativeModules?.XMAbtest?.getInt === 'function' &&
        typeof NativeModules?.XMAbtest?.getString === 'function'
      ) {
        resultArr = await Promise.all(config.map(getSingleConfig))
        config.forEach((e, index) => {
          result[e.key] = resultArr[index]
        })  
        XMABTest.setXMABTestValue(result)
      } else {
        resultArr = config.map((c) => c.defaultValue)
        config.forEach((e, index) => {
          result[e.key] = resultArr[index]
        })
        XMABTest.setXMABTestValue(result)
      }
    } catch (err) {
      console.log(err)
      resultArr = config.map((c) => c.defaultValue)
      config.forEach((e, index) => {
        result[e.key] = resultArr[index]
      })
      XMABTest.setXMABTestValue(result)
 
    } finally {
      setReady(true)
    }
    console.log('💊💊💊💊💊💊💊💊💊💊💊💊💊');
    console.log(result);

  }

  if (!ready) {
    return null
  }
  return Children.only(children)
}

export default XMABTestProvider
