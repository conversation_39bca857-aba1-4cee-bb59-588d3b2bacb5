import React from 'react'
import { View } from 'react-native'
import styled from 'styled-components'
import { ThemeStyle } from '../../typesV2/themeInfo'

type Props = {}

const Block = styled(View)`
  width: 100%;
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.item_bg_color};
  height: 214px;
  padding: 14px 16px;
`

const InnerBlock = styled(View)`
  border-radius: 2px;
  opacity: 0.15;
  /* Normal🌞/ThinTextColor */
  background: #aaaaaa;
`

const Row = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`

const SmallItemWrapper = styled(View)`
  align-items: center;
  width: 44px;
`

const ADSectionSkeleton: React.FC<Props> = (props) => {
  return (
    <Block>
      <InnerBlock style={{ height: 18, width: 83, marginBottom: 23 }} />
      <Row>
        <SmallItemWrapper>
          <InnerBlock
            style={{ width: 44, height: 44, borderRadius: 22, marginBottom: 4 }}
          />
          <InnerBlock style={{ width: 44, height: 8, borderRadius: 2 }} />
        </SmallItemWrapper>
        <SmallItemWrapper>
          <InnerBlock
            style={{ width: 44, height: 44, borderRadius: 22, marginBottom: 4 }}
          />
          <InnerBlock style={{ width: 44, height: 8, borderRadius: 2 }} />
        </SmallItemWrapper>
        <SmallItemWrapper>
          <InnerBlock
            style={{ width: 44, height: 44, borderRadius: 22, marginBottom: 4 }}
          />
          <InnerBlock style={{ width: 44, height: 8, borderRadius: 2 }} />
        </SmallItemWrapper>
        <SmallItemWrapper>
          <InnerBlock
            style={{ width: 44, height: 44, borderRadius: 22, marginBottom: 4 }}
          />
          <InnerBlock style={{ width: 44, height: 8, borderRadius: 2 }} />
        </SmallItemWrapper>
      </Row>
      <InnerBlock
        style={{ width: '100%', height: 71, borderRadius: 8, marginTop: 17 }}
      />
    </Block>
  )
}

export default React.memo(ADSectionSkeleton, () => true)
