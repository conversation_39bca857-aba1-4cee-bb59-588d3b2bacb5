import React, { useCallback, useContext, useEffect, useState } from 'react'
import isEqual from 'lodash.isequal'
import { CardListType } from '../../typesV2/cardListData'
import AdList from '../AdList'
import UseCredit from '../UseCredit'
import getCardListData from '../../servicesV2/getCardListData'
import ADSectionSkeleton from './ADSectionSkeleton'
import userInfoDetail from '../../modulesV2/userInfoDetail'
import { seas } from '../../../src/backData/seas'
import { UserInfoContext } from 'contextV2/userInfoContext'
import { useSelector } from 'react-redux'
import { RootState } from 'store'
import UserLoginStatus from 'typesV2/userLoginStatus'
import taskAtom from 'components/CashTask/store/task'
import { useAtomValue } from 'jotai'

type Props = {}

const ADSection: React.FC<Props> = (props) => {
  const [showCardListData, setShowCardListData] = useState<Boolean>(false)
  const [squareCardItem, setSquareCardItem] = useState<CardListType>()
  const [seaviewRoomItem, setSeaviewRoomItem] = useState<CardListType>()
  const [listType, setListType] = useState<'one' | 'two'>('one')
  const [isLoading, setIsLoading] = useState(true)
  // const netInfo = useNetInfo()
  const { loginStatus } = useContext(UserInfoContext)
  const { noLoginAbTestStatus } = useSelector((state: RootState) => state.signInInfo)
  const cashTask = useAtomValue(taskAtom);

  const _calJson = (serverSeas: any) => {
    let _o: any = {}
    try {
      _o = JSON.parse(serverSeas)
    } catch (err) {
      _o = seas
    }
    return _o
  }

  useEffect(() => {
    getCardList()
  }, [])

  const getCardList = useCallback(async () => {
    try {
      const xIsLogin = userInfoDetail.getDetail()?.isLogin
      const _res: any = await getCardListData()
      // const res = xIsLogin ? _res : JSON.parse(_res?.data?.[0]?.squareSea || '{}')
      const res = xIsLogin ? _res : _calJson(_res?.data?.[0]?.squareSea)
      if (res.code === 1) {
        const { cardList } = res
        const squareCardData = cardList.filter((item: CardListType) => item.cardType === 'square')[0]
        const seaviewRoomData = cardList.filter((item: CardListType) => item.cardType === 'seaviewRoom')[0]
        setSquareCardItem(squareCardData)
        checkIsShowSeaviewRoom(seaviewRoomData)
      } else {
        setShowCardListData(false)
      }
    } catch (error) {
      setShowCardListData(false)
    } finally {
      setIsLoading(false)
    }
  }, [])

  const checkIsShowSeaviewRoom = useCallback((item: CardListType) => {
    switch (item.extraInfo?.displayClass!) {
      case 'none':
        setShowCardListData(false)
        break
      case 'one':
        setShowCardListData(true)
        setListType('one')
        setSeaviewRoomItem(item)
        break
      case 'two':
        setShowCardListData(true)
        setListType('two')
        setSeaviewRoomItem(item)
        break
      default:
        setShowCardListData(false)
        break
    }
  }, [])

  // 骨架图
  if (isLoading) {
    return <ADSectionSkeleton />
  }
  const loginCondition = loginStatus === UserLoginStatus.notLogin && noLoginAbTestStatus === '2'
  return (
    <>
      {squareCardItem && <UseCredit squareCardItem={squareCardItem} />}
      {!loginCondition && showCardListData && seaviewRoomItem && cashTask === null ? <AdList type={listType} seaviewRoomItem={seaviewRoomItem} /> : null}
    </>
  )
}

export default React.memo(ADSection, isEqual)
