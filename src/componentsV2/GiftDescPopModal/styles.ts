import { BetterImage, Text, Touch } from '@xmly/rn-components'
import { Image, View } from 'react-native'
import styled from 'styled-components'
import { ThemeStyle } from '../../typesV2/themeInfo'

export const ModalContentWrapper = styled(View)`
  width: 276px;
  background: ${({ theme }: { theme: ThemeStyle }) => theme.checkInPop.wrapper_bg_color};
  border-radius: 10px;
  padding: 32px 38px 24px 38px;
  align-items: center;
`

export const Title = styled(Text)`
  font-size: 17px;
  font-family: PingFangSC-Medium;
  font-weight: bold;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color_new};
  `

export const DescWrap = styled(View)`
  padding: 0 24px;
  `

export const DescText = styled(Text)`
  margin-top: 4px;
  font-size: 15px;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.sub_title_color};
  `

export const GiftImage = styled(BetterImage)`
  margin-top: 16px;
  width: 108px;
  height: 68px;
`

export const ActionButton = styled(Touch)`
  margin-top: 24px;
  background: #ff4444;
  border-radius: 29px;
  width: 200px;
  height: 40px;
  align-items: center;
  justify-content: center;
`

export const ActionButtonLabel = styled(Text)`
  font-size: 15px;
  font-weight: 600;
  color: #ffffff;
`