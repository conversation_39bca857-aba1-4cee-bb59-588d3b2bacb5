import React, { useEffect } from 'react'
import isEqual from 'lodash.isequal'
import Pop from '../common/Pop'
import {
  ActionButton,
  ActionButtonLabel,
  ModalContentWrapper,
  Title,
  DescWrap,
  DescText,
  GiftImage
} from './styles'
import { useSelector } from 'react-redux'
import { RootState } from 'store'
import xmlog from 'utilsV2/xmlog'

interface Props {
	setClose: () => void
}

const GiftDescModalLazy: React.FC<Props> = ({setClose}) => {
  const receiveAwardPop = useSelector( (state: RootState) => state.signInInfo.receiveAwardPop);

	useEffect(() => {
		init()
	}, [])

  const init = async () => {
   console.log('init 连签礼包显示弹窗埋点')
  }

  // 关闭弹窗
  const handleClose = () => {
		setClose()
  }

  const handleCloseBtnPress = () => {
    // 任务中心-礼包说明弹窗  点击事件
  xmlog.click(53936, undefined, {currPage : '任务中心', dialogTitle : receiveAwardPop.title });
    handleClose()
  }


  return (
    <Pop handleClose={handleClose}>
      <ModalContentWrapper style={{ paddingLeft: 0, paddingRight: 0 }}>
        <Title>{receiveAwardPop.title}</Title>
        <DescWrap><DescText>{receiveAwardPop.subTitle}</DescText></DescWrap>
        <GiftImage source={{uri: receiveAwardPop.logo}} />
        <ActionButton
          onPress={handleCloseBtnPress}
        >
          <ActionButtonLabel>
          {receiveAwardPop.buttenText}
          </ActionButtonLabel>
        </ActionButton>
      </ModalContentWrapper>
    </Pop>
  )
}

export default React.memo(GiftDescModalLazy, isEqual)
