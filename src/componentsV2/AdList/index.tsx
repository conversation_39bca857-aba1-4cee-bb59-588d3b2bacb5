import React, { useCallback, useContext } from 'react'
import { useNavigation } from '@react-navigation/native'
import { BetterImage, Touch } from '@xmly/rn-components'
import { NativeModules, View, Platform } from 'react-native'
import styled from 'styled-components'
import { BodyItem, CardListType } from 'typesV2/cardListData'
import parseRNUrl from 'utilsV2/getRNUrlToOpen'
import getUrlToOpen from 'utilsV2/getUrlToOpen'
import throttle from 'utilsV2/throttle'
import PageAnalytics from '@xmly/react-native-page-analytics'
import xmlog from 'utilsV2/xmlog'
import isEqual from 'lodash.isequal'
import UserLoginStatus from '../../typesV2/userLoginStatus'
// import goToLogin from '../../utilsV2/goToLogin'
import goToLoginForAbTest from '../../utilsV2/goToLoginForAbTest'

import { UserInfoContext } from '../../contextV2/userInfoContext'
import { useSelector } from 'react-redux'
import { RootState } from '../../store'
import execVideoTaskNew from 'modulesV2/performTask/execVideoTaskNew'

const { Page } = NativeModules

const Wrapper = styled(View)`
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
  justify-content: space-between;
`

const ImageItem = styled(BetterImage)`
  width: 100%;
  height: 100%;
  border-radius: 10px;
  margin-bottom: 12px;
`

interface ImageItemsProps {
  index: number
}

const ImageItems = styled(BetterImage)<ImageItemsProps>`
  width: 100%;
  height: 100%;
  border-radius: 10px;
  margin-right: ${(props) => (props.index === 0 ? '8px' : '0')};
`

interface Props {
  type: 'one' | 'two'
  seaviewRoomItem: CardListType
}

const AdList: React.FC<Props> = (props) => {
  const { type, seaviewRoomItem } = props
  const width = useSelector((state: RootState) => state.page.screenWidth)
  const oneImgWidth = Math.floor(width - 24)
  const oneImgHeight = Math.floor(oneImgWidth / 4.43)
  const twoImgWidth = Math.floor((width - 32) / 2)
  const twoImgHeight = Math.floor(twoImgWidth / 2.15)
  const { loginStatus } = useContext(UserInfoContext)
  const navigation = useNavigation()

  const customPageView = useCallback(() => {
    // 任务中心-banner  控件曝光
    if (type === 'one') {
      xmlog.event(50924, 'slipPage', {
        currPage: '任务中心',
        url: seaviewRoomItem.body[0].landingPage || '',
      })
    } else {
      seaviewRoomItem.body.forEach((item: BodyItem) => {
        xmlog.event(50924, 'slipPage', {
          currPage: '任务中心',
          url: item.landingPage || '',
        })
      })
    }
  }, [])

  const customPageExit = useCallback(() => {}, [])

  PageAnalytics.useScreen({
    customPageView,
    customPageExit,
    navigation,
    ...props,
  })

  const handlePress = (item: BodyItem) => {
    if (loginStatus !== UserLoginStatus.login) {
      goToLoginForAbTest()
      return
    }
    const urlType = checkUrlType(item.landingPage)
    // 任务中心-banner  点击事件
    xmlog.click(50923, undefined, {
      currPage: '任务中心',
      url: item.landingPage || '',
    })
    throttleOpenUrl(urlType, item.landingPage)
  }

  const checkUrlType = (url: string) => {
    if (/^http(s)?:\/\//i.test(url)) {
      return 'h5'
    } else if (/^rn:\/\//i.test(url)) {
      return 'rn'
    } else if (/^iting:\/\//i.test(url)) {
      return 'iting'
    } else if (url.startsWith('ad-')) {
      return 'ad'
    } else {
      return null
    }
  }

  const jumpUrl = (urlType: string | null, url: string) => {
    switch (urlType) {
      case 'h5':
      case 'iting':
        Page.start(getUrlToOpen(url))
        break
      case 'rn':
        const parsedParam: any = parseRNUrl(url)
        if (parsedParam) {
          navigation.navigate(parsedParam.name, parsedParam.params)
        }
        break
      case 'ad':
        const { taskId, aid, positionId, positionName }: any = parseRNUrl(url)
        execVideoTaskNew({ id: taskId }, aid, { positionId, positionName }, { sourceName: 'home_ad' });
        break;
      default:
        Page.start(getUrlToOpen(url))
        break
    }
  }
  const throttleOpenUrl = useCallback(throttle(jumpUrl, Platform.OS === 'android' ? 1000 : 0), []);

  return (
    <>
      {type === 'one' && (
        <Touch
          style={{ width: oneImgWidth, height: oneImgHeight }}
          onPress={() => {
            handlePress(seaviewRoomItem.body[0])
          }}
        >
          <ImageItem
            source={{ uri: seaviewRoomItem.body[0].cover }}
            resizeMode="cover"
          />
        </Touch>
      )}
      {type === 'two' && (
        <Wrapper>
          {seaviewRoomItem.body.map((item, index) => (
            <Touch
              style={{ width: twoImgWidth, height: twoImgHeight }}
              accessibilityLabel={item.title || ''}
              onPress={() => {
                handlePress(item)
              }}
              key={index}
            >
              <ImageItems
                index={index}
                source={{ uri: item.cover }}
                resizeMode="cover"
              />
            </Touch>
          ))}
        </Wrapper>
      )}
    </>
  )
}

export default React.memo(AdList, isEqual)
