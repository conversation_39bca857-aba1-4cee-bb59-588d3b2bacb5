import React, { useEffect } from 'react'
import isEqual from 'lodash.isequal'
import Pop from '../common/Pop'
import styled from 'styled-components'
import { AccessibilityInfo, Image, Text, View } from 'react-native'
import { ThemeStyle } from '../../typesV2/themeInfo'
import CloseIcon from '../common/CloseIcon'
import { Touch } from '@xmly/rn-components'

const ModalContentWrapper = styled(View)`
  width: 276px;
  background: ${({ theme }: { theme: ThemeStyle }) => theme.checkInPop.wrapper_bg_color};
  border-radius: 10px;
  padding: 32px 38px 24px 38px;
  align-items: center;
`

const ModalTitle = styled(Text)`
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: bold;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color_new};
`

const AwardIcon = styled(Image)`
  width: 64px;
  height: 64px;
  margin-top: 28px;
`

const AwardLabel = styled(Text)`
  font-size: 11px;
  text-align: center;
  color: #ff4444;
  margin-top: 8px;
`

export const CloseBtnWrapper = styled(Touch)`
  justify-content: center;
  align-items: center;
  width: 37px;
  height: 37px;
  position: absolute;
  top: 0;
  right: 0;
`

export const ActionButton = styled(Touch)`
  margin-top: 15px;
  background: #ff4444;
  border-radius: 29px;
  width: 200px;
  height: 40px;
  align-items: center;
  justify-content: center;
`

export const ActionButtonLabel = styled(Text)`
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
`

type Props = {
  awardWorth: number
  recommendAwardLabel: string
  currentAwardLabel?: string
  currentAwardIcon?: string
  onClose: () => void
  onExecRecommendTask: () => void
}
const awardIconUri = {
  uri: 'https://imagev2.xmcdn.com/storages/fe10-audiofreehighqps/AD/91/GKwRINsGdLeTAAAbvAFmYFP1.png',
}

const TaskAwardModalLazy: React.FC<Props> = ({ recommendAwardLabel, awardWorth, onClose, onExecRecommendTask, currentAwardLabel, currentAwardIcon }) => {
  const handleCloseBtnPress = () => {
    onClose()
  }
  const handleExecTask = () => {
    onExecRecommendTask()
  }

  const label = currentAwardLabel ? currentAwardLabel : `${awardWorth}积分`
  const icon = currentAwardIcon ? { uri: currentAwardIcon } : awardIconUri

  useEffect(() => {
    // 发送相应文本信息给读屏应用朗读
    AccessibilityInfo.announceForAccessibility(`恭喜获得${label}`)
  }, [])

  return (
    <Pop handleClose={onClose}>
      <ModalContentWrapper>
        <ModalTitle accessibilityLabel={`恭喜获得${label}`}>恭喜获得</ModalTitle>
        <AwardIcon source={icon} />
        <AwardLabel accessible={false}>{label}</AwardLabel>
        <CloseBtnWrapper onPress={handleCloseBtnPress}>
          <CloseIcon
            size={13}
            color="#666"
          />
        </CloseBtnWrapper>

        <ActionButton
          onPress={handleExecTask}
          accessibilityLabel="关闭弹窗"
        >
          <ActionButtonLabel>{recommendAwardLabel}</ActionButtonLabel>
        </ActionButton>
      </ModalContentWrapper>
    </Pop>
  )
}

export default React.memo(TaskAwardModalLazy, isEqual)
