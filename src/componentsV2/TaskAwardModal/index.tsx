import React, { Suspense, useEffect } from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import performTask from '../../modulesV2/performTask'
import xmlog from '../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'

type Props = {}

const TaskAwardModalLazy = React.lazy(() => import('./TaskAwardModal.lazy'))

const TaskAwardModal: React.FC<Props> = (props) => {
  const taskAwardModalState = useSelector((state: RootState) => state.taskAwardModal)

  const handleClose = () => {
    store.dispatch.taskAwardModal.refresh({ visible: false })
    // 领取任务奖励后弹窗-点击按钮  弹框控件点击
    xmlog.event(51162, 'dialogClick', {
      taskId: safetyToString(taskAwardModalState.currentTaskId),
      item: '关闭',
      currPage: '任务中心',
    })
  }

  const handleExecRecommendTask = () => {
    if (taskAwardModalState.recommendTask) {
      performTask(taskAwardModalState.recommendTask, taskAwardModalState.recommendTaskAid, {
        sourceName: 'doublePopup'
      })
      store.dispatch.taskAwardModal.refresh({ visible: false })
      // 领取任务奖励后弹窗-点击按钮  弹框控件点击
      xmlog.event(51162, 'dialogClick', {
        taskId: safetyToString(taskAwardModalState.currentTaskId),
        item: taskAwardModalState.recommendAwardLabel,
        currPage: '任务中心',
      })
    }
  }

  useEffect(() => {
    if (taskAwardModalState.visible) {
      // 领取任务奖励后弹窗  弹框展示
      xmlog.event(51158, 'dialogView', {
        taskId: safetyToString(taskAwardModalState.currentTaskId),
        currPage: '任务中心',
        Item: taskAwardModalState.recommendAwardLabel,
      })
    }
  }, [taskAwardModalState.visible, taskAwardModalState.recommendAwardLabel, taskAwardModalState.currentTaskId])

  if (taskAwardModalState.visible) {
    return (
      <Suspense fallback={null}>
        <TaskAwardModalLazy
          onClose={handleClose}
          onExecRecommendTask={handleExecRecommendTask}
          awardWorth={taskAwardModalState.awardWorth}
          recommendAwardLabel={taskAwardModalState.recommendAwardLabel}
          currentAwardIcon={taskAwardModalState.currentAwardIcon}
          currentAwardLabel={taskAwardModalState.currentAwardLabel}
        />
      </Suspense>
    )
  }
  return null
}

export default React.memo(TaskAwardModal, isEqual)
