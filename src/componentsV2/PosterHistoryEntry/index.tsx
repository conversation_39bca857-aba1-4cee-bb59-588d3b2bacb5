import React, {
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react'
import { StyleSheet, Image, View } from 'react-native'
import dayjs from 'dayjs'
import { Text, Touch } from '@xmly/rn-components'
import { useNavigation } from '@react-navigation/native'
import { RootNavigationProps } from '../../router/type'
import getPosterShareTaskConfig from '../../utilsV2/getPosterShareTaskConfig'
import isEqual from 'lodash.isequal'
import xmlog from '../../utilsV2/xmlog'
import { ThemeContext } from '../../contextV2/themeContext'
import { DarkModeContext } from '../../contextV2/darkModeContext'
import icon_next_light from '../../appImagesV2/icon_next_light'

type Props = {}

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    height: '100%',
    paddingLeft: 11,
    flexDirection: 'row',
    alignItems: 'center',
  },
  posterWrapper: {
    width: 48,
    height: 62,
  },
  wrapperInner: {
    width: 48,
    alignItems: 'center',
    height: 62,
  },

  dialogueWrapper: {
    position: 'absolute',
    top: -9,
    alignItems: 'center',
    justifyContent: 'center',
  },
  bg: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  cover: {
    width: 40,
    height: 33,
  },
  dateLabel: {
    fontSize: 6,
    position: 'absolute',
    bottom: 11,
  },
  textWrapper: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  taskNotice: {
    fontSize: 11,
    fontWeight: '400',
    color: '#666666',
    lineHeight: 16,
  },
  signText: {
    fontSize: 15,
    fontWeight: 'bold',
  },
  iconRight: {
    width: 16,
    height: 16,
  },
})

const PosterHistoryEntry: React.FC<Props> = () => {
  const todayLabel = useRef(dayjs().format('MM/DD'))
  const navigation = useNavigation<RootNavigationProps>()
  const [taskValue, setTaskValue] = useState(0)
  const { isDarkMode } = useContext(DarkModeContext)
  const theme = useContext(ThemeContext)

  const getTaskValue = async () => {
    const res = await getPosterShareTaskConfig()
    setTaskValue(res)
  }

  useEffect(() => {
    getTaskValue()
  }, [])

  const handlePress = useCallback(() => {
    navigation.navigate('PosterHistory')
    xmlog.click(38580, undefined, {
      currPage: '任务中心',
    })
  }, [navigation])

  return (
    <Touch style={styles.wrapper} onPress={handlePress}>
      <View style={styles.posterWrapper}>
        <View style={styles.wrapperInner}>
          <Image
            style={styles.bg}
            source={theme.credit.icon_poster_history_entry}
            resizeMethod='resize'
            resizeMode='cover'
          />
          <Text
            style={[styles.dateLabel, { color: theme.posterHistory.color }]}
          >
            {todayLabel.current}
          </Text>
        </View>
      </View>
      <View
        style={{
          justifyContent: 'space-between',
          paddingTop: 16,
          paddingBottom: 12,
          height: '100%',
        }}
      >
        <Text style={[styles.signText, { color: theme.credit.number_color }]}>
          日签海报
        </Text>
        <View style={styles.textWrapper}>
          <Text style={[styles.taskNotice, { color: theme.credit.desc_color }]}>
            每日分享领
            {taskValue ? (
              <Text style={{ color: '#FF4646' }}>{taskValue}</Text>
            ) : null}
            积分
          </Text>
          <Image
            source={icon_next_light}
            style={[
              styles.iconRight,
              { tintColor: isDarkMode ? '#888' : undefined },
            ]}
          />
        </View>
      </View>
    </Touch>
  )
}

export default React.memo(PosterHistoryEntry, isEqual)
