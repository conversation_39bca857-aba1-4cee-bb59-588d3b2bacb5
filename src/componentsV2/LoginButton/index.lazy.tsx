import React, { useContext } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { View, StyleSheet, Image } from 'react-native'
import goToLogin from '../../utilsV2/goToLogin'
import icon_login_empty_book_dark from '../../appImagesV2/icon_login_empty_book_dark'
import { ThemeContext } from '../../contextV2/themeContext'

type Props = {}

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    zIndex: 99999,
    alignItems: 'center',
    paddingBottom: 28,
    marginTop: 16,
  },
  btnWrapper: {
    width: 80,
    height: 28,
    borderRadius: 14,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    marginTop: 12,
  },
  btnText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '700',
  },
  icon: {
    width: 75,
    height: 75,
  },
  label: {
    fontSize: 13,
    marginTop: 8,
  },
})

const LoginButton: React.FC<Props> = (props) => {
  const theme = useContext(ThemeContext)

  const handlePress = () => {
    goToLogin()
  }

  return (
    <View style={styles.wrapper}>
      <Image style={styles.icon} source={icon_login_empty_book_dark} />
      <Text style={[styles.label, { color: theme.loginButton.label_color }]}>登录后才能使用任务中心</Text>
      <Touch onPress={handlePress} style={[styles.btnWrapper, { borderColor: theme.loginButton.border_color }]}>
        <Text style={[styles.btnText, { color: theme.loginButton.button_label_color }]}>去登录</Text>
      </Touch>
    </View>
  )
}

export default React.memo(LoginButton, () => true)
