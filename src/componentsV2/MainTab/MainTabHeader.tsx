import { ScrollAreaContext } from 'contextV2/scrollAreaContext'
import React, { useContext } from 'react'
import TabBar from './TabBar'

type Props = {
  handleChangeTab: (tab: number) => void
}


const MainTabHeader: React.FC<Props> = (props) => {
  const { handleChangeTab } = props
  const { mainTabScrollValue } = useContext(ScrollAreaContext)
  return (
    <TabBar onChangeTab={ handleChangeTab } tabScrollValue={ mainTabScrollValue } />
  )
}

export default React.memo(MainTabHeader, () => {
  return true
})