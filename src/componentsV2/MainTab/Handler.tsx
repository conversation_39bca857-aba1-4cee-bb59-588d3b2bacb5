import React from 'react'
import { PanGestureHandler, State } from 'react-native-gesture-handler'
import Animated from 'react-native-reanimated'
import MainTabHeader from './MainTabHeader'

type Props = {
  gestureY: Animated.Value<number>
  velocityY: Animated.Value<number>
  gestureState: Animated.Value<State>
  onChangeTab: (index: number) => void
}

const Handler: React.FC<Props> = (props) => {
  const { gestureY, velocityY, gestureState, onChangeTab } = props
  const handleGestureEvent = Animated.event([
    {
      nativeEvent: {
        translationY: gestureY,
        velocityY: velocityY,
        state: gestureState,
      },
    },
  ])

  return (
    <PanGestureHandler
      shouldCancelWhenOutside={ false }
      onGestureEvent={ handleGestureEvent }
      onHandlerStateChange={ handleGestureEvent }
      minDist={ 10 }
      failOffsetX={ [ -10, 10 ] }
    >
      <Animated.View>
        <MainTabHeader handleChangeTab={ onChangeTab } />
      </Animated.View>
    </PanGestureHandler>
  )
}

export default React.memo(<PERSON><PERSON>, () => true)