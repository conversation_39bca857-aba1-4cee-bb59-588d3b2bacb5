import isEqual from 'lodash.isequal'
import React from 'react'
import { connect } from 'react-redux'
import { RootState } from '../../store'
import StaticListFooter from '../common/StaticListFooter'

type Props = {
  hasMore: boolean
  onShow: () => void
}

const MainTabFooter: React.FC<Props> = ({ hasMore, onShow }) => {
  return (
    <StaticListFooter
      onShow={onShow}
      hasMore={hasMore}
      inViewPortGroupName='listFooter'
    />
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    hasMore: state.commodity.hasMore,
  }
}

export default connect(mapStateToProps)(React.memo(MainTabFooter, isEqual))
