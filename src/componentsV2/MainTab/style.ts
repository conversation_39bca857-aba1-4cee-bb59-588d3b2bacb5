import { BetterImage } from '@xmly/rn-components'
import { Animated, View } from 'react-native'
import styled from 'styled-components'
import { ThemeStyle } from '../../typesV2/themeInfo'

export const ThemeBg = styled(View)`
  background: ${({ theme }: { theme: ThemeStyle }) => theme.container.bg_color};
  flex: 1;
`

export const CenterWrapper = styled(View)`
  width: 100%;
  padding: 0 12px;
`

export const HeaderBgWrapper = styled(Animated.View)`
  position: absolute;
  width: 100%;
  height: ${({ theme }: { theme: ThemeStyle }) => theme.container.height}px;
  left: 0;
  z-index: -1;
  top: 0;
`

export const HeaderBg = styled(BetterImage).attrs({
  source: ({ theme }: { theme: ThemeStyle }) => ({
    uri: theme.container.bg_image,
  }),
})`
  width: 100%;
  height: 100%;
`
