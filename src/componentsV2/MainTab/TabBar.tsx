import React, { useCallback, useContext, useRef } from 'react'
import { StyleProp, TextStyle, View } from 'react-native'
import Animated, { concat, cond, eq, Extrapolate, interpolate, Value } from 'react-native-reanimated'
import styled from 'styled-components'
import LinearGradient from 'react-native-linear-gradient'
import { interpolateColor } from 'react-native-redash'
import { width } from 'constantsV2/dimensions'
import { Touch } from '@xmly/rn-components'
import { ScrollAreaContext } from 'contextV2/scrollAreaContext'

type Props = {
  onChangeTab: (tab: number) => void;
  tabScrollValue: Value<number>;
}

const TabBarWrapper = styled(Animated.View)`
  width: 100%;
  height: 51px;
  background-color: #fff;
  flex-direction: row;
  align-items: center;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  justify-content:center;
`


const TabBarItem = styled(Touch)`
  flex: 1;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  height: 100%;
`

const TabBarItemLabel = styled(Animated.Text)`
  font-size: 15px;
  margin-bottom: 8px;
`

const IndicatorWrapper = styled(Animated.View)`
  width: 50%;
  height: 4px;
  bottom: 10px;
  left: 0;
  position: absolute;
  align-items: center;
  justify-content: center;
`

const Indicator = styled(LinearGradient)`
    height: 4px;
    width: 20px;
    border-radius: 1px;
`

const HandlerBar = styled(View)`
  position: absolute;
  top: 4px;
  width: 35px;
  height: 4px;
  background: #bebebe;
  border-radius: 2px;
`


const TabBarItemUI: React.FC<Props & { label: string, index: number }> = React.memo((props) => {
  const { label, index, onChangeTab, tabScrollValue } = props
  const handlePress = useCallback(() => {
    onChangeTab(index)
  }, [ onChangeTab ])
  const style = useRef({
    fontWeight: concat(cond(eq(tabScrollValue, index), 700, 400), ''),
    transform: [ { scale: interpolate(tabScrollValue, { inputRange: [ 0, 1 ], outputRange: index === 0 ? [ 1.1, 1 ] : [ 1, 1.1 ], extrapolate: Extrapolate.CLAMP }) } ],
    color: interpolateColor(tabScrollValue, { inputRange: [ 0, 1 ], outputRange: index === 0 ? [ '#111111', '#333333' ] : [ '#333333', '#111111' ] })
  }).current as unknown as StyleProp<Animated.AnimateStyle<TextStyle>>
  return (
    <TabBarItem onPress={ handlePress }>
      <TabBarItemLabel style={ style }>{ label }</TabBarItemLabel>
    </TabBarItem>
  )
}, () => true)


const IndicatorUI: React.FC<Pick<Props, 'tabScrollValue'>> = React.memo((props) => {
  return (
    <IndicatorWrapper
      style={
        [ {
          transform: [ {
            translateX: interpolate(props.tabScrollValue, { inputRange: [ 0, 1 ], outputRange: [ 0, width / 2 ], extrapolate: Extrapolate.CLAMP })
          } ]
        } ] }
    >
      <Indicator
        start={ { x: 0.35, y: 0 } }
        end={ { x: 1, y: 0 } }
        colors={ [ '#FF4C2E', '#ffa697' ] }
      />
    </IndicatorWrapper >
  )
}, () => true)

const TabBar: React.FC<Props> = (props) => {
  const { shouldShowCredit } = useContext(ScrollAreaContext)
  return (
    <TabBarWrapper pointerEvents='box-none'
      style={ {
        elevation: cond(shouldShowCredit, 8, 0),
        shadowColor: "rgba(0,0,0,0.3)",
        shadowOffset: {
          width: 0,
          height: 5,
        },
        shadowOpacity: shouldShowCredit,
        shadowRadius: 6.27,
        // borderTopRightRadius: cond(shouldShowCredit, 0, 16),
        // borderTopLeftRadius: cond(shouldShowCredit, 0, 16),
      } }
    >
      <HandlerBar />
      <TabBarItemUI { ...props } label={ '花积分' } index={ 0 } />
      <TabBarItemUI { ...props } label={ '赚积分' } index={ 1 } />
      <IndicatorUI tabScrollValue={ props.tabScrollValue } />
    </TabBarWrapper>
  )
}

export default React.memo(TabBar, () => true)