import React from 'react'
import isEqual from 'lodash.isequal'
import { RootState } from '../../store'
import { connect } from 'react-redux'
import { SuccessPopInfo } from '../../typesV2/signInNew'

const SuccessGetPopUI = React.lazy(() => import('./SuccessGetPopUI'))

const SuccessGetPop = ({
  ifShow,
  successPopInfo,
}: {
  ifShow: boolean
  successPopInfo: SuccessPopInfo
}) => {
  return ifShow ? (
    <React.Suspense fallback={null}>
      <SuccessGetPopUI successPopInfo={successPopInfo} />
    </React.Suspense>
  ) : null
}

export default connect(
  ({
    signInInfo: { successPopShow, successPopInfo, ifQueryCheck, signInSuccessModalVisible, hasShowOneModalBefore },
  }: RootState) => {
    const ifShow = successPopShow && ifQueryCheck && !signInSuccessModalVisible && !hasShowOneModalBefore
    return {
      ifShow,
      successPopInfo,
    }
  }
)(React.memo(SuccessGetPop, isEqual))
