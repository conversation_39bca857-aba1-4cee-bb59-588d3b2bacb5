import { BetterImage, Touch, Text } from '@xmly/rn-components'
import React from 'react'
import { View } from 'react-native'
import { store } from '../../store'
import styled from 'styled-components'
import Pop from '../common/Pop'
import { SuccessPopInfo } from '../../typesV2/signInNew'
const pop_coin = {
  uri: 'https://imagev2.xmcdn.com/storages/cdaf-audiofreehighqps/BD/14/GKwRIDoILmzTAACQJgIZK48g.png',
}
const Wrapper = styled(View)`
  width: 310px;
  background: #ffffff;
  border-radius: 10px;
  border: 1px solid transparent;
`
const BgImage = styled(BetterImage).attrs({
  source: pop_coin,
})`
  width: 220px;
  height: 220px;
  position: absolute;
  top: -98px;
  left: 45px;
`
const Title = styled(Text)`
  font-size: 16px;
  color: #111111;
  text-align: center;
  margin-top: 100px;
`
const SubTitle = styled(Text)`
  margin-top: 7px;
  font-family: PingFangSC-Regular;
  font-size: 16px;
  color: #111111;
  text-align: center;
`
const StrongText = styled(Text)`
  font-size: 36px;
  color: #ff5c37;
  text-align: center;
  font-weight: bold;
`
const Btn = styled(Touch)`
  border: 1px solid #f86745;
  border-radius: 30px;
  width: 220px;
  height: 40px;
  margin: 28px auto 35px;
`
const BtnText = styled(Text)`
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #f86a47;
  text-align: center;
  line-height: 40px;
`

type Props = {
  successPopInfo: SuccessPopInfo
}
const SuccessGetPopUI: React.FC<Props> = ({ successPopInfo }) => {
  const handleClose = () => {
    store.dispatch.signInInfo.refresh({ successPopShow: false })
  }
  return (
    <Pop handleClose={handleClose}>
      <Wrapper>
        <BgImage />
        <Title>完成收听{successPopInfo.totalTime || 0}分钟任务</Title>
        <SubTitle>
          获得<StrongText>{successPopInfo.totalWorth || 0}</StrongText>积分
        </SubTitle>
        <Btn onPress={handleClose}>
          <BtnText>我知道了</BtnText>
        </Btn>
      </Wrapper>
    </Pop>
  )
}

export default SuccessGetPopUI
