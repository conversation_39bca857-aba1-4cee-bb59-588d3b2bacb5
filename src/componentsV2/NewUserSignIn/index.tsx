import React, { Suspense, useContext, useEffect } from 'react'
import isEqual from 'lodash.isequal'
import { RootState, store } from '../../store'
import { useSelector } from 'react-redux'
import GrayScaleContext from '../../contextV2/grayScaleContext'

const NewUserSignInLazy = React.lazy(() => import('./NewUserSignIn.lazy'))
type Props = {}

const NewUserSignIn: React.FC<Props> = (props) => {
  const { showGoldCoinModule } = useContext(GrayScaleContext)
  const signInRecords = useSelector((state: RootState) => state.newUserSignIn.signInRecords)

  useEffect(() => {
    if (showGoldCoinModule) {
      store.dispatch.newUserSignIn.getSignInInfo()
    }
  }, [showGoldCoinModule])

  if (signInRecords.length && showGoldCoinModule) {
    return (
      <Suspense fallback={null}>
        <NewUserSignInLazy />
      </Suspense>
    )
  }
  return null
}

export default React.memo(NewUserSignIn, isEqual)
