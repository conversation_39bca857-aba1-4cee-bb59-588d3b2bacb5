import React, { useState } from 'react'
import { Text } from '@xmly/rn-components'
import { LayoutChangeEvent, View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { ThemeStyle } from '../../typesV2/themeInfo'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import CheckInBarItemUI from '../EarnCredit/CheckInBar/CheckInBarItemUI'
import { AwardStatus, SignInUserAwardType } from '../../typesV2/signInNew'
import { Toast } from '@xmly/rn-sdk'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import xmlog from '../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'

type Props = {}

const Wrapper = styled(View)`
  background: ${({ theme }: { theme?: ThemeStyle }) => theme?.credit.bg_color};
  border-radius: 8px;
  margin-bottom: 12px;
`

const HeaderWrapper = styled(View)`
  padding-left: 15px;
  flex-direction: row;
  margin-top: 16px;
  align-items: flex-end;
`

const Title = styled(Text)`
  font-size: 17px;
  font-weight: bold;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.common.title_color};
`
const SubTitle = styled(Text)`
  font-size: 17px;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.common.title_color};
  opacity: 0.4;
  font-size: 11px;
  margin-left: 8px;
  margin-bottom: 1px;
`

const Body = styled(View)`
  padding: 0 12px;
  margin-top: 20px;
  margin-bottom: 16px;
  flex-direction: row;
`
const eventReportProps = (item: SignInUserAwardType) => {
  let status = '未获得'
  if (item.awardStatus === AwardStatus.isDispatched) {
    status = '已领取'
  }

  if (item.awardStatus === AwardStatus.isEnabled) {
    status = '待领取'
  }

  return {
    status,
    id: safetyToString(item.productId),
    type: safetyToString(item.name),
  }
}

const NewUserSignInLazy: React.FC<Props> = (props) => {
  const [containerWidth, setContainerWidth] = useState(0)
  const { signInRecords, todaySignInDay, todaySignInStatus, title, desc } = useSelector((state: RootState) => state.newUserSignIn)

  const handleLayout = (event: LayoutChangeEvent) => {
    setContainerWidth(event.nativeEvent.layout.width)
  }

  const handleItemPress = async (item: SignInUserAwardType) => {
    // 任务中心-奖励模块  点击事件
    xmlog.click(59068, undefined, { currPage: '任务中心', ...eventReportProps(item) })
    switch (item.awardStatus) {
      case AwardStatus.isDispatched:
        Toast.info('已经领过啦')
        break
      case AwardStatus.initial:
        Toast.info('未获得奖励，请保持签到')
        break
      case AwardStatus.isEnabled:
        await store.dispatch.newUserSignIn.getAward({ day: item.day, awardCheckInInfo: item.context })
        break
    }
  }

  const judgeItemIsChecked = (item: SignInUserAwardType) => {
    if (item.awardStatus === AwardStatus.isDispatched) {
      return true
    } else {
      return false
    }
  }

  const handleItemShow = (signInRecord: SignInUserAwardType) => {
    // 任务中心-奖励模块  控件曝光
    xmlog.event(59069, 'slipPage', { currPage: '任务中心', ...eventReportProps(signInRecord) })
  }

  return (
    <Wrapper onLayout={handleLayout}>
      {containerWidth > 0 ? (
        <>
          <HeaderWrapper>
            <Title>{title}</Title>
            <SubTitle>{desc}</SubTitle>
          </HeaderWrapper>
          <Body>
            {signInRecords.map((signInRecord) => {
              return (
                <ScrollAnalyticComp key={signInRecord.day} itemKey={signInRecord.day} onShow={() => handleItemShow(signInRecord)}>
                  <CheckInBarItemUI
                    withoutTodayLabel
                    noTomorrowTip
                    showUnSignLogo
                    onPress={handleItemPress}
                    item={signInRecord}
                    containerWidth={containerWidth}
                    todaySignInDay={todaySignInDay}
                    todaySignInStatus={todaySignInStatus}
                    judgeIsChecked={judgeItemIsChecked}
                    showCanReceiveLabel={signInRecord.awardStatus === AwardStatus.isEnabled}
                  />
                </ScrollAnalyticComp>
              )
            })}
          </Body>
        </>
      ) : null}
    </Wrapper>
  )
}

export default React.memo(NewUserSignInLazy, isEqual)
