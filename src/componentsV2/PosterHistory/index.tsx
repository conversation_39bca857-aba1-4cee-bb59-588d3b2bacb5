import React, {
  useCallback,
  useEffect,
  useState,
  useRef,
  useContext,
} from 'react'
import { View, StyleSheet, FlatList } from 'react-native'
import PosterHistoryHeader from './PosterHistoryHeader'
import { useNavigation } from '@react-navigation/native'
import PosterItem from './PosterItem'
import { PosterItemType } from '../../typesV2/posterListType'
import getPosterList from '../../servicesV2/getPosterList'
import { RootState, store } from '../../store'
import { connect } from 'react-redux'
import { FromType } from '../../modelsV2/signInInfo'
import xmlog from '../../utilsV2/xmlog'
import PageAnalytics, {
  ScrollAnalyticWapper,
  ScrollEventSender,
} from '@xmly/react-native-page-analytics'
import { Text, Touch } from '@xmly/rn-components'
import { safetyToString } from '@xmly/rn-utils'
import SentryUtils from '../../utilsV2/sentryUtils'
import posterHistoryCache from '../../utilsV2/posterHistoryCache'
import { ThemeContext } from '../../contextV2/themeContext'
import ErrorView from '../common/ErrorView'

type Props = {
  signInDate: boolean
}

const styles = StyleSheet.create({
  listWrapper: {
    paddingHorizontal: 30,
    paddingTop: 10,
  },
})

const defaultPageSize = 15

const customKeyExtractor = (item: PosterItemType) => item.posterId.toString()

const posterHistoryListId = 'posterHistoryList'

const cacheKey = 'posterHistoryCacheKey'

const PosterHistory: React.FC<Props> = ({ signInDate, ...rest }) => {
  const [posterListHistory, setPosterListHistory] = useState<PosterItemType[]>(
    []
  )
  const [loading, setLoading] = useState(true)
  const [noMore, setNoMore] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [loadMoreError, setLoadMoreError] = useState(false)
  const [errorMsgCode, setErrorMsgCode] = useState('未知')
  const errorMsg = `异常错误代码：${errorMsgCode}，请重试`
  const pagination = useRef({
    page: 1,
    total: 0,
  })
  const navigation = useNavigation()
  const theme = useContext(ThemeContext)

  const customPageView = useCallback(() => {
    xmlog.pageView(38581, 'signPosterHistory')
  }, [])

  const customPageExit = useCallback(() => {
    xmlog.pageExit(38582)
  }, [])

  PageAnalytics.useScreen({
    customPageView,
    customPageExit,
    ...rest,
  })

  const restoreFromCache = () => {
    const cachedData = posterHistoryCache.get(cacheKey)
    if (cachedData) {
      setPosterListHistory(cachedData)
      setLoading(false)
    }
  }

  const fetchList = async ({ page }: { page: number }) => {
    try {
      setLoading(true)
      setHasError(false)
      setLoadMoreError(false)

      if (page === 1) {
        restoreFromCache() // 如果是第一页，判断缓存是否存在。存在则先从缓存中恢复
      }
      const res = await getPosterList({
        page: page,
        pageSize: defaultPageSize,
        dateType: 2,
      })
      console.log({ res })
      console.log({ res: res.data.posterList })
      if (page === 1) {
        setPosterListHistory(res.data.posterList)
        posterHistoryCache.update(cacheKey)(() => res.data.posterList)
      } else {
        setPosterListHistory([...posterListHistory, ...res.data.posterList])
      }
      pagination.current.page = res.data.pageId
      pagination.current.total = res.data.totalCount
    } catch (err) {
      posterHistoryCache.remove(cacheKey)
      setErrorMsgCode(safetyToString(err))
      if (page === 1) {
        setHasError(true)
      } else {
        setLoadMoreError(true)
      }
      SentryUtils.captureException(err, {
        source: 'PosterHistory.fetchList',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchList({ page: 1 })
    return () => {
      store.dispatch.signInInfo.refresh({ signInDate: true })
    }
  }, [])

  const handleHeaderLeftPress = useCallback(() => {
    navigation.goBack()
  }, [])

  const handleHeaderRightPress = useCallback(() => {
    store.dispatch.signInInfo.refresh({ signInDate: !signInDate })
    xmlog.click(38587, undefined, {
      status: signInDate ? '隐藏日期' : '显示日期',
      currPage: 'signPosterHistory',
    })
  }, [signInDate])

  const handleItemPressShare = (item: PosterItemType) => {
    store.dispatch.signInInfo.refresh({
      signInPosterWithShare: true,
      currentPoster: [item],
      shareFromType: FromType.history,
    })
  }

  const handleEndReached = async () => {
    if (pagination.current.total > posterListHistory.length) {
      await fetchList({
        page: pagination.current.page + 1,
      })
    } else {
      setNoMore(true)
    }
  }

  const renderItem = ({
    item,
    index,
  }: {
    item: PosterItemType
    index: number
  }) => {
    return (
      <PosterItem
        dateVisible={signInDate}
        item={item}
        index={index}
        onPressShare={handleItemPressShare}
      />
    )
  }

  const handleScroll = () => {
    // 滚动时发送事件通知，会触发新出现的组件的曝光事件
    ScrollEventSender.send(posterHistoryListId, 'scroll')
  }

  const handleReload = () => {
    fetchList({ page: 1 })
  }
  const handleReLoadMore = () => {
    fetchList({ page: pagination.current.page + 1 })
  }

  const renderListFooter = () => {
    let content = '加载中...'

    if (loading) {
      content = '加载中...'
    }

    if (loadMoreError) {
      content = '加载失败，请点击重试～'
    }

    if (noMore) {
      content = '我们到底了'
    }

    return (
      <Touch
        style={{
          width: '100%',
          height: 80,
          justifyContent: 'center',
          alignItems: 'center',
        }}
        disabled={!loadMoreError}
        onPress={handleReLoadMore}
      >
        {<Text style={{ color: '#999', fontSize: 12 }}>{content}</Text>}
      </Touch>
    )
  }

  return (
    <View style={{ flex: 1, backgroundColor: theme.posterHistory.bg_color }}>
      <PosterHistoryHeader
        onLeftPress={handleHeaderLeftPress}
        onRightPress={handleHeaderRightPress}
        dateVisible={signInDate}
        label='日签'
      />
      <View style={{ flex: 1 }}>
        {!hasError ? (
          <ScrollAnalyticWapper id={posterHistoryListId}>
            <FlatList
              onScroll={handleScroll}
              style={styles.listWrapper}
              data={posterListHistory}
              showsVerticalScrollIndicator={false}
              keyExtractor={customKeyExtractor}
              renderItem={renderItem}
              onEndReached={handleEndReached}
              ListFooterComponent={renderListFooter}
            />
          </ScrollAnalyticWapper>
        ) : (
          <ErrorView
            buttonLabel='重试'
            errorMsg={errorMsg}
            onReload={handleReload}
            withoutHeader
          />
        )}
      </View>
    </View>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    signInDate: state.signInInfo.signInDate,
  }
}

export default connect(mapStateToProps)(PosterHistory)
