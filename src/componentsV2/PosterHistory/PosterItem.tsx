import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { Touch } from '@xmly/rn-components'
import { safetyToString } from '@xmly/rn-utils'
import isEqual from 'lodash.isequal'
import React, { useRef } from 'react'
import { View, StyleSheet, Image } from 'react-native'
import ic_share from '../../appImagesV2/ic_share'
import {
  CardType,
  historyCardWidth,
} from '../../typesV2/postCard'
import {
  PosterItemResourceType,
  PosterItemType,
} from '../../typesV2/posterListType'
import handlePosterPress from '../../utilsV2/handlePosterPress'
import xmlog from '../../utilsV2/xmlog'
import PosterCard from '../DailySignPoster/PosterCard'

type Props = {
  onPressShare: (item: PosterItemType) => void
  item: PosterItemType
  dateVisible: boolean
  index: number
}

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    marginBottom: 20,
    borderRadius: 8,
    overflow: 'hidden',
    alignItems: 'center',
    justifyContent: 'center',
  },
  shareBtnWrapper: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderColor: '#fff',
    borderWidth: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    top: 12,
    right: 12,
  },
  shareBtnIcon: {
    width: 22.86,
    height: 22.86,
  },
  cover: { width: '100%', height: '100%' },
})

const PosterItem: React.FC<Props> = ({ onPressShare, item, index }) => {
  const itemEventReportData = useRef({
    position: safetyToString(index + 1),
    posterId: safetyToString(item.posterId),
    albumId:
      item.resourceType === PosterItemResourceType.album
        ? safetyToString(item.resourceId)
        : '-1',
    trackId:
      item.resourceType === PosterItemResourceType.track
        ? safetyToString(item.resourceId)
        : '-1',
    link: safetyToString(item.jumpUrl),
  })

  const handleItemSharePress = () => {
    onPressShare(item)
    xmlog.click(38585, undefined, itemEventReportData.current)
  }

  const handleItemPress = () => {
    handlePosterPress(item)
    xmlog.click(38583, undefined, itemEventReportData.current)
  }

  const onShowHandler = (exposeType: number) => {
    console.log(`onShow  type: ${exposeType}`)
    xmlog.event(38584, 'slipPage', itemEventReportData.current)
  }

  return (
    <View style={styles.wrapper}>
      <ScrollAnalyticComp
        // 每个元素设置一个唯一的key值
        itemKey={(item.posterId + index).toString()}
        // 曝光事件处理
        onShow={onShowHandler}
      >
        <PosterCard
          cardType={CardType.historyCard}
          posterInfo={item}
          onPress={handleItemPress}
          fontScale={historyCardWidth / 275}
        />
        <Touch
          style={styles.shareBtnWrapper}
          onPress={handleItemSharePress}
          hitSlop={{ top: 20, left: 20, right: 20, bottom: 20 }}
        >
          <Image
            style={styles.shareBtnIcon}
            source={ic_share}
            resizeMode='cover'
          />
        </Touch>
      </ScrollAnalyticComp>
    </View>
  )
}

export default React.memo(PosterItem, isEqual)
