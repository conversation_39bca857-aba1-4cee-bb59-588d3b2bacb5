import { Text, Touch } from '@xmly/rn-components'
import { hitSlop } from '@xmly/rn-utils'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import React, { useContext } from 'react'
import { View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { connect } from 'react-redux'
import { RootState } from 'store'
import styled from 'styled-components'
import BackBtn from '../common/BackBtn'
import isEqual from 'lodash.isequal'
import { ThemeStyle } from '../../typesV2/themeInfo'
import { ThemeContext } from '../../contextV2/themeContext'

type Props = {
  label: string
  onLeftPress: () => void
  onRightPress: () => void
  dateVisible: boolean
}

const HeaderWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  overflow: hidden;
  z-index: 1;
`

const HeaderContentWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`

const Label = styled(Text)`
  text-align: center;
  font-size: 18px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  position: absolute;
  left: 0;
  right: 0;
  align-items: center;
  justify-content: center;
`

const LabelWrapper = styled(View)`
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  align-items: center;
  justify-content: center;
`
const RightBtn = styled(Touch)`
  border-radius: 14px;
  border-width: 1px;
  border-style: solid;
  padding: 4px 12px;
  align-items: center;
  justify-content: center;
  height: 24px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
`

const RightBtnLabel = styled(Text)`
  font-size: 11px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
`

const PosterHistoryHeader: React.FC<Props> = (props) => {
  console.log('PosterHistoryHeader render')
  const safeArea = useSafeAreaInsets()
  const { label, onLeftPress, onRightPress, dateVisible } = props

  const paddingTop = isAndroid ? safeArea.top + 10 : safeArea.top
  const theme = useContext(ThemeContext)

  return (
    <>
      <HeaderWrapper
        style={{
          paddingTop,
          height: isAndroid ? undefined : 44 + paddingTop,
          paddingBottom: isAndroid ? 10 : 0,
        }}
      >
        <HeaderContentWrapper>
          <LabelWrapper>
            <Label>{label}</Label>
          </LabelWrapper>
          <BackBtn onPress={onLeftPress} />
          <RightBtn onPress={onRightPress} hitSlop={hitSlop(30)} style={{borderColor:theme.posterHistory.border_color}}>
            <RightBtnLabel>
              {dateVisible ? '隐藏日期' : '显示日期'}
            </RightBtnLabel>
          </RightBtn>
        </HeaderContentWrapper>
      </HeaderWrapper>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {}
}

export default connect(mapStateToProps)(
  React.memo(PosterHistoryHeader, isEqual)
)
