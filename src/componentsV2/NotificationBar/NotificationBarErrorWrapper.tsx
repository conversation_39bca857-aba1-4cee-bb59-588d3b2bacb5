import React, { PureComponent } from 'react'

interface Props {}

interface State {
  hasError: boolean
  errorMsg: any
}

export default class NotificationBarErrorWrapper extends PureComponent<
  Props,
  State
> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, errorMsg: '' }
  }

  componentDidCatch(error: any, info: any) {
    try {
      this.setState({ hasError: true, errorMsg: '' })
    } catch (err) {
      this.setState({ hasError: true, errorMsg: '' })
    }
  }

  render() {
    if (this.state.hasError) {
      return null
    }
    return this.props.children
  }
}
