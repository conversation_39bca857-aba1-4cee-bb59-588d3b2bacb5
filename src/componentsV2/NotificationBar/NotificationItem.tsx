import React, { useContext } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { StyleSheet } from 'react-native'
import isEqual from 'lodash.isequal'
import { TopSectionNotification } from '../../servicesV2/getTopSectionInfo'
import { ThemeContext } from '../../contextV2/themeContext'

type Props = {
  notification: TopSectionNotification
  onPress: (notification: TopSectionNotification) => void
}

const styles = StyleSheet.create({
  wrapper: { height: '100%', marginRight: 10, width: '100%' },
  title: { fontSize: 12, fontWeight: 'bold' },
})

const NotificationItem: React.FC<Props> = ({ notification, onPress }) => {
  const theme = useContext(ThemeContext)
  const notificationCanPress = false // hasNotification && !!data.btnText

  const handlePress = () => {
    onPress(notification)
  }

  return (
    <Touch style={styles.wrapper} onPress={handlePress}>
      <Text
        style={[
          styles.title,
          {
            color: notificationCanPress
              ? theme.notificationBar.color
              : theme.notificationBar.normalColor,
          },
        ]}
      >
        {notification.text}
      </Text>
    </Touch>
  )
}

export default React.memo(NotificationItem, isEqual)
