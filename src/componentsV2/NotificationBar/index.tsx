import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { Page } from '@xmly/rn-sdk'
import { safetyToString } from '@xmly/rn-utils'
import isEqual from 'lodash.isequal'
import React, { useContext, useEffect } from 'react'
import { Image, InteractionManager, View } from 'react-native'
import Animated, {
  cond,
  Easing,
  eq,
  set,
  useCode,
  useValue,
} from 'react-native-reanimated'
import { connect } from 'react-redux'
import { TopSectionNotification } from 'servicesV2/getTopSectionInfo'
import { RootState } from 'store'
import styled from 'styled-components'
import getUrlToOpen from 'utilsV2/getUrlToOpen'
import goScoreMarket2 from 'utilsV2/goScoreMarket2'
import xmlog from 'utilsV2/xmlog'
import icon_notification from '../../appImagesV2/icon_notification'
import { UserInfoContext } from '../../contextV2/userInfoContext'
import { ThemeStyle } from '../../typesV2/themeInfo'
import UserLoginStatus from '../../typesV2/userLoginStatus'
import { runTiming } from '../../utilsV2/animations/runTiming'
import goToLogin from '../../utilsV2/goToLogin'
import { Marquee } from '../common/Marquee'
import NotificationItem from './NotificationItem'
import NotificationBarErrorWrapper from './NotificationBarErrorWrapper'

type Props = {
  notification: TopSectionNotification[]
}

const Wrapper = styled(View)`
  width: 100%;
  padding: 0 10px;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  background: ${({ theme }: { theme: ThemeStyle }) =>
    theme.notificationBar.bg_color};
  flex-direction: row;
  align-items: center;
  border-radius: 8px;
`

// const ActionButton = styled(View)`
//   width: 60px;
//   height: 24px;
//   border-radius: 12px;
//   border: 1px solid rgba(255, 70, 70, 0.49);
//   align-items: center;
//   justify-content: center;
// `

// const ActionButtonLabel = styled(Text)`
//   font-size: 12px;
//   font-weight: bold;
//   color: #ff4646;
// `

const Icon = styled(Image)`
  width: 16px;
  height: 16px;
  margin-right: 8px;
`

const NotificationBarKey = 'NotificationBarKey'

const NotificationBar: React.FC<Props> = (props) => {
  const { notification = [] } = props
  const { loginStatus } = useContext(UserInfoContext)
  const state = useValue<0 | 1 | -1>(-1)
  const animatedValue = useValue<0 | 1>(0)
  let hasNotification = false

  const filterNotifications =
    notification.length > 0
      ? notification.filter((data) => {
          const date = Date.now()
          const { startTime, endTime } = data
          if (date < startTime && date > endTime) {
            return false
          } else {
            return true
          }
        })
      : []

  if (filterNotifications.length === 0) {
    hasNotification = false
  } else {
    hasNotification = true
  }

  const notificationCanPress = false // hasNotification && !!data.btnText

  useCode(() => {
    if (hasNotification) {
      return [
        cond(eq(state, 1), [
          set(
            animatedValue,
            runTiming({
              duration: 160,
              from: 0,
              to: 34,
              easing: Easing.out(Easing.ease),
            })
          ),
        ]),
      ]
    } else {
      return []
    }
  }, [hasNotification])

  const onshowHandler = () => {
    if (hasNotification) {
      // 任务中心-公告栏  控件曝光
      xmlog.event(52179, 'slipPage', {
        Text: notification[0].text,
        currPage: '任务中心',
      })
    }
  }

  useEffect(() => {
    let runAfterInteractions: {
      then: (
        onfulfilled?: (() => any) | undefined,
        onrejected?: (() => any) | undefined
      ) => Promise<any>
      done: (...args: any[]) => any
      cancel: () => void
    }
    let timer: NodeJS.Timeout
    if (hasNotification) {
      runAfterInteractions = InteractionManager.runAfterInteractions(() => {
        timer = setTimeout(() => {
          state.setValue(1)
        }, 200)
      })
    }
    return () => {
      state.setValue(0)
      runAfterInteractions?.cancel()
      timer && clearTimeout(timer)
    }
  }, [hasNotification])

  if (!hasNotification) return null

  const handlePress = (notificationItem: TopSectionNotification) => {
    const { jumpUrl = '', type, text } = notificationItem
    if (loginStatus === UserLoginStatus.login) {
      if (Number(type) == 1) {
        Page.start(getUrlToOpen(jumpUrl))
      } else {
        goScoreMarket2(jumpUrl, true)
      }
    } else {
      goToLogin()
    }
    xmlog.click(30537, undefined, {
      Text: safetyToString(text),
      currPage: '任务中心',
    })
  }

  return (
    <NotificationBarErrorWrapper>
      <Animated.View
        style={{
          overflow: 'hidden',
          height: animatedValue,
        }}
      >
        <ScrollAnalyticComp itemKey={NotificationBarKey} onShow={onshowHandler}>
          <Wrapper>
            {!notificationCanPress && (
              <Icon source={icon_notification} fadeDuration={0} />
            )}
            <Marquee containerStyle={{ flex: 1 }} enable>
              <View style={{ flexDirection: 'row', marginRight: 6 }}>
                {filterNotifications.map((data) => (
                  <NotificationItem
                    key={data.text}
                    onPress={handlePress}
                    notification={data}
                  />
                ))}
              </View>
            </Marquee>
            {/* {notificationCanPress && (
            <ActionButton>
              <ActionButtonLabel>{data.btnText}</ActionButtonLabel>
            </ActionButton>
          )} */}
          </Wrapper>
        </ScrollAnalyticComp>
      </Animated.View>
    </NotificationBarErrorWrapper>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    notification: state.topSection.global.notification,
  }
}

export default connect(mapStateToProps)(React.memo(NotificationBar, isEqual))
