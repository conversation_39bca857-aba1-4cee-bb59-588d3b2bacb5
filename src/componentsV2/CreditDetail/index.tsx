import React, {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import { useNavigation } from '@react-navigation/native'
import { NativeModules, Platform, View } from 'react-native'
import WebView from 'react-native-webview'
import styled from 'styled-components'
import CreditDetailHeader from './CreditDetailHeader'
import UserLoginStatus from '../../typesV2/userLoginStatus'
import { UserInfoContext } from '../../contextV2/userInfoContext'
import { Page } from '@xmly/rn-sdk'
import rnEnv from '../../../rnEnv'
import { aiServiceUrl, newCreditDetailUrl } from '../../constantsV2'
import goToLogin from '../../utilsV2/goToLogin'
import getUrlToOpen from 'utilsV2/getUrlToOpen'
import xmlog from '../../utilsV2/xmlog'
import { BetterImage } from '@xmly/rn-components'
import { ThemeStyle } from '../../typesV2/themeInfo'
import CreditNumber from './CreditNumber'
import LinearGradient from 'react-native-linear-gradient'
import { ThemeContext } from '../../contextV2/themeContext'
import { encode } from 'base-64'
import goScoreMarket2 from '../../utilsV2/goScoreMarket2'
import { usePageOnResume } from '@xmly/rn-utils'

const Wrapper = styled(View)`
  width: 100%;
  height: 100%;
`
const WrapperBg = styled(LinearGradient)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 164px;
`
const WrapperImage = styled(BetterImage).attrs({
  source: ({ theme }: { theme: ThemeStyle }) => theme.icon.bg_credit_detail,
})`
  width: 100%;
  height: 164px;
`

const ContentWrapper = styled(View)`
  width: 100%;
  height: 100%;
  flex: 1;
  margin-top: -8px;
  position: relative;
  background: #fff;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  overflow: hidden;
`
const Content = styled(View)`
  width: 100%;
  height: 100%;
  background: #fff;
  position: absolute;
  top: 0;
  left: 0;
`
const CreditView = styled(WebView)`
  width: 100%;
`
interface EventPops {
  key: string
  id: number
  logType: 'click' | 'slipPage'
  value: any
  type: 'log' | 'event_history' | 'event_detail'
  val: string
  tabIndex?: 0
}
const CreditDetail = () => {
  const navigation = useNavigation()
  const { loginStatus } = useContext(UserInfoContext)
  const theme = useContext(ThemeContext)
  const [userCookie, setUserCookie] = useState('')
  const [webviewVisible, setWebviewVisible] = useState(true)
  usePageOnResume(() => {
    if (Platform.OS == 'ios') {
      NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
    }
  })

  const handleBlur = () => {
    setWebviewVisible(false)
  }
  useEffect(() => {
    navigation.addListener('blur', handleBlur)

    return () => {
      navigation.removeListener('blur', handleBlur)
    }
  }, [])

  const getUserLoginStatus = async () => {
    try {
      const res = await NativeModules.Account.getUserInfo()
      if (res?.uid && res?.token) {
        setUserCookie(`${res.uid}&${res.token}`)
      }
    } catch (err) {}
  }

  useEffect(() => {
    getUserLoginStatus()
  }, [])
  const creditDetailSource = {
    uri: newCreditDetailUrl(rnEnv.isTest(), encode(userCookie)),
  }

  const handleHeaderLeftPress = useCallback(() => {
    navigation.goBack()
  }, [])

  useEffect(() => {
    xmlog.pageView(44982, 'point_exchange', { currPage: 'point_exchange' })

    return () => {
      xmlog.pageExit(44983, { currPage: 'point_exchange' })
    }
  }, [])

  // 产品新增智能客服入口
  const handleToService = () => {
    if (loginStatus === UserLoginStatus.login) {
      Page.start(getUrlToOpen(aiServiceUrl))
    } else {
      goToLogin()
    }
  }

  const handlePage = (e: any) => {
    const data = e?.nativeEvent?.data
    const {
      id = 0,
      value = '',
      val: url = '',
      type,
      logType = 'click',
    }: EventPops = JSON.parse(data)

    if (value) {
      const logArgs = value
      if (logType === 'click') {
        xmlog.click(id, undefined, {
          ...logArgs,
        })
      } else if (logType === 'slipPage') {
        xmlog.event(id, 'slipPage', {
          ...logArgs,
        })
      }
    }
    if (type === 'event_detail') {
      return Page.start(getUrlToOpen(url))
    }
    if (type === 'event_history') {
      return goScoreMarket2(url)
    }
  }
  return (
    <Wrapper>
      <WrapperBg
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        colors={[
          theme.creditDetail.bg_start_color,
          theme.creditDetail.bg_end_color,
        ]}
      >
        <WrapperImage />
      </WrapperBg>
      <CreditDetailHeader
        onLeftPress={handleHeaderLeftPress}
        onRightPress={handleToService}
        label='收支兑换明细'
      />
      <CreditNumber  />
      <ContentWrapper>
        <Content>
          {webviewVisible && userCookie ? (
            <CreditView
              androidLayerType='hardware'
              source={creditDetailSource}
              mixedContentMode='compatibility'
              onMessage={(event) => {
                handlePage(event)
              }}
            />
          ) : null}
        </Content>
      </ContentWrapper>
    </Wrapper>
  )
}

export default CreditDetail
