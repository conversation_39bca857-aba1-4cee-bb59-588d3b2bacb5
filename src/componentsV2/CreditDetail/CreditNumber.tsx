import React, { useContext } from 'react'
import { View } from 'react-native'
import styled from 'styled-components'
import { connect } from 'react-redux'
import { RootState } from '../../store'
import isEqual from 'lodash.isequal'
import { BetterImage } from '@xmly/rn-components'
import icon_money from '../../appImagesV2/icon_money'
import AnimatedNumbers from '../../libV2/react-native-animated-numbers'
import { ThemeContext } from '../../contextV2/themeContext'

const Wrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  margin-top: 16px;
  padding-left: 16px;
  height: 64px;
`
const Icon = styled(BetterImage).attrs({
  source: icon_money,
})`
  width: 28px;
  height: 28px;
`

type Props = {
  creditPoint: number
}

const CreditNumber: React.FC<Props> = (props) => {
  const { creditPoint } = props
  const theme = useContext(ThemeContext)

  console.log('credit render~~~!!!!!!')
  return (
    <Wrapper>
      <Icon />
      <AnimatedNumbers
        onReady={() => {}}
        animateToNumber={creditPoint}
        fontStyle={{
          fontSize: 22,
          fontWeight: 'bold',
          color: theme.credit.number_color,
        }}
      />
    </Wrapper>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    creditPoint: state.credit.creditPoint,
  }
}

export default connect(mapStateToProps)(
  React.memo(CreditNumber, (pProps, nProps) => {
    return isEqual(nProps.creditPoint, pProps.creditPoint)
  })
)
