import { Text, Touch } from '@xmly/rn-components'
import { hitSlop } from '@xmly/rn-utils'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import React, { useContext } from 'react'
import { Image, View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { connect } from 'react-redux'
import { RootState } from 'store'
import styled from 'styled-components'
import BackBtn from '../common/BackBtn'
import isEqual from 'lodash.isequal'
import { ThemeStyle } from '../../typesV2/themeInfo'
import icon_service_light from 'appImagesV2/icon_service_light'
import icon_service_dark from 'appImagesV2/icon_service_dark'
import { DarkModeContext } from 'contextV2/darkModeContext'

type Props = {
  label: string
  onLeftPress: () => void
  onRightPress: () => void
}

const HeaderWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  overflow: hidden;
  z-index: 1;
`

const HeaderContentWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`

const Label = styled(Text)`
  text-align: center;
  font-size: 18px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.header.color};
  position: absolute;
  left: 0;
  right: 0;
  align-items: center;
  justify-content: center;
`

const LabelWrapper = styled(View)`
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  align-items: center;
  justify-content: center;
`
const RightBtn = styled(Touch)`
  padding: 4px 0px;
  align-items: center;
  justify-content: center;
  height: 24px;
`
const ServiceIcon = styled(Image).attrs({
  source: icon_service_light,
  })`
  width: 22px;
  height: 22px;
`
const ServiceIconDark = styled(Image).attrs({
  source: icon_service_dark,
  })`
  width: 22px;
  height: 22px;
`

const CreditDetailHeader: React.FC<Props> = (props) => {
  const safeArea = useSafeAreaInsets()
  const { isDarkMode } = useContext(DarkModeContext)
  const { label, onLeftPress, onRightPress } = props

  const paddingTop = isAndroid ? safeArea.top + 10 : safeArea.top

  return (
    <>
      <HeaderWrapper
        style={{
          paddingTop,
          height: isAndroid ? undefined : 44 + paddingTop,
          paddingBottom: isAndroid ? 10 : 0,
        }}
      >
        <HeaderContentWrapper>
          <LabelWrapper>
            <Label>{label}</Label>
          </LabelWrapper>
          <BackBtn onPress={onLeftPress} />
          <RightBtn onPress={onRightPress} hitSlop={hitSlop(30)}>
          {isDarkMode ? <ServiceIconDark />:<ServiceIcon />}
          </RightBtn>
        </HeaderContentWrapper>
      </HeaderWrapper>
    </>
  )
}

const mapStateToProps = (state: RootState) => {
  return {}
}

export default connect(mapStateToProps)(React.memo(CreditDetailHeader, isEqual))
