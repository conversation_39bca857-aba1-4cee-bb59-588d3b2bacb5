import { Text, Touch } from '@xmly/rn-components'
import React, { useState } from 'react'
import { View } from 'react-native'
import LinearGradient from 'react-native-linear-gradient'
import styled from 'styled-components'
import xmlog from '../../utilsV2/xmlog'

const Wrapper = styled(View)`
  width: 100%;
  height: 46px;
  padding: 0 94px;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  background: #fff;
  border-bottom-width: 1px;
  border-bottom-color: #eee;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
`
const ItemWrapper = styled(Touch)`
  width: 100px;
  height: 100%;
  justify-content: center;
  align-items: center;
`
const ItemText = styled(Text)`
  font-size: 15px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #111111;
  line-height: 21px;
`

const TabActive = styled(LinearGradient)`
  position: absolute;
  bottom: -1px;
  width: 24px;
  height: 5px;
  border-radius: 1px;
`

const tabList = ['积分收支', '兑换记录']
interface Props {
  selected: number
  setSelected: (params: number) => void
}
const CreditDetailTab: React.FC<Props> = ({ selected, setSelected }) => {
  const onPress = (index: number) => {
    setSelected(index)
    xmlog.click(44986, undefined, {
      Item: index === 0 ? '积分收支' : '兑换记录',
      currPage: 'point_exchange',
    })
  }
  return (
    <Wrapper>
      {tabList.map((tab, index) => (
        <ItemWrapper onPress={() => onPress(index)}>
          <ItemText>{tab}</ItemText>
          {selected === index && (
            <TabActive
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              colors={['#FF4C2E', '#FFA697']}
            ></TabActive>
          )}
        </ItemWrapper>
      ))}
    </Wrapper>
  )
}

export default CreditDetailTab
