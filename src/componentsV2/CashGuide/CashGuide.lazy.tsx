import React, { useEffect, useRef, useState } from 'react'
import { View, LayoutChangeEvent } from 'react-native'
import isEqual from 'lodash.isequal'
import { EverydayChallengeGuideStep } from '../../modelsV2/everydayChallenge'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import Svg, { Path } from '@xmly/react-native-svg'
import rect2path from '../../utilsV2/rect2path'
import GuideWord from './GuideWord'
import GuideWordArrow from './GuideWordArrow'
import { Page } from '@xmly/rn-sdk'

type Props = {}

const stepConfig = {
  [EverydayChallengeGuideStep.A]: {
    next: EverydayChallengeGuideStep.B,
    done: false,
  },
  [EverydayChallengeGuideStep.B]: {
    next: null,
    done: true,
  },
}

const CashGuideLazy: React.FC<Props> = (props) => {
  const { layoutInfo } = useSelector((state: RootState) => state.everydayChallenge)
  const [basePath, setBasePath] = useState('')
  const [clipPath, setClipPath] = useState('')
  const [currentStep, setCurrentStep] = useState(EverydayChallengeGuideStep.A)
  const fullPath = basePath + clipPath
  const currentLayoutInfo = layoutInfo[currentStep]
  const skipTimer = useRef<null | NodeJS.Timeout>(null)
  const confirmTimer = useRef<null | NodeJS.Timeout>(null)
  console.log({ currentStep })
  useEffect(() => {
    return () => {
      confirmTimer.current && clearTimeout(confirmTimer.current)
      skipTimer.current && clearTimeout(skipTimer.current)
    }
  }, [])

  useEffect(() => {
    if (currentLayoutInfo) {
      const { x, y, width, height, radius } = currentLayoutInfo
      const path = rect2path(x, y, width, height, radius, radius)
      setClipPath(path)
    }
  }, [layoutInfo, currentLayoutInfo])

  const handleLayout = (layoutEvent: LayoutChangeEvent) => {
    const { height, width } = layoutEvent.nativeEvent.layout
    setBasePath(`M 0 0 L ${width} 0 L ${width} ${height} L 0 ${height}Z`)
  }

  const handlePress = () => {
    toNextStep(currentStep)
  }

  const toNextStep = (step: EverydayChallengeGuideStep) => {
    const nextStepInfo = stepConfig[step]
    if (nextStepInfo.done) {
      handlePressLastStep()
      return null
    } else {
      setCurrentStep(nextStepInfo.next!)
      return nextStepInfo.next!
    }
  }

  const handlePressLastStep = () => {
    Page.start('iting://open?msg_type=94&bundle=rn_gold_coin_withdraw&showGuide=1')
    store.dispatch.everydayChallenge.closeGuide()
  }

  return (
    <View
      onLayout={handleLayout}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        alignItems: 'center',
        zIndex: 2,
      }}
    >
      <Svg>{basePath ? <Path onPress={handlePress} d={fullPath} fill="rgba(0, 0, 0, 0.7)" fillRule="evenodd" /> : null}</Svg>

      {currentLayoutInfo ? (
        <>
          {currentStep === EverydayChallengeGuideStep.A ? (
            <GuideWord
              label="任务包可以获得现金啦～"
              style={{ top: currentLayoutInfo.y - 54 - 30, right: currentLayoutInfo.x - currentLayoutInfo.width * 1.5 }}
              renderArrow={() => <GuideWordArrow style={{ bottom: -6, right: 0 }} />}
              contentStyle={{ borderBottomRightRadius: 0 }}
            />
          ) : null}
          {currentStep === EverydayChallengeGuideStep.B ? (
            <>
              <GuideWord
                label="现金可在「我的现金」页面进行提现"
                style={{ top: currentLayoutInfo.y + currentLayoutInfo.height + 20, width: 176, left: currentLayoutInfo.width * 0.45 }}
                renderArrow={() => <GuideWordArrow style={{ top: -6, left: 0, transform: [{ rotate: '180deg' }] }} />}
                contentStyle={{ paddingVertical: 8, borderTopLeftRadius: 0, paddingLeft: 10, paddingRight: 6 }}
              />
            </>
          ) : null}
        </>
      ) : null}
    </View>
  )
}

export default React.memo(CashGuideLazy, isEqual)
