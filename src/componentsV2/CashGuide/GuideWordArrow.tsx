import React from 'react'
import { View, StyleSheet, StyleProp, ViewStyle } from 'react-native'
import isEqual from 'lodash.isequal'

type Props = {
  style?: StyleProp<ViewStyle>
}

const styles = StyleSheet.create({
  wrapper: {
    borderTopColor: 'transparent',
    borderBottomColor: 'transparent',
    borderBottomWidth: 6,
    borderTopWidth: 6,
    borderLeftWidth: 0,
    borderRightWidth: 12,
    borderRightColor: '#fff',
    position: 'absolute',
    borderWidth: 1,
  },
})

const GuideWordArrow: React.FC<Props> = ({ style }) => {
  return <View style={[styles.wrapper, style]} />
}

export default React.memo(GuideWordArrow, isEqual)
