import React, { ReactNode, useContext } from 'react'
import { Text } from '@xmly/rn-components'
import { View, StyleSheet, StyleProp, ViewStyle } from 'react-native'
import isEqual from 'lodash.isequal'
import { DarkModeContext } from '../../contextV2/darkModeContext'

type Props = {
  label: string
  style?: StyleProp<ViewStyle>
  contentStyle?: StyleProp<ViewStyle>
  renderArrow: () => ReactNode
}

const styles = StyleSheet.create({
  wrapper: {
    position: 'absolute',
  },
  contentWrapper: {
    paddingHorizontal: 16,
    paddingVertical: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 10,
    backgroundColor: '#fff',
  },
  label: {
    fontSize: 15,
    fontWeight: 'bold',
    color: '#333',
  },
})

const GuideWord: React.FC<Props> = ({ label, style, renderArrow, contentStyle }) => {
  return (
    <View style={[styles.wrapper, style]}>
      <View style={[styles.contentWrapper, contentStyle]}>
        <Text style={[styles.label]}>{label}</Text>
      </View>
      {renderArrow()}
    </View>
  )
}

export default React.memo(GuideWord, isEqual)
