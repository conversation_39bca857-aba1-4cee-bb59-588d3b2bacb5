import React, { Suspense } from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState } from '../../store'

const CashGuideLazy = React.lazy(() => import('./CashGuide.lazy'))
type Props = {}

const CashGuide: React.FC<Props> = (props) => {
  const showNewUserGuide = useSelector((state: RootState) => state.everydayChallenge.showNewUserGuide)

  if (showNewUserGuide ) {
    return (
      <Suspense fallback={null}>
        <CashGuideLazy />
      </Suspense>
    )
  }
  return null
}

export default React.memo(CashGuide, isEqual)
