import React, { forwardRef, useContext } from 'react'
import { Animated, LayoutChangeEvent, StyleSheet, View } from 'react-native'
import {
  ScrollAreaContext,
  ScrollAreaContextHasEffect,
} from '../../contextV2/scrollAreaContext'
import { ThemeContext } from '../../contextV2/themeContext'

const AnimatedView = Animated.createAnimatedComponent(View)

export type Props = {
  children?: React.ElementType<any>
  nextHeaderLayoutY?: number
  onLayout: (event: LayoutChangeEvent) => void
  scrollAnimatedValue: Animated.Value
  onStatusChange: (status: boolean) => void
  setScrollViewStickyHeaderLayoutY: (layoutY: number) => void
}

type State = {
  measured: boolean
  layoutY: number
  layoutHeight: number
  nextHeaderLayoutY?: number
}

class ScrollViewStickyHeaderUI extends React.Component<Props, State> {
  state: State = {
    measured: false,
    layoutY: 0,
    layoutHeight: 0,
    nextHeaderLayoutY: this.props.nextHeaderLayoutY,
  }

  isSticked = false

  listenScrollValue = ({ value }: { value: number }) => {
    if (this.state.measured && this.state.layoutHeight > 0) {
      if (value >= this.state.layoutY) {
        if (!this.isSticked) {
          this.isSticked = true
          this.props.onStatusChange(true)
        }
      } else {
        if (this.isSticked) {
          this.isSticked = false
          this.props.onStatusChange(false)
        }
      }
    }
  }

  componentDidMount() {
    this.props.scrollAnimatedValue.addListener(this.listenScrollValue)
  }

  setNextHeaderY(y: number) {
    this.setState({ nextHeaderLayoutY: y })
  }

  _onLayout = (event: LayoutChangeEvent) => {
    this.setState({
      measured: true,
      layoutY: event.nativeEvent.layout.y,
      layoutHeight: event.nativeEvent.layout.height,
    })
    this.props.setScrollViewStickyHeaderLayoutY(event.nativeEvent.layout.y)

    this.props.onLayout(event)
    const child = React.Children.only(this.props.children) as any
    if (child?.props?.onLayout) {
      child?.props?.onLayout(event)
    }
  }

  render() {
    const { measured, layoutHeight, layoutY, nextHeaderLayoutY } = this.state
    const inputRange: Array<number> = [-1, 0]
    const outputRange: Array<number> = [0, 0]
    if (measured) {
      // The interpolation looks like:
      // - Negative scroll: no translation
      // - From 0 to the y of the header: no translation. This will cause the header
      //   to scroll normally until it reaches the top of the scroll view.
      // - From header y to when the next header y hits the bottom edge of the header: translate
      //   equally to scroll. This will cause the header to stay at the top of the scroll view.
      // - Past the collision with the next header y: no more translation. This will cause the
      //   header to continue scrolling up and make room for the next sticky header.
      //   In the case that there is no next header just translate equally to
      //   scroll indefinitely.
      inputRange.push(layoutY)
      outputRange.push(0)
      // If the next sticky header has not loaded yet (probably windowing) or is the last
      // we can just keep it sticked forever.
      const collisionPoint = (nextHeaderLayoutY || 0) - layoutHeight
      if (collisionPoint >= layoutY) {
        inputRange.push(collisionPoint, collisionPoint + 1)
        outputRange.push(collisionPoint - layoutY, collisionPoint - layoutY)
      } else {
        inputRange.push(layoutY + 1)
        outputRange.push(1)
      }
    }

    const translateY = this.props.scrollAnimatedValue.interpolate({
      inputRange,
      outputRange,
    })
    const opacity = this.props.scrollAnimatedValue.interpolate({
      inputRange,
      outputRange,
    })

    const child = React.Children.only(this.props.children) as any

    return (
      <ThemeContext.Consumer>
        {({ categories: { tabBar } }) => {
          return (
            <AnimatedView
              collapsable={false}
              onLayout={this._onLayout}
              style={[
                child.props.style,
                styles.header,
                { transform: [{ translateY }] },
              ]}
            >
              <AnimatedView
                style={[
                  { backgroundColor: tabBar.backgroundColor, opacity },
                  StyleSheet.absoluteFill,
                ]}
              />
              {React.cloneElement(child, {
                style: styles.fill, // We transfer the child style to the wrapper.
                onLayout: undefined, // we call this manually through our this._onLayout
              })}
            </AnimatedView>
          )
        }}
      </ThemeContext.Consumer>
    )
  }
}

const styles = StyleSheet.create({
  header: {
    zIndex: 10,
  },
  fill: {
    flex: 1,
  },
})

const ScrollViewStickyHeader: React.FC<Omit<Props, 'onStatusChange'>> =
  forwardRef((props, ref) => {
    const { handleTabBarStickedStatusChange } = useContext(
      ScrollAreaContextHasEffect
    )
    const { setScrollViewStickyHeaderLayoutY } = useContext(ScrollAreaContext)
    return (
      <ScrollViewStickyHeaderUI
        {...props}
        ref={ref as any}
        onStatusChange={handleTabBarStickedStatusChange}
        setScrollViewStickyHeaderLayoutY={setScrollViewStickyHeaderLayoutY}
      />
    )
  })

export default ScrollViewStickyHeader
