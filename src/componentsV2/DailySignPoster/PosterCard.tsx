import React, { useRef } from 'react';
import { View } from 'react-native';
import styled from 'styled-components';
import { CardType, CardTypeData } from '../../typesV2/postCard';
import { PosterItemType } from '../../typesV2/posterListType';
import { Text, BetterImage, Touch } from '@xmly/rn-components';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { connect } from 'react-redux';
import { RootState } from '../../store';
import isEqual from 'lodash.isequal';
dayjs.locale('zh-cn');
interface CardTypeInterface {
  cardType: CardType;
}
const CardWrapper = styled(Touch)`
  position: relative;
`;
const ImageWrapper = styled(View)`
  width: 100%;
  height: 100%;
  border-radius: ${(props: CardTypeInterface) =>
    CardTypeData[props.cardType].borderRadius}px;
  position: absolute;
  top: 0;
  left: 0;
`;
const DateWrapper = styled(View)`
  padding-top: 23px;
  padding-left: 22px;
  align-items: flex-start;
  width: 200px;
  height: 80px;
`;

const DateTextWrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-left: -1px;
`;
const DateText = styled(Text)`
  font-size: ${(props: CardTypeInterface) =>
    CardTypeData[props.cardType].dateFont}px;
  font-weight: 700;
  color: #fff;
  line-height: 21px;
  letter-spacing: 2px;
  text-align: left;
`;
const Line = styled(Text)`
  font-size: ${(props: CardTypeInterface) =>
    CardTypeData[props.cardType].lineFont}px;
  font-weight: 200;
  color: #fff;
  padding: 0 1px;
`;

const WeekText = styled(Text)`
  font-size: ${(props: CardTypeInterface) =>
    CardTypeData[props.cardType].weekFont}px;
  font-weight: bold;
  color: #fff;
  line-height: 11px;
  letter-spacing: 2px;
`;

interface PosterProps {
  cardType: CardType;
  posterInfo: PosterItemType;
  onPress?: (param: PosterItemType) => void;
  signInDate: boolean;
  width?: number;
  height?: number;
  fontScale?: number;
  onCoverLoad?: () => void;
}

const PosterCard: React.FC<PosterProps> = ({
  cardType,
  posterInfo,
  onPress,
  signInDate,
  width,
  height,
  fontScale = 1,
  onCoverLoad,
}) => {
  const handlePress = () => {
    if (onPress) {
      onPress(posterInfo);
    }
  };
  const handleLoaded = () => {
    typeof onCoverLoad === 'function' && onCoverLoad();
  };
  if (Object.keys(posterInfo).length) {
    const monthLabel = useRef(dayjs(posterInfo.billDate).format('MM'));
    const dateLabel = useRef(dayjs(posterInfo.billDate).format('DD'));
    const weekLabel = useRef(dayjs(posterInfo.billDate).format('ddd'));
    const yearLabel = useRef(dayjs(posterInfo.billDate).format('YYYY'));
    return (
      <CardWrapper
        onPress={handlePress}
        activeOpacity={1}
        style={{
          width: width || CardTypeData[cardType].width,
          height: height || CardTypeData[cardType].height,
        }}
      >
        <ImageWrapper cardType={cardType}>
          <BetterImage
            onLoad={handleLoaded}
            resizeMode='cover'
            source={{ uri: posterInfo.pictureUrl }}
            style={{ width: '100%', height: '100%' }}
          />
        </ImageWrapper>
        {signInDate ? (
          <DateWrapper
            style={[
              {
                transform: [
                  { translateX: -100 },
                  { translateY: -40 },
                  { scale: fontScale },
                  { translateX: 100 },
                  { translateY: 40 },
                ],
              },
            ]}
          >
            <DateTextWrapper>
              <DateText cardType={cardType}>{monthLabel.current}</DateText>
              <Line cardType={cardType}>/</Line>
              <DateText cardType={cardType}>{dateLabel.current}</DateText>
            </DateTextWrapper>
            <WeekText cardType={cardType}>
              {weekLabel.current}·{yearLabel.current}
            </WeekText>
          </DateWrapper>
        ) : null}
      </CardWrapper>
    );
  } else {
    return null;
  }
};

export default connect(({ signInInfo: { signInDate } }: RootState) => ({
  signInDate,
}))(React.memo(PosterCard, isEqual));
