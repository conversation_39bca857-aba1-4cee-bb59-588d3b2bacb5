import { Text, Touch } from '@xmly/rn-components'
import React, { useState, useEffect, useContext } from 'react'
import { View } from 'react-native'
import { connect } from 'react-redux'
import styled from 'styled-components'
import { RootState, store } from '../../store'
import Pop from '../common/Pop'
import isEqual from 'lodash.isequal'
import xmlog from '../../utilsV2/xmlog'
import { CheckInInfo, SignInUserAwardType } from '../../typesV2/signInNew'
import { CardType, CardTypeData } from '../../typesV2/postCard'
import DialogueBox from '../common/DialogueBox'
import ModalCloseBtn from '../common/ModalCloseBtn'
import {
  PosterItemResourceType,
  PosterListType,
} from '../../typesV2/posterListType'
import getPosterShareTaskConfig from '../../utilsV2/getPosterShareTaskConfig'
import { FromType } from '../../modelsV2/signInInfo'
import PosterCard from './PosterCard'
import SectionShadow from '../common/SectionShadow.ios'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import { NativeInfoContext } from '../../contextV2/nativeInfoContext'
import { safetyToString } from '@xmly/rn-utils'

const TopWrapper = styled(View)`
  width: 295px;
  background: #ffffff;
  border-radius: 10px;
  padding: 24px 0 24px;
  justify-content: center;
  align-items: center;
`
const Title = styled(Text)`
  font-size: 18px;
  font-family: PingFangSC-Medium;
  font-weight: bold;
  text-align: center;
  color: #333333;
  line-height: 25px;
  margin-bottom: 4px;
`

const SubTitle = styled(Text)`
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  color: #ff4646;
  line-height: 20px;
  letter-spacing: 0px;
`

const ShareBtn = styled(Touch)`
  width: 73%;
  height: 44px;
  background: #ff4646;
  border-radius: 24px;
  align-items: center;
  justify-content: center;
  position: relative;
`
const ShareBtnText = styled(Text)`
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 22px;
  letter-spacing: 0px;
`
const CountWrapper = styled(View)`
  position: absolute;
  top: -10px;
  right: 10px;
  width: 27%;
`

const CloseBtn = styled(Touch)`
  margin-top: 24px;
`
interface Props {
  signInRecords: SignInUserAwardType[] // 签到列表
  todaySignInDay: number // 今天是签到列表中的第几天
  ifQueryCheck: boolean // 是否请求过签到接口

  todayScore: string // 今天获得分数
  tomorrowScore: string // 明天获得分数

  currentPoster: PosterListType
  checkInInfo: CheckInInfo
}

// 灰度a方案签到面板
const SignInPosterUI: React.FC<Props> = ({
  todaySignInDay,
  currentPoster,
  checkInInfo,
}) => {
  const [taskValue, setTaskValue] = useState(0)
  const { srcChannel = '', channelName = '' } = useContext(NativeInfoContext)

  useEffect(() => {
    // 任务中心-日签弹窗  弹框展示
    init()
    getTaskValue()
  }, [])

  const getTaskValue = async () => {
    const res = await getPosterShareTaskConfig()
    setTaskValue(res)
  }

  const init = async () => {
    xmlog.event(38571, 'dialogView', {
      currPage: '任务中心',
      sourceType: safetyToString(channelName || srcChannel || ''),
    })
  }

  // 关闭弹窗
  const handleClose = () => {
    // 任务中心-日签弹窗-关闭  弹框控件点击
    xmlog.event(38573, 'dialogClick', { currPage: '任务中心' })
    store.dispatch.signInInfo.refresh({ signInPosterShow: false })
  }

  const handleShare = () => {
    // 任务中心-日签弹窗-分享图片  弹框控件点击
    xmlog.event(38572, 'dialogClick', { currPage: '任务中心' })
    handleClose()
    store.dispatch.signInInfo.refresh({
      shareFromType: FromType.shareA,
      signInPosterWithShare: true,
    })
  }

  const handleLog = () => {
    const posterInfo = currentPoster ? currentPoster[0] : null
    if (posterInfo) {
      // 任务中心-日签弹窗-点击海报封面  弹框控件点击
      xmlog.event(38574, 'dialogClick', {
        currPage: '任务中心',
        posterId: posterInfo.posterId + '',
        albumId:
          posterInfo.resourceType === PosterItemResourceType.album
            ? posterInfo.resourceId + ''
            : '-1',
        trackId:
          posterInfo.resourceType === PosterItemResourceType.track
            ? posterInfo.resourceId + ''
            : '-1',
        link: posterInfo.jumpUrl ? posterInfo.jumpUrl : '-1',
      })
    }
  }

  if (currentPoster?.length) {
    return (
      <Pop
        handleClose={handleClose}
        maskStyle={{ backgroundColor: 'rgba(0,0,0,0.75)' }}
      >
        <TopWrapper>
          <Title>
            {todaySignInDay === 1 ? `签到成功` : `连续签到${todaySignInDay}天`}
          </Title>

          {typeof checkInInfo?.posterMaskDescTitle === 'string' ? (
            <SubTitle>{checkInInfo?.posterMaskDescTitle}</SubTitle>
          ) : null}
          <SectionShadow
            startColor={isAndroid ? 'rgba(0,0,0,0.05)' : 'rgba(0,0,0,0.15)'}
            containerViewStyle={{
              marginTop: 12,
              marginBottom: 25,
              width: CardTypeData[CardType.shareCard].width,
              height: CardTypeData[CardType.shareCard].height,
              alignItems: 'center',
              justifyContent: 'center',
            }}
            contentViewStyle={{
              overflow: 'hidden',
              borderRadius: 8,
            }}
            offset={[0, 7]}
            distance={isAndroid ? 20 : 30}
          >
            <PosterCard
              cardType={CardType.shareCard}
              posterInfo={currentPoster[0]}
              onPress={handleLog}
            />
          </SectionShadow>
          <ShareBtn onPress={handleShare}>
            <ShareBtnText>分享图片</ShareBtnText>
            {taskValue ? (
              <CountWrapper>
                <DialogueBox
                  content={`+${taskValue}积分`}
                  height={17}
                  fontSize={10}
                  arrowStyle={{ width: 3, background: '#4e4e4e' }}
                />
              </CountWrapper>
            ) : null}
          </ShareBtn>
        </TopWrapper>
        <CloseBtn onPress={handleClose}>
          <ModalCloseBtn />
        </CloseBtn>
      </Pop>
    )
  } else {
    return null
  }
}

export default connect(
  ({
    signInInfo: {
      signInRecords,
      todaySignInDay,
      ifQueryCheck,
      currentPoster,
      checkInInfo,
    },
  }: RootState) => ({
    checkInInfo,
    signInRecords,
    todaySignInDay,
    ifQueryCheck,
    todayScore:
      signInRecords.find((item) => todaySignInDay === item.day)?.name || '0',
    tomorrowScore:
      signInRecords.find(
        (item) =>
          todaySignInDay + 1 === item.day ||
          (todaySignInDay === 7 && item.day === 1)
      )?.name || '0',
    currentPoster,
  })
)(React.memo(SignInPosterUI, isEqual))
