import React, { useEffect, useState } from 'react'
import { View, StyleSheet } from 'react-native'
import icon_poster_footer from '../../appImagesV2/icon_poster_footer'
import {
  PosterItemResourceType,
  PosterItemType,
} from '../../typesV2/posterListType'
import { ImageModule } from '@xmly/rn-sdk/dist/ImageModule'
import { IMAGE_LOCAL_PATH_PREFIX } from '../../constantsV2'
import { BetterImage, Text } from '@xmly/rn-components'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import rnEnv from '../../../rnEnv'
import isEqual from 'lodash.isequal'
import { ConfigCenter } from '@xmly/rn-sdk'
interface Props {
  posterInfo: PosterItemType
  height: number
  fontScale: number
  width: number
}

const PosterFooter: React.FC<Props> = ({
  posterInfo,
  height,
  fontScale,
  width,
}) => {
  const [shareQRCodeLocalPath, setShareQRCodeLocalPath] = useState('')

  useEffect(() => {
    drawQRCode()
  }, [posterInfo])

  const drawQRCode = async () => {
    try {
      if (Object.keys(posterInfo).length) {
        let link = ''
        switch (posterInfo.resourceType) {
          case PosterItemResourceType.track:
            link = `https://m${
              rnEnv.isTest() ? '.test' : ''
            }.ximalaya.com/selfshare/sound/${
              posterInfo.resourceId
            }?srcType=7&subType=1116`
            break
          case PosterItemResourceType.album:
            link = `https://m${
              rnEnv.isTest() ? '.test' : ''
            }.ximalaya.com/selfshare/album/${
              posterInfo.resourceId
            }?srcType=6&subType=1115`
            break
          case PosterItemResourceType.activity:
            link = posterInfo.jumpUrl
            break
          default:
            break
        }
        console.log('😭😭😭😭😭😭😭😭😭' + link + '😭😭😭😭😭😭😭😭😭😭😭')
        if (!link) {
          try {
            const defaultLink = await ConfigCenter.getConfig(
              'toc',
              'posters_default_link'
            )
            link = defaultLink || ''
          } catch (err) {
            link = ''
          }
        }

        if (link) {
          const res = await ImageModule.createQRCode({
            content: link,
            width: 300,
            height: 300,
            margin: 0,
          })

          let shareQRCodeLocalPath
          if (isAndroid) {
            shareQRCodeLocalPath = IMAGE_LOCAL_PATH_PREFIX + res
          } else {
            const RNFS = require('react-native-fs')
            shareQRCodeLocalPath = await RNFS.readFile(
              IMAGE_LOCAL_PATH_PREFIX + res,
              'base64'
            )
          }
          setShareQRCodeLocalPath(shareQRCodeLocalPath)
        } else {
          setShareQRCodeLocalPath('')
        }
      }
    } catch (error) {
      console.log(error)
    }
  }
  const QRSource = shareQRCodeLocalPath
    ? isAndroid
      ? { uri: shareQRCodeLocalPath }
      : { uri: `data:image/png;base64,${shareQRCodeLocalPath}` }
    : null

  const qrcodeSize = width * 0.1279
  return (
    <View style={[styles.wrapper, { height }]}>
      <BetterImage
        source={icon_poster_footer}
        style={styles.wrapper_img}
        resizeMode='cover'
      />
      <View
        style={[
          styles.content,
          {
            height: qrcodeSize,
            bottom: 0.2 * height,
            paddingRight: 15 * fontScale,
          },
        ]}
      >
        <View
          style={[
            styles.text_wrapper,
            { paddingRight: 6 * fontScale },
            {
              transform: [
                { translateX: 50 },
                { scale: fontScale },
                { translateX: -50 },
              ],
            },
          ]}
        >
          <Text style={[styles.text]}>听见美好 遇见世界</Text>
          <Text style={[styles.text]}>
            {posterInfo.resourceType === 1 || posterInfo.resourceType === 2
              ? '长按扫码收听'
              : '长按扫码查看'}
          </Text>
        </View>
        <View
          style={[
            styles.scan_wrapper,
            { width: qrcodeSize, height: qrcodeSize },
          ]}
        >
          {QRSource ? (
            <BetterImage
              style={{ width: qrcodeSize, height: qrcodeSize }}
              source={QRSource}
            />
          ) : null}
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  wrapper: {
    width: '100%',
    position: 'relative',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
  },
  wrapper_img: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    left: 0,
    top: 0,
  },
  content: {
    width: '100%',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'flex-end',
    position: 'absolute',
  },
  text_wrapper: {
    height: '100%',
    textAlign: 'right',
    alignItems: 'flex-end',
    justifyContent: 'space-around',
    paddingVertical: 3.5,
    width: 100,
  },
  text: {
    fontSize: 8,
    fontFamily: 'PingFangSC-Light',
    color: '#333333',
  },
  scan_wrapper: {
    borderRadius: 1,
  },
})

export default React.memo(PosterFooter, isEqual)
