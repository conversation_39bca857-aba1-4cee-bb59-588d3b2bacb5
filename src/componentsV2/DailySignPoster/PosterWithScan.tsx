import React from 'react'
import { View } from 'react-native'
import styled from 'styled-components'
import ViewShot from 'react-native-view-shot'
import PosterCard from './PosterCard'
import { CardType, getPosterWithShareCardSize } from '../../typesV2/postCard'
import PosterFooter from './PosterFooter'
import {
  PosterItemResourceType,
  PosterItemType,
} from '../../typesV2/posterListType'
import { FromType } from '../../modelsV2/signInInfo'
import { Touch } from '@xmly/rn-components'
import xmlog from '../../utilsV2/xmlog'
import isEqual from 'lodash.isequal'
import { SharePanelBaseHeight } from '../../constantsV2/layout'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

const TopWrapper = styled(View)`
  overflow: hidden;
`
const RadiusWrapper = styled(View)`
  overflow: hidden;
  border-radius: 10px;
`
const TopCard = styled(Touch)``

interface PosterProps {
  posterInfo: PosterItemType
  viewShotRef: React.LegacyRef<ViewShot>
  shareFromType: FromType
  index: number
}

const PosterWithScan: React.FC<PosterProps> = ({
  posterInfo,
  viewShotRef,
  shareFromType,
  index,
}) => {
  const safeAreaInsets = useSafeAreaInsets()
  const handleLog = () => {
    // 任务中心-分享海报弹窗-点击海报封面  弹框控件点击
    xmlog.event(38578, 'dialogClick', {
      from: shareFromType,
      position: index + 1 + '',
      posterId: posterInfo.posterId + '',
      albumId:
        posterInfo.resourceType === PosterItemResourceType.album
          ? posterInfo.resourceId + ''
          : '-1',
      trackId:
        posterInfo.resourceType === PosterItemResourceType.track
          ? posterInfo.resourceId + ''
          : '-1',
      link: posterInfo.jumpUrl ? posterInfo.jumpUrl : '-1',
      currPage: '任务中心',
    })
  }
  const posterWithShareCardSize = getPosterWithShareCardSize(
    SharePanelBaseHeight + safeAreaInsets.bottom
  )
  return (
    <TopWrapper
      style={[
        {
          alignItems: 'center',
          justifyContent: 'center',
          width: posterWithShareCardSize.cardWidth + 24,
        },
      ]}
    >
      <RadiusWrapper
        style={{
          width: posterWithShareCardSize.cardWidth,
        }}
      >
        <ViewShot ref={viewShotRef}>
          <TopCard
            onPress={handleLog}
            activeOpacity={1}
            style={{
              width: posterWithShareCardSize.cardWidth,
            }}
          >
            <PosterCard
              width={posterWithShareCardSize.cardWidth}
              height={posterWithShareCardSize.cardWithoutFooterHeight}
              cardType={CardType.screenCard}
              posterInfo={posterInfo}
              onPress={handleLog}
              fontScale={posterWithShareCardSize.shouldFontScaled}
            />
            <PosterFooter
              width={posterWithShareCardSize.cardWidth}
              posterInfo={posterInfo}
              height={posterWithShareCardSize.cardFooterHeight}
              fontScale={posterWithShareCardSize.shouldFontScaled}
            />
          </TopCard>
        </ViewShot>
      </RadiusWrapper>
    </TopWrapper>
  )
}

export default React.memo(PosterWithScan, isEqual)
