import { Text } from '@xmly/rn-components'
import React, { useEffect, useState, useRef, useContext } from 'react'
import { View, InteractionManager, PixelRatio } from 'react-native'
import { connect } from 'react-redux'
import styled from 'styled-components'
import ViewShot, { captureRef } from 'react-native-view-shot'
import { RootState, store } from '../../store'
import Pop from '../common/Pop'
import isEqual from 'lodash.isequal'
import xmlog from '../../utilsV2/xmlog'
import { CheckInInfo, SignInUserAwardType } from '../../typesV2/signInNew'
import ScrollableTabView from '../../libV2/rn-scrollable-tab-view'
import PosterWithScan from './PosterWithScan'
import SharePanel from '../common/SharePanel'
import {
  PosterItemResourceType,
  PosterItemType,
  PosterListType,
} from '../../typesV2/posterListType'
import { SnapShot } from '@xmly/rn-sdk/dist/SnapShot'
import { XMShareChannel } from '../../constantsV2/share'
import { getPosterWithShareCardSize } from '../../typesV2/postCard'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import { FromType } from '../../modelsV2/signInInfo'
import { SharePanelBaseHeight } from '../../constantsV2/layout'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { Toast } from '@xmly/rn-sdk'
import getAllTaskList from '../../servicesV2/allTaskRecordsList'
import { TaskStatus } from '../../typesV2/taskList'
import getPosterShareTaskConfig from '../../utilsV2/getPosterShareTaskConfig'
import { NativeInfoContext } from '../../contextV2/nativeInfoContext'
import { safetyToString } from '@xmly/rn-utils'

const Wrapper = styled(View)`
  flex: 1;
  align-items: center;
  justify-content: center;
  padding-top: 42px;
  padding-bottom: 31px;
`

const DialogContent = styled(View)``

const CheckInText = styled(View)`
  justify-content: center;
`
const Title = styled(Text)`
  font-size: 20px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
  line-height: 28px;
  margin-bottom: 4px;
`

const SubTitle = styled(Text)`
  font-size: 14px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #fcd9b4;
  line-height: 20px;
  margin-bottom: 16px;
  text-align: center;
`

interface SignInPosterUIProp {
  signInRecords: SignInUserAwardType[] // 签到列表
  todaySignInDay: number // 今天是签到列表中的第几天
  ifQueryCheck: boolean // 是否请求过签到接口

  todayScore: string // 今天获得分数

  currentPoster: PosterListType //当前海报列表
  posterListToday: PosterListType
  signInText: boolean //是否显示签到文案
  signInPosterShow: boolean //b方案当前海报是否显示
  signInPosterWithShare: boolean //a方案或者历史海报点击当前海报是否显示
  shareFromType: FromType
  checkInInfo: CheckInInfo
}

const viewShotSet: { [key: string]: { current: ViewShot | null } } = {}

const SignInPosterUI: React.FC<SignInPosterUIProp> = (props) => {
  const [shareShow, setShareShow] = useState(true)
  const [currentPage, setCurrentPage] = useState(0)
  const [currentPosterInfo, setCurrentPosterInfo] = useState<PosterItemType>(
    null!
  )
  const [snapShot, setSnapShot] = useState<SnapShot | null>(null)
  const safeAreaInsets = useSafeAreaInsets()
  const [taskValue, setTaskValue] = useState(0)
  const [taskStatus, setTaskStatus] = useState(-1)
  const [taskId, setTaskId] = useState(0)
  const { srcChannel = '', channelName = '' } = useContext(NativeInfoContext)
  const shareNumber = useRef(0)

  useEffect(() => {
    init()
  }, [props.currentPoster])

  useEffect(() => {
    getTaskStatus()
  }, [])

  const init = async () => {
    // 任务中心-分享海报弹窗  弹框展示

    if (currentPoster && currentPoster.length) {
      const info = currentPoster[0]
      console.log(currentPoster.length)
      setCurrentPosterInfo(info)
      xmlog.event(38575, 'dialogView', {
        posterId: info.posterId + '',
        albumId:
          info.resourceType === PosterItemResourceType.album
            ? info.resourceId + ''
            : '-1',
        trackId:
          info.resourceType === PosterItemResourceType.track
            ? info.resourceId + ''
            : '-1',
        link: info.jumpUrl ? info.jumpUrl : '-1',
        from: props.shareFromType,
        currPage: '任务中心',
        sourceType: signInText
          ? safetyToString(channelName || srcChannel || '')
          : '',
      })
    }
    InteractionManager.runAfterInteractions(async () => {
      setSnapShot(new SnapShot({ viewShotRef: viewShotSet[0] }))
    })
  }
  const getTaskValue = async () => {
    const res = await getPosterShareTaskConfig()
    setTaskValue(res)
  }

  const getTaskStatus = async () => {
    const res = await getAllTaskList()
    console.log(res.data.taskItems)

    const { id, status } = res.data.taskItems[0]
    console.log('🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱', id, status)
    setTaskStatus(status)
    setTaskId(id)

    if (status !== TaskStatus.finished && status !== TaskStatus.received) {
      getTaskValue()
    }
  }

  const setShareNumber = () => {
    shareNumber.current = 1
  }

  const onChangeTab = ({ i }: { i: number }) => {
    setCurrentPosterInfo(currentPoster[i])
    setCurrentPage(i)
    InteractionManager.runAfterInteractions(async () => {
      setSnapShot(new SnapShot({ viewShotRef: viewShotSet[i] }))
    })
    // 任务中心-分享海报弹窗-切换海报  控件曝光
    xmlog.event(38577, 'slipPage', {
      from: props.shareFromType,
      position: i + 1 + '',
      currPage: '任务中心',
    })
  }

  // 保存截图
  const handleSaveViewShot = () => {
    snapShot?.saveViewShot()
  }
  // 获取截图链接
  const handleGetViewShotLocalPath = async (type: XMShareChannel) => {
    return (
      (isAndroid ? '' : 'file://') +
      (await captureRef(viewShotSet[currentPage], {
        format: 'jpg',
        quality: 1.0,
        width: posterWithShareCardSize.cardWidth * PixelRatio.get(),
        height: posterWithShareCardSize.cardHeight * PixelRatio.get(),
      }))
    )
  }
  // 关闭弹窗
  const handleClose = () => {
    // 同时关闭签到面板组件
    store.dispatch.signInInfo.refresh({
      signInPosterShow: false,
      signInPosterWithShare: false,
      signInText: false,
    })
    if (shareNumber.current !== 0) {
      Toast.info(`成功分享海报+${taskValue}积分`)
    }
  }

  const handleViewShotRef = (index: number, ref: ViewShot | null) => {
    viewShotSet[index] = { current: ref }
  }

  const handleRenderPosterWithScan = () => {
    if (currentPoster && currentPoster.length > 0) {
      return currentPoster.map((ele, index) => {
        const handleRef = (ref: ViewShot | null) => {
          handleViewShotRef(index, ref)
        }
        return (
          <PosterWithScan
            key={ele.posterId}
            posterInfo={ele}
            viewShotRef={handleRef}
            index={index}
            shareFromType={props.shareFromType}
          />
        )
      })
    } else {
      return null
    }
  }

  const { todaySignInDay, currentPoster, signInText, checkInInfo } = props
  const posterWithShareCardSize = getPosterWithShareCardSize(
    SharePanelBaseHeight + safeAreaInsets.bottom
  )
  if (currentPoster && currentPoster.length) {
    const commonContentProps = { bounces: false, overScrollMode: 'never' }
    const contentProps = isAndroid
      ? commonContentProps
      : {
          ...commonContentProps,
          style: {
            overflow: 'visible',
            flex: 1,
          },
        }
    return (
      <Pop
        handleClose={handleClose}
        closeOnPressModal={false}
        maskStyle={{ backgroundColor: 'rgba(0,0,0,0.75)' }}
      >
        <Wrapper
          style={{
            width: posterWithShareCardSize.cardWidth + 24,
          }}
        >
          {signInText ? (
            <CheckInText>
              <Title>
                {todaySignInDay === 1
                  ? `签到成功`
                  : `连续签到${todaySignInDay}天`}
              </Title>

              {typeof checkInInfo?.posterMaskDescTitle === 'string' ? (
                <SubTitle>{checkInInfo?.posterMaskDescTitle}</SubTitle>
              ) : null}
            </CheckInText>
          ) : null}
          <DialogContent
            style={{
              height: posterWithShareCardSize.cardHeight,
              width: posterWithShareCardSize.cardWidth + 24,
            }}
          >
            <ScrollableTabView
              style={{
                width: posterWithShareCardSize.cardWidth + 24,
              }}
              contentProps={contentProps}
              page={currentPage}
              prerenderingSiblingsNumber={Infinity} // 预渲染所有子tab
              initialPage={0}
              onChangeTab={onChangeTab}
              withoutTabBar
            >
              {handleRenderPosterWithScan()}
            </ScrollableTabView>
          </DialogContent>
        </Wrapper>
        {shareShow ? (
          <SharePanel
            shareInfo={currentPosterInfo}
            handleSaveViewShot={handleSaveViewShot}
            handleGetViewShotLocalPath={handleGetViewShotLocalPath}
            handleClose={() => {
              handleClose()
              setShareShow(false)
            }}
            setShareNumber={setShareNumber}
            taskInfo={{ taskValue, taskStatus, taskId }}
            setTaskStatus={setTaskStatus}
          />
        ) : null}
      </Pop>
    )
  } else {
    return null
  }
}

export default connect(
  ({
    signInInfo: {
      signInRecords,
      todaySignInDay,
      ifQueryCheck,
      currentPoster,
      posterListToday,
      signInText,
      signInPosterShow,
      signInPosterWithShare,
      shareFromType,
      checkInInfo,
    },
  }: RootState) => ({
    signInRecords,
    todaySignInDay,
    ifQueryCheck,
    todayScore:
      signInRecords.find((item) => todaySignInDay === item.day)?.name || '0',
    currentPoster,
    posterListToday,
    signInText,
    signInPosterShow,
    signInPosterWithShare,
    shareFromType,
    checkInInfo,
  })
)(React.memo(SignInPosterUI, isEqual))
