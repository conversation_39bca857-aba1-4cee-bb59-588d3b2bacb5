import { Text, Touch } from '@xmly/rn-components'
import React, { useState, useEffect, useContext } from 'react'
import { View } from 'react-native'
import { useSelector } from 'react-redux'
import styled from 'styled-components'
import { RootState, store } from '../../store'
import Pop from '../common/Pop'
import isEqual from 'lodash.isequal'
import xmlog from '../../utilsV2/xmlog'
import { CardType } from '../../typesV2/postCard'
import ModalCloseBtn from '../common/ModalCloseBtn'
import { PosterItemResourceType } from '../../typesV2/posterListType'
import getPosterShareTaskConfig from '../../utilsV2/getPosterShareTaskConfig'
import { FromType } from '../../modelsV2/signInInfo'
import PosterCard from './PosterCard'
import { NativeInfoContext } from '../../contextV2/nativeInfoContext'
import { safetyToString } from '@xmly/rn-utils'
import { signInGiftTooltip } from '../../constantsV2/signIn'
import GiftItem from '../CheckInPop/GiftItem'

const TopWrapper = styled(View)`
  width: 275px;
  background: #ffffff;
  border-radius: 10px;
  justify-content: center;
  align-items: center;
  overflow: hidden;
`
const Title = styled(Text)`
  font-size: 16px;
  font-family: PingFangSC-Medium;
  font-weight: bold;
  text-align: center;
  color: #333333;

  margin-top: 20px;
`

const ShareBtn = styled(Touch)`
  width: 200px;
  height: 40px;
  background: #ff4444;
  border-radius: 24px;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-top: 16px;
  margin-bottom: 24px;
`
const ShareBtnText = styled(Text)`
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  color: #ffffff;
`

const SignInInfoWrapper = styled(View)`
  align-items: center;
  width: 100%;
`

const SignInGiftListWrapper = styled(View)`
  margin-top: 20px;
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
`

const CloseBtn = styled(Touch)`
  margin-top: 24px;
`
interface Props {}

const SignInPosterUI: React.FC<Props> = () => {
  const todaySignInDay = useSelector(
    (state: RootState) => state.signInInfo.todaySignInDay
  )

  const currentPoster = useSelector(
    (state: RootState) => state.signInInfo.currentPoster
  )

  const checkInInfo = useSelector(
    (state: RootState) => state.signInInfo.checkInInfo
  )

  const userIDTag = useSelector(
    (state: RootState) => state.signInInfo.userIDTag
  )

  const randomQuantity = useSelector(
    (state: RootState) => state.signInInfo.randomQuantity
  )

  const [taskValue, setTaskValue] = useState(0)
  const { srcChannel = '', channelName = '' } = useContext(NativeInfoContext)

  useEffect(() => {
    store.dispatch.signInInfo.getPosterListToday()
  }, [])

  useEffect(() => {
    // 任务中心-日签弹窗  弹框展示
    init()
    getTaskValue()
  }, [])

  const getTaskValue = async () => {
    const res = await getPosterShareTaskConfig()
    setTaskValue(res)
  }

  const init = async () => {
    xmlog.event(38571, 'dialogView', {
      currPage: '任务中心',
      sourceType: safetyToString(channelName || srcChannel || ''),
    })
  }

  // 关闭弹窗
  const handleClose = () => {
    // 任务中心-日签弹窗-关闭  弹框控件点击
    xmlog.event(38573, 'dialogClick', { currPage: '任务中心' })
    store.dispatch.signInInfo.refresh({ signInPosterShow: false })
  }

  // 点击分享按钮
  const handleShare = () => {
    // 任务中心-日签弹窗-分享图片  弹框控件点击
    xmlog.event(38572, 'dialogClick', { currPage: '任务中心' })
    handleClose()
    store.dispatch.signInInfo.refresh({
      shareFromType: FromType.shareA,
      signInPosterWithShare: true,
    })
  }

  // 埋点
  const handleLog = () => {
    const posterInfo = currentPoster ? currentPoster[0] : null
    if (posterInfo) {
      // 任务中心-日签弹窗-点击海报封面  弹框控件点击
      xmlog.event(38574, 'dialogClick', {
        currPage: '任务中心',
        posterId: posterInfo.posterId + '',
        albumId:
          posterInfo.resourceType === PosterItemResourceType.album
            ? posterInfo.resourceId + ''
            : '-1',
        trackId:
          posterInfo.resourceType === PosterItemResourceType.track
            ? posterInfo.resourceId + ''
            : '-1',
        link: posterInfo.jumpUrl ? posterInfo.jumpUrl : '-1',
      })
    }
  }

  const signTitle = `${
    todaySignInDay === 1 ? `签到成功` : `连续签到${todaySignInDay}天`
  }，恭喜获得`

  const RenderGift = () => {
    const { awards } = checkInInfo
    if (awards && awards.length > 0) {
      const giftTooltip = signInGiftTooltip[userIDTag]
      return (
        <SignInGiftListWrapper>
          {awards.map((gift, index) => (
            <GiftItem
              gift={gift}
              isFirst={index === 0}
              giftTooltip={giftTooltip}
              randomQuantity={randomQuantity}
            />
          ))}
        </SignInGiftListWrapper>
      )
    }

    return null
  }

  if (currentPoster?.length) {
    return (
      <Pop
        handleClose={handleClose}
        maskStyle={{ backgroundColor: 'rgba(0,0,0,0.75)' }}
      >
        <TopWrapper>
          <PosterCard
            width={275}
            height={323}
            cardType={CardType.signInModalNew}
            posterInfo={currentPoster[0]}
            onPress={handleLog}
            fontScale={1}
          />
          <SignInInfoWrapper>
            <Title>{signTitle}</Title>

            {RenderGift()}
            <ShareBtn onPress={handleShare}>
              <ShareBtnText>
                分享图片{taskValue ? `+${taskValue}积分` : ''}
              </ShareBtnText>
            </ShareBtn>
          </SignInInfoWrapper>
        </TopWrapper>
        <CloseBtn onPress={handleClose}>
          <ModalCloseBtn />
        </CloseBtn>
      </Pop>
    )
  } else {
    return null
  }
}

export default React.memo(SignInPosterUI, isEqual)
