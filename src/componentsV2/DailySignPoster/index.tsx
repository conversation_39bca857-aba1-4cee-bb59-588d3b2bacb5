import isEqual from 'lodash.isequal'
import React from 'react'
import { connect } from 'react-redux'
import { RootState } from '../../store'
import { ABRuleTag } from '../../typesV2/signInNew'

const SignInPosterUIA = React.lazy(() => import('./SignInPosterNoShare'))
const SignInPosterUIB = React.lazy(() => import('./SignInPosterWithShare'))
const SignInPosterNoShareNew = React.lazy(
  () => import('./SignInPosterNoShareNew')
)

const DailySignPoster = ({
  signInPosterShow,
  signInPosterWithShare,
  abRuleTag,
}: {
  signInPosterShow: boolean /** 是否显示签到弹窗 */
  signInPosterWithShare: boolean
  abRuleTag: ABRuleTag
}) => {
  // 显示没有分享面板的海报弹窗
  if (signInPosterShow) {
    if (abRuleTag === ABRuleTag.false) {
      //  ab 实验对照组
      return (
        <React.Suspense fallback={null}>
          <SignInPosterUIA />
        </React.Suspense>
      )
    } else {
      // ab 实验实验组（命中实验）
      return (
        <React.Suspense fallback={null}>
          <SignInPosterNoShareNew />
        </React.Suspense>
      )
    }
  }

  // 显示有分享面板的海报弹窗
  if (signInPosterWithShare) {
    return (
      <React.Suspense fallback={null}>
        <SignInPosterUIB />
      </React.Suspense>
    )
  }

  return null
}

export default connect(
  ({
    signInInfo: { signInPosterShow, signInPosterWithShare, abRuleTag },
  }: RootState) => ({
    signInPosterShow,
    signInPosterWithShare,
    abRuleTag,
  })
)(React.memo(DailySignPoster, isEqual))
