import React, { useContext } from 'react'
import { Text } from '@xmly/rn-components'
import { View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { textAlignMiddleStyle } from '../../constantsV2/layout'
import ReSwitch from '../common/ReSwitch'
import { EnumNotificationStatus } from '../../typesV2/notification'
import { store } from '../../store'
import xmlog from '../../utilsV2/xmlog'
import { ThemeStyle } from '../../typesV2/themeInfo'
import { DarkModeContext } from '../../contextV2/darkModeContext'

type Props = {
  notificationStatus: EnumNotificationStatus
}

const Wrapper = styled(View)`
  flex-direction: row;
  align-items: center;
`

const Label = styled(Text)`
  font-size: 11px;
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.checkInBar.notification_switch_label}; ;
`

const ConfirmBodyLabel = styled(Text)`
  font-size: 11px;
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.checkInBar.notification_switch_label};
  margin-top: 8px;
`

const SignInNotificationSwitch: React.FC<Props> = ({ notificationStatus }) => {
  const { isDarkMode } = useContext(DarkModeContext)

  const handlePress = (value: boolean) => {
    store.dispatch.notification.toggleNotification({
      confirmBody: (text: string) => (
        <ConfirmBodyLabel>{text}</ConfirmBodyLabel>
      ),
      enable: !value,
      onStatusChanged(status) {
        // 任务中心-签到提醒开关  点击事件
        xmlog.click(48862, undefined, {
          currPage: '任务中心',
          Item: status ? '打开' : '关闭',
        })
      },
    })
  }

  const switchValue = notificationStatus === EnumNotificationStatus.enable

  return (
    <Wrapper>
      <Label style={textAlignMiddleStyle}>订阅签到提醒</Label>
      <ReSwitch
        value={switchValue}
        onPress={handlePress}
        containerStyle={{ marginLeft: 4 }}
        trackOffColor={isDarkMode ? '#323235' : undefined}
      />
    </Wrapper>
  )
}

export default React.memo(SignInNotificationSwitch, isEqual)
