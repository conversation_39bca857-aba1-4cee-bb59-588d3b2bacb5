import { BetterImage, Text, Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import React, { useContext } from 'react'
import { IndexSectionGourds } from 'servicesV2/getTopSectionInfo'
import styled from 'styled-components'
import getImageUri from 'utilsV2/getImageUri'
import LinearGradient from 'react-native-linear-gradient'
import BoxShadow from '../common/BoxShadow'
import { View } from 'react-native'
import rnEnv from '../../../rnEnv'
import { ThemeStyle } from '../../typesV2/themeInfo'
import { ThemeContext } from '../../contextV2/themeContext'

type Props = {
  onPress: (item: IndexSectionGourds, isLast: boolean) => void
  item: IndexSectionGourds
  isLast: boolean
}

const ItemWrapper = styled(LinearGradient)`
  width: 100%;
  height: 28px;
`
const ItemTouch = styled(Touch)`
  width: 100%;
  height: 90px;
  background: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.item_bg_color};
`
const ItemCover = styled(BetterImage)`
  position: absolute;
  right: 0;
  top: 0;
  height: 90px;
  width: 84px;
`

const ItemText = styled(View)`
  position: absolute;
  top: 15px;
  justify-content: flex-start;
  align-items: flex-start;
  padding: 0 0 12px 10px;
`
const ItemName = styled(Text)`
  font-size: 12px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.groundList.name_color};
  line-height: 17px;
`
const ItemTitle = styled(Text)`
  font-size: 15px;
  font-family: SourceHanSansCN-Bold;
  font-weight: bold;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.groundList.title_color};
  line-height: 23px;
  margin-bottom: 4px;
`

const ItemBtnWrapper = styled(View)`
  width: 58px;
  border-radius: 12px;
  height: 22px;
  overflow: hidden;
`
const ItemBtn = styled(Text)`
  text-align: center;
  font-size: 11px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  line-height: 22px;
`
const GourdItem: React.FC<Props> = (props) => {
  const theme = useContext(ThemeContext)
  const { item, onPress, isLast } = props
  const handlePress = () => {
    onPress(item, isLast)
  }
  const btnStyle = isLast
    ? {
        backgroundColor: theme.groundList.btn_right_bg,
        color: theme.groundList.btn_right_color,
      }
    : {
        backgroundColor: theme.groundList.btn_left_bg,
        color: theme.groundList.btn_left_color,
      }
  const bgColor = isLast
    ? [
        theme.groundList.bg_right_color_top,
        theme.groundList.bg_right_color_bottom,
      ]
    : [
        theme.groundList.btn_left_color_top,
        theme.groundList.btn_left_color_bottom,
      ]
  return (
    <BoxShadow style={{ marginRight: isLast ? 0 : 11 }}>
      <ItemTouch onPress={handlePress}>
        <ItemWrapper
          colors={bgColor}
          start={{ x: 0, y: 0 }}
          end={{ x: 0, y: 1 }}
        ></ItemWrapper>
        <ItemText>
          <ItemName>{item.name} </ItemName>
          <ItemTitle>{item.title}</ItemTitle>
          <ItemBtnWrapper style={[btnStyle]}>
            <ItemBtn style={[btnStyle]}>{item.button}</ItemBtn>
          </ItemBtnWrapper>
        </ItemText>

        <ItemCover
          source={{
            uri: getImageUri(item.iconUrl, {
              width: 84 * 3,
              height: 90 * 3,
              test: rnEnv.isTest(),
            }),
          }}
          resizeMode='cover'
        />
      </ItemTouch>
    </BoxShadow>
  )
}

export default React.memo(GourdItem, (pProps, nProps) =>
  isEqual(pProps.item, nProps.item)
)
