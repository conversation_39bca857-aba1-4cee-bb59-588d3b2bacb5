import isEqual from 'lodash.isequal'
import React, { useCallback, useEffect, useState } from 'react'
import { View, NativeModules } from 'react-native'
import { IndexSectionGourds } from 'servicesV2/getTopSectionInfo'
import styled from 'styled-components'
import getUrlToOpen from 'utilsV2/getUrlToOpen'
import goScoreMarket2 from 'utilsV2/goScoreMarket2'
import xmlog from 'utilsV2/xmlog'
import GourdItem from './GourdItem'
import getIndexSpendConfig from '../../servicesV2/getIndexSpend'
import { useNavigation } from '@react-navigation/native'
import { RouteMap } from '../../constantsV2/routeMap'

const { Page } = NativeModules

type Props = {}

const ListWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 12px;
  height: 90px;
`

const index: React.FC<Props> = (props) => {
  const navigate = useNavigation()
  const [gourdList, setGourdList] = useState<IndexSectionGourds[]>([
    // {
    //   name: '',
    //   title: '',
    //   button: '',
    //   jumpUrl: '',
    //   iconUrl: '',
    //   type: 2,
    // },
    // {
    //   name: '',
    //   title: '',
    //   button: '',
    //   jumpUrl: '',
    //   iconUrl: '',
    //   type: 2,
    // },
  ])

  useEffect(() => {
    init()
  }, [])

  const init = async () => {
    // console.log('🐢🐢🐢🐢🐢🐢🐢🐢🐢gourds render');
    try {
      const res = await getIndexSpendConfig()
      console.log('🐢🐢🐢🐢🐢getIndexSpendConfig🐢🐢🐢🐢')
      console.log(res)
      setGourdList(res?.data[0]?.gourds ?? [])
    } catch (error) {}
  }
  const handleItemPress = useCallback(
    async (item: IndexSectionGourds, isLast) => {
      const { type, jumpUrl, navigationUrl } = item
      if (jumpUrl) {
        switch (Number(type)) {
          case 1:
            // 打开H5
            Page.start(getUrlToOpen(jumpUrl))
            break
          case 2:
            // 兑吧
            goScoreMarket2(jumpUrl)
            break
          case 3:
            typeof navigationUrl === 'string' &&
              RouteMap.includes(navigationUrl) &&
              navigate.navigate(navigationUrl)
            break
          default:
            // 打开H5
            Page.start(getUrlToOpen(jumpUrl))
            break
        }

        // 任务中心-活动 banner  点击事件
        xmlog.click(39743, undefined, {
          Item: isLast ? '免费会员' : '幸运大转盘',
          currPage: '任务中心',
        })
      }
    },
    []
  )
  if (gourdList?.length) {
    return (
      <ListWrapper>
        {gourdList.map((item, index) => (
          <GourdItem
            item={item}
            isLast={index === 1}
            key={index}
            onPress={handleItemPress}
          />
        ))}
      </ListWrapper>
    )
  } else {
    return null
  }
}

export default React.memo(index, isEqual)
