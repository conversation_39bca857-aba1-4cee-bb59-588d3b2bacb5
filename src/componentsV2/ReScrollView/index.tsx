import { isAndroid } from '@xmly/rn-utils/dist/device'
import {
  ScrollAreaContext,
  ScrollAreaContextHasEffect,
} from 'contextV2/scrollAreaContext'
import isEqual from 'lodash.isequal'
import React, {
  forwardRef,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react'
import {
  LayoutChangeEvent,
  StyleSheet,
  StyleProp,
  ViewStyle,
  View,
} from 'react-native'
import { TapGestureHandlerStateChangeEvent } from 'react-native-gesture-handler'
import {
  PanGestureHandler,
  PanGestureHandlerProperties,
  State,
  TapGestureHandler,
} from 'react-native-gesture-handler'
import Animated, {
  add,
  and,
  call,
  Clock,
  clockRunning,
  cond,
  eq,
  Extrapolate,
  floor,
  greaterOrEq,
  greaterThan,
  interpolate,
  lessOrEq,
  lessThan,
  not,
  onChange,
  set,
  stopClock,
  useCode,
  useValue,
} from 'react-native-reanimated'
import { clamp, decay } from 'react-native-redash'
import {
  ReScrollViewContext,
  ReScrollViewContextValue,
} from './ReScrollViewContext'
// import transitionTo from './transitionTo'

type Props = {
  style?: StyleProp<ViewStyle>
  contentContainerStyle?: StyleProp<ViewStyle>
  PanGestureHandlerProps?: Omit<
    PanGestureHandlerProperties,
    'onGestureEvent' | 'onHandlerStateChange'
  >
  translateYParent?: Animated.Adaptable<number>
  name?: string
  DEBUG?: boolean
  hasChildrenScrollView?: boolean
  onScroll?: (offset?: number) => void
  onBottom?: () => void
  onNotBottom?: () => void
  childrenLength?: number
  children?: ReactNode
  reScrollViewRef?: any
}

const debug = (any: any) =>
  call([], () => {
    console.log(any)
  })

const debugValue = (value: readonly Animated.Adaptable<any>[], msg?: any) =>
  call(value, (value) => {
    console.log('msg: ', msg, value)
  })

const Index = forwardRef<PanGestureHandler, Props>((props, ref) => {
  const { commodityTabScrollValue } = useContext(ScrollAreaContext)
  const { commodityTabCurrentTab } = useContext(ScrollAreaContextHasEffect)
  const {
    children,
    style,
    contentContainerStyle,
    PanGestureHandlerProps,
    translateYParent,
    name = '',
    DEBUG,
    hasChildrenScrollView,
    onBottom,
    onNotBottom,
    onScroll,
    childrenLength,
    reScrollViewRef,
  } = props
  const [containerHeight, setContainerHeight] = useState<null | number>(null)
  const [contentHeight, setContentHeight] = useState<null | number>(null)

  const getChildTranslate = (length: number) => {
    return Array(length)
      .fill(1)
      .map((t) => new Animated.Value<number>(0))
  }

  const [childTranslate, setChildTranslate] = useState([
    new Animated.Value<number>(0),
  ])
  const gestureY = useValue<number>(0)
  const offsetY = useValue<number>(0)
  const velocityY = useValue<number>(0)
  const translateY = useValue(0) // add(commonTranslateY, childTranslate[ commodityTabCurrentTab ] || new Animated.Value(0)) // useValue<number>(0)  // hasChildrenScrollView ? getCurrentTranslateY(commodityTabCurrentTab) : useValue<number>(0)
  const gestureState = useValue<State>(State.UNDETERMINED)
  const selfScrollMax = useValue<number>(0) // containerHeight && contentHeight ? containerHeight - contentHeight > 0 ? useValue(containerHeight - contentHeight) : useValue(0) : useValue(-1)
  const selfScrollMaxRef = useRef(0) // containerHeight && contentHeight ? containerHeight - contentHeight > 0 ? useValue(containerHeight - contentHeight) : useValue(0) : useValue(-1)
  const decayClock = useRef(new Clock()).current
  const isOnTopDragDown = useValue<0 | 1>(0)
  const isParentOnTop =
    typeof translateYParent === 'undefined' ? 1 : eq(translateYParent, 0)
  const shouldStopDecay = useValue<0 | 1>(0)
  const isSelfOnTop = greaterOrEq(translateY, 0)
  const isSelfOnBottom = eq(translateY, selfScrollMax)
  const selfScrollValue = interpolate(translateY, {
    inputRange: [selfScrollMax, 0],
    outputRange: [selfScrollMax, 0],
    extrapolate: Extrapolate.CLAMP,
  })
  const decayValue = decay({
    clock: decayClock,
    from: translateY,
    velocity: velocityY,
    deceleration: 0.998,
  })
  const translateYMaxSet = useRef<Animated.Value<number>[]>([])
  const [childTranslateYMaxReady, setChildTranslateYMaxReady] = useState(false)

  const getMax = (index: number) => {
    console.log({ 'translateYMaxSet.current33333': translateYMaxSet })
    return translateYMaxSet.current[index] || new Animated.Value<number>(0)
  }

  // const [translateYMax, setTranslateYMax] = useState(selfScrollMax)
  const translateYMax = useValue(0)

  useEffect(() => {
    if (typeof childrenLength === 'number' && childrenLength > 0) {
      setChildTranslate(getChildTranslate(childrenLength))
    }
  }, [childrenLength])

  // let translateYMax = hasChildrenScrollView ? getMax(commodityTabCurrentTab) : selfScrollMax

  const initTranslateYMaxSet = (
    index: number,
    value: Animated.Value<number>
  ) => {
    translateYMaxSet.current[index] = value
    console.log('translateYMaxSet.current', translateYMaxSet.current)
    if (
      typeof childrenLength === 'number' &&
      childrenLength > 0 &&
      translateYMaxSet.current.length === childrenLength &&
      translateYMaxSet.current.every((max) => {
        return !!max
      })
    ) {
      setChildTranslateYMaxReady(true)
      translateYMax.setValue(getMax(commodityTabCurrentTab))
    }
  }

  useEffect(() => {
    hasChildrenScrollView &&
      translateYMax.setValue(getMax(commodityTabCurrentTab))
  }, [commodityTabCurrentTab])

  const reScrollViewContextValue: ReScrollViewContextValue = useMemo(
    () => ({
      isReScrollViewParentOnBottom: isSelfOnBottom,
      isReScrollViewParentOnTop: isSelfOnTop,
      parentContainerHeight: containerHeight as number,
      translateY,
      scrollViewName: name,
      decayValue,
      decayClock,
      parentScrollMax: selfScrollMax,
      velocityY,
      gestureState,
      gestureY,
      translateYMax,
      shouldStopDecay,
      initTranslateYMaxSet,
      childTranslate,
    }),
    [containerHeight, childTranslate]
  )

  // const calcTransitionToPosition = () => {
  //   return snapPoint(modalProgress, velocityY, [
  //     0,
  //     modalTranslateYMax,
  //   ]) as Animated.Value<number>
  // }

  const handleGestureEvent = Animated.event([
    {
      nativeEvent: {
        translationY: gestureY,
        velocityY: velocityY,
        state: gestureState,
      },
    },
  ])

  // useCode(() => {
  //   return [
  //     debugValue([translateY], 'translateY iiiiii'),
  //     debugValue([selfScrollMax], 'selfScrollMax iiiiii'),
  //     debugValue([translateYMax], 'translateYMax iiiiii'),
  //   ]
  // }, [])

  useEffect(() => {
    if (
      typeof containerHeight === 'number' &&
      typeof contentHeight === 'number'
    ) {
      calcSelfScrollMax()
    }
  }, [containerHeight, contentHeight])

  const calcSelfScrollMax = () => {
    console.log('calcSelfScrollMax parent')
    if (containerHeight && contentHeight) {
      const diff = containerHeight - contentHeight
      if (diff >= 0) {
        selfScrollMax.setValue(0)
        selfScrollMaxRef.current = 0
      } else {
        selfScrollMax.setValue(diff)
        selfScrollMaxRef.current = diff
      }
      !hasChildrenScrollView && setTranslateYMax(selfScrollMax)
    }
  }

  const handleContainerLayout = (e: LayoutChangeEvent) => {
    setContainerHeight(e.nativeEvent.layout.height)
  }

  const handleContentLayout = (event: LayoutChangeEvent) => {
    setContentHeight(event.nativeEvent.layout.height)
  }

  DEBUG &&
    console.log('打印滑动面板高度 !@#', { containerHeight, contentHeight })

  useCode(() => {
    if (typeof onBottom === 'function' && typeof onNotBottom === 'function') {
      return [
        onChange(
          [lessOrEq(translateY, selfScrollMax)],
          [
            cond(
              lessOrEq(translateY, selfScrollMax),
              [
                call([], () => {
                  onBottom()
                }),
              ],
              [
                call([], () => {
                  onNotBottom()
                }),
              ]
            ),
          ]
        ),
      ]
    } else {
      return []
    }
  }, [onBottom, onNotBottom])

  useCode(() => {
    return [
      cond(
        [eq(gestureState, State.ACTIVE)],
        [
          cond(
            and(eq(translateY, 0), greaterThan(gestureY, 0)),
            [set(isOnTopDragDown, 1)],
            [set(isOnTopDragDown, 0)]
          ),
        ]
      ),
    ]
  }, [])

  // 停止decay 动画
  useCode(() => {
    return [
      cond(isSelfOnTop, [
        cond(clockRunning(decayClock), stopClock(decayClock)),
        set(velocityY, 0),
        set(translateY, 0),
      ]),
    ]
  }, [
    containerHeight,
    contentHeight,
    commodityTabCurrentTab,
    translateY,
    childTranslateYMaxReady,
    translateYMax,
  ])

  // 停止decay 动画
  useCode(() => {
    return [
      onChange(commodityTabScrollValue, [
        cond(clockRunning(decayClock), stopClock(decayClock)),
        set(velocityY, 0),
      ]),
      cond(lessOrEq(translateY, translateYMax), [
        cond(clockRunning(decayClock), stopClock(decayClock)),
        set(velocityY, 0),
        set(translateY, translateYMax),
      ]),
    ]
  }, [
    containerHeight,
    contentHeight,
    commodityTabCurrentTab,
    translateY,
    translateYMax,
    commodityTabScrollValue,
    childTranslateYMaxReady,
  ])

  useCode(() => {
    return childTranslate.map((child, index) => {
      return onChange(translateY, [
        cond(
          eq(index, floor(commodityTabScrollValue)),
          [set(child, translateY)],
          []
        ),
      ])
    })
  }, [
    commodityTabCurrentTab,
    selfScrollValue,
    childTranslateYMaxReady,
    childTranslate,
  ])

  useCode(() => {
    return childTranslate.map((child, index) => {
      return onChange(selfScrollValue, [
        cond(
          eq(index, floor(commodityTabScrollValue)),
          [],
          [set(child, selfScrollValue)]
        ),
      ])
    })
  }, [
    commodityTabCurrentTab,
    selfScrollValue,
    childTranslateYMaxReady,
    childTranslate,
  ])

  useCode(() => {
    return [
      cond(
        [eq(gestureState, State.BEGAN)],
        [
          cond(clockRunning(decayClock), stopClock(decayClock)),
          set(velocityY, 0),
          set(offsetY, translateY),
        ]
      ),
      cond(
        [
          and(
            eq(gestureState, State.END),
            not(isOnTopDragDown),
            not(shouldStopDecay),
            isParentOnTop
          ),
        ],
        [set(translateY, decayValue), set(offsetY, translateY)]
      ),
      cond(
        [eq(gestureState, State.ACTIVE)],
        [
          cond(clockRunning(decayClock), stopClock(decayClock)),
          set(shouldStopDecay, 0),
          set(translateY, clamp(add(offsetY, gestureY), translateYMax, 0)),
        ]
      ),
    ]
  }, [
    containerHeight,
    contentHeight,
    commodityTabCurrentTab,
    translateY,
    translateYMax,
    childTranslateYMaxReady,
  ])

  // onscroll event
  useCode(() => {
    if (typeof onScroll === 'function') {
      return [
        onChange(
          [selfScrollValue],
          [
            call([selfScrollValue], ([selfScrollValue]) => {
              onScroll(selfScrollValue)
            }),
          ]
        ),
      ]
    } else {
      return []
    }
  }, [onScroll])

  const gotoBottom = useCallback(() => {
    translateY.setValue(selfScrollMax)
    offsetY.setValue(selfScrollMax)
  }, [translateY, offsetY])

  useImperativeHandle(reScrollViewRef, () => {
    return {
      gotoBottom,
    }
  })

  const renderContent = () => {
    return (
      <PanGestureHandler
        {...PanGestureHandlerProps}
        ref={ref}
        onGestureEvent={handleGestureEvent}
        onHandlerStateChange={handleGestureEvent}
        minDist={10}
        failOffsetX={[-10, 10]}
        shouldCancelWhenOutside={false}
      >
        <Animated.View
          collapsable={false}
          style={{ width: '100%', height: '100%' }}
        >
          <Animated.View
            onLayout={handleContentLayout}
            collapsable={false}
            style={StyleSheet.flatten([
              styles.contentWrapper,
              contentContainerStyle,
              {
                transform: [
                  {
                    translateY: selfScrollValue,
                  },
                ],
              },
            ])}
          >
            <ReScrollViewContext.Provider value={reScrollViewContextValue}>
              {children}
            </ReScrollViewContext.Provider>
          </Animated.View>
        </Animated.View>
      </PanGestureHandler>
    )
  }

  const handleTapEvent = (event: TapGestureHandlerStateChangeEvent) => {
    if (event.nativeEvent.state === State.BEGAN) {
      gestureState.setValue(State.BEGAN)
    }
  }

  if (isAndroid) {
    return (
      <View
        onLayout={handleContainerLayout}
        style={StyleSheet.flatten([styles.containerWrapper, style])}
      >
        {renderContent()}
      </View>
    )
  } else {
    return (
      <TapGestureHandler onHandlerStateChange={handleTapEvent}>
        <View
          onLayout={handleContainerLayout}
          style={StyleSheet.flatten([styles.containerWrapper, style])}
        >
          {renderContent()}
        </View>
      </TapGestureHandler>
    )
  }
})

const styles = StyleSheet.create({
  contentWrapper: {
    width: '100%',
    position: 'absolute',
  },
  containerWrapper: {
    width: '100%',
    overflow: 'hidden',
  },
})

export default React.memo(Index, isEqual)
