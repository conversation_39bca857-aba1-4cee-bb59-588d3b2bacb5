import React from 'react';
import { State } from 'react-native-gesture-handler';
import Animated, { Clock } from 'react-native-reanimated';





export type ReScrollViewContextValue = {
  isReScrollViewParentOnTop: Animated.Adaptable<0 | 1>
  isReScrollViewParentOnBottom: Animated.Adaptable<0 | 1>
  translateY: Animated.Value<number>
  scrollViewName?: string
  onSync?: (arg: { gestureYToSync: Animated.Value<number>, gestureStateToSync: Animated.Value<State>, velocityYToSync: Animated.Value<number> }) => Animated.Node<number>,
  decayValue: Animated.Node<number>,
  parentScrollMax: Animated.Node<number>,
  velocityY: Animated.Value<number>,
  gestureY: Animated.Value<number>,
  gestureState: Animated.Value<number>,
  shouldStopDecay: Animated.Value<0 | 1>,
  translateYMax: Animated.Value<number>,
  decayClock: Clock,
  parentContainerHeight: number,
  initTranslateYMaxSet: (index: number, value: Animated.Adaptable<number>) => void,
  childTranslate: Animated.Value<number>[],
}

export const ReScrollViewContext = React.createContext<ReScrollViewContextValue>({
  isReScrollViewParentOnTop: new Animated.Value(0),
  isReScrollViewParentOnBottom: new Animated.Value(0),
  translateY: new Animated.Value(0),
  onSync: () => new Animated.Value(0),
  decayValue: new Animated.Value(0),
  parentScrollMax: new Animated.Value(0),
  velocityY: new Animated.Value(0),
  gestureY: new Animated.Value(0),
  gestureState: new Animated.Value(0),
  decayClock: new Clock(),
  shouldStopDecay: new Animated.Value(0),
  translateYMax: new Animated.Value(0),
  parentContainerHeight: 0,
  initTranslateYMaxSet: () => { },
  childTranslate: [],
});