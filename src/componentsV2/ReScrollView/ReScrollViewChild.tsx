import isEqual from 'lodash.isequal'
import React, {
  forwardRef,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from 'react'
import {
  LayoutChangeEvent,
  StyleSheet,
  StyleProp,
  ViewStyle,
  View,
} from 'react-native'
import {
  PanGestureHandler,
  PanGestureHandlerProperties,
} from 'react-native-gesture-handler'
import Animated, {
  call,
  cond,
  Extrapolate,
  interpolate,
  onChange,
  useCode,
  useValue,
  add,
} from 'react-native-reanimated'
import { ReScrollViewContext } from './ReScrollViewContext'
// import { CanITouchContext, CanITouchContextValue } from './CanITouchContext'

type Props = {
  style?: StyleProp<ViewStyle>
  contentContainerStyle?: StyleProp<ViewStyle>
  PanGestureHandlerProps?: Omit<
    PanGestureHandlerProperties,
    'onGestureEvent' | 'onHandlerStateChange'
  >
  index: number
  renderFooter?: () => JSX.Element
  onScroll?: (offset?: number) => void
  children?: ReactNode
}

const debug = (any: any) =>
  call([], () => {
    console.log(any)
  })

const debugValue = (value: readonly Animated.Adaptable<any>[], msg?: any) =>
  call(value, (value) => {
    console.log('msg: ', msg, value)
  })

const ReScrollViewChild: React.FC<Props> = forwardRef((props, ref) => {
  const {
    index,
    children,
    style,
    contentContainerStyle,
    PanGestureHandlerProps,
    renderFooter,
    onScroll,
  } = props
  const {
    velocityY,
    gestureY,
    gestureState,
    parentScrollMax,
    initTranslateYMaxSet,
    childTranslate,
  } = useContext(ReScrollViewContext)

  const selfTranslateY = childTranslate[index] || useValue<number>(0)
  const [containerHeight, setContainerHeight] = useState<null | number>(null)
  const [contentHeight, setContentHeight] = useState<null | number>(null)
  const selfScrollMax = useValue<number>(0)
  const canScroll = useValue<0 | 1>(1)
  // const gestureX = useValue<number>(0)
  // const velocityX = useValue<number>(0)

  const calcSelfScrollMax = () => {
    console.log('calcSelfScrollMax child')
    if (containerHeight && contentHeight) {
      const diff = containerHeight - contentHeight
      if (diff >= 0) {
        selfScrollMax.setValue(0)
        canScroll.setValue(0)
      } else {
        selfScrollMax.setValue(diff)
        canScroll.setValue(1)
      }
      initTranslateYMaxSet(index as number, add(selfScrollMax, parentScrollMax))
    }
  }

  useCode(() => {
    return [debugValue([selfTranslateY], 'selfTranslateY333')]
  }, [])

  useEffect(() => {
    if (
      typeof containerHeight === 'number' &&
      typeof contentHeight === 'number'
    ) {
      calcSelfScrollMax()
    }
  }, [containerHeight, contentHeight])

  const handleGestureEvent = Animated.event([
    {
      nativeEvent: {
        translationY: gestureY,
        // translationX: gestureX,
        velocityY: velocityY,
        // velocityX: velocityX,
        state: gestureState,
      },
    },
  ])

  const handleContentLayout = (event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout
    setContentHeight(height)
  }

  const handleContainerLayout = (e: LayoutChangeEvent) => {
    const { height } = e.nativeEvent.layout
    setContainerHeight(height)
  }

  // 自身滑动距离插值
  const selfScrollValue = cond(
    canScroll,
    interpolate(selfTranslateY, {
      inputRange: [add(parentScrollMax, selfScrollMax), parentScrollMax],
      outputRange: [selfScrollMax, 0],
      extrapolate: Extrapolate.CLAMP,
    }),
    0
  )

  useCode(() => {
    if (typeof onScroll === 'function') {
      return [
        onChange(
          [selfScrollValue],
          [
            call([selfScrollValue], ([selfScrollValue]) => {
              onScroll(selfScrollValue)
            }),
          ]
        ),
      ]
    } else {
      return []
    }
  }, [onScroll])

  return (
    // <CanITouchContext.Provider value={ canITouchContextValue }>
    <View
      onLayout={handleContainerLayout}
      collapsable={false}
      style={StyleSheet.flatten([styles.containerWrapper, style])}
    >
      <PanGestureHandler
        {...PanGestureHandlerProps}
        onGestureEvent={handleGestureEvent}
        onHandlerStateChange={handleGestureEvent}
        minDist={10}
        failOffsetX={[-10, 10]}
        shouldCancelWhenOutside={false}
      >
        <Animated.View
          collapsable={false}
          style={{ width: '100%', height: '100%' }}
        >
          <Animated.View
            onLayout={handleContentLayout}
            collapsable={false}
            style={StyleSheet.flatten([
              styles.contentWrapper,
              {
                transform: [
                  {
                    translateY: cond(canScroll, selfScrollValue, 0),
                  },
                ],
              },
            ])}
          >
            <View
              // { ...panResponder.panHandlers }
              style={StyleSheet.flatten([
                { width: '100%' },
                contentContainerStyle,
              ])}
            >
              {children}
            </View>
            {renderFooter && renderFooter()}
          </Animated.View>
        </Animated.View>
      </PanGestureHandler>
    </View>
    // </CanITouchContext.Provider>
  )
})

const styles = StyleSheet.create({
  contentWrapper: {
    width: '100%',
    position: 'absolute',
  },
  containerWrapper: {
    width: '100%',
    overflow: 'hidden',
  },
})

export default React.memo(ReScrollViewChild, isEqual)
