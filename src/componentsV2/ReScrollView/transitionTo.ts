import Animated, { block, Clock, clockRunning, cond, Easing, multiply, not, set, spring, startClock, stopClock, timing } from "react-native-reanimated"

type Binary = 0 | 1

const SPRING_VELOCITY_SCALE = 1
const TRUE = 1
const FALSE = 0
const SPRING_CONFIG = {
  stiffness: 1000,
  damping: 500,
  mass: 3,
  overshootClamping: true,
  restDisplacementThreshold: 0.01,
  restSpeedThreshold: 1,
}
const TIMING_CONFIG = {
  duration: 200,
  easing: Easing.out(Easing.cubic),
}

function transitionTo ({ isSwipeGesture, position, progress, velocityY, transitionToClock }: { transitionToClock: Clock, velocityY: Animated.Value<number>, progress: Animated.Value<number>, isSwipeGesture: Animated.Value<Binary>, position: Animated.Value<number> }) {
  const toValue = new Animated.Value<number>(0)
  const frameTime = new Animated.Value<number>(0)
  const springVelocityScale = new Animated.Value<number>(SPRING_VELOCITY_SCALE)
  const initialVelocityForSpring = new Animated.Value<number>(0)
  const state = {
    position: progress,
    time: new Animated.Value<number>(0),
    finished: new Animated.Value<Binary>(FALSE),
  }

  return block([
    // init
    cond(clockRunning(transitionToClock), FALSE, [
      // Animation wasn't running before
      // Set the initial values and start the clock
      set(toValue, position),
      set(frameTime, 0),
      set(state.time, 0),
      set(state.finished, FALSE),
    ]),
    cond(isSwipeGesture, [
      cond(
        not(clockRunning(transitionToClock)),
        [
          set(initialVelocityForSpring, multiply(velocityY, springVelocityScale)),
          cond(not(clockRunning(transitionToClock)), startClock(transitionToClock)),
          spring(
            transitionToClock,
            { ...state, velocity: initialVelocityForSpring },
            { ...SPRING_CONFIG, toValue }
          ),
        ],
        timing(transitionToClock, { ...state, frameTime }, { ...TIMING_CONFIG, toValue })
      ),
    ]),
    cond(state.finished, [
      // Reset values
      set(isSwipeGesture, FALSE),
      set(velocityY, 0),
      // When the animation finishes, stop the clock
      stopClock(transitionToClock),
      // call([ progress ], (value) => {
      //   // props.onSnap && props.onSnap(value)
      // }),
    ]),
  ])
}

export default transitionTo