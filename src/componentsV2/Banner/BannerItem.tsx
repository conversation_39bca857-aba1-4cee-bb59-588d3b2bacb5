import { Touch } from '@xmly/rn-components';
import HeightFitImage from '../common/HeightFitImage';
import React from 'react'
import { TopSectionBanners } from 'servicesV2/getTopSectionInfo'
import getImageUri from 'utilsV2/getImageUri';
import { width } from 'constantsV2/dimensions';
import isEqual from 'lodash.isequal';
import { View } from 'react-native';
import { NativeViewGestureHandler } from 'react-native-gesture-handler';

type Props = {
  item: TopSectionBanners
  index: number
  onPress: (item: TopSectionBanners) => void
}

const BannerItem: React.FC<Props> = (props) => {
  function handlePress () {
    props.onPress(props.item)
  }
  return (
    <NativeViewGestureHandler>
      <View>
        <Touch onPress={ handlePress }>
          <HeightFitImage
            containerWidth={ width - 32 }
            fadeDuration={ 0 }
            source={ { uri: getImageUri(props.item.picUrl, { width: 780, height: 180 }) } }
            style={ { borderRadius: 10, overflow: 'hidden' } }
            resizeMethod='resize'
          />
        </Touch>
      </View>
    </NativeViewGestureHandler>
  );
}

export default React.memo(BannerItem, isEqual)