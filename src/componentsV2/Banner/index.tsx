import { safetyToString } from '@xmly/rn-utils'
import { isIOS } from '@xmly/rn-utils/dist/device'
import { width } from 'constantsV2/dimensions'
import React, { useCallback, useRef, useState } from 'react'
import { NativeModules, Platform, StyleProp, StyleSheet, View, ViewStyle } from 'react-native'
import { PanGestureHandler, ScrollView } from 'react-native-gesture-handler'
import Animated from 'react-native-reanimated'
import Carousel, { Pagination } from 'react-native-snap-carousel'
import { TopSectionBanners } from 'servicesV2/getTopSectionInfo'
import getUrlToOpen from 'utilsV2/getUrlToOpen'
import goScoreMarket2 from 'utilsV2/goScoreMarket2'
import xmlog from 'utilsV2/xmlog'
import BannerItem from './BannerItem'

const { Page } = NativeModules

type Props = {
  list: TopSectionBanners[]
}

const bannerHeight = Platform.isPad ? 0.224 * width : 84

const index: React.FC<Props> = props => {
  const [ current, setCurrent ] = useState(0)
  const handleItemPress = useCallback(async item => {
    const { type, jumpUrl } = item
    if (Number(type) === 1) {
      // 打开H5
      Page.start(getUrlToOpen(jumpUrl))
    } else {
      // 兑吧
      goScoreMarket2(jumpUrl)
    }
    xmlog.click(30545, undefined, {
      url: safetyToString(jumpUrl),
      currPage: '任务中心'
    })
  }, [])

  const _renderItem = ({
    item,
    index
  }: {
    item: TopSectionBanners
    index: number
  }) => {
    return <BannerItem item={ item } index={ index } onPress={ handleItemPress } />
  }

  const handleSnapToItem = useCallback((index) => {
    setCurrent(index)
  }, [])

  const slideStyle = useRef<StyleProp<ViewStyle>>({
    paddingHorizontal: 32,
    alignItems: 'center',
    justifyContent: 'center'
  }).current

  const handleGetItemLayout = useCallback((data: TopSectionBanners[], index: number) => ({ length: width, offset: width * index, index }), [])

  const renderContent = () => {
    return (
      <>
        <ScrollView scrollEnabled={ false } contentContainerStyle={ { height: bannerHeight } }>
          <Carousel
            lockScrollWhileSnapping
            data={ props.list }
            renderItem={ _renderItem }
            sliderWidth={ width }
            itemWidth={ width }
            autoplay
            onSnapToItem={ handleSnapToItem }
            loop
            removeClippedSubviews={ false }
            slideStyle={ slideStyle }
            getItemLayout={ handleGetItemLayout }
          />
        </ScrollView>
        <Pagination
          dotsLength={ props.list.length }
          activeDotIndex={ current }
          dotContainerStyle={ styles.dotContainerStyle }
          containerStyle={ styles.containerStyle }
          dotStyle={ styles.dotStyle }
          inactiveDotOpacity={ 0.2 }
          inactiveDotScale={ 1 }
        />
      </>
    )
  }

  if (isIOS) {
    return (
      <PanGestureHandler>
        <Animated.View style={ { marginBottom: 16, height: bannerHeight, alignItems: 'center', justifyContent: 'center' } }>
          {
            renderContent()
          }
        </Animated.View>
      </PanGestureHandler>
    )
  } else {
    return (
      <View style={ { marginBottom: 16, height: bannerHeight, alignItems: 'center', justifyContent: 'center' } }>
        {
          renderContent()
        }
      </View>
    )
  }


}

const styles = StyleSheet.create(
  {
    dotContainerStyle: {
      marginHorizontal: 4
    },
    containerStyle: {
      position: 'absolute',
      zIndex: 2,
      bottom: 10,
      backgroundColor: 'rgba(0,0,0,.3)',
      paddingHorizontal: 4,
      paddingVertical: 2,
      borderRadius: 5,
    },
    dotStyle: {
      width: 5,
      height: 5,
      marginHorizontal: 0,
      paddingHorizontal: 0,
      borderRadius: 5,
      backgroundColor: 'rgba(255, 255, 255, 0.5)'
    }
  }
)

export default React.memo(index)
