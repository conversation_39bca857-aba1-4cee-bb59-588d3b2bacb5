import React, { useEffect } from 'react'
import CategoryItem from './CategoryItem'
import { RootState } from 'store'
import { connect } from 'react-redux'
import CommodityCoverLazyControl from './CommodityCoverLazyControl'
import SectionShadow from '../common/SectionShadow.android'
import { CommodityDisplayTabs } from '../../typesV2/commodity'
import isEqual from 'lodash.isequal'

type Props = {
  displayTabs: CommodityDisplayTabs
}

const index: React.FC<Props> = (props) => {
  const { displayTabs } = props
  useEffect(() => {
    return () => {
      CommodityCoverLazyControl.checker.dispose({ abort: true })
    }
  }, [])

  if (displayTabs && Array.isArray(displayTabs) && displayTabs.length > 0) {
    return (
      <>
        <SectionShadow
          containerViewStyle={styles.containerViewStyle}
          offset={[0, 7]}
          distance={22}
        >
          <CategoryItem />
        </SectionShadow>
      </>
    )
  }
  return null
}

const styles = {
  containerViewStyle: {
    flex: 1,
    marginHorizontal: 12,
  },
}

const mapStateToProps = (state: RootState) => {
  return {
    displayTabs: state.commodity.displayTabs,
  }
}

export default connect(mapStateToProps)(React.memo(index, isEqual))
