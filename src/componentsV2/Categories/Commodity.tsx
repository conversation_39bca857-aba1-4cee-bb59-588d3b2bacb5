import { BetterImage, Text, Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import React, { useCallback, useEffect, useState } from 'react'
import { Image, View, Text as RNText, Dimensions } from 'react-native'
import goScoreMarket2 from 'utilsV2/goScoreMarket2'
import styled from 'styled-components'
import xmlog from 'utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import CommodityCoverLazyControl from './CommodityCoverLazyControl'
import icon_money from '../../appImagesV2/icon_money'
import ActivityCommodityCountdown from './ActivityCommodityCountdown'
import { store } from '../../store'
import getActivityCommodityItemStatus from './getActivityCommodityItemStatus'
import {
  ActivityCommodityItemStatus,
  CommodityDisplayProductItem,
} from '../../typesV2/commodity'
import { ThemeStyle } from '../../typesV2/themeInfo'
import getUrlToOpen from '../../utilsV2/getUrlToOpen'
import { Page, Toast } from '@xmly/rn-sdk'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import SentryUtils from '../../utilsV2/sentryUtils'
import { defaultCommodityCover } from '../../constantsV2'

type Props = {
  commodity: CommodityDisplayProductItem
  index: number
  onClickEventReport?: (commodity: CommodityDisplayProductItem) => void
  onShowEventReport?: (commodity: CommodityDisplayProductItem) => void
}


const CountdownWrapper = styled(View)`
  align-items: center;
  justify-content: center;
  flex-direction: row;
`

const CountDownUnit = styled(Text)`
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  font-size: 11px;
  margin-right: 5px;
`

const ItemWrapper = styled(Touch)`
  flex-shrink: 0;
  align-items: center;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.item_bg_color};
  margin: 0 0 12px 0;
  border-radius: 8px;
  padding-bottom: 11px;
`

const ItemDetailInfoWrapper = styled(View)`
  padding: 0 12px;
  width: 100%;
`

const ItemCoverWrapper = styled(View)`
  border-radius: 8px;
  width: 100%;
  overflow: hidden;
`

const ItemCover = styled(BetterImage)`
  width: 100%;
  aspect-ratio:1;
`

const ItemLabelWrapper = styled(View)`
  margin-top: 8px;
  width: 100%;
`

const ItemCreditIcon = styled(Image)`
  height: 18px;
  width: 18px;
  margin-left: -2px;
`

const ItemLabel = styled(Text)`
  font-size: 15px;
  text-align: left;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  width: 100%;
  line-height: 17px;
`

const ItemPriceWrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-right: auto;
  margin-top: 8px;
`

const ActivityItemStatusWrapper = styled(View)`
  margin-top: 6px;
  flex-direction: row;
  align-items: center;
  margin-right: auto;
`
const ItemPrice = styled(RNText)`
  color: #ff4646;
  font-size: 21px;
  font-weight: 700;
`
const ItemPricePlus = styled(Text)`
  margin-left: 3px;
  color: #999999;
  font-size: 16px;
`
const TagIconHeight = 16
const ItemTagWrapper = styled(View)`
  margin-top: 4px;
  height: ${TagIconHeight}px;
  width: 100%;
`

const ItemIcon = styled(BetterImage)`
  height: ${TagIconHeight}px;
`

const Commodity: React.FC<Props> = (props) => {
  const { commodity, index, onClickEventReport, onShowEventReport } = props
  const [nowTime, setNowTime] = useState(Date.now())
  const [tagIconWidth, setTagIconWidth] = useState(0)
  const showIcon =
    typeof commodity.cornerImgUrl !== undefined && !!commodity.cornerImgUrl
  const coverUri = { uri: commodity.commodityCover || defaultCommodityCover }
  const handleItemPress = useCallback(() => {
    if (commodity?.commodityUrl) {
      if (commodity?.commodityUrl?.includes('duiba.com')) {
        goScoreMarket2(commodity.commodityUrl, true)
      } else {
        Page.start(getUrlToOpen(commodity.commodityUrl, { asH5: true }))
      }
    } else {
      Toast.info('跳转失败～')
    }
    if (typeof onClickEventReport === 'function') {
      onClickEventReport(commodity)
    } else {
      const {
        commodity: { currentTab, displayTabs },
      } = store.getState()
      xmlog.click(30549, undefined, {
        currPage: '任务中心',
        productId: safetyToString(commodity.id),
        tabId: safetyToString(displayTabs[currentTab]?.id),
        activityName: safetyToString(commodity.commodityName),
      })
    }
  }, [commodity, onClickEventReport])

  useEffect(() => {
    getIconWidth()
  }, [showIcon, commodity.cornerImgUrl])

  const getIconWidth = async () => {
    if (showIcon && typeof commodity.cornerImgUrl === 'string') {
      try {
        Image.getSize(
          commodity.cornerImgUrl,
          (width, height) => {
            const scaleWidth = TagIconHeight * (width / height)
            setTagIconWidth(scaleWidth)
          },
          () => {
            setTagIconWidth(46)
          }
        )
      } catch (err) {
        setTagIconWidth(46)
      }
    }
  }

  const handleCountdownTimeout = useCallback(() => {
    setNowTime(Date.now())
  }, [])

  const handleTimeout = useCallback(async () => {
    await store.dispatch.commodity.initProducts({ isRefresh: true })
    handleCountdownTimeout()
  }, [])

  const renderCountDown = () => {
    const { startTime, endTime } = commodity
    if (typeof startTime === 'number' && typeof endTime === 'number') {
      const { count, status } = getActivityCommodityItemStatus(
        { startTime, endTime },
        nowTime
      )
      if (
        (status === ActivityCommodityItemStatus.ing ||
          status === ActivityCommodityItemStatus.notStart) &&
        count > 0
      ) {
        return (
          <CountdownWrapper>
            {status === ActivityCommodityItemStatus.ing && (
              <CountDownUnit
                style={[
                  { textAlignVertical: 'center', includeFontPadding: false },
                ]}
              >
                距结束
              </CountDownUnit>
            )}
            {status === ActivityCommodityItemStatus.notStart && (
              <CountDownUnit
                style={[
                  { textAlignVertical: 'center', includeFontPadding: false },
                ]}
              >
                距开始
              </CountDownUnit>
            )}
            <ActivityCommodityCountdown
              count={count}
              onTimeout={handleTimeout}
              key={nowTime}
            />
          </CountdownWrapper>
        )
      }
      if (status === ActivityCommodityItemStatus.finished) {
        return (
          <CountDownUnit style={[{ color: '#999999' }]}>已结束</CountDownUnit>
        )
      }
      return null
    }
    return null
  }

  const onSingleTap = () => {
    handleItemPress()
  }

  const onShowHandler = useCallback(() => {
    if (typeof onShowEventReport === 'function') {
      onShowEventReport(commodity)
    } else {
      try {
        const { commodity: commodityState } = store.getState()
        // 任务中心-独立商品  控件曝光
        xmlog.event(44533, 'slipPage', {
          productId: safetyToString(commodity.id),
          tabId: safetyToString(
            commodityState?.displayTabs[commodityState?.currentTab]?.id
          ),
          activityName: safetyToString(commodity.commodityName),
          currPage: '任务中心',
        })
      } catch (err) {
        console.log(err)
        SentryUtils.captureException(err, { source: 'commodity.onShowHandler' })
      }
    }
  }, [commodity, onShowEventReport])

  return (
    <ItemWrapper accessible onPress={onSingleTap}>
      <ItemCoverWrapper>
        <ItemCover
          source={coverUri}
          fadeDuration={0}
          resizeMode='cover'
          resizeMethod='resize'
          lazy
          lazyGroup={CommodityCoverLazyControl.groupName}
        />
      </ItemCoverWrapper>
      <ItemDetailInfoWrapper>
        <ItemLabelWrapper>
          <ItemLabel numberOfLines={2}>{commodity.commodityName}</ItemLabel>
        </ItemLabelWrapper>
        {showIcon && tagIconWidth !== 0 ? (
          <ItemTagWrapper collapsable={false}>
            <ItemIcon
              style={[{ width: tagIconWidth }]}
              fadeDuration={0}
              resizeMethod='resize'
              resizeMode='contain'
              source={{ uri: commodity.cornerImgUrl }}
            />
          </ItemTagWrapper>
        ) : null}

        {commodity.isActivity ? (
          <ActivityItemStatusWrapper>
            {renderCountDown()}
          </ActivityItemStatusWrapper>
        ) : null}

        <ItemPriceWrapper>
          <ItemCreditIcon source={icon_money} />
          <ItemPrice
            style={{
              includeFontPadding: false,
              textAlignVertical: 'center',
              fontFamily: 'XmlyNumber',
            }}
          >
            {commodity.exchangePrice}
          </ItemPrice>
          {commodity.exchangeMoney && commodity.exchangeMoney !== '0' ? (
            <>
              <ItemPricePlus>+</ItemPricePlus>
              <ItemPrice
                style={{
                  marginLeft: 4,
                  includeFontPadding: false,
                  textAlignVertical: 'center',
                  fontFamily: 'XmlyNumber',
                }}
              >
                <ItemPrice
                  style={{ fontSize: 16, fontFamily: 'XmlyNumber' }}
                >
                  ¥{' '}
                </ItemPrice>
                {commodity.exchangeMoney}
              </ItemPrice>
            </>
          ) : null}
        </ItemPriceWrapper>
      </ItemDetailInfoWrapper>
      <ScrollAnalyticComp
        key={(commodity.commodityName + index).toString()}
        // 每个元素设置一个唯一的key值
        itemKey={(commodity.commodityName + index).toString()}
        // 曝光事件处理
        onShow={onShowHandler}
      />
    </ItemWrapper>
  )
}

export default React.memo(Commodity, isEqual)
