import { Touch, Text } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import React, { useContext } from 'react'
import { LayoutChangeEvent, LayoutRectangle } from 'react-native'
import styled from 'styled-components'
import { ThemeContext } from '../../contextV2/themeContext'

type TabBarItemProps = {
  tabId: number
  tabLabel: string
  onPress: (index: number) => void
  index: number
  currentTabIndex: number
  setTabBarLayout?: ({
    index,
    data,
  }: {
    index: number
    data: LayoutRectangle
  }) => void
}

const TabBarButton = styled(Touch)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0 14px;
  flex-shrink: 0;
  height: 100%;
`

const TabBarLabel = styled(Text)`
  text-align: center;
  align-items: center;
  justify-content: center;
`

const TabBarItem: React.FC<TabBarItemProps> = (props) => {
  const { tabId, tabLabel, onPress, index, setTabBarLayout, currentTabIndex } =
    props
  const theme = useContext(ThemeContext)

  const handlePress = () => {
    onPress(index)
  }

  const handleLayout = (event: LayoutChangeEvent) => {
    typeof setTabBarLayout === 'function' &&
      setTabBarLayout({ index, data: event.nativeEvent.layout })
  }

  return (
    <TabBarButton
      activeOpacity={1}
      onPress={handlePress}
      onLayout={handleLayout}
      hitSlop={{ top: 30, bottom: 30 }}
    >
      <TabBarLabel
        style={{
          fontWeight: currentTabIndex === index ? '700' : '400',
          color:
            currentTabIndex === index
              ? theme.categories.tabBar.label_active_color
              : theme.categories.tabBar.label_color,
          fontSize: currentTabIndex === index ? 15 : 14,
        }}
        key={tabId}
      >
        {tabLabel}
      </TabBarLabel>
    </TabBarButton>
  )
}

export default React.memo(TabBarItem, isEqual)
