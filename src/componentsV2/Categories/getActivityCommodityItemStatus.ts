import { ActivityCommodityItemStatus } from "../../typesV2/commodity"

const getActivityCommodityItemStatus = ({ startTime, endTime }: { startTime: number, endTime: number }, nowTime: number): { count: number; status: ActivityCommodityItemStatus } => {
  let count = 0
  let status: ActivityCommodityItemStatus = ActivityCommodityItemStatus.soldOut
  if (nowTime < startTime) {
    // 即将开始
    count = startTime - nowTime
    status = ActivityCommodityItemStatus.notStart
  } else if (nowTime >= startTime && nowTime <= endTime) {
    //马上抢
    count = endTime - nowTime
    status = ActivityCommodityItemStatus.ing
  } else {
    //活动结束
    status = ActivityCommodityItemStatus.finished
  }
  return { status, count }
}


export default getActivityCommodityItemStatus