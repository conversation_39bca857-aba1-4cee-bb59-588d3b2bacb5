import React, { useMemo } from 'react'
import { StyleSheet, View } from 'react-native'
import isEqual from 'lodash.isequal'
import Commodity from './Commodity'
import { CommodityDisplayProductItem } from '../../typesV2/commodity'
import { useSelector } from 'react-redux'
import { RootState } from '../../store'
import CommodityBanner from './CommodityBanner'

type Props = {}

const CategoryItem: React.FC<Props> = () => {
  const displayProducts = useSelector(
    (state: RootState) => state.commodity.displayProducts
  )
  const renderItem = ({
    item,
    index,
  }: {
    item: CommodityDisplayProductItem
    index: number
  }) => {
    return (
      <Commodity
        commodity={item}
        key={item.id + item.commodityName}
        index={index}
      />
    )
  }

  const getColumn = useMemo(
    () => (numberOfN: number, colIndex: number) => {
      return displayProducts.filter(
        (_, index) => index % numberOfN === colIndex
      )
    },
    [displayProducts]
  )

  return (
    <View style={styles.container}>
      <View>
        <CommodityBanner />
        {getColumn(2, 0).map((commodity, index) =>
          renderItem({ item: commodity, index })
        )}
      </View>
      <View>
        {getColumn(2, 1).map((commodity, index) =>
          renderItem({ item: commodity, index })
        )}
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginTop: 12,
  },
})

export default React.memo(CategoryItem, isEqual)
