import React, { Suspense } from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState } from '../../../store'

type Props = {}

const CommodityBannerLazy = React.lazy(() => import('./CommodityBanner.lazy'))

const CommodityBanner: React.FC<Props> = (props) => {
  const commodityBanner = useSelector(
    (state: RootState) => state.commodity.commodityBanner
  )
 

  if (commodityBanner.length > 0) {
    return (
      <Suspense fallback={null}>
        <CommodityBannerLazy commodityBanner={commodityBanner} />
      </Suspense>
    )
  } else {
    return null
  }
}

export default React.memo(CommodityBanner, isEqual)
