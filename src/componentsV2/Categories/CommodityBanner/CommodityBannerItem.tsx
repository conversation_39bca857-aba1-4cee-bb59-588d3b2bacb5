import React from 'react'
import { BetterImage, Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import { CommodityBannerItem } from '../../../typesV2/commodity'
import styled from 'styled-components'
import { CommodityItemWidth } from '../../../constantsV2/layout'
import { Page } from '@xmly/rn-sdk'
import getUrlToOpen from '../../../utilsV2/getUrlToOpen'

type Props = {
  item: CommodityBannerItem
  height: number
}

const Wrapper = styled(Touch)`
  width: ${CommodityItemWidth}px;
`

const Cover = styled(BetterImage)`
  width: ${CommodityItemWidth}px;
  position: absolute;
  top: 0;
  left: 0;
`

const CommodityBannerItemUI: React.FC<Props> = ({ item, height }) => {
  const handlePress = () => {
    if (item.link) {
      Page.start(getUrlToOpen(item.link))
    }
  }

  return (
    <Wrapper style={{ height }} onPress={handlePress} disabled={!item.link}>
      <Cover
        style={{ height }}
        source={{ uri: item.cover }}
        resizeMethod='resize'
      />
    </Wrapper>
  )
}

export default React.memo(CommodityBannerItemUI, isEqual)
