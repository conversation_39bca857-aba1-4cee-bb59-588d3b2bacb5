import { Text } from '@xmly/rn-components'
import React from 'react'
import { View } from 'react-native'
import styled from 'styled-components'
import { ThemeStyle } from '../../typesV2/themeInfo'

type Props = {
  count: number
  onTimeout?: () => void
}

const Wrapper = styled(View)`
  flex-direction: row;
  align-items: center;
`

const CountdownTextWrapper = styled(View)`
  width: 14px;
  height: 14px;
  background: ${({ theme }: { theme: ThemeStyle }) =>
    theme.categories.commodity.countdown_wrapper_bg};
  border-radius: 2px;
  align-items: center;
  justify-content: center;
`

const CountdownText = styled(Text)`
  color: #ff4646;
  font-size: 9px;
  text-align: center;
`

const CountdownTextIcon = styled(CountdownText)`
  color: #666666;
  font-size: 10px;
  margin: 0 2px;
`

const dayTs = 1000 * 3600 * 24

function parseNum(num: number) {
  return num < 10 ? '0' + num : num
}

const parseTimeStamp = (ts: number) => {
  if (typeof ts !== 'number') return {}
  const d = parseInt(String(ts / dayTs), 10)
  const h = parseInt(String((ts % dayTs) / 3600000), 10)
  const m = parseInt(String((ts % 3600000) / 60000), 10)
  const s = parseInt(String((ts % 60000) / 1000), 10)
  const ms = parseInt(String(ts % 1000))
  return {
    d: parseNum(d),
    h: parseNum(h),
    m: parseNum(m),
    s: parseNum(s),
    ms: parseNum(ms),
  }
}

const tick = 1000
class ActivityCommodityCountdown extends React.Component<
  Props,
  { countTime: number }
> {
  constructor(props: Props) {
    super(props)
    this.state = {
      countTime: props.count,
    }
  }

  timer: NodeJS.Timeout | null = null

  componentDidMount() {
    this.start()
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer)
  }

  start = () => {
    if (this.state.countTime <= 0) {
      typeof this.props.onTimeout === 'function' && this.props.onTimeout()
    } else {
      this.timer = setTimeout(() => {
        this.setState((state) => ({
          countTime: state.countTime - tick,
        }))
        this.start()
      }, tick)
    }
  }

  render() {
    const timeParams = parseTimeStamp(this.state.countTime)
    const { h = 0, m = 0, s = 0 } = timeParams || {}
    return (
      <Wrapper>
        <CountdownTextWrapper>
          <CountdownText
            style={[{ textAlignVertical: 'center', includeFontPadding: false }]}
          >
            {h}
          </CountdownText>
        </CountdownTextWrapper>
        <CountdownTextIcon
          style={[{ textAlignVertical: 'center', includeFontPadding: false }]}
        >
          :
        </CountdownTextIcon>
        <CountdownTextWrapper>
          <CountdownText
            style={[{ textAlignVertical: 'center', includeFontPadding: false }]}
          >
            {m}
          </CountdownText>
        </CountdownTextWrapper>
        <CountdownTextIcon
          style={[{ textAlignVertical: 'center', includeFontPadding: false }]}
        >
          :
        </CountdownTextIcon>
        <CountdownTextWrapper>
          <CountdownText
            style={[{ textAlignVertical: 'center', includeFontPadding: false }]}
          >
            {s}
          </CountdownText>
        </CountdownTextWrapper>
      </Wrapper>
    )
  }

  shouldComponentUpdate(nextProps: Props, nextState: { countTime: number }) {
    return (
      nextProps.count !== this.props.count ||
      nextState.countTime !== this.state.countTime
    )
  }
}

export default ActivityCommodityCountdown
