import React, { useContext, useEffect } from 'react'
import isEqual from 'lodash.isequal'
import { RootState, store } from '../../store'
import { useSelector } from 'react-redux'
import GrayScaleContext from '../../contextV2/grayScaleContext'
import { useAtomValue } from 'jotai'
import taskAtom from '../../components/CashTask/store/task'
import { hitTraitsAtom } from '../../components/CashTask/store/hitTraits'
type Props = {}

const EverydayChallengeV2Lazy = React.lazy(() => import('../EverydayChallengeV2/EverydayChallenge.lazy'))

const Index: React.FC<Props> = () => {
  const { taskList, summaryTask } = useSelector((state: RootState) => state.everydayChallenge)
  const { showGoldCoinModule } = useContext(GrayScaleContext)
  const cashTask = useAtomValue(taskAtom);
  const hitTraits = useAtomValue(hitTraitsAtom);

  useEffect(() => {
    if (showGoldCoinModule) {
      store.dispatch.everydayChallenge.getTaskList()
    }
  }, [showGoldCoinModule])

  if (showGoldCoinModule && taskList.length > 0 && summaryTask && summaryTask?.stepInfos && summaryTask?.stepInfos?.length > 0) {
    return (
      <React.Suspense fallback={null}>
        {cashTask || hitTraits ? null : <EverydayChallengeV2Lazy />}
      </React.Suspense>
    )
  }
  return null
}

export default React.memo(Index, isEqual)
