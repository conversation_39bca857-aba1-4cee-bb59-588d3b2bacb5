import React from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import SummaryTaskList from './SummaryTaskList'
import { LayoutChangeEvent, View } from 'react-native'
import { EverydayChallengeGuideStep } from '../../modelsV2/everydayChallenge'

type Props = {}

const Body: React.FC<Props> = () => {
  const summaryTask = useSelector((state: RootState) => state.everydayChallenge.summaryTask)

  const handleItemLayout = ({ index, itemRef }: { index: number; event: LayoutChangeEvent; itemRef: React.RefObject<View | undefined> }) => {
    if (summaryTask.stepInfos && summaryTask.stepInfos?.length > 0 && index === summaryTask.stepInfos?.length - 1 && itemRef.current && itemRef.current.measure) {
      itemRef.current.measure(async (x, y, width, height, pageX, pageY) => {
        await store.dispatch.everydayChallenge.setLayoutInfo({
          name: EverydayChallengeGuideStep.A,
          info: { x: pageX, y: pageY - 3, width: width, height: height + 6, radius: 10 },
        })
        store.dispatch.everydayChallenge.judgeShowGuide({})
      })
    }
  }

  if (summaryTask.stepInfos && summaryTask.stepInfos?.length > 0) {
    return <SummaryTaskList isInModal={false} onItemLayout={handleItemLayout} />
  }
  return null
}

export default React.memo(Body, isEqual)
