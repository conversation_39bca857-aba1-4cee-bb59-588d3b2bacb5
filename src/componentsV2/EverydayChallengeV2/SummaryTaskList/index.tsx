import React, { useState } from 'react'
import { View, LayoutChangeEvent, StyleProp, ViewStyle } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { useSelector } from 'react-redux'
import { RootState } from '../../../store'
import SummaryTaskItem from './SummaryTaskItem'

type Props = {
  containerStyle?: StyleProp<ViewStyle>
  itemMargin?: number
  itemHeight?: number
  defaultWrapperSize?: number
  onItemShowEventMetaId?: number
  onItemPressEventMetaId?: number
  awardLabelFontSize?: number
  totalConditionLabelFontSize?: number
  isInModal?: boolean
  onItemLayout?: (args: { index: number; event: LayoutChangeEvent; itemRef: React.RefObject<View | undefined> }) => void
}

const TaskItemWrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin: 0 3px;
  position: relative;
`
const SummaryTaskList: React.FC<Props> = ({
  containerStyle,
  itemMargin,
  itemHeight,
  defaultWrapperSize,
  onItemShowEventMetaId,
  onItemPressEventMetaId,
  awardLabelFontSize,
  totalConditionLabelFontSize,
  isInModal,
  onItemLayout,
}) => {
  const [itemWrapperWidth, setItemWrapperWidth] = useState(defaultWrapperSize ?? 330)
  const summaryTask = useSelector((state: RootState) => state.everydayChallenge.summaryTask)

  const progress = useSelector((state: RootState) => state.everydayChallenge.progress)

  const handleLayout = (event: LayoutChangeEvent) => {
    setItemWrapperWidth(event.nativeEvent.layout.width)
  }

  const mItemMargin = itemMargin ?? 3
  const itemCount = summaryTask?.stepInfos?.length || 3
  const itemWidth = itemWrapperWidth / itemCount

  if (summaryTask?.stepInfos && summaryTask?.stepInfos.length && summaryTask?.stepInfos?.length > 0) {
    return (
      <TaskItemWrapper onLayout={handleLayout} style={containerStyle}>
        {summaryTask.stepInfos.map((step, index) => {
          return (
            <SummaryTaskItem
              onShowEventMetaId={onItemShowEventMetaId}
              onPressEventMetaId={onItemPressEventMetaId}
              itemHeight={itemHeight}
              itemMargin={mItemMargin}
              taskItem={summaryTask}
              awardLabelFontSize={awardLabelFontSize}
              totalConditionLabelFontSize={totalConditionLabelFontSize}
              index={index}
              key={index}
              width={itemWidth}
              item={step}
              progress={progress}
              itemCount={itemCount}
              isInModal={isInModal}
              isLast={index === summaryTask.stepInfos!.length - 1}
              onLayout={onItemLayout}
            />
          )
        })}
      </TaskItemWrapper>
    )
  }
  return null
}

export default React.memo(SummaryTaskList, isEqual)
