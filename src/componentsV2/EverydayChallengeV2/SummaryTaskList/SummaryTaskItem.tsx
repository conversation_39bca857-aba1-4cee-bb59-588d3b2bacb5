import React, { useContext, useRef } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { Image, LayoutChangeEvent, View } from 'react-native'
import { StepInfo, TaskItemType, TaskStatus } from '../../../typesV2/taskList'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import xmlog from '../../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import { store } from '../../../store'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import getTaskStatusStr from '../../../utilsV2/getTaskStatusStr'
import TaskListModalControl from '../TaskListModal/TaskListModalControl'
import { Toast } from '@xmly/rn-sdk'
import getEverydayChallengeAidByAB from '../../../utilsV2/getEverydayChallengeAidByAB'
import { DarkModeContext } from '../../../contextV2/darkModeContext'

type Props = {
  width: number
  item: StepInfo
  progress: number
  index: number
  taskItem: TaskItemType
  itemMargin?: number
  itemHeight?: number
  onShowEventMetaId?: number
  onPressEventMetaId?: number
  awardLabelFontSize?: number
  totalConditionLabelFontSize?: number
  itemCount?: number
  isInModal?: boolean
  isLast?: boolean
  onLayout?: (args: { index: number; event: LayoutChangeEvent; itemRef: React.RefObject<View | undefined> }) => void
}

const TaskItem = styled(View)`
  position: relative;
`
const TaskWrapper = styled(Touch)`
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge.summaryTaskList.bg_item};
  flex: 1;
  overflow: hidden;
`
const TaskItemStatus = styled(View)`
  position: absolute;
  right: 0;
  top: 0;
  width: 38px;
  height: 14px;
  opacity: 0.8;
  justify-content: center;
  align-items: center;
  background: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge.summaryTaskList.bg_progress_bar};
  border-bottom-left-radius: 7px;
  border-top-right-radius: 5px;
  z-index: 1;
`
const TaskItemStatusLabel = styled(Text)`
  font-size: 8px;
  font-weight: 500;
  text-align: center;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge.summaryTaskList.text};
`
const TaskItemLabelWrapper = styled(View)`
  align-items: center;
  justify-content: center;
  margin-top: 3px;
`

const TaskItemLabel = styled(Text)`
  font-size: 14px;
  font-weight: 700;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge.summaryTaskList.text};
  text-align: center;
`

const TaskItemIcon = styled(Image)`
  width: 75%;
  height: 40%;
`

const ConditionText = styled(Text)`
  font-size: 11px;
  text-align: center;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge.summaryTaskList.sub_text};
`
const TaskItemProgress = styled(View)`
  position: absolute;
  bottom: 9px;
  width: 64px;
  height: 11px;
  border-radius: 100px;
  background-color: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge.summaryTaskList.bg_progress};
  overflow: hidden;
`
const TaskItemProgressLabel = styled(Text)`
  font-size: 8px;
  line-height: 11px;
  font-weight: normal;
  text-align: center;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge.summaryTaskList.text};
`
const TaskItemProgressBar = styled(View)`
  position: absolute;
  left: 0px;
  top: 0px;
  height: 11px;
  border-radius: 100px;
  background-color: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge.summaryTaskList.bg_progress_bar};
`

const getConditionText = (status: TaskStatus, condition: number) => {
  // if (status === TaskStatus.finished) {
  //   return '去领取'
  // }
  // if (status === TaskStatus.received) {
  //   return '已领取'
  // }

  // if (status === TaskStatus.unfinished) {
  return `完成${condition}个`
  // }

  // return '去完成'
}

const getTaskStatusText = (status: TaskStatus) => {
  if (status === TaskStatus.finished) {
    return '待领取'
  }
  if (status === TaskStatus.received) {
    return '已领取'
  }
  return '去完成'
}

const SummaryTaskItem: React.FC<Props> = ({
  width,
  item,
  index,
  taskItem,
  itemHeight,
  onPressEventMetaId,
  onShowEventMetaId,
  awardLabelFontSize,
  totalConditionLabelFontSize,
  itemCount,
  isInModal,
  onLayout,
  isLast,
}) => {
  const containerRef = useRef<View>(null)
  const conditionText = getConditionText(item.stepStatus, item.condition)
  const statusText = getTaskStatusText(item.stepStatus)
  const isReceiving = useRef(false)
  const { isDarkMode } = useContext(DarkModeContext)
  const showBG = isLast

  const bgSource = {
    uri: isDarkMode
      ? 'https://imagev2.xmcdn.com/storages/523e-audiofreehighqps/DF/04/GKwRIMAJNiYMAAAYuAKB8FJz.png'
      : 'https://imagev2.xmcdn.com/storages/a1f6-audiofreehighqps/F1/1A/GKwRIUEJNiqxAAAblQKB8qcx.png',
  }

  const eventReportProps = {
    currPage: '任务中心',
    positionNew: safetyToString(index + 1),
    rewardName: safetyToString(item.desc),
    status: getTaskStatusStr(item.stepStatus),
  }

  const mAwardLabelFontSize = { fontSize: awardLabelFontSize ?? 14 }
  const mTotalConditionLabelFontSize = {
    fontSize: totalConditionLabelFontSize ?? 11,
  }

  const onShowHandler = () => {
    // 任务中心-奖励卡片  控件曝光
    xmlog.event(onShowEventMetaId || 55484, 'slipPage', eventReportProps)
  }

  const handleOpenModal = () => {
    xmlog.click(55481, undefined, {
      currPage: '任务中心',
      Item: '去完成',
    })
    TaskListModalControl.open()
  }

  const _handleItemPress = async () => {
    if (isInModal) {
      // 弹窗中:点击领取奖励
      if (item.stepStatus === TaskStatus.finished || item.stepStatus === TaskStatus.received) {
        handleItemPress()
      }
    } else {
      // 首页列表中：
      if (item.stepStatus === TaskStatus.finished || item.stepStatus === TaskStatus.received) {
        handleItemPress()
      } else {
        handleOpenModal()
      }
    }
  }

  const handleItemPress = async () => {
    try {
      if (isReceiving.current) return
      isReceiving.current = true
      if (item.stepStatus === TaskStatus.finished) {
        // 任务中心-奖励卡片  点击事件
        xmlog.click(onPressEventMetaId || 55483, undefined, eventReportProps)
        const currentStep = taskItem?.stepInfos && taskItem?.stepInfos[index] // 拿到当前分步任务的子任务
        if (currentStep) {
          const aid = await getEverydayChallengeAidByAB()
          const isMultiAwards = currentStep.awards && currentStep.awards?.length > 1 // 如果当前子任务有多个 award，走多选一逻辑
          if (isMultiAwards) {
            // 处理多选一奖励
            store.dispatch.everydayChallenge.handleMultiAwardsSelect({
              taskItem,
              stepNos: [item.stepNo],
              aid,
              awards: currentStep.awards!,
            })
          } else {
            store.dispatch.everydayChallenge.getTaskAward({
              taskItem,
              stepNos: [item.stepNo],
              aid,
            })
          }
        }
      } else if (item.stepStatus === TaskStatus.received) {
        Toast.info('奖励已领取')
      }
    } catch (err) {
    } finally {
      isReceiving.current = false
    }
  }

  const handleLayout = (event: LayoutChangeEvent) => {
    if (!isInModal && typeof onLayout === 'function' && containerRef.current && containerRef.current.measure) {
      onLayout({ index, event, itemRef: containerRef })
    }
  }

  return (
    <ScrollAnalyticComp
      itemKey={'EverydayChallengeTaskItem' + item.stepNo}
      onShow={onShowHandler}
    >
      <View
        ref={containerRef}
        onLayout={handleLayout}
      >
        <TaskItem
          style={{
            width,
            height: itemHeight ?? 120,
          }}
        >
          <TaskWrapper
            style={{
              marginHorizontal: 3,
              borderRadius: 5,
            }}
            onPress={_handleItemPress}
            activeOpacity={1}
          >
            {(item.stepStatus === TaskStatus.finished || item.stepStatus === TaskStatus.received) && (
              <TaskItemStatus>
                <TaskItemStatusLabel>{statusText}</TaskItemStatusLabel>
              </TaskItemStatus>
            )}
            <TaskItemLabelWrapper>
              <TaskItemLabel style={mAwardLabelFontSize}>{item.desc}</TaskItemLabel>
              <ConditionText style={mTotalConditionLabelFontSize}>{conditionText}</ConditionText>
            </TaskItemLabelWrapper>
            <TaskItemIcon
              resizeMethod="resize"
              resizeMode="contain"
              source={{
                uri: item.awardCover,
              }}
            />
            {showBG ? (
              <Image
                style={{
                  width: width - 6,
                  height: itemHeight ?? 120,
                  position: 'absolute',
                  top: 0,
                  opacity: .8,
                }}
                resizeMode="stretch"
                source={bgSource}
              />
            ) : null}
            {!isInModal && itemCount === index + 1 && (
              <TaskItemProgress>
                <TaskItemProgressBar
                  style={{
                    width: `${taskItem.taskCompleteRate || 0}%`,
                  }}
                />
                <TaskItemProgressLabel>{(taskItem.taskCompleteRate || 0) > 99 ? '即将抢完' : `${taskItem.taskCompleteCount}人已领`}</TaskItemProgressLabel>
              </TaskItemProgress>
            )}
          </TaskWrapper>
        </TaskItem>
      </View>
    </ScrollAnalyticComp>
  )
}

export default React.memo(SummaryTaskItem, isEqual)
