import React, { useContext, useEffect, useRef, useState } from 'react'
import { ReSlideUpPanel, Text, Touch } from '@xmly/rn-components'
import { AccessibilityInfo, DeviceEventEmitter, findNodeHandle, Image, ScrollView, View } from 'react-native'
import isEqual from 'lodash.isequal'
import TaskListModalControl from './TaskListModalControl'
import styled from 'styled-components'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import icon_pc_close_dark from '../../../appImagesV2/icon_pc_close_dark'
import { ThemeContext } from '../../../contextV2/themeContext'
import TaskProgress from './TaskProgress'
import List from './List'
import { ScrollAnalyticWapper, ScrollEventSender } from '@xmly/react-native-page-analytics'
import { EverydayChallengeModalListID } from '../../../constantsV2'
import { useSelector } from 'react-redux'
import { RootState } from '../../../store'
import LinearGradient from 'react-native-linear-gradient'
import { hitSlop } from '@xmly/rn-utils'
import { height } from '../../../constantsV2/dimensions'
import ChangeButton from './ChangeButton'
import { NativeInfoContext } from '../../../contextV2/nativeInfoContext'
import log from 'utils/log'

type Props = {}
const ModalDescIcon = styled(Image).attrs({
  source: {
    uri: 'https://imagev2.xmcdn.com/storages/d682-audiofreehighqps/E1/51/GMCoOSMIs1hZAAA7xgJKXTeT.png',
  },
})`
  width: 165px;
  height: 137px;
  top: 22px;
`
const Wrapper = styled(LinearGradient)`
  padding: 20px 12px 0;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  position: relative;
  height: ${height * 0.8};
`

const TitleWrapper = styled(View)`
  font-size: 17px;
  font-weight: bold;
  padding-bottom: 14px;
  margin-left: 4px;
`

const Title = styled(Text)`
  font-size: 22px;
  font-weight: bold;
  color: ${({ theme }: { theme?: ThemeStyle }) => theme?.everydayChallenge?.taskListModal?.text};
`

const CloseButton = styled(Touch)`
  position: absolute;
  align-items: center;
  justify-content: center;
  top: 18px;
  right: 18px;
  width: 16px;
  height: 16px;
  z-index: 2;
`

const CloseButtonIcon = styled(Image)`
  width: 16px;
  height: 16px;
`

const viewStyle = { flex: 1 }

const TaskListModal: React.FC<Props> = (props) => {
  const [listVisible, setListVisible] = useState(false)
  const theme = useContext(ThemeContext)
  const nativeInfo = useContext(NativeInfoContext)
  const visibleCheckTimer = useRef<null | NodeJS.Timeout>(null)
  const titleWrapperRef = useRef<View>(null)
  const { activityTitle, refreshConfig } = useSelector((state: RootState) => state.everydayChallenge)
  const { signInSuccessModalVisible } = useSelector((state: RootState) => state.signInInfo)
  const [hasLayout, setHasLayout] = useState(false);
  const hasOpenedByIting = useRef(false)

  useEffect(() => {
    // 当 iting 中有 action 参数，且为 taskListModal 时自动打开这个弹窗
    if (nativeInfo?.action && nativeInfo.action === 'taskListModal' && !hasOpenedByIting.current && !signInSuccessModalVisible && hasLayout) {
      TaskListModalControl.open()
      hasOpenedByIting.current = true
    }
  }, [signInSuccessModalVisible, hasLayout])

  useEffect(() => {
    if (hasLayout) {
      const listener = DeviceEventEmitter.addListener("onArgumentsUpdate", (args) => {
        log('debug__onArgumentsUpdate', { action: args?.action, condition: args?.action === 'tasklistModal' })
        if (args?.action === 'taskListModal') {
          TaskListModalControl.open()
        }
      });

      return () => {
        listener.remove()
      }
    }
    return () => { }
  }, [hasLayout])

  const handleCloseModal = () => {
    TaskListModalControl.close()
  }

  useEffect(() => {
    return () => {
      visibleCheckTimer.current && clearTimeout(visibleCheckTimer.current)
    }
  }, [])

  const handleScroll = () => {
    ScrollEventSender.send(EverydayChallengeModalListID, 'scroll')
  }

  const setAccessibilityFocus = () => {
    try {
      if (titleWrapperRef.current) {
        const reactTag = findNodeHandle(titleWrapperRef.current)
        if (reactTag) {
          // 将无障碍焦点聚焦到 reactTag 元素上
          AccessibilityInfo.setAccessibilityFocus(reactTag)
        }
      }
    } catch (err) { }
  }

  const handleVisibleChange = ({ visible }: { visible: boolean }) => {
    setListVisible(visible)
    if (visible) {
      setAccessibilityFocus()
      visibleCheckTimer.current = setTimeout(() => {
        ScrollEventSender.send(EverydayChallengeModalListID, 'refreshed')
      }, 230)
    } else {
      visibleCheckTimer.current && clearTimeout(visibleCheckTimer.current)
    }
  }

  const renderListFooter = () => {
    if (refreshConfig.supportRefresh) {
      return <ChangeButton cost={refreshConfig.refreshPoint} />
    }
    return null
  }

  function onLayout() {
    setHasLayout(true);
  }

  return (
    <ReSlideUpPanel
      {...TaskListModalControl}
      overlayContainerStyle={{
        zIndex: 999,
        backgroundColor: 'rgba(0,0,0,0.7)',
      }}
      onVisibleChange={handleVisibleChange}
    >
      <Touch
        activeOpacity={1}
        onPress={handleCloseModal}
        accessibilityLabel="关闭弹窗"
      >
        <ModalDescIcon />
      </Touch>
      <Wrapper
        importantForAccessibility={'yes'}
        accessibilityViewIsModal
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        colors={[theme.everydayChallenge.taskListModal.bg_start_color, theme.everydayChallenge.taskListModal.bg_end_color]}
        onLayout={onLayout}
      >
        <TitleWrapper>
          <View
            ref={titleWrapperRef}
            collapsable={false}
          >
            <Title numberOfLines={1}>{activityTitle}</Title>
          </View>
        </TitleWrapper>
        <CloseButton
          accessibilityLabel="关闭弹窗"
          onPress={handleCloseModal}
          hitSlop={hitSlop(20)}
        >
          <CloseButtonIcon
            source={icon_pc_close_dark}
            style={{
              tintColor: theme.everydayChallenge.taskListModal.closeButtonIconTintColor,
            }}
          />
        </CloseButton>
        <ScrollAnalyticWapper
          id={EverydayChallengeModalListID}
          viewStyle={viewStyle}
        >
          <ScrollView
            style={viewStyle}
            onScroll={handleScroll}
          >
            <TaskProgress />
            <List
              listVisible={listVisible}
              renderListFooter={renderListFooter}
            />
          </ScrollView>
        </ScrollAnalyticWapper>
      </Wrapper>
    </ReSlideUpPanel>
  )
}

export default React.memo(TaskListModal, isEqual)
