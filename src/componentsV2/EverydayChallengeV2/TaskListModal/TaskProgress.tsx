import React, { useContext } from 'react'
import { Text } from '@xmly/rn-components'
import { View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { RootState } from '../../../store'
import { useSelector } from 'react-redux'
import SummaryTaskList from '../SummaryTaskList'
import LinearGradient from 'react-native-linear-gradient'
import { ThemeContext } from '../../../contextV2/themeContext'

type Props = {}

const Wrapper = styled(LinearGradient)`
  padding: 8px 4px 8px 8px;
  border-radius: 16px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`

const LeftSection = styled(View)``

const LeftSectionTitle = styled(Text)`
  font-size: 18px;
  color: #fff;
  margin-bottom: 3px;
  font-weight: 700;
`

const LeftSectionSubTitle = styled(Text)`
  font-size: 11px;
  color: #fff;
  opacity: 0.8;
`

const RightSection = styled(View)`
  flex: 1;
  margin-left: 13px;
`

const TaskProgress: React.FC<Props> = () => {
  const theme = useContext(ThemeContext)
  const { progress } = useSelector((state: RootState) => state.everydayChallenge)

  return (
    <Wrapper
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      colors={[theme.everydayChallenge.taskListModal.bg_list_start_color, theme.everydayChallenge.taskListModal.bg_list_end_color]}
    >
      <LeftSection>
        <LeftSectionTitle>任务奖励</LeftSectionTitle>
        <LeftSectionSubTitle>已完成{progress}个任务</LeftSectionSubTitle>
      </LeftSection>
      <RightSection>
        <SummaryTaskList
          onItemShowEventMetaId={55488}
          onItemPressEventMetaId={55487}
          containerStyle={{ margin: 0, width: '100%', marginLeft: 0 }}
          itemMargin={0}
          defaultWrapperSize={242}
          itemHeight={93}
          awardLabelFontSize={12}
          totalConditionLabelFontSize={10}
          isInModal={true}
        />
      </RightSection>
    </Wrapper>
  )
}

export default React.memo(TaskProgress, isEqual)
