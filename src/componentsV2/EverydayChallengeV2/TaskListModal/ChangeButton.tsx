import React, { useContext } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { View, StyleSheet, Image } from 'react-native'
import isEqual from 'lodash.isequal'
import { store } from '../../../store'
import { textAlignMiddleStyle } from '../../../constantsV2/layout'
import ic_refresh_n_n_line_regular_24 from '../../../appImagesV2/ic_refresh_n_n_line_regular_24'
import icon_money from '../../../appImagesV2/icon_money'
import XMLoading from '../../common/XMLoading'
import { DarkModeContext } from '../../../contextV2/darkModeContext'
import xmlog from '../../../utilsV2/xmlog'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { Toast } from '@xmly/rn-sdk'

type Props = {
  cost: number
}

const styles = StyleSheet.create({
  wrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 40,
    width: '100%',
    marginTop: 2,
    backgroundColor: '#F6F7F8',
    flexDirection: 'row',
    borderRadius: 6,
  },
  leftSide: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginRight: 8,
  },
  rightSide: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
  },
  icon: {
    width: 16,
    height: 16,
    marginRight: 3,
    marginLeft: 5,
  },
  iconMoney: {
    width: 16,
    height: 16,
    marginLeft: 4,
  },
  label: {
    ...textAlignMiddleStyle,
    fontSize: 14,
    color: '#000',
  },
  highlightText: {
    ...textAlignMiddleStyle,
    fontSize: 14,
    color: '#fe4444',
    marginRight: 4,
  },
})

const ChangeButton: React.FC<Props> = ({ cost }) => {
  const { isDarkMode } = useContext(DarkModeContext)

  const backgroundColorDark = isDarkMode ? { backgroundColor: 'rgba(0,0,0,0.3)' } : undefined
  const colorDark = isDarkMode ? { color: '#fff' } : undefined
  const iconTintColorDark = isDarkMode ? { tintColor: '#fff' } : undefined

  const isFree = cost === 0
  const handleChange = async () => {
    // 任务中心-每日挑战-换一换  点击事件
    xmlog.click(59062, undefined, { currPage: '任务中心' })
    const currentScores = store.getState().credit.creditPoint
    if (currentScores < cost) {
      return Toast.info('积分不足');
    }
    try {
      XMLoading.show()
      await store.dispatch.everydayChallenge.changeTaskList(null)
    } catch (err) {
    } finally {
      XMLoading.hide()
    }
  }

  const handleShow = () => {
    // 任务中心-每日挑战-换一换  控件曝光
    xmlog.event(59063, 'slipPage', { currPage: '任务中心' })
  }

  return (
    <ScrollAnalyticComp itemKey={'changeButton'} onShow={handleShow}>
      <Touch style={[styles.wrapper, backgroundColorDark]} onPress={handleChange}>
        <View style={styles.leftSide}>
          <Image source={ic_refresh_n_n_line_regular_24} style={[styles.icon, iconTintColorDark]} />
          <Text style={[styles.label, colorDark]}>换一换</Text>
        </View>
        <View style={styles.rightSide}>
          {isFree ? (
            <Text style={[styles.label, colorDark]}>首次免费</Text>
          ) : (
            <>
              <Text style={[styles.label, colorDark]}>使用</Text>
              <Image style={styles.icon} source={icon_money} />
              <Text style={styles.highlightText}>{cost}</Text>
              <Text style={[styles.label, colorDark]}>积分</Text>
            </>
          )}
        </View>
      </Touch>
    </ScrollAnalyticComp>
  )
}

export default React.memo(ChangeButton, isEqual)
