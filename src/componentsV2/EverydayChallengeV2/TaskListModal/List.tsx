import React, { ReactNode } from 'react'
import { View } from 'react-native'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../../store'
import displayTaskListFilter from '../../../utilsV2/displayTaskListFilter'
import RenderTaskListV2 from '../../EarnCredit/TaskCenter/RenderTaskListV2'
import styled from 'styled-components'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import { TaskItemKind, TaskItemType, TaskStatus } from '../../../typesV2/taskList'
import xmlog from '../../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import getEverydayChallengeAidByAB from '../../../utilsV2/getEverydayChallengeAidByAB'
import adDPTaskCtrl from '../../../utilsV2/adDPTask'

type Props = {
  listVisible: boolean
  renderListFooter?: () => ReactNode
}

const Wrapper = styled(View)`
  padding: 6px 16px 12px;
  background-color: ${({ theme }: { theme: ThemeStyle }) => theme.everydayChallenge.taskListModal.sectionItemBg};
  border-radius: 16px;
  margin-top: 12px;
  margin-bottom: 20px;
`

const getEventReportProps = (item: TaskItemType, index: number, options?: { isClick: boolean }) => {
  const ubtMateInfo = options?.isClick ? { xmRequestId: item.UBTMateInfo.xmRequestId } : item.UBTMateInfo
  const commonProps = {
    currPage: '任务中心',
    taskId: safetyToString(item.id),
    taskTitle: safetyToString(item.title),
    Item: safetyToString(item.statusText),
    positionNew: safetyToString(index + 1),
  }
  if (ubtMateInfo) {
    return {
      ...commonProps,
      ...ubtMateInfo,
    }
  }

  return commonProps
}

const List: React.FC<Props> = ({ listVisible, renderListFooter }) => {
  const { taskList, pointExchangeTask } = useSelector((state: RootState) => state.everydayChallenge)
  const thirdpartyTaskExist = useSelector((state: RootState) => state.thirdpartyTask.isExist)

  const filteredTaskList = displayTaskListFilter(taskList, thirdpartyTaskExist)

  const handleReportTaskPressEvent = (item: TaskItemType, index: number) => {
    if (item.code === 111) {
      const commonProps = {
        currPage: '任务中心',
        status: item.status === TaskStatus.unfinished ? '未使用' : '已使用',
        score: String(item.condition ?? 0),
      }

      const eventReportProps = item?.UBTMateInfo?.xmRequestId
        ? {
            ...commonProps,
            xmRequestId: item?.UBTMateInfo?.xmRequestId,
          }
        : commonProps

      // 任务中心-积分跳过模块  点击事件
      xmlog.click(56661, undefined, eventReportProps)
    } else {
      // 任务中心-每日挑战弹窗-任务  点击事件
      xmlog.click(55485, undefined, getEventReportProps(item, index, { isClick: true }))
    }
  }

  const handleTaskShow = (item: TaskItemType, index: number) => {
    if (listVisible) {
      if (item.taskType === TaskItemKind.AdDPSlotTask && item.adDpItemInfo) {
        adDPTaskCtrl.onExpo({ adItem: item.adDpItemInfo, responseId: item.adDpResponseId! })
      }
      const ubtMateInfo = item.UBTMateInfo ? item.UBTMateInfo : {}
      if (item.code === 111) {
        xmlog.event(56662, 'slipPage', {
          currPage: '任务中心',
          status: item.status === TaskStatus.unfinished ? '未使用' : '已使用',
          score: String(item.condition ?? 0),
          ...ubtMateInfo,
        })
      } else {
        // 任务中心-每日挑战弹窗-任务  控件曝光
        xmlog.event(55486, 'slipPage', getEventReportProps(item, index))
      }
    }
  }

  const handleGetTaskAwardAction = async (options: { taskItem: TaskItemType; aid: number; stepNos?: number[] | undefined }) => {
    await store.dispatch.everydayChallenge.getTaskAward(options)
  }

  const handleRequestAid = async () => {
    return getEverydayChallengeAidByAB()
  }

  const shouldButtonDisabled = (taskItem: TaskItemType) => {
    return taskItem.status === TaskStatus.finished || taskItem.status === TaskStatus.received || taskItem.status === TaskStatus.nonValid
  }

  if (filteredTaskList.length > 0) {
    return (
      <Wrapper>
        <RenderTaskListV2
          shouldButtonDisabled={shouldButtonDisabled}
          taskList={filteredTaskList}
          itemContainerStyle={{ paddingVertical: 12, borderWidth: 0 }}
          judgeItemIsLast={(index) => index + 1 === filteredTaskList.length}
          onTaskShow={handleTaskShow}
          onReportTaskPressEvent={handleReportTaskPressEvent}
          onRequestAid={handleRequestAid}
          onGetTaskAwardAction={handleGetTaskAwardAction}
          pointExchangeTask={pointExchangeTask}
        />
        {typeof renderListFooter === 'function' ? renderListFooter() : null}
      </Wrapper>
    )
  }
  return null
}

export default React.memo(List, isEqual)
