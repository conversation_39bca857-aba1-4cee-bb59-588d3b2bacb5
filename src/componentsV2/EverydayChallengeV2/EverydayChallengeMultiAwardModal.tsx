import React from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState } from '../../store'

type Props = {}

const MultiAwardModalLazy = React.lazy(() => import('../MultiAwardModal'))

const EverydayChallengeMultiAwardModal: React.FC<Props> = () => {
  const visible = useSelector(
    (state: RootState) => state.everydayChallenge.multiAwardModalVisible
  )
  if (visible) {
    return (
      <React.Suspense fallback={null}>
        <MultiAwardModalLazy forEverydayChallenge />
      </React.Suspense>
    )
  }
  return null
}

export default React.memo(EverydayChallengeMultiAwardModal, isEqual)
