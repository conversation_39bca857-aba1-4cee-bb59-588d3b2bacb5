import React, { useContext } from 'react'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import Header from './Header'
import Body from './Body'
import LinearGradient from 'react-native-linear-gradient'
import { ThemeContext } from '../../contextV2/themeContext'

type Props = {}

const Wrapper = styled(LinearGradient)`
  border-radius: 8px;
  padding: 0 0 6px;
  margin-bottom: 12px;
`

const EverydayChallenge: React.FC<Props> = () => {
  const theme = useContext(ThemeContext)
  return (
    <Wrapper
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      colors={[
        theme.everydayChallenge.summaryTaskList.bg_start_color,
        theme.everydayChallenge.summaryTaskList.bg_end_color,
      ]}
    >
      <Header />
      <Body />
    </Wrapper>
  )
}

export default React.memo(EverydayChallenge, isEqual)
