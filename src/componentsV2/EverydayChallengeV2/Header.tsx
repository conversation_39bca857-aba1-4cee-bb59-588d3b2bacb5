import React from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { View, Image } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { textAlignMiddleStyle } from '../../constantsV2/layout'
import TaskListModalControl from './TaskListModal/TaskListModalControl'
import { useSelector } from 'react-redux'
import { RootState } from '../../store'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import xmlog from '../../utilsV2/xmlog'
import { ThemeStyle } from '../../typesV2/themeInfo'

type Props = {}

const Wrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  position: relative;
  height: 46px;
`
const HeaderWrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
`
const HeaderIcon = styled(Image).attrs({
  source: {
    uri: 'https://imagev2.xmcdn.com/storages/1ed9-audiofreehighqps/54/A0/GKwRIaIIs4xLAAAcjQJKd9zy.png',
  },
})`
  width: 44px;
  height: 46px;
`
const HeaderTitle = styled(Text)`
  color: #fff;
  font-size: 16px;
  font-weight: 700;
`
const HeaderSubTitle = styled(Text)`
  font-size: 11px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  margin-left: 7px;
  margin-top: 4px;
`

const Button = styled(Touch)`
  position: relative;
  min-width: 64px;
  padding: 0 4px;
  height: 26px;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 16px;
  background-color: ${({ theme }: { theme: ThemeStyle }) => theme.everydayChallenge.summaryTaskList.bg_button};
  margin-right: 12px;
`

const ButtonLabel = styled(Text)`
  font-size: 12px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.everydayChallenge.summaryTaskList.text};
  font-weight: bold;
`

const Header: React.FC<Props> = () => {
  const {
    progress,
    activityTitle,
    summaryTask: { guideText },
  } = useSelector((state: RootState) => state.everydayChallenge)

  const handleOpenModal = () => {
    xmlog.click(55481, undefined, {
      currPage: '任务中心',
      Item: '去完成',
    })
    TaskListModalControl.open()
  }

  const onShowHandler = () => {
    xmlog.event(55482, 'slipPage', {
      currPage: '任务中心',
      Item: '去完成',
    })
  }

  return (
    <Wrapper>
      <HeaderWrapper>
        <HeaderIcon />
        <HeaderTitle numberOfLines={1}>{activityTitle}</HeaderTitle>
        <HeaderSubTitle>已完成{progress}个任务</HeaderSubTitle>
      </HeaderWrapper>
      <ScrollAnalyticComp
        itemKey={'EverydayChallengeButton'}
        onShow={onShowHandler}
      >
        <Button
          onPress={handleOpenModal}
          accessibilityLabel={`打开${activityTitle}活动的任务列表弹窗`}
        >
          <ButtonLabel style={textAlignMiddleStyle}>{guideText || '领VIP'}</ButtonLabel>
        </Button>
      </ScrollAnalyticComp>
    </Wrapper>
  )
}

export default React.memo(Header, isEqual)
