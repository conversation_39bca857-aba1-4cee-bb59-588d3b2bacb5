import React, { useContext, useState } from 'react'
import {
  Image,
  ImageSourcePropType,
  LayoutChangeEvent,
  StyleProp,
  Text,
  View,
  ViewStyle,
} from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import AnimatedNumbers from '../../libV2/react-native-animated-numbers'
import { ThemeContext } from '../../contextV2/themeContext'
import { UserInfoContext } from 'contextV2/userInfoContext'
import UserLoginStatus from 'typesV2/userLoginStatus'
import { useSelector } from 'react-redux'
import { RootState } from 'store'
import { textAlignMiddleStyle } from '../../constantsV2/layout'
import CashHint from './CashHint'
import { useIfShowCashHint } from './CashHint/useIfShowCashHint'

type Props = {
  icon: ImageSourcePropType
  value: number
  buttonLabel?: string
  onPress?: () => void
  buttonRef?: any
  iconRef?: any
  onIconLayout?: (event: LayoutChangeEvent) => void
  onButtonLayout?: (event: LayoutChangeEvent) => void
  onNumberAnimationFinished?: () => void
  iconWrapperStyle?: StyleProp<ViewStyle>
  buttonDisableWhenValueZero?: boolean
  customFontSize?: number
  isPoint?: boolean
}

const Wrapper = styled(View)`
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  position: relative;
`

const Icon = styled(Image)`
  width: 20px;
  height: 20px;
`

interface INoLoginValue {
  fColor: string
}

const NoLoginValue = styled(Text) <INoLoginValue>`
  font-size: 22px;
  color: ${(props) => props.fColor};
  font-family: 'XmlyNumber';
`

const CreditBarItemValue: React.FC<Props> = ({
  icon,
  value,
  iconRef,
  onIconLayout,
  onNumberAnimationFinished,
  iconWrapperStyle,
  customFontSize,
  isPoint
}) => {
  const showCashHint = useIfShowCashHint();
  const needAnimation = typeof value === 'number'
  const [animationFinished, setAnimationFinished] = useState(needAnimation ? false : true)
  const theme = useContext(ThemeContext)
  const { loginStatus } = useContext(UserInfoContext)
  const { noLoginAbTestStatus } = useSelector(
    (state: RootState) => state.signInInfo
  )

  let fontSize = 22

  if (value >= 999999) {
    fontSize = 19
  }
  if (value >= 9999999) {
    fontSize = 16
  }
  if (value >= 99999999) {
    fontSize = 13
  }

  const handleIconLayout = (layoutEvent: LayoutChangeEvent) => {
    if (typeof onIconLayout === 'function') {
      onIconLayout(layoutEvent)
    }
  }
  function handleAnimationFinished() {
    setAnimationFinished(true)
    if (onNumberAnimationFinished) {
      onNumberAnimationFinished?.();
    }
  }

  const renderText = () => {
    if (!needAnimation) {
      return (
        <Text
          style={{
            fontSize:
              typeof customFontSize === 'number' ? customFontSize : fontSize,
            color: theme.credit.number_color,
            ...textAlignMiddleStyle,
            fontFamily: 'XmlyNumber',
          }}
        >
          {value}
        </Text>
      )
    } else {
      return (
        <AnimatedNumbers
          animateToNumber={value}
          onAnimationFinished={handleAnimationFinished}
          fontStyle={{
            fontSize,
            color: theme.credit.number_color,
            fontFamily: 'XmlyNumber',
            ...textAlignMiddleStyle,
          }}
        />
      )
    }
  }

  const iconStyle = [{ marginRight: 2 }, iconWrapperStyle]

  return (
    <Wrapper>
      <View onLayout={handleIconLayout} ref={iconRef} style={iconStyle}>
        <Icon source={icon} />
      </View>
      {loginStatus === UserLoginStatus.notLogin &&
        noLoginAbTestStatus === '2' ? (
        <NoLoginValue
          fColor={theme.credit.number_color}
          style={textAlignMiddleStyle}
        >
          ****
        </NoLoginValue>
      ) : (
        renderText()
      )}
      {isPoint && animationFinished && showCashHint ? <CashHint point={Number(value)} /> : null}
    </Wrapper>
  )
}

export default React.memo(CreditBarItemValue, isEqual)
