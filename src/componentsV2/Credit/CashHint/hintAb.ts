import { NativeModules } from 'react-native'
import SentryUtils from 'utilsV2/sentryUtils';

enum AbTestValue {
  HIDDEN = '1',
  DISPLAY = '2'
}

export default async function hintAbQuest() {
  let value = AbTestValue.HIDDEN
  const defaultValue = AbTestValue.HIDDEN
  try {
    const res = (await NativeModules?.XMAbtest?.getString?.(
      'value_display_enhance',
      defaultValue
    ))
    value = res
  } catch (err) {
    SentryUtils.captureException(err, {
      source: 'hintAbQuest'
    });
  }
  return value === AbTestValue.DISPLAY
}