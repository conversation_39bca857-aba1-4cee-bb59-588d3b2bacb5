import { Text } from '@xmly/rn-components'
import React from 'react'
import { View, StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  hint: {
    borderRadius: 8,
    borderBottomLeftRadius: 0,
    backgroundColor: '#FF4444',
    paddingHorizontal: 6,
    height: 18,
    position: 'absolute',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    top: -18,
    right: -20,
  },
  cash: {
    fontSize: 12,
    color: '#fff',
    fontFamily: 'XmlyNumber',
  }
});

interface Props {
  point: number
}

export function formatCashNumber(value: number): string {
  if (!Number.isFinite(value)) return '';
  const numArr = `${value}`.split('.');
  const intNumber = numArr?.[0] ?? '';
  let decimals = numArr?.[1] ?? '';
  decimals = decimals.slice(0, 2);
  return `${intNumber}.${decimals}`;
}

export default function CashHint(props: Props) {
  const cash = formatCashNumber(props.point / 33000);
  return (
    Number(cash) > 0 ?
      <View style={styles.hint}>
        <Text style={styles.cash}>
          {cash}元
        </Text>
      </View> : null
  )
}
