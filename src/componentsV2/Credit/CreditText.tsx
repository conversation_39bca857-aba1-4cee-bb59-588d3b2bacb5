import { Touch } from '@xmly/rn-components'
import React, { useEffect } from 'react'
import styled from 'styled-components'
import icon_money from '../../appImagesV2/icon_money'
import CreditBarItemSubLabel from './CreditBarItemSubLabel'
import CreditBarItemValue from './CreditBarItemValue'
import { NativeModules } from 'react-native'
import { AndroidDisableAutoSize } from '../../utilsV2/native'

type Props = {
  creditPoint: number
  onPress: () => void
}

const Wrapper = styled(Touch)`
  width: 100%;
  height: 100%;
  align-items: flex-start;
  justify-content: center;
`

const CreditText: React.FC<Props> = ({ creditPoint, onPress }) => {
  useEffect(() => {
    AndroidDisableAutoSize()
    if (creditPoint) {
      // 通知native 积分数值变化
      NativeModules.RNBroadcast.sendBroadcast('RNCreditCenter_CreditChange', {})
    }
  }, [creditPoint])

  return (
    <Wrapper
      activeOpacity={1}
      onPress={onPress}
      accessibilityLabel={`我的积分${creditPoint}`}
    >
      <CreditBarItemValue
        value={creditPoint}
        icon={icon_money}
        isPoint
      />
      <CreditBarItemSubLabel label="我的积分" />
    </Wrapper>
  )
}

export default React.memo(CreditText, (pProps, nProps) => {
  return pProps.creditPoint === nProps.creditPoint
})
