import React, { useContext } from 'react'
import { Touch } from '@xmly/rn-components'
import { StyleProp, ViewStyle } from 'react-native'
import isEqual from 'lodash.isequal'
import {
  CreditItemLabelWrapper,
  CreditItemLabel,
  CreditItemRuleIcon,
  ExpiredCountText,
} from './styles'
import { hitSlop } from '@xmly/rn-utils'
import { DarkModeContext } from '../../contextV2/darkModeContext'

type Props = {
  onPressRule?: () => void
  label: string
  containerStyle?: StyleProp<ViewStyle>
  expiredCountLabel?: string
}

const CreditBarItemLabel: React.FC<Props> = ({
  label,
  onPressRule,
  containerStyle,
  expiredCountLabel,
}) => {
  const { isDarkMode } = useContext(DarkModeContext)
  return (
    <CreditItemLabelWrapper
      style={[
        containerStyle,
        {
          marginLeft: 6,
        },
      ]}
    >
      <CreditItemLabel>{label}</CreditItemLabel>
      <Touch onPress={onPressRule} hitSlop={hitSlop(20)}>
        <CreditItemRuleIcon
          style={{ tintColor: isDarkMode ? '#888' : undefined }}
        />
      </Touch>
      {typeof expiredCountLabel === 'string' ? (
        <ExpiredCountText>{expiredCountLabel}</ExpiredCountText>
      ) : null}
    </CreditItemLabelWrapper>
  )
}

export default React.memo(CreditBarItemLabel, isEqual)
