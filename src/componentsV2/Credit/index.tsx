import React, { useContext, useEffect, useRef, useState } from 'react'
import { connect, useSelector } from 'react-redux'
import { RootState, store } from '../../store'
import CreditText from './CreditText'
import isEqual from 'lodash.isequal'
import xmlog from 'utilsV2/xmlog'
import { Page } from '@xmly/rn-sdk'
import { Wrapper, WrapperCenter, CreditContent, CreditItem, Line, CreditContentTop, MaskWrap, Overlay, Button, ButtonText } from './styles'
import { UserInfoContext } from 'contextV2/userInfoContext'
import UserLoginStatus from 'typesV2/userLoginStatus'
import { NativeModules, TouchableWithoutFeedback, View } from 'react-native'
import goToLoginForAbTest from 'utilsV2/goToLoginForAbTest'
import CashBalance from './CashBalance'
import getUrlToOpen from '../../utilsV2/getUrlToOpen'
import { chipsDetailUrl, newCashUrl, newPointUrl } from '../../constantsV2'
import rnEnv from '../../../rnEnv'
import { encode } from 'base-64'
import GrayScaleContext from '../../contextV2/grayScaleContext'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import ChipsEntry from './ChipsEntry'
import { EverydayChallengeGuideStep } from '../../modelsV2/everydayChallenge'
import { cashBalanceAtom } from 'atom/refs'
import { useSetAtom } from 'jotai'

// const MonthlyTicketEntryLazy = React.lazy(() => import('./MonthlyTicketEntry'))

type Props = {
  creditPoint: number
}

const Index: React.FC<Props> = ({ creditPoint }) => {
  const [userCookie, setUserCookie] = useState('')
  const cashEntryRef = useRef<View>(null)
  const cashBalance = useSelector((state: RootState) => state.goldCoin.balance)
  const chipsBalance = useSelector((state: RootState) => state.chips.chipsBalance)
  const { loginStatus } = useContext(UserInfoContext)
  const { showGoldCoinModule } = useContext(GrayScaleContext)
  const { noLoginAbTestStatus } = useSelector((state: RootState) => state.signInInfo)
  const setCashNode = useSetAtom(cashBalanceAtom)

  useEffect(() => {
    store.dispatch.credit.getCredit({ init: true })
    getUserLoginStatus()
  }, [])

  const getUserLoginStatus = async () => {
    try {
      const res = await NativeModules.Account.getUserInfo()
      if (res?.uid && res?.token) {
        setUserCookie(`${res.uid}&${res.token}`)
      }
    } catch (err) {}
  }

  // 去收支兑换明细
  const handleWrapperPress = () => {
    Page.start(
      getUrlToOpen(
        newPointUrl({
          isTest: rnEnv.isTest(),
          cookie: encode(userCookie),
          no_cash: !showGoldCoinModule,
        }),
      ),
    )
    xmlog.click(30605, undefined, {
      currPage: '任务中心',
    })
  }

  const handleOpenCashDetail = () => {
    Page.start(
      getUrlToOpen(
        newCashUrl({
          isTest: rnEnv.isTest(),
          cookie: encode(userCookie),
        }),
      ),
    )
    // 任务中心-我的现金  点击事件
    xmlog.click(57613, undefined, { currPage: '任务中心' })
  }

  const checkCredit = () => {
    goToLoginForAbTest(true)
  }

  const handleCashBalanceEntryShow = () => {
    // 任务中心-我的现金  控件曝光
    xmlog.event(57614, 'slipPage', { currPage: '任务中心' })
  }

  const handleChipsEntryShow = () => {
    // 任务中心-我的碎片  控件曝光
    xmlog.event(57885, 'slipPage', { currPage: '任务中心' })
  }
  const handleChipsEntryPress = () => {
    Page.start(
      getUrlToOpen(
        chipsDetailUrl({
          isTest: rnEnv.isTest(),
          cookie: encode(userCookie),
          noCash: !showGoldCoinModule,
        }),
      ),
    )

    // 任务中心-我的碎片  点击事件
    xmlog.click(57884, undefined, { currPage: '任务中心' })
  }

  const handleCashEntryLayout = () => {
    if (cashEntryRef.current && cashEntryRef.current.measure) {
      setCashNode(cashEntryRef.current)
      cashEntryRef.current.measure((x, y, width, height, pageX, pageY) => {
        store.dispatch.everydayChallenge.setLayoutInfo({
          name: EverydayChallengeGuideStep.B,
          info: { x: pageX - 14, y: pageY, width: width + 18, height, radius: 10 },
        })
      })
    }
  }

  return (
    <Wrapper>
      {loginStatus === UserLoginStatus.notLogin && noLoginAbTestStatus === '2' && (
        <TouchableWithoutFeedback onPress={checkCredit}>
          <MaskWrap>
            <Overlay />
            <Button onPress={checkCredit}>
              <ButtonText>登录查看资产</ButtonText>
            </Button>
          </MaskWrap>
        </TouchableWithoutFeedback>
      )}
      <WrapperCenter>
        <CreditContent>
          <CreditContentTop>
            {showGoldCoinModule ? (
              <>
                <View
                  ref={cashEntryRef}
                  onLayout={handleCashEntryLayout}
                  style={{ flex: 1 }}
                >
                  <CreditItem>
                    <CashBalance
                      onPress={handleOpenCashDetail}
                      cashBalance={cashBalance}
                    />
                    <ScrollAnalyticComp
                      itemKey={'CashBalanceEntry'}
                      onShow={handleCashBalanceEntryShow}
                    />
                  </CreditItem>
                </View>
                <Line />
              </>
            ) : null}
            <CreditItem>
              <CreditText
                creditPoint={creditPoint}
                onPress={handleWrapperPress}
              />
            </CreditItem>
            <Line />
            <CreditItem>
              <ChipsEntry
                balance={chipsBalance}
                onPress={handleChipsEntryPress}
              />
              <ScrollAnalyticComp
                itemKey={'chipsEntry'}
                onShow={handleChipsEntryShow}
              />
            </CreditItem>
            {/* {showGoldCoinModule ? (
              <CreditItem>
                <ChipsEntry balance={chipsBalance} onPress={handleChipsEntryPress} />
                <ScrollAnalyticComp itemKey={'chipsEntry'} onShow={handleChipsEntryShow} />
              </CreditItem>
            ) : (
              <CreditItem>
                <Suspense fallback={null}>
                  <MonthlyTicketEntryLazy buttonLabel={showGoldCoinModule ? undefined : '去投票'} />
                </Suspense>
              </CreditItem>
            )} */}
          </CreditContentTop>
        </CreditContent>
      </WrapperCenter>
    </Wrapper>
  )
}

const mapStateToProps = (state: RootState) => {
  return {
    creditPoint: state.credit.creditPoint,
  }
}

export default connect(mapStateToProps)(React.memo(Index, isEqual))
