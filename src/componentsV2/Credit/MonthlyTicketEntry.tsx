import React, { useContext, useRef } from 'react'
import { LayoutChangeEvent, Text, View } from 'react-native'
import isEqual from 'lodash.isequal'
import CreditBarItemValue from './CreditBarItemValue'
import icon_monthly_ticket from '../../appImagesV2/icon_monthly_ticket'
import CreditBarItemSubLabel from './CreditBarItemSubLabel'
import monthlyTicketGuideContext from '../../contextV2/monthlyTicketGuideContext'
import { MonthlyTicketGuideStep } from '../../typesV2/monthlyTicketGuide'
import { useSelector } from 'react-redux'
import { RootState } from '../../store'
import goToMonthlyTicketVote from '../../utilsV2/goToMonthlyTicketVote'
import { Page, Toast } from '@xmly/rn-sdk'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import xmlog from '../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import GlobalEventEmitter from '../../utilsV2/globalEventEmitter'
import isMonthlyTicketAvailable from '../../utilsV2/isMonthlyTicketAvailable'
import styled from 'styled-components'
import { Touch } from '@xmly/rn-components'
import { ThemeStyle } from '../../typesV2/themeInfo'
import { textAlignMiddleStyle } from '../../constantsV2/layout'

type Props = {
  buttonLabel?: string
}

const ActionButton = styled(Touch)`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 7px 12px;
  border-radius: 484px;
  border: 1px solid
    ${({
      buttonDisable,
      theme,
    }: {
      buttonDisable?: boolean
      theme: ThemeStyle
    }) => (buttonDisable ? theme.errorView.button_border_color : '#ff4444')};
`
const ActionButtonLabel = styled(Text)`
  font-size: 12px;
  color: ${({
    buttonDisable,
    theme,
  }: {
    buttonDisable?: boolean
    theme: ThemeStyle
  }) => (buttonDisable ? theme.errorView.button_border_color : '#ff4444')};
`

const Spacer = styled(View)`
  width: 18px;
`

const MonthlyTicketEntry: React.FC<Props> = ({ buttonLabel }) => {
  const containerRef = useRef<View>()
  const iconRef = useRef<View>()

  const monthlyTicketInfo = useSelector(
    (state: RootState) => state.monthlyTicket.monthlyTicketInfo
  )
  const { setLayoutInfo, setMonthlyTicketIconLayoutInfo } = useContext(
    monthlyTicketGuideContext
  )

  const expiredCountLabel = monthlyTicketInfo.nearExpiredTicket
    ? `${monthlyTicketInfo.nearExpiredTicket}张将过期`
    : '我的月票'

  const haveExpiredTicket = monthlyTicketInfo.nearExpiredTicket! > 0

  const eventReportStatus =
    (monthlyTicketInfo.nearExpiredTicket || 0) > 0 ? '是' : '否'

  const handlePressAction = async () => {
    // 任务中心-月票资产模块  点击事件
    xmlog.click(48418, undefined, {
      currPage: '任务中心',
      Item: '去投票',
      numbers: safetyToString(monthlyTicketInfo.availableTicket),
      status: eventReportStatus,
    })
    goToMonthlyTicketVote()
  }

  const handleLayout = () => {
    if (containerRef.current) {
      containerRef.current.measure((x, y, width, height, pageX, pageY) => {
        setLayoutInfo(
          [
            MonthlyTicketGuideStep.NormalA,
            MonthlyTicketGuideStep.NormalB,
            MonthlyTicketGuideStep.VoteModalA,
          ],
          {
            x: pageX - 6,
            y: pageY - 8,
            width: width + 16,
            height: height + 16,
            radius: 10,
          }
        )
      })
    }
  }

  const handlePressVoteRecord = async () => {
    // 任务中心-月票资产模块  点击事件
    xmlog.click(48418, undefined, {
      currPage: '任务中心',
      Item: '投票记录',
      numbers: safetyToString(monthlyTicketInfo.availableTicket),
      status: eventReportStatus,
    })

    const isAvailable = await isMonthlyTicketAvailable()
    if (isAvailable) {
      Page.start('iting://open?msg_type=94&bundle=rn_ticket')
    } else {
      Toast.info('新功能！更新APP即可体验～')
    }
  }

  const handleShow = () => {
    // 任务中心-月票资产模块  控件曝光
    xmlog.event(48419, 'slipPage', {
      currPage: '任务中心',
      numbers: safetyToString(monthlyTicketInfo.availableTicket),
      status: eventReportStatus,
    })
  }

  const handleIconLayout = (layoutEventInfo: LayoutChangeEvent) => {
    setMonthlyTicketIconLayoutInfo(layoutEventInfo.nativeEvent.layout)
    if (iconRef.current) {
      iconRef.current.measure((x, y, width, height, pageX, pageY) => {
        setMonthlyTicketIconLayoutInfo({
          x: pageX,
          y: pageY,
          width: width,
          height: height,
        })
      })
    }
  }

  const handleNumberAnimationFinished = () => {
    GlobalEventEmitter.emit('monthlyTicketAnimationFinished')
  }

  const showButton =
    typeof buttonLabel === 'string' && typeof handlePressAction === 'function'

  const buttonDisable =
    (monthlyTicketInfo.availableTicket || 0) === 0 && showButton

  return (
    <View
      onLayout={handleLayout}
      ref={containerRef as any}
      style={{
        width: '100%',
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <View>
        <ScrollAnalyticComp
          itemKey={'MonthlyTicketEntry'}
          onShow={handleShow}
        />
        <CreditBarItemValue
          icon={icon_monthly_ticket}
          onIconLayout={handleIconLayout}
          value={monthlyTicketInfo.availableTicket || 0}
          iconRef={iconRef}
          onNumberAnimationFinished={handleNumberAnimationFinished}
        />
        <CreditBarItemSubLabel
          haveExpiredTicket={haveExpiredTicket}
          label={expiredCountLabel}
          onPress={handlePressVoteRecord}
        />
      </View>
      {showButton ? (
        <>
          <Spacer />
          <View>
            <ActionButton
              disabled={buttonDisable}
              onPress={handlePressAction}
              //@ts-ignore
              buttonDisable={buttonDisable}
            >
              <ActionButtonLabel
                style={textAlignMiddleStyle}
                //@ts-ignore
                buttonDisable={buttonDisable}
              >
                {buttonLabel}
              </ActionButtonLabel>
            </ActionButton>
          </View>
        </>
      ) : null}
    </View>
  )
}

export default React.memo(MonthlyTicketEntry, isEqual)
