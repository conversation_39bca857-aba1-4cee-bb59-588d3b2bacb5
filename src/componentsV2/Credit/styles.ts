import { Text } from '@xmly/rn-components'
import { Image, TouchableOpacity, View } from 'react-native'
import styled from 'styled-components'
import icon_credit_label_rule from '../../appImagesV2/icon_credit_label_rule'
import { ThemeStyle } from '../../typesV2/themeInfo'

export const Wrapper = styled(View)`
  width: 100%;
  margin-bottom: 8px;
`
export const WrapperCenter = styled(View)`
  width: 100%;
  padding: 0 16px;
  border-bottom-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.credit.line_color};
  border-bottom-width: 0.5px;
`
export const CreditContent = styled(View)`
  width: 100%;
  height: 68px;
`
export const CreditContentTop = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
`
export const CreditItem = styled(View)`
  height: 100%;
  justify-content: center;
  align-items: center;
  flex: 1;
`
export const Line = styled(View)`
  width: 0.5px;
  height: 34px;
  border: 0.5px solid;
  border-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.credit.line_color};
  margin-right: 6px;
`

export const CreditItemLabelWrapper = styled(View)`
  flex-direction: row;
  align-items: center;
  margin-bottom: 5px;
`

export const CreditItemLabel = styled(Text)`
  margin-right: 2px;
  font-size: 11px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.credit.label_color};
`

export const CreditItemRuleIcon = styled(Image).attrs({
  source: icon_credit_label_rule,
})`
  width: 10px;
  height: 10px;
`

export const ExpiredCountText = styled(Text)`
  margin-left: 4;
  font-size: 10px;
  color: #ff4444;
`

export const MaskWrap = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
`

export const Overlay = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.credit.overlay_warp_bg_color};
  z-index: 1;
`

export const Button = styled(TouchableOpacity)`
  position: absolute;
  top: 24px;
  align-self: center;
  background-color: #ff4444;
  padding: 6px 15px;
  border-radius: 165px;
  z-index: 1;
`

export const ButtonText = styled(Text)`
  color: white;
  font-size: 12px;
  font-weight: 500;
  line-height: 16px;
`
