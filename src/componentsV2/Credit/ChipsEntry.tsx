import React, { useEffect } from 'react'
import { Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import { store } from '../../store'
import styled from 'styled-components'
import CreditBarItemSubLabel from './CreditBarItemSubLabel'
import CreditBarItemValue from './CreditBarItemValue'

type Props = { balance: number; onPress: () => void }

const Wrapper = styled(Touch)`
  width: 100%;
  height: 100%;
  align-items: flex-start;
  justify-content: center;
`
const ChipsEntry: React.FC<Props> = ({ balance, onPress }) => {
  useEffect(() => {
    store.dispatch.chips.getChipsBalance()
  }, [])

  let fontSize = 22

  if (balance >= 99999) {
    fontSize = 19
  }
  if (balance >= 999999) {
    fontSize = 16
  }
  if (balance >= 99999999) {
    fontSize = 13
  }

  const icon = {
    uri: 'https://imagev2.xmcdn.com/storages/66d0-audiofreehighqps/D0/52/GKwRIJEI_qNoAAAUXAJrmI65.png',
  }

  return (
    <Wrapper
      activeOpacity={1}
      onPress={onPress}
      accessibilityLabel={`我的VIP碎片${balance}`}
    >
      <CreditBarItemValue
        value={balance}
        icon={icon}
        customFontSize={fontSize}
      />
      <CreditBarItemSubLabel label="我的VIP碎片" />
    </Wrapper>
  )
}

export default React.memo(ChipsEntry, isEqual)
