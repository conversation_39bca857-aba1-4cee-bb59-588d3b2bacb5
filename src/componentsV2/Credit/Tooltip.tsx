import { Text, Touch } from '@xmly/rn-components'
import icon_tooltip_icon from 'appImagesV2/icon_tooltip_icon'
import icon_tooltip_triangle from 'appImagesV2/icon_tooltip_triangle'
import isEqual from 'lodash.isequal'
import React, { createRef, useCallback,  useState } from 'react'
import { View,  Image, LayoutChangeEvent, useWindowDimensions } from 'react-native'
import styled from 'styled-components'
import { TaskItemType } from 'typesV2/taskList'



const TooltipWrapper = styled(Touch)`
  flex-shrink: 0;
`

const TooltipContent = styled(View)`
  background: #ffe7d8;
  border-radius: 16px;
  padding: 8px;
  flex-direction: row;
  align-items: center;
  flex: 1;
  flex-shrink: 0;
  flex-wrap: nowrap;
`

const TooltipLabel = styled(Text)`
  font-size : 11px;
  flex-wrap: nowrap;
  color: #111;
`

const TooltipActionIcon = styled(Image)`
  width: 12px;
  height: 12px;
`
const TooltipIcon = styled(Image)`
  position: absolute;
  flex-shrink: 0;
  width: 10px;
  height: 5px;
  bottom: -4px;
`


const TooltipLabelActionLabel = styled(Text)`
  font-size : 11px;
  color: #ff6045;
  margin-left: 4px;
`

type Props = {
  onTooltipPress: () => void
  tooltipData: { taskItem: TaskItemType, position: { x: number, y: number } } | null
}

const defaultTooltipPosition = { x: -999999, y: -999999, centerX: 0 }


const Tooltip: React.FC<Props> = (props) => {
  const { tooltipData, onTooltipPress } = props
  const [ tooltipPosition, setTooltipPosition ] = useState<{ x: number, y: number, centerX: number }>(defaultTooltipPosition)
  const selfRef = createRef<View>()
  const { width: windowWidth } = useWindowDimensions()
  const handleTooltipPress = useCallback(() => {
    onTooltipPress()
  }, [ onTooltipPress ])

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout
    if (!tooltipData) return
    const centerX = width / 2
    let result: { x: number, y: number, centerX: number } = defaultTooltipPosition
    if (tooltipData.position.x >= centerX) {
      result = {
        x: tooltipData.position.x - centerX,
        y: tooltipData.position.y - 30,
        centerX: centerX - 5
      }
      if (result.x + width >= windowWidth) {
        result.x = windowWidth - width - 10
        result.centerX = (tooltipData.position.x - (windowWidth - width) + 5) > (width - 20) ? (width - 20) : (tooltipData.position.x - (windowWidth - width) + 5)
      }
    } else {
      result = {
        x: 10,
        y: tooltipData.position.y - 30,
        centerX: tooltipData.position.x - 15
      }
    }
    setTooltipPosition(result)
  }


  if (!tooltipData) return null
  return (
    <View ref={ selfRef } collapsable={ false } style={ { position: 'absolute', top: tooltipPosition.y, left: tooltipPosition.x } } onLayout={ handleLayout }>
      <TooltipWrapper onPress={ handleTooltipPress }>
        <TooltipContent>
          <TooltipLabel>{ tooltipData.taskItem.title }</TooltipLabel>
          <TooltipLabelActionLabel>{ tooltipData.taskItem.statusText }</TooltipLabelActionLabel>
          <TooltipActionIcon source={ icon_tooltip_icon } fadeDuration={ 0 } />
        </TooltipContent>
        <TooltipIcon source={ icon_tooltip_triangle } style={ { left: tooltipPosition.centerX } } />
      </TooltipWrapper>
    </View>
  )
}

export default React.memo(Tooltip, (pProps, nProps) => isEqual(pProps.tooltipData, nProps.tooltipData))