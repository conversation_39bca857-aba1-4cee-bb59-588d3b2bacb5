import React from 'react'
import { Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import CreditBarItemSubLabel from './CreditBarItemSubLabel'
import CreditBarItemValue from './CreditBarItemValue'
import icon_cash from '../../appImagesV2/icon_cash'

type Props = {
  cashBalance: string | number
  onPress: () => void
}

const Wrapper = styled(Touch)`
  width: 100%;
  height: 100%;
  align-items: flex-start;
  justify-content: center;
`

const CashBalance: React.FC<Props> = ({ onPress, cashBalance }) => {
  let fontSize = 22
  const length = String(cashBalance).length;

  if (length >= 5) {
    fontSize = 19
  }
  if (length >= 6) {
    fontSize = 16
  }
  if (length >= 8) {
    fontSize = 13
  }

  return (
    <Wrapper
      activeOpacity={1}
      onPress={onPress}
      accessibilityLabel={`我的现金${cashBalance}`}
    >
      <CreditBarItemValue
        value={Number(cashBalance)}
        icon={icon_cash}
        customFontSize={fontSize}
      />
      <CreditBarItemSubLabel label="我的现金" />
    </Wrapper>
  )
}

export default React.memo(CashBalance, isEqual)
