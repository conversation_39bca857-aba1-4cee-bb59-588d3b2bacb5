import React, { useContext } from 'react'
import { Text } from '@xmly/rn-components'
import { Image, View } from 'react-native'
import isEqual from 'lodash.isequal'
import { DarkModeContext } from '../../contextV2/darkModeContext'
import styled from 'styled-components'
import icon_next_light from '../../appImagesV2/icon_next_light'
import { ThemeStyle } from '../../typesV2/themeInfo'
import { textAlignMiddleStyle } from '../../constantsV2/layout'

type Props = {
  label: string
  haveExpiredTicket?: boolean
  onPress?: () => void
}

const Wrapper = styled(View)`
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
`

interface TitleTextProps {
  haveExpiredTicket?: boolean
}

const TitleText = styled(Text)<TitleTextProps>`
  font-size: 11px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  text-align: center;
  color: ${({ theme, haveExpiredTicket }: { theme: ThemeStyle; haveExpiredTicket: boolean }) => {
    if (haveExpiredTicket) {
      return '#FF4444'
    }
    return theme.credit.desc_color
  }};
  margin-right: -1px;
`
const ArrowIcon = styled(Image).attrs({
  source: icon_next_light,
})`
  width: 16px;
  height: 16px;
`
const CreditBarItemSubLabel: React.FC<Props> = ({ label, haveExpiredTicket, onPress }) => {
  const { isDarkMode } = useContext(DarkModeContext)
  return (
    <Wrapper>
      <TitleText
        haveExpiredTicket={haveExpiredTicket}
        style={textAlignMiddleStyle}
      >
        {label}
      </TitleText>
      <ArrowIcon
        style={{
          tintColor: haveExpiredTicket ? '#FF4444' : isDarkMode ? '#66666B' : '#999',
        }}
      />
    </Wrapper>
  )
}

export default React.memo(CreditBarItemSubLabel, isEqual)
