import React from 'react'
import { Text, StyleSheet, View } from 'react-native'

const styles = StyleSheet.create({
  container: {},
  message: {
    fontSize: 16,
    textAlign: 'left',
    lineHeight: 23,
    justifyContent: 'center',
    color: '#888',
  },
})
const TipsText = () => {
  // const [isDarkMode, setIsDarkMode] = useState(
  //   Appearance.getColorScheme() === 'dark'
  // )

  // const handleChange = (preferences: Appearance.AppearancePreferences) => {
  //   setIsDarkMode(preferences.colorScheme === 'dark')
  // }

  // useEffect(() => {
  //   const listener = Appearance.addChangeListener(handleChange)

  //   return () => {
  //     listener.remove()
  //   }
  // }, [])

  return (
    <View style={styles.container}>
      <Text style={styles.message}>1-请先下载安装APP</Text>
      <Text style={styles.message}>
        2-安装后请再点击一次“去完成”，点击跳转成功即可完成任务
      </Text>
    </View>
  )
}

export default TipsText
