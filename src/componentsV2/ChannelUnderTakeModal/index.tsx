import React, { Suspense } from 'react'
import isEqual from 'lodash.isequal'
import { useSelector } from 'react-redux'
import { RootState, store } from '../../store'

const ChannelUnderTakeModalLazy = React.lazy(
  () => import('./ChannelUnderTakeModal.lazy')
)

// 渠道承接页弹窗
interface Props {}

const ChannelUnderTakeModal: React.FC<Props> = () => {
  const { modalVisible, channelModalConfigInfo } = useSelector(
    (state: RootState) => state.channelUndertake
  )

  const handleClose = () => {
    store.dispatch.channelUndertake.refresh({
      modalVisible: false,
    })
  }

  if (modalVisible) {
    return (
      <Suspense fallback={null}>
        <ChannelUnderTakeModalLazy
          onClose={handleClose}
          channelModalConfigInfo={channelModalConfigInfo}
        />
      </Suspense>
    )
  } else {
    return null
  }
}

export default React.memo(ChannelUnderTakeModal, isEqual)
