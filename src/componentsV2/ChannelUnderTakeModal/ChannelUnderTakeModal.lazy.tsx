import React, { useContext, useEffect } from 'react'
import { Text, View, Image } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { BetterImage, Touch } from '@xmly/rn-components'
import { Page } from '@xmly/rn-sdk'
import Pop from '../common/Pop'
import { fillOrigin } from '../../utilsV2/image2CustomSize'
import getUrlToOpen from '../../utilsV2/getUrlToOpen'
import rnEnv from '../../../rnEnv'
import { ChannelModalConfigType } from '../../typesV2/channelModal'
import { store } from '../../store'
import pop_close from '../../appImagesV2/pop_close'
import xmlog from '../../utilsV2/xmlog'
import { NativeInfoContext } from '../../contextV2/nativeInfoContext'

const ModalContentWrapper = styled(View)`
  align-items: center;
`

const ModalImage = styled(BetterImage)`
  width: 275px;
  height: 378px;
`
const ModalButton = styled(Touch)`
  width: 241px;
  height: 45px;
  margin: 24px 0;
  border-radius: 22.5px;
  background: #fe5a2a;
  justify-content: center;
  align-items: center;
`
const ModalButtonText = styled(Text)`
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
`
const CloseButton = styled(Image).attrs({
  source: pop_close,
})`
  width: 24px;
  height: 24px;
`

// 渠道承接页弹窗
interface ChannelUnderTakeModalProps {
  onClose: () => void
  channelModalConfigInfo: ChannelModalConfigType | null
}

const ChannelUnderTakeModalLazy: React.FC<ChannelUnderTakeModalProps> = ({
  onClose,
  channelModalConfigInfo,
}) => {
  const { srcChannel = '' } = useContext(NativeInfoContext)

  useEffect(() => {
    // 任务中心-礼包弹窗  控件曝光
    xmlog.event(55970, 'slipPage', {
      currPage: '任务中心',
      utmsource: srcChannel,
      url: channelModalConfigInfo?.btnLink || '',
    })
  }, [])

  const handlePress = () => {
    channelModalConfigInfo?.btnLink &&
      Page.start(getUrlToOpen(channelModalConfigInfo?.btnLink))
    // 任务中心-礼包弹窗  点击事件
    xmlog.click(55969, undefined, {
      currPage: '任务中心',
      utmsource: srcChannel,
      url: channelModalConfigInfo?.btnLink || '',
      Item: channelModalConfigInfo?.btnText || '',
    })
    store.dispatch.channelUndertake.refresh({
      modalVisible: false,
    })
  }

  if (channelModalConfigInfo) {
    return (
      <Pop handleClose={onClose}>
        <ModalContentWrapper>
          {channelModalConfigInfo.imgPath && (
            <ModalImage
              resizeMethod='resize'
              resizeMode='contain'
              source={{
                uri: `https:${fillOrigin(
                  channelModalConfigInfo.imgPath,
                  3,
                  rnEnv.isTest()
                )}`,
              }}
            />
          )}

          <ModalButton onPress={handlePress}>
            <ModalButtonText>{channelModalConfigInfo?.btnText}</ModalButtonText>
          </ModalButton>
          <Touch onPress={onClose}>
            <CloseButton />
          </Touch>
        </ModalContentWrapper>
      </Pop>
    )
  } else return null
}

export default React.memo(ChannelUnderTakeModalLazy, isEqual)
