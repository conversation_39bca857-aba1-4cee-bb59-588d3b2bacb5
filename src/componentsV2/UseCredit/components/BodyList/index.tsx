import { useNavigation } from '@react-navigation/native'
import {
  ScrollAnalyticWapper,
  ScrollEventSender,
} from '@xmly/react-native-page-analytics'
import isEqual from 'lodash.isequal'
import React, { useCallback, useMemo } from 'react'
import { FlatList } from 'react-native'
import { BodyItem } from 'typesV2/cardListData'
import BodyItemEle from './components/BodyItem'
import { useAtomValue } from 'jotai'
import taskAtom from '../../../../components/CashTask/store/task'
import { hitTraitsAtom } from 'components/CashTask/store/hitTraits'

interface Props {
  list: BodyItem[]
}

const BodyListId = 'BodyListId'

const keyExtractor = (item: BodyItem, index: number) => {
  return item.cover + index
}

const BodyList: React.FC<Props> = ({ list }) => {
  const handleScroll = () => {
    ScrollEventSender.send(BodyListId, 'scroll')
  }
  const cashTask = useAtomValue(taskAtom);
  const hideVip = useAtomValue(hitTraitsAtom);

  const filteredList = useMemo(() => {
    const adList = cashTask ? [{
      title: '抢红包',
      cover: 'https://imagev2.xmcdn.com/storages/5e9b-audiofreehighqps/04/72/GAqhJRIKnLx_AAAHtAMD-pbW.png',
      landingPage: 'iting://open?msg_type=94&bundle=rn_credit_center&action=taskListModal',
    }, ...list] : list;
    if (hideVip) {
      return adList.filter(item => !['抢红包', '抽大奖', '换现金'].includes(item.title ?? ''))
    }
    return adList
  }, [list, hideVip, cashTask])

  return (
    <ScrollAnalyticWapper id={BodyListId} useNavigation={useNavigation}>
      <FlatList
        horizontal
        data={filteredList}
        // data={list}
        keyExtractor={keyExtractor}
        showsHorizontalScrollIndicator={false}
        onScroll={handleScroll}
        renderItem={({ item, index }) => (
          <BodyItemEle
            len={list.length}
            item={item}
            index={index}
            key={index}
          />
        )}
      />
    </ScrollAnalyticWapper>
  )
}

export default React.memo(BodyList, isEqual)
