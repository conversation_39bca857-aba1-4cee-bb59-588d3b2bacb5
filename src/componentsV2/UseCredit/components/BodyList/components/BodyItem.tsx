import React, { useCallback, useContext } from 'react'
import { useNavigation } from '@react-navigation/native'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { Touch } from '@xmly/rn-components'
import { safetyToString } from '@xmly/rn-utils'
import { Dimensions, Image, NativeModules, Platform, Text, View } from 'react-native'
import styled from 'styled-components'
import { BodyItem } from 'typesV2/cardListData'
import { ThemeStyle } from 'typesV2/themeInfo'
import parseRNUrl from 'utilsV2/getRNUrlToOpen'
import getUrlToOpen from 'utilsV2/getUrlToOpen'
import xmlog from 'utilsV2/xmlog'
import { UserInfoContext } from '../../../../../contextV2/userInfoContext'
import UserLoginStatus from '../../../../../typesV2/userLoginStatus'
// import goToLogin from '../../../../../utilsV2/goToLogin'
import goToLoginForAbTest from '../../../../../utilsV2/goToLoginForAbTest'
import TaskListModalControl from '../../../../EverydayChallengeV2/TaskListModal/TaskListModalControl'
import throttle from 'utilsV2/throttle'
import execVideoTaskNew from 'modulesV2/performTask/execVideoTaskNew'
import { useSelector } from 'react-redux'
import { RootState } from '../../../../../store'
import { TaskItemKind, TaskStatus } from 'typesV2/taskList'
import { Toast } from '@xmly/rn-sdk'

const { Page } = NativeModules

const Wrap = styled(Touch)`
  background: ${({ theme }: { theme: ThemeStyle }) => theme.credit.bg_color};
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 4px;
`

const Marker = styled(View)`
  width: 24px;
  height: 14px;
  position: absolute;
  top: -5px;
  right: 2px;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  align-items: center;
  justify-content: center;
  background-color: red;
`
const MarkerText = styled(Text)`
  color: white;
  font-size: 9px;
`
interface ItemProps {
  width: number
}

const Item = styled(View) <ItemProps>`
  width: ${(props) => props.width}px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`

const Icon = styled(Image)`
  width: 46px;
  height: 46px;
  margin-bottom: 2px;
`

const Title = styled(Text)`
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  font-size: 13px;
`

interface Props {
  len: number
  item: BodyItem
  index: number
}

const BodyItemEle: React.FC<Props> = ({ len, item, index }: { len: number; item: BodyItem; index: number }) => {
  const navigate = useNavigation()
  const { width } = Dimensions.get('window')
  const splitCount = len > 4 ? 4.5 : 4
  const ItemWidth = Math.floor((width - 56) / splitCount)
  const { loginStatus } = useContext(UserInfoContext)
  const { taskList } = useSelector((state: RootState) => state.taskCenter)
  // const check = useCheckTask();

  const handleOnPress = () => {
    if (loginStatus !== UserLoginStatus.login) {
      // goToLogin()
      goToLoginForAbTest()
      return
    }
    // 任务中心-糖葫芦(新)  点击事件
    xmlog.click(52163, undefined, { currPage: '任务中心', Item: safetyToString(item.title), positionNew: safetyToString(index + 1) })

    const type = checkUrlType(item.landingPage)
    throttleOpenUrl(type, item.landingPage)
  }

  // 定义 checkUrlType 函数，参数为 url 字符串类型，校验url 类型，如果url 以http 或 https 开头，则类型为 h5, 以 rn:// 开头，则类型为rn， 以iting:// 开头，则为 iting

  const checkUrlType = (url: string) => {
    if (/^http(s)?:\/\//i.test(url)) {
      return 'h5'
    } else if (/^rn:\/\//i.test(url)) {
      return 'rn'
    } else if (/^iting:\/\//i.test(url)) {
      return 'iting'
    } else if (url.startsWith('ad-')) {
      return 'ad'
    } else {
      return null
    }
  }

  // const checkTask = useCallback((taskId: number) => {
  //   console.info('debug_tasklist_', taskList);
  //   return taskList.some((task) => task.id === taskId && task.status === TaskStatus.unfinished)
  // }, [JSON.stringify(taskList)])

  // const jumpUrl = (type: string | null, url: string) => {
  //   switch (type) {
  //     case 'h5':
  //     case 'iting':
  //       if (url.includes('action=taskListModal') && url.includes('bundle=rn_credit_center')) {
  //         // 打开任务包弹窗
  //         TaskListModalControl.open()
  //         return
  //       }

  //       Page.start(getUrlToOpen(url))
  //       break
  //     case 'rn':
  //       const { name, params }: any = parseRNUrl(url)
  //       navigate.navigate(name, params)
  //       break
  //     case 'ad':
  //       const { taskId, aid, positionId, positionName }: any = parseRNUrl(url)
  //       const unfinished: boolean = check(taskId);
  //       console.info('debug_tasklist_', unfinished);
  //       if (unfinished) {
  //         execVideoTaskNew({ id: taskId }, aid, { positionId, positionName });
  //       } else {
  //         Toast.info('今日已达上限，明日再来～');
  //       }
  //       break;
  //     default:
  //       Page.start(getUrlToOpen(url))
  //       break
  //   }
  // }

  const jumpUrl = useCallback((type: string | null, url: string) => {
    switch (type) {
      case 'h5':
      case 'iting':
        if (url.includes('action=taskListModal') && url.includes('bundle=rn_credit_center')) {
          // 打开任务包弹窗
          TaskListModalControl.open()
          return
        }

        Page.start(getUrlToOpen(url))
        break
      case 'rn':
        const { name, params }: any = parseRNUrl(url)
        navigate.navigate(name, params)
        break
      case 'ad':
        const { taskId, aid, positionId, positionName }: any = parseRNUrl(url)
        const unfinished = taskList.some((task) => task.id === +taskId && task.status === TaskStatus.unfinished)
        const newVideoTask = taskList.find((task) => task.taskType === TaskItemKind.newVideoTask)
        if (unfinished) {
          execVideoTaskNew({ id: taskId }, aid, { positionId, positionName }, { sourceName: 'home' });
        } else if (newVideoTask && newVideoTask?.status === TaskStatus.unfinished) { // 兼容新样式和老样式实验组看视频得积分任务
          execVideoTaskNew({ id: newVideoTask?.id }, aid, { positionId, positionName }, { sourceName: 'home' });
        } else {
          Toast.info('今日已达上限，明日再来～');
        }
        break;
      default:
        Page.start(getUrlToOpen(url))
        break
    }
  }, [taskList]);
  const throttleOpenUrl = useCallback(throttle(jumpUrl, Platform.OS === 'android' ? 1000 : 0), [taskList]);

  const onShowHandler = () => {
    // 任务中心-糖葫芦(新)  控件曝光
    xmlog.event(52164, 'slipPage', { currPage: '任务中心', Item: safetyToString(item.title), positionNew: safetyToString(index + 1) })
  }

  return (
    <Wrap onPress={handleOnPress}>
      <ScrollAnalyticComp
        itemKey={(index.toString() || '')?.toString()}
        onShow={onShowHandler}
      />
      <Item
        width={ItemWidth}
        key={index}
      >
        <Icon source={{ uri: item.cover }} />
        <Title>{item.title}</Title>
      </Item>
      {item.wrap?.subscript && (
        <Marker>
          <MarkerText>{item.wrap?.subscript}</MarkerText>
        </Marker>
      )}
    </Wrap>
  )
}

export default BodyItemEle
