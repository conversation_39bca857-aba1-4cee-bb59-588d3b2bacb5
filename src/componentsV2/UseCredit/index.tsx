import React from 'react'
import { Text, View } from 'react-native'
import styled from 'styled-components'
import { CardListType } from 'typesV2/cardListData'
import { ThemeStyle } from 'typesV2/themeInfo'
import BodyList from './components/BodyList'
import isEqual from 'lodash.isequal'

const Wrap = styled(View)`
  background: ${({ theme }: { theme: ThemeStyle }) => theme.credit.bg_color};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
`

const HeaderTitle = styled(Text)`
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  background: ${({ theme }: { theme: ThemeStyle }) => theme.credit.bg_color};
  font-size: 17px;
  font-weight: 700;
`

interface Props {
  squareCardItem: CardListType
}

const UseCredit: React.FC<Props> = ({ squareCardItem }) => {
  return (
    <Wrap>
      <HeaderTitle>{squareCardItem.header?.title}</HeaderTitle>
      <BodyList list={squareCardItem.body} />
    </Wrap>
  )
}

export default React.memo(UseCredit, isEqual)
