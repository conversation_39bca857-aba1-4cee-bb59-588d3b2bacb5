import React, { Component } from 'react'
import { StyleSheet, Text, View } from 'react-native'

import Swiper from '../libV2/react-native-swiper'

const styles = StyleSheet.create({
  wrapper: {
    height: '100%'
  },
  slide1: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#9DD6EB'
  },
  slide2: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#97CAE5'
  },
  slide3: {
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#92BBD9'
  },
  text: {
    color: '#fff',
    fontSize: 30,
    fontWeight: 'bold'
  }
})

export default class Test extends Component {
  render () {
    return (
      <Swiper containerStyle={ { height: '100%' } } style={ styles.wrapper } showsButtons={ true } loop={ false }>
        <View style={ styles.slide1 }>
          <Text style={ styles.text }>Hello Swiper</Text>
        </View>
        <View style={ styles.slide2 }>
          <Text style={ styles.text }>Beautiful</Text>
        </View>
        <View style={ styles.slide3 }>
          <Text style={ styles.text }>And simple</Text>
        </View>
      </Swiper>
    )
  }
}