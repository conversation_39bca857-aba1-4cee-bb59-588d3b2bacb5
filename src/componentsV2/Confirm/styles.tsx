import { StyleSheet, Dimensions } from 'react-native'

export default StyleSheet.create({
	wrap: {
		position: 'absolute',
		width: Dimensions.get('screen').width,
		height: Dimensions.get('screen').height,
		alignItems: 'center',
		justifyContent: 'center'
	},
	mask: {
		backgroundColor: 'rgba(0, 0, 0, 0.7)',
		height: Dimensions.get('screen').height,
		width: Dimensions.get('screen').width,
		position: 'absolute',
		bottom: 0
	},
	dialog: {
		zIndex: 10,
		borderRadius: 10,
		overflow: 'hidden'
	},
	dialogContent: {
		alignItems: 'center',
		paddingBottom: 24,
		width: '100%',
		paddingHorizontal: 30
	},
	title: {
		fontSize: 16,
		letterSpacing: -0.38,
		textAlign: 'center',
		lineHeight: 22,
		marginTop: 30
	},
	message: {
		fontSize: 16,
		textAlign: 'center',
		lineHeight: 23,
		justifyContent: 'center',
		fontWeight: '700'
	},
	footerWrap: {
		width: '100%',
		justifyContent: 'space-around',
		borderTopWidth: 0.5
	},
	myButton: {
		alignItems: 'center',
		justifyContent: 'center',
		height: 45
	},
	linearGradientButton: {
		alignItems: 'center',
		justifyContent: 'center',
		width: '100%',
		height: 45,
		backgroundColor: '#F77062'
	},
	buttonText: {
		fontSize: 16,
		letterSpacing: -0.38,
		textAlign: 'center',
		lineHeight: 22
	},
	closeBtnTouchableWrap: {
		padding: 12,
		position: 'absolute',
		top: 0,
		right: 0,
		zIndex: 10,
		alignItems: 'center',
		justifyContent: 'center'
	},
	buttonLine: {
		width: 1,
		height: '100%',
		position: 'absolute',
		top: 0,
		zIndex: 10,
		left: '50%',
		opacity: 0.5
	},
	iconWrap: {
		width: 13,
		height: 13
	},
	close: {
		width: 13,
		height: 13
	},
	touchHighlight: {
		overflow: 'hidden'
	}
})
