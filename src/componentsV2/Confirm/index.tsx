import isEqual from 'lodash.isequal'
import React, { Component, createRef } from 'react'
import { View, Text, TouchableWithoutFeedback, KeyboardAvoidingView, Appearance, StyleProp, TextStyle, findNodeHandle, AccessibilityInfo } from 'react-native'
import { Easing } from 'react-native-reanimated'

import { RootViewManager, withDarkMode } from '@xmly/rn-sdk'
import confirmStyles from './styles'
import { Icon } from '@xmly/rn-components/dist/Icon'
import { BackBtnPressListenerHelper } from '@xmly/rn-components/dist/BackBtnPressListenerHelper'
import { Transition, TransitionAnimationType } from '@xmly/rn-components/dist/Transition'

interface ButtonProps {
  text?: string
  color?: string
  darkModeColor?: string
  darkModeBackground?: string
  primary?: boolean
  onPress?: () => void
  count: number
  index: number
  background?: string
  isDarkMode?: boolean
  accessibilityLabel?: string
}

const Button = ({ text, color, primary, onPress, count, background, isDarkMode, darkModeBackground = '#282828', darkModeColor = '#8D8D91', accessibilityLabel }: ButtonProps) => {
  const textColor = () => (primary ? '#fff' : isDarkMode ? darkModeColor || color : color)
  const backgroundColor = () => (primary ? '#FF4444' : isDarkMode ? darkModeBackground || background : background)

  return (
    <TouchableWithoutFeedback
      onPress={onPress}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole="button"
    >
      <View
        style={{
          ...confirmStyles.myButton,
          width: count !== 2 ? '100%' : '50%',
          backgroundColor: backgroundColor() || '#fff',
        }}
      >
        <Text
          style={{
            ...confirmStyles.buttonText,
            color: textColor() || '#666666',
          }}
        >
          {text}
        </Text>
      </View>
    </TouchableWithoutFeedback>
  )
}

interface ConfirmProps {
  title?: string
  message?: string
  body?: any
  actions: Omit<ButtonProps, 'index' | 'count'>[]
  closable?: boolean
  closeEvent?: () => void
  noBg?: boolean
  rootViewManager: RootViewManager
  isDarkMode: boolean
  contentFullWidth?: boolean
  titleStyle?: StyleProp<TextStyle>
}

interface ConfirmState {
  disabled: boolean
  show: boolean
  isDarkMode: boolean
}
class Confirm extends Component<ConfirmProps, ConfirmState> {
  constructor(props: ConfirmProps) {
    super(props)
    this.state = {
      show: true,
      disabled: true,
      isDarkMode: props.isDarkMode,
    }
  }

  setAccessibilityFocusTimer: null | NodeJS.Timeout = null

  contentRef = createRef<View>()

  modalCtrl = Transition.getCtrl()
  // XMDarkModeEmitter = new NativeEventEmitter(NativeModules.XMDarkMode);

  animationEndCallback = () => {}

  handleModalAnimationEnd = () => {
    this.animationEndCallback()
    this.animationEndCallback = () => {}
    this.props.rootViewManager.removeView()
  }

  maskAnimationConfig: TransitionAnimationType = {
    property: [{ name: 'opacity', fromStyle: 0, toStyle: 1 }],
    duration: 150,
    easing: Easing.linear,
  }

  dialogAnimationConfig: TransitionAnimationType = {
    property: [{ name: 'scale', fromStyle: 0, toStyle: 1 }],
    duration: 150,
    easing: Easing.linear,
    afterLeave: this.handleModalAnimationEnd,
  }

  setAccessibilityFocus = () => {
    try {
      if (this.contentRef.current) {
        const nodeTag = findNodeHandle(this.contentRef.current)
        if (nodeTag) {
          AccessibilityInfo.setAccessibilityFocus(nodeTag)
        }
      }
    } catch (err) {
      console.log(err)
    }
  }

  componentWillUnmount() {
    this.setAccessibilityFocusTimer && clearTimeout(this.setAccessibilityFocusTimer)
  }

  componentDidMount() {
    this.modalCtrl.enter()
    this.setAccessibilityFocusTimer = setTimeout(() => {
      this.setAccessibilityFocus()
    }, 1000)
  }

  handleBackBtnPress = () => {
    this.onClose()
    return true
  }

  onCloseView = () => {
    this.modalCtrl.leave()
  }

  onClose = () => {
    if (typeof this.props.closeEvent === 'function') {
      this.props.closeEvent()
    }
    this.onCloseView()
  }

  renderActionButton = () => {
    const { actions } = this.props
    return actions.map((button, index: number) => {
      const handlePress = () => {
        this.onCloseView()
        if (typeof button.onPress === 'function') {
          this.animationEndCallback = button.onPress
        }
      }
      return (
        <Button
          isDarkMode={this.props.isDarkMode}
          count={actions.length}
          key={index}
          index={index + 1}
          text={button.text}
          darkModeColor={button.darkModeColor}
          darkModeBackground={button.darkModeBackground}
          color={button.color}
          background={button.background}
          primary={button.primary}
          onPress={handlePress}
          accessibilityLabel={button.accessibilityLabel}
        />
      )
    })
  }

  render() {
    const { title, message, body, actions, closable, contentFullWidth, titleStyle } = this.props

    return (
      <View
        style={confirmStyles.wrap}
        importantForAccessibility={'yes'}
        accessibilityViewIsModal
      >
        <Transition
          style={confirmStyles.mask}
          {...this.modalCtrl}
          animations={this.maskAnimationConfig}
        />
        <KeyboardAvoidingView
          behavior="position"
          style={{
            width: '74%',
            maxWidth: 390,
          }}
        >
          <Transition
            style={{
              ...confirmStyles.dialog,
              backgroundColor: this.state.isDarkMode ? '#282828' : '#ffffff',
            }}
            {...this.modalCtrl}
            animations={this.dialogAnimationConfig}
          >
            {closable && (
              <View style={confirmStyles.closeBtnTouchableWrap}>
                <TouchableWithoutFeedback
                  hitSlop={{ top: 20, left: 20, right: 20, bottom: 20 }}
                  onPress={this.onClose}
                  accessibilityLabel="关闭"
                  accessibilityRole="button"
                >
                  <View style={confirmStyles.iconWrap}>
                    <Icon
                      style={confirmStyles.close}
                      tintColor="#666666"
                      type={this.state.isDarkMode ? 'icon_modal_close_dark' : 'icon_modal_close'}
                    />
                  </View>
                </TouchableWithoutFeedback>
              </View>
            )}
            <View
              style={{
                ...confirmStyles.dialogContent,
                paddingHorizontal: contentFullWidth ? 0 : confirmStyles.dialogContent.paddingHorizontal,
              }}
              accessible
              collapsable={false}
              ref={this.contentRef}
            >
              {title ? (
                <Text
                  style={[
                    {
                      ...confirmStyles.title,
                      color: this.state.isDarkMode ? '#ffffff' : '#111111',
                      marginBottom: actions.length > 2 ? 19 : 22,
                    },
                    titleStyle,
                  ]}
                >
                  {title}
                </Text>
              ) : null}
              {message ? (
                <Text
                  style={{
                    ...confirmStyles.message,
                    marginTop: typeof title === 'undefined' ? 30 : 0,
                    color: this.state.isDarkMode ? '#CFCFCF' : '#333333',
                  }}
                >
                  {message}
                </Text>
              ) : null}
              {body ? body : null}
            </View>
            <View
              style={{
                ...confirmStyles.footerWrap,
                flexDirection: actions.length !== 2 ? 'column' : 'row',
                borderTopColor: this.state.isDarkMode ? '#000' : '#e8e8e8',
              }}
            >
              {this.renderActionButton()}
              <View style={confirmStyles.buttonLine} />
            </View>
          </Transition>
        </KeyboardAvoidingView>
        <BackBtnPressListenerHelper onBackBtnPress={this.handleBackBtnPress} />
      </View>
    )
  }

  shouldComponentUpdate(nextProps: ConfirmProps, nextState: ConfirmState) {
    return !isEqual(nextProps, this.props) || this.state.isDarkMode !== nextState.isDarkMode
  }
}

const alert = ({
  title,
  message,
  body,
  actions = [{ text: '确定', color: 'black' }],
  closable = false,
  closeEvent,
  noBg = true,
  contentFullWidth,
  ...rest
}: Omit<ConfirmProps, 'rootViewManager' | 'isDarkMode'>) => {
  const rootViewManager = new RootViewManager()
  const WithDarkModeConfirm = withDarkMode(Confirm, Appearance.getColorScheme() === 'dark')

  rootViewManager.setView(
    <WithDarkModeConfirm
      contentFullWidth={contentFullWidth}
      rootViewManager={rootViewManager}
      noBg={noBg}
      title={title}
      message={message}
      body={body}
      actions={actions}
      closable={closable}
      closeEvent={closeEvent}
      {...rest}
    />,
  )
}

export default alert
