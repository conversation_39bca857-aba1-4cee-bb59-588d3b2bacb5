import React, { useRef } from 'react'
import { Text } from '@xmly/rn-components'
import {
  InViewPort,
  InViewPortCheckEmitter,
} from '@xmly/rn-components/dist/InViewPort'
import { StyleProp, View, ViewStyle } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { ThemeStyle } from '../../typesV2/themeInfo'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

type Props = {
  hasMore: boolean
  onShow: () => void
  inViewPortGroupName: string
  containerStyle?: StyleProp<ViewStyle>
}

const FooterWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  padding: 16px;
  justify-content: center;
`

const Label = styled(Text)`
  font-size: 12px;
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.no_more_tip_color};
  text-align: center;
  flex-shrink: 0;
  margin: 0 16px;
`

const StaticListFooter: React.FC<Props> = ({
  hasMore,
  onShow,
  inViewPortGroupName,
  containerStyle,
}) => {
  const safeAreaInsets = useSafeAreaInsets()
  let context = ''

  if (hasMore) {
    context = '加载中...'
  } else {
    context = '没有更多了'
  }

  const isInView = useRef(false)

  const handleOutViewPort = () => {
    isInView.current = false
  }

  const handleOnShow = () => {
    if (isInView.current) return
    onShow()
    isInView.current = true
  }

  return (
    <FooterWrapper
      style={[containerStyle, { marginBottom: safeAreaInsets.bottom }]}
    >
      <InViewPort
        group={inViewPortGroupName}
        onInViewPort={handleOnShow}
        onOutViewPort={handleOutViewPort}
        checkEmitter={InViewPortCheckEmitter.event}
      >
        <Label>{context}</Label>
      </InViewPort>
    </FooterWrapper>
  )
}

export default React.memo(StaticListFooter, isEqual)
