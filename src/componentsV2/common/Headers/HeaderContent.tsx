import React, { use<PERSON><PERSON>back, useContext } from 'react'
import { NativeModules, Platform, StyleSheet, Text, View } from 'react-native'
import isEqual from 'lodash.isequal'
import BackBtn from '../BackBtn'
import { HeaderContentWrapper, LabelWrapper, Label, StoreBtn, StoreIcon, StoreText, LabelIcon } from './styles'
import { Page } from '@xmly/rn-sdk'
import { getXIMAMallUrl, newCreditUrl } from '../../../constantsV2'
import { DarkModeContext } from '../../../contextV2/darkModeContext'
import { UserInfoContext } from '../../../contextV2/userInfoContext'
import UserLoginStatus from '../../../typesV2/userLoginStatus'
import goToLogin from '../../../utilsV2/goToLogin'
import xmlog from '../../../utilsV2/xmlog'
import getUrlToOpen from '../../../utilsV2/getUrlToOpen'
import EntrancePromptConfirm from '../../EntrancePromptConfirm'
import { NativeInfoContext } from '../../../contextV2/nativeInfoContext'
import { withTheme } from 'styled-components'
import { Touch } from '@xmly/rn-components'
import { px } from 'utils/px'

type Props = {
  label: React.ReactNode
  withoutRight?: boolean
  labelIcon?: boolean
  tabs?: string[]
  activeTab?: string
  onTabPress?: () => void
  hideBack?: boolean
}

const styles = StyleSheet.create({
  tab: {
    alignItems: 'center',
    justifyContent: 'center',
    width: 100,
    marginRight: 22,
    flexDirection: 'row',
  },
  tabText: {
    fontSize: 15,
    fontWeight: 'normal',
    color: 'rgba(36, 0, 0, .55)'
  },
  activeTab: {
    fontSize: px(16),
    fontFamily: 'PingFang SC',
    fontWeight: Platform.select({ ios: '600', android: 'bold' }),
  },
  touchArea: {
    padding: 10,    // 扩大点击区域
    margin: -10,    // 负margin抵消padding，保持视觉位置不变
  }
})
// Wrap the styled components that need theme
const ThemedLabel = withTheme(Label)
const ThemedStoreText = withTheme(StoreText)

const HeaderContent: React.FC<Props> = ({ label, withoutRight, labelIcon, tabs, activeTab, onTabPress, hideBack = false }) => {
  const { isDarkMode } = useContext(DarkModeContext)
  const { loginStatus } = useContext(UserInfoContext)
  const { channelName, srcChannel } = useContext(NativeInfoContext)
  // const jumpUrl = getXIMAMallUrl()

  // const handleGoStore = () => {
  //   if (loginStatus === UserLoginStatus.login) {
  //     Page.start(getUrlToOpen(jumpUrl, { asH5: true }))
  //   } else {
  //     goToLogin()
  //   }

  //   xmlog.click(39740, undefined, {
  //     currPage: '任务中心',
  //   })
  // }
  const handleCheckRule = () => {
    if (loginStatus === UserLoginStatus.login) {
      Page.start(getUrlToOpen(newCreditUrl))
    } else {
      goToLogin()
    }
    xmlog.click(44987, undefined, {
      currPage: '规则',
    })
  }

  const handleBack = useCallback(async function () {
    try {
      await EntrancePromptConfirm(channelName || srcChannel || '')
      NativeModules.Page.finish(NativeModules.Page.OK, '')
    } catch (err) { }
  }, [])


  return (
    <HeaderContentWrapper>
      <LabelWrapper>
        {tabs ? tabs.map((tab, index) => {
          return tab === activeTab ? (
            <View key={index} style={styles.tab}>
              {/* {labelIcon && <LabelIcon />} */}
              <ThemedLabel style={styles.activeTab}>{tab}</ThemedLabel>
            </View>
          ) : (
            <View key={index} style={styles.tab}>
              <Touch
                style={styles.touchArea}
                onPress={onTabPress}
              >
                <Text style={[styles.tabText, {
                  color: isDarkMode ? 'rgba(255, 255, 255, .55)' : 'rgba(36, 0, 0, .55)',
                }]}>
                  {tab}
                </Text>
              </Touch>
            </View>
          )
        }) : <>
          {labelIcon && <LabelIcon />}
          <ThemedLabel>{label}</ThemedLabel></>
        }
      </LabelWrapper>

      {!hideBack ? <BackBtn onPress={handleBack} /> : null}

      {!withoutRight && (
        <StoreBtn onPress={handleCheckRule}>
          <ThemedStoreText>规则</ThemedStoreText>
        </StoreBtn>
      )}
    </HeaderContentWrapper>
  )
}

export default React.memo(HeaderContent, isEqual)
