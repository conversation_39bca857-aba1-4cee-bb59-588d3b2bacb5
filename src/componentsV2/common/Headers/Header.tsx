import { isAndroid } from '@xmly/rn-utils/dist/device'
import { ScrollAreaContext } from 'contextV2/scrollAreaContext'
import React, { useContext } from 'react'
import { Animated, View, LayoutChangeEvent } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { ThemeContext } from '../../../contextV2/themeContext'
import { HeaderWrapper } from './styles'
import HeaderContent from './HeaderContent'

type Props = {
  label: React.ReactNode
  withoutRight?: boolean
  labelIcon?: boolean
  onLayout?: (event: LayoutChangeEvent) => void
  tabs?: string[]
  activeTab?: string
  onTabPress?: () => void
}

const Header: React.FC<Props> = ({ label, withoutRight, labelIcon, onLayout, tabs, activeTab, onTabPress }) => {
  const { scrollValue = new Animated.Value(0) } = useContext(ScrollAreaContext) || {}
  const safeArea = useSafeAreaInsets()
  const theme = useContext(ThemeContext)
  const paddingTop = isAndroid ? 52 : safeArea.top
  const animatedBgStyle: any = {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.common.bg_color,
    opacity: scrollValue.interpolate({
      inputRange: [0, 300],
      outputRange: [0, 1],
      extrapolate: 'clamp',
    }),
  }
  const onLayoutProperty = onLayout ? { onLayout } : {};

  return (
    <View {...onLayoutProperty}>
      <HeaderWrapper
        style={{
          paddingTop,
          height: isAndroid ? undefined : 44 + paddingTop,
          paddingBottom: isAndroid ? 16 : 0,
        }}
      >
        <Animated.View style={animatedBgStyle} />
        <HeaderContent
          label={label}
          withoutRight={withoutRight}
          labelIcon={labelIcon}
          tabs={tabs}
          activeTab={activeTab}
          onTabPress={onTabPress}
        />
      </HeaderWrapper>
    </View>
  )
}

export default React.memo(Header, (pProps, nProps) => {
  return pProps.label === nProps.label
})
