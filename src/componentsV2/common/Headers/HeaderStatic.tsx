import React from 'react'
import { StyleProp, ViewStyle } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import { HeaderWrapper } from './styles'
import HeaderContent from './HeaderContent'

type Props = {
  style?: StyleProp<ViewStyle>
  label?: string
  hideBack?: boolean
}

const HeaderStatic: React.FC<Props> = ({ style, label = '签到领福利', hideBack = false }) => {
  console.log('Header render')
  const safeArea = useSafeAreaInsets()
  const paddingTop = isAndroid ? 52 : safeArea.top
  const wrapperStyle = [  
    {
      paddingTop,
      height: isAndroid ? undefined : 44 + paddingTop,
      paddingBottom: isAndroid ? 16 : 0,
    },
    style,
  ]
  return (
    <HeaderWrapper style={wrapperStyle}>
      <HeaderContent label={label} withoutRight hideBack={hideBack} />
    </HeaderWrapper>
  )
}

export default React.memo(HeaderStatic, () => true)
