import { Text } from '@xmly/rn-components'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import React, { useCallback, useContext } from 'react'
import { View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import styled from 'styled-components'
import BackBtn from '../BackBtn'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import { ThemeContext } from '../../../contextV2/themeContext'

type Props = {
  label: string
  onPressBack: () => void
}

const HeaderWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  overflow: hidden;
  z-index: 1;
`

const HeaderContentWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`

const Label = styled(Text)`
  text-align: center;
  font-size: 18px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.header.color};
  position: absolute;
  left: 0;
  right: 0;
  align-items: center;
  justify-content: center;
`

const LabelWrapper = styled(View)`
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  align-items: center;
  justify-content: center;
`

const HeaderPlain: React.FC<Props> = (props) => {
  const safeArea = useSafeAreaInsets()
  const { label, onPressBack } = props
  const theme = useContext(ThemeContext)

  const handleBack = useCallback(function () {
    onPressBack()
  }, [])

  const paddingTop = isAndroid ? 52 : safeArea.top

  return (
    <>
      <HeaderWrapper
        style={{
          paddingTop,
          height: isAndroid ? undefined : 44 + paddingTop,
          paddingBottom: isAndroid ? 16 : 0,
          backgroundColor: theme.common.bg_color,
        }}
      >
        <HeaderContentWrapper>
          <LabelWrapper>
            <Label>{label}</Label>
          </LabelWrapper>
          <BackBtn onPress={handleBack} />
        </HeaderContentWrapper>
      </HeaderWrapper>
    </>
  )
}

export default React.memo(HeaderPlain, (pProps, nProps) => {
  return pProps.label === nProps.label
})
