import { Text, Touch } from '@xmly/rn-components'
import { View, Image } from 'react-native'
import styled from 'styled-components'
import icon_money from '../../../appImagesV2/icon_money'
import ic_shop from '../../../appImagesV2/ic_shop'
import { ThemeStyle } from '../../../typesV2/themeInfo'

export const HeaderWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  overflow: hidden;
  z-index: 1;
`

export const HeaderContentWrapper = styled(View)`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`
export const LabelWrapper = styled(View)`
  position: absolute;
  flex-direction: row;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  align-items: center;
  justify-content: center;
  flex-direction: row;
`
export const LabelIcon = styled(Image).attrs({
  source: icon_money,
})`
  width: 20px;
  height: 20px;
  margin-right: 2px;
`

export const Label = styled(Text)`
  text-align: center;
  font-size: 18px;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.header.color};
  align-items: center;
  justify-content: center;
`



export const StoreBtn = styled(Touch)`
  flex-direction: row;
  align-items: center;
`

export const StoreIcon = styled(Image).attrs({
  source: ic_shop,
})`
  width: 20px;
  height: 20px;
  margin-right: 2px;
`

export const StoreText = styled(Text)`
  font-size: 14px;
  font-weight: 400;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.header.color};
`

export const RuleButton = styled(Touch)`
  width: 16px;
  height: 16px;
  margin-left: 2px;
`

export const RuleButtonIcon = styled(Image)`
  width: 16px;
  height: 16px;
`
