import isEqual from 'lodash.isequal'
import React, { ReactNode } from 'react'
import { StyleProp, ViewStyle, View } from 'react-native'

type Props = {
  containerViewStyle?: StyleProp<ViewStyle>
  contentViewStyle?: StyleProp<ViewStyle>
  offset?: [x: number, y: number]
  distance?: number
  children?: ReactNode
}

const SectionShadow: React.FC<Props> = (props) => {
  return (
    <View style={[props.containerViewStyle, { borderRadius: 8 }]}>
      <View style={[props.contentViewStyle]}>{props.children}</View>
    </View>
  )
}

export default React.memo(SectionShadow, isEqual)
