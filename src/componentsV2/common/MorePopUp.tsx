import { Text, Touch } from '@xmly/rn-components'
import { Page } from '@xmly/rn-sdk'
import more_pop_up_record from 'appImagesV2/more_pop_up_record'
import more_pop_up_rule from 'appImagesV2/more_pop_up_rule'
import { HeaderLayoutContext } from 'contextV2/headerLayoutContext'
import React, { useContext, useEffect } from 'react'
import { Image, View } from 'react-native'
import styled from 'styled-components'
import getUrlToOpen from 'utilsV2/getUrlToOpen'
import goScoreMarket2 from 'utilsV2/goScoreMarket2'
import xmlog from 'utilsV2/xmlog'
import rnEnv from '../../../rnEnv'
import { creditRuleURL } from '../../constantsV2'
import { UserInfoContext } from '../../contextV2/userInfoContext'
import UserLoginStatus from '../../typesV2/userLoginStatus'
import goToLogin from '../../utilsV2/goToLogin'

type MorePopUpProps = {
  onClose: () => void
}

const Wrapper = styled(View)`
  width: 120px;
  background-color: #fff;
  position: absolute;
  z-index: 10000;
  top: 0;
  border-radius: 6px;
`

const Mask = styled(Touch)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
`

const Item = styled(Touch)`
  width: 100%;
  border-radius: 6px;
  flex-direction: row;
  align-items: center;
  padding: 16px;
  background-color: #fff;
`

const ItemIcon = styled(Image)`
  width: 26px;
  height: 26px;
  opacity: 1;
  margin-right: 6px;
`

const ItemLabel = styled(Text)`
  font-size: 14px;
  color: #333333;
`

const Arrow = styled(View)`
  position: absolute;
  width: 10px;
  height: 10px;
  background-color: #fff;
  border-radius: 2;
  top: -5px;
  right: 16px;
  z-index: -1;
`

const MorePopUp: React.FC<MorePopUpProps> = (props) => {
  const { mainHeaderLayoutRectangle } = useContext(HeaderLayoutContext)
  const { loginStatus } = useContext(UserInfoContext)
  const { onClose } = props

  useEffect(() => {
    xmlog.event(30538, 'dialogView', {
      currPage: '任务中心',
    })
  }, [])

  const reportEvent = (item: '兑换记录' | '积分说明') => {
    xmlog.event(30539, 'dialogClick', {
      currPage: '任务中心',
      item,
    })
  }

  const handleCheckRule = () => {
    if (loginStatus === UserLoginStatus.login) {
      Page.start(getUrlToOpen(creditRuleURL(rnEnv.isTest())))
      reportEvent('积分说明')
    } else {
      goToLogin()
    }
    onClose()
  }

  const handleCheckRecord = () => {
    if (loginStatus === UserLoginStatus.login) {
      goScoreMarket2(
        'https://activity.m.duiba.com.cn/crecord/record?xx=ximalaya.com'
      )
      reportEvent('兑换记录')
    } else {
      goToLogin()
    }
    onClose()
  }

  return (
    <>
      <Mask activeOpacity={1} onPress={onClose} />
      <Wrapper
        style={{
          top: mainHeaderLayoutRectangle
            ? mainHeaderLayoutRectangle?.height + 5
            : 0,
          right: 7,
        }}
      >
        <Arrow style={{ transform: [{ rotate: '45deg' }] }} />
        <Item onPress={handleCheckRecord}>
          <ItemIcon source={more_pop_up_record} fadeDuration={0} />
          <ItemLabel>兑换记录</ItemLabel>
        </Item>
        <Item onPress={handleCheckRule}>
          <ItemIcon source={more_pop_up_rule} fadeDuration={0} />
          <ItemLabel>积分规则</ItemLabel>
        </Item>
      </Wrapper>
    </>
  )
}

export default React.memo(MorePopUp, () => true)
