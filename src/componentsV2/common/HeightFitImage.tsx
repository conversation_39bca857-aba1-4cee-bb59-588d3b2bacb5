import React, { Component } from 'react'
import { Image, StyleSheet, ImageProps, View } from 'react-native'
import isEqual from 'lodash.isequal'
import { BetterImage, FastImageProps } from '@xmly/rn-components'

type FitImageProps = ImageProps &
  FastImageProps & {
    containerWidth: number
    onHeightCalc?: (height: number) => void
    defaultHeight?: number
  }

type State = {
  coverHeight: number | string
}

class FitImage extends Component<FitImageProps, State> {
  state = {
    coverHeight: 0,
  }

  componentDidCatch() {
    this.setState({
      coverHeight: this.props.defaultHeight || '100%',
    })
  }

  componentDidMount() {
    this.calcImageSize()
  }

  calcImageSize = () => {
    try {
      const { containerWidth, source } = this.props
      // @ts-ignore
      if (
        typeof source === 'object' &&
        source?.uri &&
        typeof (source as { uri: string }).uri === 'string'
      ) {
        Image.getSize(
          (source as { uri: string }).uri,
          (width, height) => {
            const scale = containerWidth / width
            this.setState({
              coverHeight: scale * height,
            })
            typeof this.props.onHeightCalc === 'function' &&
              this.props.onHeightCalc(scale * height)
          },
          (err) => {
            // incase image won't show
            this.setState({
              coverHeight: this.props.defaultHeight || '100%',
            })
            console.log("incase image won't show", err)
          }
        )
      } else {
        this.setState({
          coverHeight: this.props.defaultHeight || '100%',
        })
      }
    } catch (err) {
      console.log(err)
    }
  }

  componentDidUpdate(prevProps: FitImageProps) {
    if (
      this.props.containerWidth !== prevProps.containerWidth ||
      this.props.style !== prevProps.style ||
      !isEqual(prevProps.source, this.props.source)
    ) {
      this.calcImageSize()
    }
  }

  render() {
    return (
      <View
        style={{
          width: this.props.containerWidth,
          height: this.state.coverHeight,
        }}
      >
        <BetterImage
          {...this.props}
          style={StyleSheet.flatten([this.props.style, styles.image])}
        />
      </View>
    )
  }

  shouldComponentUpdate(nextProps: FitImageProps, nextState: State) {
    return (
      !isEqual(nextProps, this.props) ||
      nextState.coverHeight !== this.state.coverHeight
    )
  }
}

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
  },
})

export default FitImage
