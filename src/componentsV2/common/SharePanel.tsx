import React from 'react'
import { View, Image, NativeModules } from 'react-native'

import styled from 'styled-components'
import { Text, Touch } from '@xmly/rn-components'

import { hitSlop, safetyToString } from '@xmly/rn-utils'

import { RootState, store } from '../../store'
import { connect } from 'react-redux'
import { XMShareChannel } from '../../constantsV2/share'

import { SHARE_TYPE_DATA } from '../../constantsV2/share'
import icon_save from '../../appImagesV2/icon_save'
import {
  PosterItemResourceType,
  PosterItemType,
} from '../../typesV2/posterListType'
import refreshClientTask from '../../servicesV2/refreshClientTask'
import { TaskStatus } from '../../typesV2/taskList'
import xmlog from '../../utilsV2/xmlog'
import { FromType } from '../../modelsV2/signInInfo'
import { Toast } from '@xmly/rn-sdk'
import isEqual from 'lodash.isequal'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { SharePanelBaseHeight } from '../../constantsV2/layout'
import GlobalEventEmitter from '../../utilsV2/globalEventEmitter'
import { ThemeStyle } from '../../typesV2/themeInfo'

const Wrapper = styled(View)`
  width: 100%;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: ${(props: { paddingBottom: number }) =>
    SharePanelBaseHeight + props.paddingBottom}px;
  background: ${({ theme }: { theme: ThemeStyle }) => theme.credit.line_color};
`
const ShareTitle = styled(Text)`
  font-size: 16px;
  line-height: 54px;
  height: 54px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  text-align: center;
`
const ShareActionWrapper = styled(View)`
  width: 100%;
  padding-left: 22px;
  padding-right: 22px;
  padding-bottom: 20px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
`

const ShareActionBtnIcon = styled(Image)`
  border-radius: 25px;
  width: 50px;
  height: 50px;
`
const ShareActionBtnLabel = styled(Text)`
  width: 100%;
  font-size: 12px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  text-align: center;
  margin-top: 8px;
`

const CloseBtnWrapper = styled(Touch)`
  height: ${(props: { paddingBottom: number }) => 54 + props.paddingBottom}px;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.credit.bg_color};
  position: absolute;
  width: 100%;
  bottom: 0;
`
const CloseBtn = styled(Text)`
  width: 100%;
  height: 54px;
  line-height: 54px;
  justify-content: center;
  font-size: 16px;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.common.title_color};
  text-align: center;
  background: ${({ theme }: { theme: ThemeStyle }) => theme.credit.bg_color};
`
interface TaskInfo {
  taskValue: number
  taskStatus: number
  taskId: number
}
interface SharePanelProps {
  handleSaveViewShot: () => void
  handleClose: () => void
  shareInfo: PosterItemType
  handleGetViewShotLocalPath: (type: XMShareChannel) => Promise<string>
  shareFromType: FromType
  setShareNumber: () => void
  taskInfo: TaskInfo
  setTaskStatus: (param: TaskStatus) => void
}

const SharePanel: React.FC<SharePanelProps> = ({
  handleClose,
  handleSaveViewShot,
  handleGetViewShotLocalPath,
  shareInfo,
  shareFromType,
  setShareNumber,
  taskInfo,
  setTaskStatus,
}) => {
  console.log('---------------------shareInfo--------------')
  const safeAreaInsets = useSafeAreaInsets()
  const { resourceType = 0, resourceId = 0, jumpUrl = '' } = shareInfo || {}
  //保存图片
  const handleDownload = async () => {
    try {
      // EventTracking.poster.clickShareChannelBtn({
      // 	channelName: '下载图片',
      // 	contentId: this.props.posterData.contentId,
      // 	shareType: this.props.shareType,
      // 	tabType: this.props.tabType
      // })

      handleSaveViewShot()
    } catch (error) {
      console.log(error)
    }
  }

  // 分享专辑、单音、混音类型截图到微信、微博、朋友圈
  const handleShareByType = async (type: XMShareChannel) => {
    try {
      // 分享图片本地路径
      const shareImageLocalPath = await handleGetViewShotLocalPath(type)

      let params = {
        srcId:
          resourceType !== PosterItemResourceType.activity
            ? safetyToString(resourceId)
            : safetyToString(jumpUrl),
        srcType:
          resourceType === PosterItemResourceType.track
            ? '7'
            : resourceType === PosterItemResourceType.album
            ? '6'
            : '',
        subType:
          resourceType === PosterItemResourceType.track
            ? '1116'
            : resourceType === PosterItemResourceType.album
            ? '1115'
            : '',
        imgUrl: shareImageLocalPath,
        type: 'picture',
        shareWay: '2',
      }
      if (
        typeof NativeModules.Share.shareV2 === 'function' &&
        resourceType !== PosterItemResourceType.activity
      ) {
        console.log('🌲🌲🌲🌲🌲🌲🌲shareV2🌲🌲🌲🌲🌲🌲🌲')
        // Alert.alert('🌲🌲🌲🌲🌲🌲🌲shareV2🌲🌲🌲🌲🌲🌲🌲')
        NativeModules.Share.shareV2({
          shareType: 'customRequest',
          channel: type,
          params,
        })
      } else {
        console.log('🌲🌲🌲🌲🌲🌲🌲share🌲🌲🌲🌲🌲🌲🌲')
        // Alert.alert('🌲🌲🌲🌲🌲🌲🌲share🌲🌲🌲🌲🌲🌲🌲')

        NativeModules.Share.share({
          shareType: 'default',
          channel: [type],
          params,
        })
      }

      console.log(
        '🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱taskStatus, taskId🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱🥱'
      )
      console.log(taskInfo.taskStatus, taskInfo.taskId)

      if (
        taskInfo.taskStatus !== TaskStatus.finished &&
        taskInfo.taskStatus !== TaskStatus.received
      ) {
        await refreshClientTask({ taskId: taskInfo.taskId })
        store.dispatch.credit.getCredit()
        setTaskStatus(TaskStatus.finished)
        setShareNumber()
        GlobalEventEmitter.emit('shareNumberChange')
      }
    } catch (error) {}
  }
  /**点击分享 */
  const handleShare = async (type: XMShareChannel) => {
    try {
      handleShareByType(type)
      // 任务中心-分享海报弹窗-分享渠道  弹框控件点击
      xmlog.event(38576, 'dialogClick', {
        Item: SHARE_TYPE_DATA.find((item) => item.type === type)
          ?.title as string,
        from: shareFromType,
        posterId: shareInfo.posterId + '',
        albumId:
          shareInfo.resourceType === PosterItemResourceType.album
            ? shareInfo.resourceId + ''
            : '-1',
        trackId:
          shareInfo.resourceType === PosterItemResourceType.track
            ? shareInfo.resourceId + ''
            : '-1',
        link: shareInfo.jumpUrl ? shareInfo.jumpUrl : '-1',
        currPage: '任务中心',
      })
    } catch (err) {
      console.log(err)
      Toast.info('分享失败')
    }
  }

  return (
    <Wrapper paddingBottom={safeAreaInsets.bottom}>
      <ShareTitle>
        {taskInfo.taskValue && taskInfo.taskStatus !== TaskStatus.finished
          ? `分享图片+${taskInfo.taskValue}积分`
          : '分享至'}
      </ShareTitle>
      <ShareActionWrapper>
        <Touch
          onPress={handleDownload}
          hitSlop={hitSlop(20)}
          style={{ alignItems: 'center' }}
          accessibilityLabel='保存相册'
          accessibilityRole='button'
        >
          <ShareActionBtnIcon source={icon_save} />
          <ShareActionBtnLabel>保存相册</ShareActionBtnLabel>
        </Touch>
        {SHARE_TYPE_DATA.map((item, index) => (
          <Touch
            key={index}
            onPress={() => handleShare(item.type)}
            hitSlop={hitSlop(20)}
            style={{ alignItems: 'center' }}
            accessibilityLabel={item.title}
            accessibilityRole='button'
          >
            <ShareActionBtnIcon source={item.icon} />
            <ShareActionBtnLabel>{item.title}</ShareActionBtnLabel>
          </Touch>
        ))}
      </ShareActionWrapper>
      <CloseBtnWrapper
        onPress={handleClose}
        paddingBottom={safeAreaInsets.bottom}
      >
        <CloseBtn>取消</CloseBtn>
      </CloseBtnWrapper>
    </Wrapper>
  )
}

export default connect(({ signInInfo: { shareFromType } }: RootState) => ({
  shareFromType,
}))(React.memo(SharePanel, isEqual))
