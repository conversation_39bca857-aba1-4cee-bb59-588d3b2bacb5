import React from 'react'
import { View, StyleSheet } from 'react-native'

type Props = {}

const styles = StyleSheet.create({
  wrapper: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: '#fff',
    borderRadius: 20,
  },
  line: {
    width: 1.5,
    height: 20,
    backgroundColor: '#fff',
    borderRadius: 5,
    position: 'absolute',
  },
  lineWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
})

const ModalCloseBtn: React.FC<Props> = () => {
  return (
    <View style={styles.wrapper}>
      <View style={styles.lineWrapper}>
        <View style={[styles.line, { transform: [{ rotate: '45deg' }] }]} />
        <View style={[styles.line, { transform: [{ rotate: '135deg' }] }]} />
      </View>
    </View>
  )
}

export default ModalCloseBtn
