import React, { useContext } from 'react'
import { Text, Touch } from '@xmly/rn-components'
import { View, Image, NativeModules } from 'react-native'
import { ErrorViewProps } from './type'
import styled from 'styled-components'
import { ThemeStyle } from '../../../typesV2/themeInfo'
import { ThemeContext } from '../../../contextV2/themeContext'
import { Page } from '@xmly/rn-sdk'
import Header from '../Headers/Header'

const Container = styled(View)`
  align-items: center;
  flex: 1;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.bg_color};
  height: 100%;
`
const ErrorImage = styled(Image)`
  margin-top: 190px;
  margin-bottom: 8px;
`
const ErrorText = styled(Text)`
  margin-bottom: 24px;
  font-size: 13px;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.errorView.label_color};
`
const ReloadBtn = styled(View)`
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 28px;
  border-radius: 20px;
  border: 1px solid
    ${({ theme }: { theme: ThemeStyle }) => theme.errorView.button_border_color};
`
const ReloadBtnText = styled(Text)`
  font-size: 13px;
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.errorView.button_label_color};
`

const ErrorView: React.FC<ErrorViewProps> = ({
  errorMsg,
  onReload,
  buttonLabel,
  withoutHeader,
}) => {
  const theme = useContext(ThemeContext)

  const reload = () => {
    if (typeof onReload === 'function') {
      onReload()
    } else {
      NativeModules.Page.finish(NativeModules.Page.OK, '')
      Page.start('iting://open?msg_type=94&bundle=rn_credit_center')
    }
  }

  return (
    <Container>
      {!withoutHeader ? <Header label='签到领福利' withoutRight /> : null}
      <ErrorImage source={theme.errorView.error_icon} />
      <ErrorText>{errorMsg || '加载失败，请尝试重试'}</ErrorText>
      <Touch
        onPress={reload}
        accessibilityLabel={buttonLabel}
        accessibilityRole='button'
      >
        <ReloadBtn>
          <ReloadBtnText>{buttonLabel}</ReloadBtnText>
        </ReloadBtn>
      </Touch>
    </Container>
  )
}

export default ErrorView
