import React from 'react'

type Props = {
  count: number
  onTimeout?: () => void
  onCount: (count: number) => void
}

type State = {
  countTime: number
}

const tick = 1
const interval = 1000

class CountdownWorker extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      countTime: props.count,
    }
  }

  timer: NodeJS.Timeout | null = null

  componentDidMount() {
    this.start()
  }

  componentWillUnmount() {
    this.timer && clearTimeout(this.timer)
  }

  start = () => {
    if (this.state.countTime <= 0) {
      typeof this.props.onTimeout === 'function' && this.props.onTimeout()
    } else {
      this.timer = setTimeout(() => {
        this.setState(
          (state) => ({
            countTime: state.countTime - tick,
          }),
          () => {
            this.props.onCount(this.state.countTime)
          }
        )
        this.start()
      }, interval)
    }
  }

  render() {
    return null
  }

  shouldComponentUpdate(nextProps: Props, nextState: { countTime: number }) {
    return nextState.countTime !== this.state.countTime
  }
}

export default CountdownWorker
