import { Page } from '@xmly/rn-sdk'
import React, { PureComponent } from 'react'
import { NativeModules } from 'react-native'
import SentryUtils from '../../utilsV2/sentryUtils'
import ErrorView from './ErrorView'

interface Props {}

interface State {
  hasError: boolean
  errorMsg: any
}

export default class ErrorBoundary extends PureComponent<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, errorMsg: '' }
  }

  componentDidCatch(error: any, info: any) {
    try {
      this.setState({ hasError: true, errorMsg: String(error) })
    } catch (err) {
      this.setState({ hasError: true, errorMsg: '' })
    }
    SentryUtils.captureException(error, {
      source: 'ErrorBoundary.componentDidCatch',
    })
  }

  handleReload = () => {
    NativeModules.Page.finish(NativeModules.Page.OK, '')
    Page.start('iting://open?msg_type=94&bundle=rn_credit_center')
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorView
          buttonLabel='重进首页'
          onReload={this.handleReload}
          errorMsg={this.state.errorMsg}
        />
      )
    }
    return this.props.children
  }
}
