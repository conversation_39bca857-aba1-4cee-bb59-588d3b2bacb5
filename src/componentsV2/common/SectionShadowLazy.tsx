import isEqual from 'lodash.isequal'
import React, { ReactNode } from 'react'
import { StyleProp, ViewStyle } from 'react-native'
import { Shadow } from '@xmly/react-native-shadow-2'

type Props = {
  containerViewStyle?: StyleProp<ViewStyle>
  contentViewStyle?: StyleProp<ViewStyle>
  offset?: [x: number, y: number]
  distance?: number
  children?: ReactNode
  startColor?: string
}

const SectionShadow: React.FC<Props> = (props) => {
  const {
    containerViewStyle,
    contentViewStyle,
    children,
    offset,
    distance,
    startColor,
  } = props
  return (
    <Shadow
      offset={offset}
      radius={8}
      distance={distance || 12}
      startColor={startColor || 'rgba(0,0,0,0.03)'}
      containerViewStyle={containerViewStyle}
      viewStyle={contentViewStyle}
    >
      {children}
    </Shadow>
  )
}

export default React.memo(SectionShadow, isEqual)
