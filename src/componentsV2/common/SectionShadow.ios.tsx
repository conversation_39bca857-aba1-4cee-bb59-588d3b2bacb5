import isEqual from 'lodash.isequal'
import React, { ReactNode } from 'react'
import { StyleProp, ViewStyle } from 'react-native'

type Props = {
  containerViewStyle?: StyleProp<ViewStyle>
  contentViewStyle?: StyleProp<ViewStyle>
  offset?: [x: number, y: number]
  distance?: number
  children?: ReactNode
  startColor?: string
}
const ShadowLazy = React.lazy(() => import('./SectionShadowLazy'))

const SectionShadow: React.FC<Props> = (props) => {
  return (
    <React.Suspense fallback={null}>
      <ShadowLazy {...props} />
    </React.Suspense>
  )
}

export default React.memo(SectionShadow, isEqual)
