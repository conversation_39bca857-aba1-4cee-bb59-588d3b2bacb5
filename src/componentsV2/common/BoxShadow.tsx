import React from 'react'
import SectionShadow from './SectionShadow.ios'
import { isAndroid } from '@xmly/rn-utils/dist/device'
import { StyleProp, ViewStyle } from 'react-native'

interface Props {
  style?: StyleProp<ViewStyle>
  children: React.ReactNode
}
const BoxShadow: React.FC<Props> = ({ children, style }) => {
  return (
    <SectionShadow
      startColor={isAndroid ? 'rgba(0,0,0,0.03)' : 'rgba(0,0,0,0.01)'}
      contentViewStyle={{
        overflow: 'hidden',
        borderRadius: 8,
        width: '100%',
      }}
      containerViewStyle={[
        {
          flex: 1,
        },
        style,
      ]}
      offset={[0, 7]}
      distance={isAndroid ? 20 : 30}
    >
      {children}
    </SectionShadow>
  )
}

export default BoxShadow
