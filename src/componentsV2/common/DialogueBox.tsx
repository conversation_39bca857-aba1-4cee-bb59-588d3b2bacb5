import { Text } from '@xmly/rn-components'
import React from 'react'
import { View } from 'react-native'
import styled from 'styled-components'

interface OverStyle {
  background?: string
  height?: number
  color?: string
  fontSize?: number
  paddingHorizontal?: number
  paddingVertical?: number
}

interface ArrowStyle {
  width: number
  background: string
}

const CountWrapper = styled(View)`
  position: absolute;
  height: ${(props: { overStyle: OverStyle }) => props.overStyle.height}px;
  line-height: ${(props: { overStyle: OverStyle }) => props.overStyle.height}px;
  background: ${(props: { overStyle: OverStyle }) =>
    props.overStyle.background};
  border-radius: ${(props: { overStyle: OverStyle }) =>
    props.overStyle.height}px;
  text-align: center;
  padding: ${(props: { overStyle: OverStyle }) =>
    (props.overStyle.paddingVertical || 0) +
    'px' +
    ' ' +
    (props.overStyle.paddingHorizontal || 6) +
    'px'};
  flex-wrap: nowrap;
  align-items: center;
  justify-content: center;
`
const Arrow = styled(View)`
  position: absolute;
  bottom: ${(props: { arrowStyle: ArrowStyle }) =>
    -props.arrowStyle.width * 2}px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: ${(props: { arrowStyle: ArrowStyle }) =>
    props.arrowStyle.width}px;
  border-top-color: ${(props: { arrowStyle: ArrowStyle }) =>
    props.arrowStyle.background};
  border-left-color: transparent;
  border-bottom-color: transparent;
  border-right-color: transparent;
`
const CountText = styled(Text)`
  color: ${(props: { overStyle: OverStyle }) => props.overStyle.color};
  font-size: ${(props: { overStyle: OverStyle }) => props.overStyle.fontSize}px;
  line-height: ${(props: { overStyle: OverStyle }) => props.overStyle.height};
  text-align: center;
`

interface DialogueInterface {
  content: string
  height: number
  background?: string
  color?: string
  fontSize: number
  arrowStyle: ArrowStyle
  paddingHorizontal?: number
  paddingVertical?: number
}
const DialogueBox: React.FC<DialogueInterface> = ({
  background = '#4e4e4e',
  height,
  color = '#ffd99a',
  content,
  fontSize,
  arrowStyle,
  paddingHorizontal,
  paddingVertical,
}) => {
  return (
    <CountWrapper
      overStyle={{
        background,
        height,
        color,
        paddingHorizontal,
        paddingVertical,
      }}
    >
      <CountText overStyle={{ height, color, fontSize }}>{content}</CountText>
      <Arrow arrowStyle={arrowStyle}></Arrow>
    </CountWrapper>
  )
}

export default DialogueBox
