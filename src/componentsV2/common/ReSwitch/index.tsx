import React, { useRef } from 'react'
import { StyleProp, StyleSheet, ViewStyle } from 'react-native'
import isEqual from 'lodash.isequal'
import Animated, { Clock, cond, Easing, eq, Extrapolate, interpolate, interpolateColors, set, useCode, useValue } from 'react-native-reanimated'
import { runTiming } from '../../../utilsV2/animations/runTiming'
import { Touch } from '@xmly/rn-components'

type Props = {
  containerStyle?: StyleProp<ViewStyle>
  value: boolean
  disabled?: boolean
  barSpace?: number
  barSize?: number
  trackOnColor?: string
  barOnColor?: string
  trackOffColor?: string
  barOffColor?: string
  onChange?: (value: boolean) => void
  onRequestChange?: () => boolean
  onPress?: (value: boolean) => void
}

enum SwitchState {
  on = 1,
  off = 0,
}

const defaultBarSpace = 1.2

const defalutBarSize = 15

const defaultBarColor = '#fff'

const defaultTrackOnColor = '#ff4444'

const defaultTrackOffColor = '#e9e9ea'

const getTrackStyle = ({ barSize, barSpace }: { barSize: number; barSpace: number }) => {
  const height = barSize + barSpace * 2
  const borderRadius = height / 2
  const width = barSize * 2
  return {
    height,
    borderRadius,
    width,
  }
}

const ReSwitch: React.FC<Props> = ({
  value,
  barSpace,
  barSize,
  trackOffColor,
  trackOnColor,
  barOffColor,
  barOnColor,
  disabled,
  onRequestChange,
  onChange,
  containerStyle,
  onPress,
}) => {
  const switchAnimationValue = useValue<0 | 1>(value ? 1 : 0)
  const timingClock = useRef(new Clock()).current
  const switchState = useValue<SwitchState>(value ? SwitchState.on : SwitchState.off)

  const timingConfig = useRef({
    clock: timingClock,
    from: switchAnimationValue,
    duration: 120,
    easing: Easing.inOut(Easing.ease),
  }).current

  useCode(() => {
    return [
      set(switchState, value ? SwitchState.on : SwitchState.off),
      cond(
        eq(switchState, SwitchState.on),
        [
          set(
            switchAnimationValue,
            runTiming({
              ...timingConfig,
              to: 1,
            }),
          ),
        ],
        [
          set(
            switchAnimationValue,
            runTiming({
              ...timingConfig,
              to: 0,
            }),
          ),
        ],
      ),
    ]
  }, [value])

  const mBarSpace = typeof barSpace === 'number' ? barSpace : defaultBarSpace
  const mBarSize = typeof barSize === 'number' ? barSize : defalutBarSize
  const mBarOnColor = typeof barOnColor === 'string' ? barOnColor : defaultBarColor
  const mBarOffColor = typeof barOffColor === 'string' ? barOffColor : defaultBarColor

  const barAnimationStyle: any = {
    width: mBarSize,
    height: mBarSize,
    borderRadius: mBarSize / 2,
    backgroundColor:
      mBarOnColor === mBarOffColor
        ? mBarOnColor
        : interpolateColors(switchAnimationValue, {
            inputRange: [0, 1],
            outputColorRange: [mBarOffColor, mBarOnColor],
          }),
    transform: [
      {
        translateX: interpolate(switchAnimationValue, {
          inputRange: [0, 1],
          outputRange: [0 + mBarSpace, mBarSize - mBarSpace],
          extrapolate: Extrapolate.CLAMP,
        }),
      },
      {
        scaleX: interpolate(switchAnimationValue, {
          inputRange: [0, 0.2, 0.8, 1],
          outputRange: [1, 1.3, 1.3, 1],
          extrapolate: Extrapolate.CLAMP,
        }),
      },
    ],
  }

  const mTrackOnColor = typeof trackOnColor === 'string' ? trackOnColor : defaultTrackOnColor
  const mTrackOffColor = typeof trackOffColor === 'string' ? trackOffColor : defaultTrackOffColor

  const trackAnimationStyle: any = {
    backgroundColor: interpolateColors(switchAnimationValue, {
      inputRange: [0, 1],
      outputColorRange: [mTrackOffColor, mTrackOnColor],
    }),
  }

  const handlePress = () => {
    typeof onPress === 'function' && onPress(value)
  }

  const accessibilityLabel = value ? '单选按钮，订阅签到提醒已开启' : '单选按钮，订阅签到提醒已关闭'

  return (
    <Touch
      onPress={handlePress}
      activeOpacity={1}
      disabled={disabled}
      accessibilityLabel={accessibilityLabel}
      accessibilityRole="switch"
    >
      <Animated.View
        style={[
          containerStyle,
          styles.track,
          getTrackStyle({
            barSize: mBarSize,
            barSpace: mBarSpace,
          }),
          trackAnimationStyle,
        ]}
      >
        <Animated.View style={[styles.bar, barAnimationStyle]} />
      </Animated.View>
    </Touch>
  )
}

const styles = StyleSheet.create({
  track: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: defaultTrackOffColor,
  },
  bar: {
    width: defalutBarSize,
    height: defalutBarSize,
    borderRadius: defalutBarSize / 2,
    backgroundColor: defaultBarColor,
    position: 'absolute',
  },
})

export default React.memo(ReSwitch, isEqual)
