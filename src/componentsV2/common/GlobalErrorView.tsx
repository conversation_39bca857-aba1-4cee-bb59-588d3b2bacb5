import React, { useContext } from 'react'
import { View, Image, NativeModules } from 'react-native'
import styled from 'styled-components'
import { Text, Touch } from '@xmly/rn-components'
import Header from './Headers/Header'
import { Page } from '@xmly/rn-sdk'
import { ThemeStyle } from '../../typesV2/themeInfo'
import { ThemeContext } from '../../contextV2/themeContext'

const Container = styled(View)`
  align-items: center;
  flex: 1;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.bg_color};
  height: 100%;
`
const ErrorImage = styled(Image)`
  margin-top: 190px;
  margin-bottom: 8px;
`
const ErrorText = styled(Text)`
  margin-bottom: 16px;
  font-size: 13px;
  text-align: center;
  color: ${({ theme }: { theme: ThemeStyle }) => theme.errorView.label_color};
`
const ReloadBtn = styled(View)`
  justify-content: center;
  align-items: center;
  width: 121px;
  height: 38px;
  border: 1px solid
    ${({ theme }: { theme: ThemeStyle }) => theme.errorView.button_border_color};
  border-radius: 4px;
`
const ReloadBtnText = styled(Text)`
  font-size: 13px;
  color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.errorView.button_label_color};
`

const GlobalErrorView: React.FC = (props) => {
  const theme = useContext(ThemeContext)
  const reload = () => {
    NativeModules.Page.finish(NativeModules.Page.OK, '')
    Page.start('iting://open?msg_type=94&bundle=rn_credit_center')
  }
  return (
    <>
      <Container>
        <Header label='签到领福利' withoutRight />
        <ErrorImage source={theme.errorView.error_icon} />
        <ErrorText>加载失败，请尝试重试</ErrorText>
        <Touch
          onPress={reload}
          accessibilityLabel='重进首页'
          accessibilityRole='button'
        >
          <ReloadBtn>
            <ReloadBtnText>重进首页</ReloadBtnText>
          </ReloadBtn>
        </Touch>
      </Container>
    </>
  )
}

export default GlobalErrorView
