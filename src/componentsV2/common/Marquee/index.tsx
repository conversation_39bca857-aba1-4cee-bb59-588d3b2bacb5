import isEqual from 'lodash.isequal'
import React, { Component } from 'react'
import {
  StyleSheet,
  LayoutChangeEvent,
  StyleProp,
  ViewStyle,
  View,
  ViewProps,
} from 'react-native'
import Animated, {
  interpolate,
  multiply,
  block,
  cond,
  greaterThan,
  set,
  and,
  Extrapolate,
  divide,
  abs,
  add,
  call,
} from 'react-native-reanimated'
import { loopTiming as loop } from '../../../utilsV2/animations/loopTiming'

type Props = {
  speed?: number
  enable?: boolean
  containerStyle?: StyleProp<ViewStyle>
}

type State = {
  needMarquee: boolean
}

export class Marquee extends Component<Props, State> {
  state = {
    needMarquee: false,
  }

  static defaultMargin = 15

  private clock = new Animated.Clock()
  private contentWidth = new Animated.Value<number>(0)
  private containerWidth = new Animated.Value<number>(0)
  private contentWidthValue = 0
  private containerWidthValue = 0
  private speed =
    (typeof this.props.speed === 'number' ? this.props.speed : 16) / 1000
  private animationValue = new Animated.Value(0)

  private contentLongerThanContainer = () =>
    greaterThan(this.contentWidth, this.containerWidth)

  private contentAndContainerLayout = () =>
    and(this.contentWidth, this.containerWidth)

  private calcDuration = () => {
    return block([
      cond(
        this.contentAndContainerLayout(),
        [abs(divide(this.contentWidth, this.speed))],
        [0]
      ),
    ])
  }

  private contentTranslateX = cond(
    this.contentAndContainerLayout(),
    interpolate(this.animationValue, {
      inputRange: [0.05, 0.95],
      outputRange: [
        0,
        multiply(add(this.contentWidth, Marquee.defaultMargin), -1),
      ],
      extrapolate: Extrapolate.CLAMP,
    }),
    0
  )

  judgeNeedMarquee = () => {
    if (!this.props.enable) {
      return
    }
    if (this.contentWidthValue > 0 && this.containerWidthValue > 0) {
      const diff = Math.floor(this.contentWidthValue - this.containerWidthValue)
      console.log({
        diff,
      })
      if (diff > 0) {
        this.setState({
          needMarquee: true,
        })
      } else {
        this.setState({
          needMarquee: false,
        })
      }
    }
  }

  handleContentLayout = (e: LayoutChangeEvent) => {
    const contentWidth = e.nativeEvent.layout.width
    console.log({ contentWidth })
    this.contentWidth.setValue(contentWidth)
    this.contentWidthValue = contentWidth
    this.judgeNeedMarquee()
  }

  handleContainerLayout = (e: LayoutChangeEvent) => {
    const containerWidth = e.nativeEvent.layout.width
    this.containerWidth.setValue(containerWidth)
    this.containerWidthValue = containerWidth
    this.judgeNeedMarquee()
  }

  componentDidUpdate(prevProps: Props, prevState: State) {
    if (this.props.enable !== prevProps.enable) {
      if (!this.props.enable) {
        this.animationValue.setValue(0)
      }
      if (this.props.enable) {
        this.judgeNeedMarquee()
      }
    }
    if (prevState.needMarquee !== this.state.needMarquee) {
      if (!this.state.needMarquee) {
        this.animationValue.setValue(0)
      }
    }
  }

  shouldComponentUpdate(nextProps: Props, nextState: State) {
    return (
      !isEqual(nextProps, this.props) ||
      this.state.needMarquee !== nextState.needMarquee
    )
  }

  private renderContent = (options?: { withLayout?: boolean }) => {
    return React.isValidElement(this.props.children)
      ? React.cloneElement(
          this.props.children as React.ReactElement<ViewProps>,
          {
            onLayout: options?.withLayout
              ? this.handleContentLayout
              : undefined,
            collapsable: false,
          }
        )
      : null
  }

  render() {
    return (
      <>
        {
          <View
            style={{
              position: 'absolute',
              left: 99999,
              top: 99999,
              opacity: 0,
            }}
          >
            {this.renderContent({ withLayout: true })}
          </View>
        }
        <Animated.View
          style={[this.props.containerStyle, styles.container]}
          onLayout={this.handleContainerLayout}
        >
          <Animated.View
            style={[
              styles.contentContainer,
              {
                width: this.state.needMarquee
                  ? cond(
                      this.contentAndContainerLayout(),
                      cond(
                        this.contentLongerThanContainer(),
                        add(
                          Marquee.defaultMargin,
                          multiply(this.contentWidth, 2)
                        ),
                        this.contentWidth
                      ),
                      this.contentWidth
                    )
                  : this.contentWidth,
                transform: [
                  {
                    translateX: this.contentTranslateX,
                  },
                ],
              },
            ]}
          >
            {this.renderContent()}
            {this.state.needMarquee && (
              <Animated.View
                style={[
                  styles.contentCopy,
                  { left: add(Marquee.defaultMargin, this.contentWidth) },
                ]}
              >
                {this.renderContent()}
              </Animated.View>
            )}
          </Animated.View>
        </Animated.View>
        {this.state.needMarquee && this.props.enable && (
          <Animated.Code
            exec={block([
              cond(this.contentAndContainerLayout(), [
                cond(
                  this.contentLongerThanContainer(),
                  [
                    set(
                      this.animationValue,
                      loop({
                        clock: this.clock,
                        // @ts-ignore
                        duration: this.calcDuration(),
                      })
                    ),
                    call([], () => {
                      if (!this.state.needMarquee) {
                        this.setState({
                          needMarquee: true,
                        })
                      }
                    }),
                  ],
                  [
                    set(this.animationValue, 0),
                    call([], () => {
                      if (this.state.needMarquee) {
                        this.setState({
                          needMarquee: false,
                        })
                      }
                    }),
                  ]
                ),
              ]),
            ])}
          />
        )}
      </>
    )
  }
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    overflow: 'hidden',
    maxWidth: '100%',
    width: '100%',
  },
  contentContainer: {
    flexDirection: 'row',
  },
  contentCopy: {
    position: 'absolute',
  },
})
