import { BackBtnPressList<PERSON><PERSON>elper<PERSON>lain, Touch } from '@xmly/rn-components'
import React, { Component, PropsWithChildren } from 'react'
import styled from 'styled-components'
import Animated, { Easing, Extrapolate, interpolate } from 'react-native-reanimated'
import { StyleProp, ViewStyle } from 'react-native'
import { withTimingTransition } from '../../utilsV2/animations/transitions'

const Container = styled(Animated.View)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`
const TouchMask = styled(Touch).attrs({
  activeOpacity: 1,
})`
  background: rgba(0, 0, 0, 0.7);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  align-items: center;
  justify-content: center;
`
interface Props {
  handleClose: () => void
  closeOnPressModal?: boolean
  maskStyle?: StyleProp<ViewStyle>
}

class Pop extends Component<PropsWithChildren<Props>> {
  animationState = new Animated.Value<number>(0)
  animationValue = withTimingTransition(this.animationState, {
    duration: 300,
    easing: Easing.out(Easing.ease),
  })
  opacity = interpolate(this.animationValue, {
    inputRange: [0, 1],
    outputRange: [0, 1],
    extrapolate: Extrapolate.CLAMP,
  })

  componentDidMount() {
    this.animationState.setValue(1)
  }

  handleBackBtnPress = () => {
    this.props.handleClose()
    return true
  }

  handleClose = () => {
    const { closeOnPressModal = true } = this.props
    if (closeOnPressModal) {
      this.props.handleClose()
    }
  }

  render() {
    return (
      <Container
        style={{ opacity: this.opacity }}
        importantForAccessibility={'yes'}
        accessibilityViewIsModal
      >
        <TouchMask
          onPress={this.handleClose}
          style={[this.props.maskStyle]}
        />
        {this.props.children}
        <BackBtnPressListenerHelperPlain onBackBtnPress={this.handleBackBtnPress} />
      </Container>
    )
  }
}

export default Pop
