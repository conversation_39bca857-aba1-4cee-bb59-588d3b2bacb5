import { View, StyleSheet, Animated, Platform } from 'react-native'
import React from 'react'
import { RootViewManager, TipView } from '@xmly/rn-sdk'
import XMNativeLoadingView from './XMLoadingView.native'

type LoadingViewState = {
  fadeAnim: Animated.Value
}

class LoadingView extends React.Component<{ onTimeout?: () => void; rootViewManager: RootViewManager; timerInterval?: number }, LoadingViewState> {
  constructor(props: any) {
    super(props)
    this.timer = null
  }

  timer: null | NodeJS.Timeout = null

  state = {
    fadeAnim: new Animated.Value(1),
  }

  componentDidMount() {
    if (Platform.OS === 'ios') {
      TipView.showLoading()
    }
    this.timer = setTimeout(
      () => {
        if (typeof this.props.onTimeout === 'function') {
          this.props.onTimeout()
        }
      },
      typeof this.props.timerInterval === 'number' ? this.props.timerInterval : 8000
    )
  }

  componentWillUnmount() {
    this.props.rootViewManager.removeView()
    if (Platform.OS === 'ios') {
      TipView.hideLoading()
    }
    if (this.timer) {
      clearTimeout(this.timer)
    }
  }

  shouldComponentUpdate() {
    return false
  }

  render() {
    if (Platform.OS === 'android') {
      return (
        <View style={[styles.container, StyleSheet.absoluteFillObject]}>
          <XMNativeLoadingView style={styles.loadingView} />
        </View>
      )
    } else {
      return null
    }
  }
}

const XMLoading = () => {
  const rootViewManager = new RootViewManager()
  const show = (onTimeout?: () => void, timerInterval?: number) => {
    rootViewManager.setView(<LoadingView onTimeout={onTimeout} rootViewManager={rootViewManager} timerInterval={timerInterval} />)
  }

  const hide = () => {
    rootViewManager.removeView()
  }

  return {
    show,
    hide,
  }
}

export default XMLoading()

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 999,
  },
  loadingView: {
    width: 50,
    height: 50,
  },
  loadingBg: {
    padding: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 15,
  },
})
