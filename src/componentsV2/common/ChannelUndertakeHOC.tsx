// 渠道承接
import React, { PropsWithChildren, useEffect } from 'react'
import isEqual from 'lodash.isequal'
import { ChannelUndertakeModalStatus } from '../../typesV2/channelModal'
import { store } from '../../store'

type Props = {
  srcChannel?: string // 来源
}

const ChannelUndertakeHOC: React.FC<PropsWithChildren<Props>> = (props) => {
  const { srcChannel } = props

  console.log('❤️❤️❤️❤️❤️❤️', { srcChannel })

  useEffect(() => {
    // 如果iting不带有srcChannel参数，则不展示渠道承接弹窗
    if (!srcChannel || srcChannel === '') {
      store.dispatch.channelUndertake.refresh({
        modalStatus: ChannelUndertakeModalStatus.noNeed,
      })
    } else {
      store.dispatch.channelUndertake.init(srcChannel)
    }
  }, [])

  return <>{props.children}</>
}

export default React.memo(ChannelUndertakeHOC, isEqual)
