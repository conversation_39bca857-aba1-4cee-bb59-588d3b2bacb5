import React, { PropsWithChildren, useCallback, useEffect, useMemo, useState } from 'react'
import { LayoutRectangle } from 'react-native'
import isEqual from 'lodash.isequal'
import MonthlyTicketGuideContext from '../../contextV2/monthlyTicketGuideContext'
import { MonthlyTicketGuideStatus, MonthlyTicketGuideStep } from '../../typesV2/monthlyTicketGuide'
import { useContext } from 'react'
import GrayScaleContext from '../../contextV2/grayScaleContext'
import requestEarnGuideMonthlyTicket from '../../servicesV2/requestEarnGuideMonthlyTicket'
import { store } from '../../store'

type Props = {}

const MonthlyTicketGuideProvider: React.FC<PropsWithChildren<Props>> = (props) => {
  const [hasShowMonthlyTicketGuide, setHasShowMonthlyTicketGuide] = useState(false)
  const { showMonthlyTicketGuide, isOldTypeMonthlyTicket } = useContext(GrayScaleContext)
  const [layoutInfo, setLayoutInfo] = useState({})
  const [guideStatus, setGuideStatus] = useState<MonthlyTicketGuideStatus>(MonthlyTicketGuideStatus.UNSET)
  const [monthlyTicketIconLayoutInfo, setMonthlyTicketIconLayoutInfo] = useState<LayoutRectangle | null>(null)

  const mSetMonthlyTicketIconLayoutInfo = (layoutInfo: LayoutRectangle) => {
    setMonthlyTicketIconLayoutInfo(layoutInfo)
  }

  const fetchGuideStatus = async () => {
    try {
      const res = await requestEarnGuideMonthlyTicket()
      if (res && res.ret === 0 && res.data) {
        setGuideStatus(MonthlyTicketGuideStatus.need)
        setHasShowMonthlyTicketGuide(true)
      } else {
        setGuideStatus(MonthlyTicketGuideStatus.noNeed)
        store.dispatch.monthlyTicket.refreshMonthlyTicketData({
          withMonthlyTicket: isOldTypeMonthlyTicket,
        })
      }
    } catch (err) {
      setGuideStatus(MonthlyTicketGuideStatus.noNeed)
      store.dispatch.monthlyTicket.refreshMonthlyTicketData({
        withMonthlyTicket: isOldTypeMonthlyTicket,
      })
    }
  }

  useEffect(() => {
    if (showMonthlyTicketGuide) {
      fetchGuideStatus()
    } else {
      setGuideStatus(MonthlyTicketGuideStatus.noNeed)
      store.dispatch.monthlyTicket.refreshMonthlyTicketData({
        withMonthlyTicket: isOldTypeMonthlyTicket,
      })
    }
  }, [showMonthlyTicketGuide])

  const onLayout = useCallback(
    (name: MonthlyTicketGuideStep | MonthlyTicketGuideStep[], _layoutInfo: LayoutRectangle & { radius: number }) => {
      if (Array.isArray(name)) {
        name.forEach((n) => {
          layoutInfo[n] = _layoutInfo
          setLayoutInfo({ ...layoutInfo })
        })
      } else {
        layoutInfo[name] = _layoutInfo
        setLayoutInfo({ ...layoutInfo })
      }
    },
    [layoutInfo]
  )

  const mSetHasShowMonthlyTicketGuide = (has: boolean) => {
    setHasShowMonthlyTicketGuide(has)
  }

  const value = useMemo(
    () => ({
      layoutInfo,
      setLayoutInfo: onLayout,
      hasShowMonthlyTicketGuide,
      guideStatus,
      setGuideStatus,
      setMonthlyTicketIconLayoutInfo: mSetMonthlyTicketIconLayoutInfo,
      monthlyTicketIconLayoutInfo,
      setHasShowMonthlyTicketGuide: mSetHasShowMonthlyTicketGuide,
    }),
    [layoutInfo, onLayout, guideStatus, monthlyTicketIconLayoutInfo, hasShowMonthlyTicketGuide]
  )

  return <MonthlyTicketGuideContext.Provider value={value}>{props.children}</MonthlyTicketGuideContext.Provider>
}

export default React.memo(MonthlyTicketGuideProvider, isEqual)
