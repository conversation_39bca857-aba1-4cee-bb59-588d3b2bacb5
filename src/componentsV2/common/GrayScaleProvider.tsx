import React, { PropsWithChildren, useEffect, useMemo, useState } from 'react'
import isEqual from 'lodash.isequal'
import GrayScaleContext from '../../contextV2/grayScaleContext'
import { FetchStatus } from '../../typesV2/common'
import requestReportCashVisit from '../../servicesV2/requestReportCashVisit'
import requestCashConfig from '../../servicesV2/requestCashConfig'
import { CashConfigChangeTaskId, CashConfigExpGroupId } from '../../typesV2/goldCoin'
import goldCoinABValue from '../../modulesV2/goldCoinABValue'
import { defaultGoldCoinABConfig } from '../../constantsV2'

type Props = {}

const GrayScaleProvider: React.FC<PropsWithChildren<Props>> = (props) => {
  const [fetchStatus, setFetchStatus] = useState(FetchStatus.loading)
  const [showGoldCoinModule, setShowGoldCoinModule] = useState(false)
  // const [isNew, setIsNew] = useState(true)
  const [goldCoinABConfig, setGoldCoinABConfig] =
    useState<{ groupId: CashConfigExpGroupId; changeTasksId: CashConfigChangeTaskId; showTutorial: boolean; risky: boolean }>(defaultGoldCoinABConfig)
  // const [isOldTypeMonthlyTicket, setIsOldTypeMonthlyTicket] = useState(false)
  // const [showMonthlyTicketGuide, setShowMonthlyTicketGuide] = useState(false)
  // const [monthlyTicketSignInInfo, setMonthlyTicketSignInInfo] = useState<null | SignInListNewRes>(null)

  // 获取月票的 ab
  // const fetchMonthlyTicketGrayScale = async () => {
  //   try {
  //     const [monthlyGuideRes, monthlyTicketSignInInfoRes]: [any, any] = await Promise.all([
  //       requestMonthlyTicketGuide(),
  //       getSignInListNew({
  //         isMonthlyTicket: true,
  //       }),
  //     ])
  //     const didNotPatMonthlyTicket = monthlyTicketSignInInfoRes?.data?.signInUserInfo?.totalDay === 0

  //     const mShowMonthTicketGuide = monthlyGuideRes.data && monthlyGuideRes.data?.isGray
  //     setShowMonthlyTicketGuide(mShowMonthTicketGuide)

  //     setIsOldTypeMonthlyTicket(!mShowMonthTicketGuide && !didNotPatMonthlyTicket)

  //     setIsNew(!(monthlyGuideRes.data && monthlyGuideRes.data?.isGray && didNotPatMonthlyTicket))
  //     setMonthlyTicketSignInInfo(monthlyTicketSignInInfoRes.data)
  //   } catch (err) {
  //     console.log('fetchGrayScale-err: ', err)
  //   }
  // }

  // 获取金币的 ab 配置
  const fetchGoldCoinGrayScale = async () => {
    let config = defaultGoldCoinABConfig
    try {
      const res = await requestCashConfig()
      if (res) {
        if (res.data.taskPackageCashId === CashConfigExpGroupId['VIP&chip&cash']) {
          // 上报访问了现金活动（因为30 天未访问的用户需要清空他的余额，所以当用户每次进入的时候，需要通知服务端）
          requestReportCashVisit()
        }
        config = {
          changeTasksId: res.data.changeTasksId || CashConfigChangeTaskId.default,
          groupId: res.data?.taskPackageCashId,
          showTutorial: res.data?.showTutorial,
          risky: res.data.risky,
        }
      } else {
        throw new Error()
      }
    } catch (err) {
      console.log(err)
      config = defaultGoldCoinABConfig
    } finally {
      setGoldCoinABConfig(config)
      goldCoinABValue.setValue(config)
      setShowGoldCoinModule(config.groupId === CashConfigExpGroupId['VIP&chip&cash'])
      return config
    }
  }

  const fetchGrayScale = async () => {
    try {
      await fetchGoldCoinGrayScale()
      // // 只有在默认分组才判断月票引导状态
      // if (cashConfig.groupId === CashConfigExpGroupId.default) {
      //   await fetchMonthlyTicketGrayScale()
      // }
    } catch (err) {
      console.log('fetchGrayScale-err: ', err)
    } finally {
      setFetchStatus(FetchStatus.success)
    }
  }

  useEffect(() => {
    fetchGrayScale()
  }, [])

  const renderContent = () => {
    if (fetchStatus === FetchStatus.success) {
      return props.children
    }

    return null
  }

  const value = useMemo(
    () => ({
      isNew: false,
      monthlyTicketSignInInfo: null,
      showMonthlyTicketGuide: false,
      isOldTypeMonthlyTicket: false,
      goldCoinABConfig,
      showGoldCoinModule,
    }),
    [
      goldCoinABConfig,
      showGoldCoinModule,
      // isNew,
      //  monthlyTicketSignInInfo,
      //   showMonthlyTicketGuide,
      //   isOldTypeMonthlyTicket,
    ]
  )

  return <GrayScaleContext.Provider value={value}>{renderContent()}</GrayScaleContext.Provider>
}

export default React.memo(GrayScaleProvider, isEqual)
