import React from 'react'
import { View, StyleSheet } from 'react-native'

type Props = {
  color?: string
  lineRadius?: number
  size?: number
  strokeWidth?: number
}

const styles = StyleSheet.create({
  line: {
    width: 1.5,
    height: 20,
    backgroundColor: '#fff',
    borderRadius: 5,
    position: 'absolute',
  },
  lineWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
  },
})

const CloseIcon: React.FC<Props> = ({
  color = '#fff',
  lineRadius = 5,
  size = 14.14,
  strokeWidth = 1.5,
}) => {
  const height = Math.floor(2 * Math.sqrt(Math.pow(size, 2) / 2))

  return (
    <View style={styles.lineWrapper}>
      <View
        style={[
          styles.line,
          {
            height,
            backgroundColor: color,
            borderRadius: lineRadius,
            width: strokeWidth,
          },
          { transform: [{ rotate: '45deg' }] },
        ]}
      />
      <View
        style={[
          styles.line,
          {
            height,
            backgroundColor: color,
            borderRadius: lineRadius,
            width: strokeWidth,
          },
          { transform: [{ rotate: '135deg' }] },
        ]}
      />
    </View>
  )
}

export default React.memo(CloseIcon, () => true)
