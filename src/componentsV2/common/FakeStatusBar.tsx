import React from 'react'
import { View } from "react-native"
import { useSafeAreaInsets } from 'react-native-safe-area-context'

const FakeStatusBar: React.FC = () => {
  const safeArea = useSafeAreaInsets()
  return (
    <View style={ { height: safeArea.top, position: 'absolute', zIndex: 3, top: 0, left: 0, width: '100%' } } pointerEvents='none' />
  )
}


export default React.memo(FakeStatusBar, () => true)
