import { Touch } from '@xmly/rn-components'
import { hitSlop } from '@xmly/rn-utils'
import isEqual from 'lodash.isequal'
import React, { useContext } from 'react'
import { Image } from 'react-native'
import styled from 'styled-components'
import arrow_left_light from '../../appImagesV2/arrow_left_light'
import { DarkModeContext } from '../../contextV2/darkModeContext'
import { StyleSheet, View } from 'react-native';

const BackBtnWrapper = styled(View)`
  padding: 2px;
  align-items: center;
  position: relative;
`

const Icon = styled(Image)`
  width: 16px;
  height: 16px;
`

const styles = StyleSheet.create({
  area: {
    width: 40,
    height: 40,
    position: 'absolute',
    left: 0,
    right: 0,
    transform: [{ translateX: -10 }, { translateY: -10 }],
  }
});

type Props = {
  onPress?: () => void
}

const BackBtn: React.FC<Props> = (props) => {
  const { isDarkMode } = useContext(DarkModeContext)
  return (
    <BackBtnWrapper>
      <Icon
        source={arrow_left_light}
        resizeMode="contain"
        resizeMethod="resize"
        fadeDuration={0}
        style={{
          tintColor: isDarkMode ? '#fff' : '#111',
        }}
      />
      <Touch
        onPress={props.onPress}
        hitSlop={hitSlop(30)}
        accessibilityLabel="退出积分中心"
        style={[styles.area]}
      />
    </BackBtnWrapper>
  )
}

export default React.memo(BackBtn, isEqual)
