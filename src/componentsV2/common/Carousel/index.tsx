import React, { PropsWithChildren, useState } from 'react'
import { LayoutChangeEvent, View } from 'react-native'
import isEqual from 'lodash.isequal'
import Animated, {
  useValue,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated'
import { loopTiming } from '../../../utilsV2/animations/loopTiming'

type Props = {
  contentList: React.ReactNode[]
}

const Carousel: React.FC<PropsWithChildren<Props>> = ({ contentList }) => {
  const [interval, setInterval] = useState<Animated.Adaptable<number>>(
    useValue(0)
  )

  const [containerHeight, setContainerHeight] = useState(0)

  const handleLayout = (event: LayoutChangeEvent) => {
    setContainerHeight(event.nativeEvent.layout.height)
    setInterval(loopTiming({ duration: 12000, toValue: 12 }))
  }

  const translateY = interpolate(interval, {
    inputRange: [5, 6, 10, 11],
    outputRange: [0, -containerHeight, -containerHeight, -2 * containerHeight],
    extrapolate: Extrapolate.CLAMP,
  })

  return (
    <>
      {containerHeight === 0 ? (
        <View style={{ position: 'absolute' }}>{contentList[0]}</View>
      ) : null}

      <View
        style={{
          overflow: 'hidden',
          height: containerHeight === 0 ? undefined : containerHeight,
          opacity: containerHeight === 0 ? 0 : 1,
        }}
      >
        <Animated.View style={{ transform: [{ translateY }] }}>
          {contentList.map((content, index) => {
            return (
              <View key={index} onLayout={handleLayout}>
                {content}
              </View>
            )
          })}
          <View>{contentList[0]}</View>
        </Animated.View>
      </View>
    </>
  )
}

export default React.memo(Carousel, isEqual)
