import React, { useCallback, useEffect, useState } from 'react'
import { BetterImage, Text, Touch } from '@xmly/rn-components'
import { View, StyleSheet, Image } from 'react-native'
import getImageUri from '../../utilsV2/getImageUri'
import rnEnv from '../../../rnEnv'
import isEqual from 'lodash.isequal'
import icon_suspense_close from '../../appImagesV2/icon_suspense_close'
import {
  ThirdpartyTaskStatus,
  ThirdpartyTaskType,
} from '../../typesV2/thirdpartyTask'
import lodashGet from 'lodash.get'
import ThirdpartyBrowseTaskControl from './ThirdpartyBrowseTaskControl'

type Props = {
  taskDetail: any
  taskFinishedDetail: any
  onFinishTask: () => void
  onClose: () => void
  taskStatus: ThirdpartyTaskStatus
  onPressJumpBack: () => void
}

const defaultIconStyle = {
  width: 92,
  height: 74,
}

const ThirdpartyTaskUI: React.FC<Props> = ({
  taskDetail,
  onFinishTask,
  onClose,
  taskStatus,
  taskFinishedDetail,
  onPressJumpBack,
}) => {
  const [browseTime, setBrowseTime] = useState(taskDetail.countdown)
  console.log({ taskFinishedDetail })
  const taskFinishedTitle = lodashGet(
    taskDetail,
    'taskFinishedConfig.title',
    ''
  )
  const taskFinishedSubTitle = lodashGet(
    taskDetail,
    'taskFinishedConfig.subTitle',
    ''
  )

  useEffect(() => {
    if (taskDetail.taskType === ThirdpartyTaskType.sign) {
      onFinishTask()
    }
  }, [])

  const handleCount = useCallback((count: number) => {
    setBrowseTime(count)
  }, [])

  const handleFinishBrowseTask = useCallback(() => {
    onFinishTask()
  }, [onFinishTask])

  const handleClose = useCallback(() => {
    onClose()
  }, [onClose])

  const handleJumpBack = useCallback(() => {
    onPressJumpBack()
  }, [onPressJumpBack])

  const iconStyleConfigWidth = lodashGet(
    taskDetail,
    'iconStyleDetail.width',
    defaultIconStyle.width
  )
  const iconStyleConfigHeight = lodashGet(
    taskDetail,
    'iconStyleDetail.height',
    defaultIconStyle.height
  )

  const iconUri = getImageUri(taskDetail.icon, {
    width: iconStyleConfigWidth * 3,
    height: iconStyleConfigHeight * 3,
    test: rnEnv.isTest(),
  })

  const renderFinishTitle = () => {
    return (
      <>
        {taskFinishedTitle ? (
          <Text style={styles.tipText}>{taskFinishedTitle}</Text>
        ) : null}
        {taskFinishedSubTitle ? (
          <Text style={styles.tipText}>{taskFinishedSubTitle}</Text>
        ) : null}
      </>
    )
  }

  const renderBrowseTaskContent = () => {
    return (
      <>
        {iconUri && (
          <BetterImage
            style={{
              width: iconStyleConfigWidth,
              height: iconStyleConfigHeight,
            }}
            source={{
              uri: iconUri,
            }}
            resizeMode='contain'
          />
        )}
        <View style={styles.tipTextWrapper}>
          {taskStatus === ThirdpartyTaskStatus.running ? (
            <>
              <Text style={styles.tipText}>
                还剩
                <Text>{browseTime}</Text>s
              </Text>
              <Text style={styles.tipText}>即可领奖</Text>
            </>
          ) : (
            renderFinishTitle()
          )}
        </View>
      </>
    )
  }

  const renderSignTaskContent = () => {
    if (taskStatus === ThirdpartyTaskStatus.finished) {
      return (
        <>
          {iconUri && (
            <BetterImage
              style={{
                width: iconStyleConfigWidth,
                height: iconStyleConfigHeight,
              }}
              source={{
                uri: iconUri,
              }}
              resizeMode='contain'
            />
          )}
          <View style={styles.tipTextWrapper}>{renderFinishTitle()}</View>
        </>
      )
    }
    return null
  }

  const renderContent = () => {
    switch (taskDetail.taskType) {
      case ThirdpartyTaskType.browse:
        return renderBrowseTaskContent()
      case ThirdpartyTaskType.sign:
        return renderSignTaskContent()
      default:
        return null
    }
  }

  if (taskStatus === ThirdpartyTaskStatus.UNSET) {
    return null
  }

  return (
    <>
      <Touch
        style={[
          styles.wrapper,
          { width: iconStyleConfigWidth, height: iconStyleConfigHeight },
        ]}
        onPress={handleJumpBack}
      >
        {renderContent()}
        <Touch style={styles.closeBtnWrapper} onPress={handleClose}>
          <Image source={icon_suspense_close} style={styles.closeBtnIcon} />
        </Touch>
      </Touch>

      {taskDetail.taskType === ThirdpartyTaskType.browse ? (
        <ThirdpartyBrowseTaskControl
          onCount={handleCount}
          onFinish={handleFinishBrowseTask}
          browseTime={browseTime}
          taskStatus={taskStatus}
        />
      ) : null}
    </>
  )
}

const styles = StyleSheet.create({
  wrapper: {
    position: 'absolute',
    bottom: 100,
    right: 8,
    zIndex: 9,
    alignItems: 'center',
  },
  tipTextWrapper: {
    position: 'absolute',
    width: 69,
    bottom: 0,
    paddingBottom: 3,
    justifyContent: 'center',
    alignItems: 'center',
    height: 30,
  },
  tipText: {
    color: '#CF632C',
    fontSize: 10,
    fontWeight: '700',
    textAlign: 'center',
    includeFontPadding: false,
  },
  closeBtnWrapper: {
    position: 'absolute',
    right: 0,
    top: 0,
  },
  closeBtnIcon: {
    width: 18,
    height: 18,
  },
})

export default React.memo(ThirdpartyTaskUI, isEqual)
