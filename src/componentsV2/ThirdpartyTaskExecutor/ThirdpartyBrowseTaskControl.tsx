import React, { useEffect, useRef, useState } from 'react'
import isEqual from 'lodash.isequal'
import CountdownWorker from '../common/CountdownWorker'
import { ThirdpartyTaskStatus } from '../../typesV2/thirdpartyTask'
import {
  AppState,
  AppStateStatus,
  NativeEventEmitter,
  NativeModules,
} from 'react-native'
import { usePageOnResume } from '@xmly/rn-utils'

type Props = {
  taskStatus: ThirdpartyTaskStatus
  browseTime: number
  onCount: (count: number) => void
  onFinish: () => void
}
const PageEventEmitter = new NativeEventEmitter(NativeModules.Page)

const ThirdpartyBrowseTaskControl: React.FC<Props> = ({
  taskStatus,
  browseTime,
  onCount,
  onFinish,
}) => {
  const [isBrowsing, setIsBrowsing] = useState(true)
  // const pageResumeListener = useRef<any>(null)
  const pagePauseListener = useRef<any>(null)
  const removePageListener = () => {
    // pageResumeListener.current && pageResumeListener.current.remove()
    pagePauseListener.current && pagePauseListener.current.remove()
  }

  const startBrowsing = () => {
    taskStatus !== ThirdpartyTaskStatus.finished && setIsBrowsing(true)
  }

  const handleAppStateChange = (state: AppStateStatus) => {
    if (state === 'active') {
      startBrowsing()
    } else {
      setIsBrowsing(false)
    }
  }

  usePageOnResume(startBrowsing)

  useEffect(() => {
    AppState.addEventListener('change', handleAppStateChange)
    // pageResumeListener.current = PageEventEmitter.addListener(
    //   'onResume',
    //   startBrowsing
    // )
    pagePauseListener.current = PageEventEmitter.addListener('onPause', () => {
      setIsBrowsing(false)
    })
    return removePageListener
  }, [startBrowsing])
  if (isBrowsing) {
    return (
      <CountdownWorker
        count={browseTime}
        onCount={onCount}
        onTimeout={onFinish}
      />
    )
  }
  return null
}

export default React.memo(ThirdpartyBrowseTaskControl, isEqual)
