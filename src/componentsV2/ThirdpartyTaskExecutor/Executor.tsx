import React, { useEffect, useRef, useState } from 'react'
import finishThirdpartyTask from '../../servicesV2/finishThirdpartyTask'
import getThirdpartyTaskDetail from '../../servicesV2/getThirdpartyTaskDetail'
import isEqual from 'lodash.isequal'
import uuid from '../../utilsV2/uuid'
import { Toast } from '@xmly/rn-sdk'
import ThirdpartyTaskUI from './ThirdpartyTaskUI'
import {
  ThirdpartyTaskStatus,
  ThirdpartyTaskStatusLabel,
  ThirdpartyTaskType,
} from '../../typesV2/thirdpartyTask'
import { store } from '../../store'
import SentryUtils from '../../utilsV2/sentryUtils'
import lodashGet from 'lodash.get'
import openThirdpartyApp from '../../utilsV2/openThirdpartyApp'
import xmlog from '../../utilsV2/xmlog'
import generateSig from '../../utilsV2/generateSig'

type Props = {
  channelName: string
  task: string
  token: string
}

const Executor: React.FC<Props> = ({ channelName, token, task }) => {
  const [taskDetail, setTaskDetail] = useState<any>(null)
  const [taskStatus, setTaskStatus] = useState<ThirdpartyTaskStatus>(
    ThirdpartyTaskStatus.UNSET
  )
  const [taskFinishedDetail, setTaskFinishedDetail] = useState<any>(null)

  const retryCount = useRef(0)

  useEffect(() => {
    fetchThirdpartyTaskDetail()
  }, [])

  useEffect(() => {
    if (taskStatus === ThirdpartyTaskStatus.running) {
      xmlog.event(40050, 'slipPage', {
        moduleName: ThirdpartyTaskStatusLabel.running,
        sourceType: channelName,
        currPage: '任务中心',
      })
    }

    if (taskStatus === ThirdpartyTaskStatus.finished) {
      xmlog.event(40050, 'slipPage', {
        moduleName: ThirdpartyTaskStatusLabel.finished,
        sourceType: channelName,
        currPage: '任务中心',
      })
    }
  }, [taskStatus])

  const removeTask = () => {
    store.dispatch.thirdpartyTask.refresh({ isExist: false })
  }

  const fetchThirdpartyTaskDetail = async () => {
    try {
      const res = await getThirdpartyTaskDetail(channelName!)
      const resData = lodashGet(res, 'data[0]', null)
      if (!resData) {
        // 如果获取配置异常，则把任务去掉
        removeTask()
        return
      }
      setTaskDetail(resData)
      if (res.data[0].taskType === ThirdpartyTaskType.sign) {
        // 如果是签到类型，直接发起完成任务的请求
        await handleFinishTask(true)
      } else {
        setTaskStatus(ThirdpartyTaskStatus.running)
      }
    } catch (err) {
      console.log(err)
      removeTask()
      SentryUtils.captureException(err, {
        source: 'Executor.fetchThirdpartyTaskDetail',
      })
    }
  }

  const handleFinishTask = async (shouldRemoveAfterFinished?: boolean) => {
    retryCount.current += 1
    try {
      const params = {
        channelName,
        token,
        task,
        nonce: uuid(),
        channelId: taskDetail.channelId,
        timestamp: Date.now(),
      }
      const sig = await generateSig(params)
      if (sig) {
        const res = await finishThirdpartyTask({
          ...params,
          sig,
        })
        if (res.code === 200) {
          setTaskStatus(ThirdpartyTaskStatus.finished)
          if (shouldRemoveAfterFinished) {
            removeTask()
          }
          setTaskFinishedDetail(res.data)
        } else {
          throw new Error('完成失败')
        }
      } else {
        throw new Error('sig 生成失败')
      }
    } catch (err) {
      if (retryCount.current < 2) {
        handleFinishTask()
      } else {
        Toast.info(taskDetail.failToast || '任务完成失败～')
        removeTask()
      }
      SentryUtils.captureException(err, {
        source: 'Executor.handleFinishTask',
      })
    }
  }

  const handleClose = () => {
    removeTask()
  }

  const handlePressJumpBack = () => {
    if (taskStatus === ThirdpartyTaskStatus.finished) {
      const schema = lodashGet(taskFinishedDetail, 'schema', '')
      const h5Link = lodashGet(taskFinishedDetail, 'url', '')
      openThirdpartyApp({
        schema,
        h5Link,
        onFail: () => {
          Toast.info('打开失败')
        },
      })
      xmlog.click(40049, undefined, {
        moduleName: ThirdpartyTaskStatusLabel.finished,
        currPage: '任务中心',
        sourceType: channelName,
      })
      handleClose()
    } else {
      xmlog.click(40049, undefined, {
        moduleName: ThirdpartyTaskStatusLabel.running,
        currPage: '任务中心',
        sourceType: channelName,
      })
    }
  }

  if (taskDetail) {
    return (
      <ThirdpartyTaskUI
        onPressJumpBack={handlePressJumpBack}
        onClose={handleClose}
        taskDetail={taskDetail}
        taskStatus={taskStatus}
        taskFinishedDetail={taskFinishedDetail}
        onFinishTask={handleFinishTask}
      />
    )
  }
  return null
}

export default React.memo(Executor, isEqual)
