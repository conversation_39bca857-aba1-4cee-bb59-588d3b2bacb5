import React, { PureComponent, Suspense } from 'react'
import { connect } from 'react-redux'
import { NativeInfoContext } from '../../contextV2/nativeInfoContext'
import { RootState, store } from '../../store'
import SentryUtils from '../../utilsV2/sentryUtils'

const ExecutorLazy = React.lazy(() => import('./Executor'))
type Props = { isExist: boolean }

class ThirdpartyTaskExecutor extends PureComponent<Props> {
  componentDidCatch(err: any) {
    store.dispatch.thirdpartyTask.refresh({
      isExist: false,
    })
    SentryUtils.captureException(err, {
      source: 'ThirdpartyTaskExecutor.componentDidCatch',
    })
  }

  render() {
    if (!this.props.isExist) return null
    return (
      <NativeInfoContext.Consumer>
        {({ channelName, task, token }) => (
          <Suspense fallback={null}>
            <ExecutorLazy
              channelName={channelName!}
              task={task!}
              token={token!}
            />
          </Suspense>
        )}
      </NativeInfoContext.Consumer>
    )
  }
}

const mapStateToProps = (state: RootState) => {
  return {
    isExist: state.thirdpartyTask.isExist,
  }
}

export default connect(mapStateToProps)(ThirdpartyTaskExecutor)
