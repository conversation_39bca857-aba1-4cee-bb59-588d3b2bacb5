import React from 'react'
import { Image, View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { MonthlyTicketGuideStep } from '../../typesV2/monthlyTicketGuide'
import { Title, ButtonLabel, Button } from './styles'
import icon_mt_guide_arrow_left from '../../appImagesV2/icon_mt_guide_arrow_left'
type Props = {
  onPress: (step: MonthlyTicketGuideStep) => void
  top?: number
  left?: number
}

const Container = styled(View)`
  align-items: center;
  position: absolute;
`

const ArrowIconWrapper = styled(View)`
  width: 22.19px;
  height: 71.56px;
  margin-bottom: 20px;
`

const ArrowIcon = styled(Image)`
  width: 100%;
  height: 100%;
`

const GuideWordsB: React.FC<Props> = ({ onPress, top }) => {
  const handlePress = () => onPress(MonthlyTicketGuideStep.VoteModalB)
  return (
    <Container style={{ top }}>
      <ArrowIconWrapper>
        <ArrowIcon source={icon_mt_guide_arrow_left} resizeMode='contain' />
      </ArrowIconWrapper>
      <Title>连续签到领月票</Title>
      <Button onPress={handlePress} style={{ marginTop: 80 }}>
        <ButtonLabel>返回投票</ButtonLabel>
      </Button>
    </Container>
  )
}

export default React.memo(GuideWordsB, isEqual)
