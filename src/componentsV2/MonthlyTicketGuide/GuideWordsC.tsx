import React from 'react'
import { Image, View } from 'react-native'
import isEqual from 'lodash.isequal'
import { Title, Content } from './styles'
import styled from 'styled-components'
import icon_mt_guide_arrow_right from '../../appImagesV2/icon_mt_guide_arrow_right'

type Props = {
  top?: number
  left?: number
}

const Container = styled(View)`
  align-items: center;
  position: absolute;
`

const ArrowWrapper = styled(View)`
  width: 41.2px;
  height: 112.21px;
  margin-bottom: 20px;
  align-self: flex-end;
  margin-right: 14px;
`

const ArrowIcon = styled(Image)`
  width: 100%;
  height: 100%;
`

const GuideWordsC: React.FC<Props> = ({ top, left }) => {
  return (
    <Container style={{ top, left }}>
      <ArrowWrapper>
        <ArrowIcon source={icon_mt_guide_arrow_right} />
      </ArrowWrapper>
      <Title>投月票</Title>

      <Content>去试试给喜欢的作品投月票吧</Content>
    </Container>
  )
}

export default React.memo(GuideWordsC, isEqual)
