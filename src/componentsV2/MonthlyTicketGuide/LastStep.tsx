import React, { useRef } from 'react'
import { Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import { Image } from 'react-native'
import icon_guide_pointer from '../../appImagesV2/icon_guide_pointer'
import { hitSlop } from '@xmly/rn-utils'
import Animated, {
  Easing,
  interpolate,
  useValue,
} from 'react-native-reanimated'
import { loopTiming } from '../../utilsV2/animations/loopTiming'

type Props = {
  onPress: () => void
  positionStyle: {
    top: number
    left: number
    width: number
    height: number
  }
}

const Container = styled(Touch)`
  position: absolute;
`

const PointerWrapper = styled(Animated.View)`
  width: 60px;
  height: 60px;
  position: absolute;
`

const PointerImage = styled(Image)`
  width: 100%;
  height: 100%;
`

const LastStep: React.FC<Props> = ({ onPress, positionStyle }) => {
  const animationValue = useValue(0)
  const move = useRef(
    loopTiming({
      from: animationValue,
      duration: 300,
      easing: Easing.inOut(Easing.ease),
      boomerang: true,
    })
  ).current

  const transformStyle = useRef([
    {
      translateY: interpolate(move, {
        inputRange: [0, 1],
        outputRange: [0, -10],
      }),
    },
    {
      scale: interpolate(move, {
        inputRange: [0, 1],
        outputRange: [1, 0.95],
      }),
    },
    {
      translateX: interpolate(move, {
        inputRange: [0, 1],
        outputRange: [5, 0],
      }),
    },
  ]).current

  return (
    <Container
      onPress={onPress}
      style={{
        left: positionStyle.left,
        top: positionStyle.top,
        width: positionStyle.width,
        height: positionStyle.height,
      }}
      hitSlop={hitSlop(20)}
    >
      <PointerWrapper
        pointerEvents='none'
        style={{
          right: -10,
          top: '50%',
          transform: transformStyle,
        }}
      >
        <PointerImage source={icon_guide_pointer} />
      </PointerWrapper>
    </Container>
  )
}

export default React.memo(LastStep, isEqual)
