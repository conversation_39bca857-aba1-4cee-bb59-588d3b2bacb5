import { Text, Touch } from '@xmly/rn-components'
import styled from 'styled-components'

export const Title = styled(Text)`
  font-size: 22px;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 8px;
`

export const Content = styled(Text)`
  font-size: 18px;
  color: #ffffff;
`

export const HighlightContent = styled(Content)`
  color: #ff4444;
`

export const Button = styled(Touch)`
  display: flex;
  padding: 8px 43px;
  border-radius: 34px;
  border: 1px solid #ffffff;
  margin-top: 230;
`

export const ButtonLabel = styled(Text)`
  font-size: 17px;
  color: #ffffff;
`
