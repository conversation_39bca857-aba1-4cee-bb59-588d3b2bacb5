import React, { useContext, useEffect, useState } from 'react'
import { BetterImage, Text, Touch } from '@xmly/rn-components'
import { Image, View } from 'react-native'
import isEqual from 'lodash.isequal'
import styled from 'styled-components'
import Animated, {
  call,
  cond,
  eq,
  Extrapolate,
  greaterThan,
  set,
  useCode,
  useValue,
} from 'react-native-reanimated'
import { textAlignMiddleStyle } from '../../constantsV2/layout'
import { runTiming } from '../../utilsV2/animations/runTiming'
import { width } from '../../constantsV2/dimensions'
import monthlyTicketGuideContext from '../../contextV2/monthlyTicketGuideContext'

type Props = {
  onConfirm: () => void
}

const iconSize = 80

const Wrapper = styled(Animated.View)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
`

const HeaderCoverWrapper = styled(Animated.View)`
  width: 246.23px;
  height: 80.32px;
  margin-bottom: 45.22px;
  align-self: center;
  margin-top: 240.7px;
`

const HeaderCover = styled(BetterImage)`
  width: 100%;
  height: 100%;
`

const ModalContentWrapper = styled(Animated.View)`
  width: 275px;
  height: 198.31px;
  border-radius: 10px;
  background: #ffffff;
  align-items: center;
  padding: 56.5px 20px 0;
  align-self: center;
`

const MonthlyTicketIconWrapper = styled(Animated.View)`
  position: absolute;
  width: ${iconSize}px;
  height: ${iconSize}px;
  z-index: 1;
`

const MonthlyTicketIcon = styled(Image)`
  width: 100%;
  height: 100%;
`

const ModalLabel = styled(Text)`
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  text-align: center;
  color: #333333;
`

const ModalLabelHighlight = styled(ModalLabel)`
  color: #ff4444;
`

const ButtonWrapper = styled(Touch)`
  width: 151px;
  height: 40px;
  border-radius: 34px;
  background: #ff4444;
  align-items: center;
  justify-content: center;
  margin-top: 28px;
`

const ButtonLabel = styled(Text)`
  font-family: PingFangSC-Medium;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
`

const monthlyTicketIconInitPosition = {
  left: (width - iconSize) / 2,
  top: 329,
}

const animationValueStage = {
  init: 0,
  one: 1,
  middle: 2,
  final: 3.5,
}

const MonthlyTicketAwardModal: React.FC<Props> = ({ onConfirm }) => {
  const animationStage = useValue<number>(-1)
  const animationValue = useValue(animationValueStage.init)
  const [animationFinished, setAnimationFinished] = useState(false)
  const { monthlyTicketIconLayoutInfo } = useContext(monthlyTicketGuideContext)

  useEffect(() => {
    const timer = setTimeout(() => {
      animationStage.setValue(0)
    }, 10)

    return () => {
      clearTimeout(timer)
    }
  }, [])

  useCode(() => {
    return [
      cond(eq(animationStage, 0), [
        set(
          animationValue,
          runTiming({
            duration: 300,
            from: animationValue,
            to: animationValueStage.one,
          })
        ),
      ]),
      cond(eq(animationStage, 1), [
        set(
          animationValue,
          runTiming({
            duration: 700,
            from: animationValue,
            to: animationValueStage.final,
          })
        ),
      ]),

      cond(eq(animationValue, animationValueStage.final), [
        call([], () => {
          setAnimationFinished(true)
        }),
      ]),
    ]
  }, [])

  const handlePress = () => {
    onConfirm()
    animationStage.setValue(1)
  }

  const iconAnimationStyle = {
    transform: [
      { translateY: -iconSize / 2 },
      { translateX: -iconSize / 2 },
      {
        translateY: animationValue.interpolate({
          inputRange: [
            animationValueStage.init,
            animationValueStage.middle,
            animationValueStage.final,
          ],
          outputRange: [
            monthlyTicketIconInitPosition.top,
            monthlyTicketIconInitPosition.top,
            monthlyTicketIconLayoutInfo?.y || monthlyTicketIconInitPosition.top,
          ],
          extrapolate: Extrapolate.CLAMP,
        }),
      },
      {
        translateX: animationValue.interpolate({
          inputRange: [
            animationValueStage.init,
            animationValueStage.middle,
            animationValueStage.final,
          ],
          outputRange: [
            monthlyTicketIconInitPosition.left,
            monthlyTicketIconInitPosition.left,
            monthlyTicketIconLayoutInfo?.x ||
              monthlyTicketIconInitPosition.left,
          ],
          extrapolate: Extrapolate.CLAMP,
        }),
      },
      {
        scale: animationValue.interpolate({
          inputRange: [animationValueStage.middle, animationValueStage.final],
          outputRange: [
            1,
            (monthlyTicketIconLayoutInfo?.width || 0) / iconSize,
          ],
          extrapolate: Extrapolate.CLAMP,
        }),
      },
      { translateY: iconSize / 2 },
      { translateX: iconSize / 2 },
    ],
    opacity: animationValue.interpolate({
      inputRange: [animationValueStage.middle, animationValueStage.final],
      outputRange: [1, 0],
      extrapolate: Extrapolate.CLAMP,
    }),
  }

  const headerCoverAnimationStyle = {
    opacity: animationValue.interpolate({
      inputRange: [animationValueStage.one, animationValueStage.middle],
      outputRange: [1, 0],
      extrapolate: Extrapolate.CLAMP,
    }),
    transform: [
      {
        scale: animationValue.interpolate({
          inputRange: [animationValueStage.init, 0.7],
          outputRange: [0, 1],
          extrapolate: Extrapolate.CLAMP,
        }),
      },
    ],
  }

  const modalContentWrapperAnimationStyle = {
    opacity: animationValue.interpolate({
      inputRange: [animationValueStage.one, animationValueStage.middle],
      outputRange: [1, 0],
      extrapolate: Extrapolate.CLAMP,
    }),
  }

  if (animationFinished) {
    return null
  }

  return (
    <Wrapper
      style={{
        opacity: cond(eq(animationValue, animationValueStage.final), 0, 1),
      }}
    >
      <HeaderCoverWrapper style={headerCoverAnimationStyle}>
        <HeaderCover
          source={{
            uri: 'https://imagev2.xmcdn.com/storages/c367-audiofreehighqps/36/50/GKwRIUEHPlmGAABD8gHFE2Tm.png',
          }}
        />
      </HeaderCoverWrapper>

      <MonthlyTicketIconWrapper style={iconAnimationStyle}>
        <MonthlyTicketIcon
          source={{
            uri: 'https://imagev2.xmcdn.com/storages/3b33-audiofreehighqps/DE/FC/GKwRIasHNqA_AAAp9QG-oIFS.png',
          }}
        />
      </MonthlyTicketIconWrapper>

      <ModalContentWrapper style={modalContentWrapperAnimationStyle}>
        <ModalLabel>
          月票是<ModalLabelHighlight>支持节目</ModalLabelHighlight>
          的有力道具，投票能让更多用户看到节目
        </ModalLabel>

        <ButtonWrapper onPress={handlePress}>
          <ButtonLabel style={textAlignMiddleStyle}>立即收下</ButtonLabel>
        </ButtonWrapper>
      </ModalContentWrapper>
    </Wrapper>
  )
}

export default React.memo(MonthlyTicketAwardModal, isEqual)
