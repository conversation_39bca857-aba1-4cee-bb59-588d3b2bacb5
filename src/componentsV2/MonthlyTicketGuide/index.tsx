import React, { useCallback, useContext, useEffect, useRef, useState } from 'react'
import { LayoutChangeEvent, NativeModules, View } from 'react-native'
import isEqual from 'lodash.isequal'
import { Path, Svg } from '@xmly/react-native-svg'
import monthlyTicketGuideContext from '../../contextV2/monthlyTicketGuideContext'
import rect2path from '../../utilsV2/rect2path'
import { MonthlyTicketGuideStatus, MonthlyTicketGuideStep } from '../../typesV2/monthlyTicketGuide'
import GuideWordsB from './GuideWordsB'
import GuideWordsC from './GuideWordsC'
import LastStep from './LastStep'
import goToMonthlyTicketVote from '../../utilsV2/goToMonthlyTicketVote'
import MonthlyTicketAwardModal from './MonthlyTicketAwardModal'
import { store } from '../../store'
import GlobalEventEmitter from '../../utilsV2/globalEventEmitter'
import { NativeInfoContext } from '../../contextV2/nativeInfoContext'
import { MonthlyTicketVoteModalSrcName } from '../../constantsV2'
import { Page } from '@xmly/rn-sdk'

type Props = {}

const MonthlyTicketGuideStepSetCommon = {
  [MonthlyTicketGuideStep.Modal]: {
    next: MonthlyTicketGuideStep.NormalA,
    done: false,
  },
  [MonthlyTicketGuideStep.NormalA]: {
    next: MonthlyTicketGuideStep.NormalB,
    done: false,
  },
  [MonthlyTicketGuideStep.NormalB]: { next: null, done: true },
}

const MonthlyTicketGuideStepSetFromVoteModal = {
  [MonthlyTicketGuideStep.Modal]: {
    next: MonthlyTicketGuideStep.VoteModalA,
    done: false,
  },
  [MonthlyTicketGuideStep.VoteModalA]: {
    next: MonthlyTicketGuideStep.VoteModalB,
    done: false,
  },
  [MonthlyTicketGuideStep.VoteModalB]: {
    next: null,
    done: true,
  },
}

const Index: React.FC<Props> = (props) => {
  const { srcChannel } = useContext(NativeInfoContext)
  const isFromVoteModal = srcChannel === MonthlyTicketVoteModalSrcName
  const MonthlyTicketGuideStepSet = !isFromVoteModal ? MonthlyTicketGuideStepSetCommon : MonthlyTicketGuideStepSetFromVoteModal

  const { layoutInfo, setGuideStatus } = useContext(monthlyTicketGuideContext)
  const [basePath, setBasePath] = useState('')
  const [clipPath, setClipPath] = useState('')
  const [currentStep, setCurrentStep] = useState(MonthlyTicketGuideStep.Modal)
  const fullPath = basePath + clipPath
  const currentLayoutInfo = layoutInfo[currentStep]
  const skipTimer = useRef<null | NodeJS.Timeout>(null)
  const confirmTimer = useRef<null | NodeJS.Timeout>(null)

  const handleMonthlyTicketAnimationFinished = useCallback(
    (options?: { isSkip?: boolean; nextStep: MonthlyTicketGuideStep }) => {
      const mCurrentStep = options?.nextStep || currentStep
      if (options?.isSkip || mCurrentStep === MonthlyTicketGuideStep.NormalA || mCurrentStep === MonthlyTicketGuideStep.VoteModalA) {
        skipTimer.current && clearTimeout(skipTimer.current)
        handlePress(mCurrentStep)
      }
    },
    [currentStep]
  )

  useEffect(() => {
    const listener = GlobalEventEmitter.addListener('monthlyTicketAnimationFinished', handleMonthlyTicketAnimationFinished)
    return () => {
      listener.remove()
    }
  }, [handleMonthlyTicketAnimationFinished])

  useEffect(() => {
    return () => {
      confirmTimer.current && clearTimeout(confirmTimer.current)
      skipTimer.current && clearTimeout(skipTimer.current)
    }
  }, [])

  useEffect(() => {
    if (currentLayoutInfo) {
      const { x, y, width, height, radius } = currentLayoutInfo
      const path = rect2path(x, y, width, height, radius, radius)
      setClipPath(path)
    }
  }, [layoutInfo, currentLayoutInfo])

  const handleLayout = (layoutEvent: LayoutChangeEvent) => {
    const { height, width } = layoutEvent.nativeEvent.layout
    setBasePath(`M 0 0 L ${width} 0 L ${width} ${height} L 0 ${height}Z`)
  }

  const handlePress = (step: MonthlyTicketGuideStep) => {
    const nextStepInfo = MonthlyTicketGuideStepSet[step]
    if (nextStepInfo.done) {
      handlePressLastStep()
      return null
    } else {
      setCurrentStep(nextStepInfo.next!)
      return nextStepInfo.next!
    }
  }

  const handlePressLastStep = () => {
    if (isFromVoteModal) {
      Page.start('iting://open?msg_type=371')
      NativeModules.Page.finish(NativeModules.Page.OK, '')
    } else {
      goToMonthlyTicketVote()
    }
    setGuideStatus(MonthlyTicketGuideStatus.noNeed)
  }

  const handleModalConfirm = () => {
    confirmTimer.current = setTimeout(() => {
      const nextStep = handlePress(MonthlyTicketGuideStep.Modal)

      store.dispatch.monthlyTicket.getMonthlyTicketInfo()

      skipTimer.current = setTimeout(
        () => {
          handleMonthlyTicketAnimationFinished({ isSkip: true, nextStep })
        },
        isFromVoteModal ? 1300 : 1200
      )
    }, 230)
  }

  return (
    <View
      onLayout={handleLayout}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        alignItems: 'center',
        zIndex: 2,
      }}
    >
      <Svg>{basePath ? <Path d={fullPath} fill="rgba(0, 0, 0, 0.7)" fillRule="evenodd" /> : null}</Svg>
      {basePath ? <MonthlyTicketAwardModal onConfirm={handleModalConfirm} /> : null}

      {currentLayoutInfo ? (
        <>
          {currentStep === MonthlyTicketGuideStep.VoteModalB && isFromVoteModal ? (
            <GuideWordsB onPress={handlePress} top={currentLayoutInfo.y + currentLayoutInfo.height + 20} />
          ) : null}
          {currentStep === MonthlyTicketGuideStep.NormalB && !isFromVoteModal ? (
            <>
              <GuideWordsC top={currentLayoutInfo.y + currentLayoutInfo.height + 33} />
              <LastStep
                positionStyle={{
                  left: currentLayoutInfo.x,
                  top: currentLayoutInfo.y,
                  width: currentLayoutInfo.width,
                  height: currentLayoutInfo.height,
                }}
                onPress={handlePressLastStep}
              />
            </>
          ) : null}
        </>
      ) : null}
    </View>
  )
}

export default React.memo(Index, isEqual)
