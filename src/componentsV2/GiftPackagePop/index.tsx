import isEqual from 'lodash.isequal'
import React, { useContext } from 'react'
import { connect } from 'react-redux'
import monthlyTicketGuideContext from '../../contextV2/monthlyTicketGuideContext'
import { RootState } from '../../store'
import { MonthlyTicketGuideStatus } from '../../typesV2/monthlyTicketGuide'

const GiftPackagePop = React.lazy(() => import('./GiftPackagePop'))

type Props = {
	hasShowOneModalBefore: boolean /** 之前未登录时是否显示过弹窗（承接｜未登录签到） */
	signInBigGiftPackageModalVisible: boolean /** 是否显示连签礼包弹窗 */
}

const Index = ({hasShowOneModalBefore, signInBigGiftPackageModalVisible }: Props) => {
	const { guideStatus } = useContext(monthlyTicketGuideContext)
	if (
		!hasShowOneModalBefore &&
		signInBigGiftPackageModalVisible &&
		guideStatus === MonthlyTicketGuideStatus.noNeed
	) {
		return (
			<React.Suspense fallback={null}>
				<GiftPackagePop />
			</React.Suspense>
		)
	}

	return null
}

export default connect(
	({ signInInfo: { signInBigGiftPackageModalVisible, hasShowOneModalBefore } }: RootState) => ({
		signInBigGiftPackageModalVisible, hasShowOneModalBefore
	})
)(React.memo(Index, isEqual))
