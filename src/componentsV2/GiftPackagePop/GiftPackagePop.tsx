import React, { useEffect, useState } from 'react'
import { Animated, TouchableOpacity, Text, View } from 'react-native'
import styled from 'styled-components/native'
import gift_package_title_img from '../../appImagesV2/gift_package_title_img'
import { store } from '../../store'
import xmlog from 'utilsV2/xmlog'

const gift_package_content_img = {
  uri: 'https://imagev2.xmcdn.com/storages/be40-audiofreehighqps/84/E1/GKwRIDoILmq_AACuwwIZKooB.png',
}

const Overlay = styled(View)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  justify-content: center;
  align-items: center;
`

const Image1 = styled(Animated.Image)`
  width: 149px;
  height: 72px;
`

const Image2 = styled(Animated.Image)`
  width: 244px;
  height: 170px;
  margin-top: 18px;
`

const Button = styled(TouchableOpacity)`
  width: 124px;
  height: 40px;
  align-items: center;
  justify-content: center;
  background-color: #ff4444;
  border-radius: 30px;
  margin-top: 22px;
`

const ButtonText = styled(Text)`
  color: #fff;
  font-size: 16px;
`

interface PopupProps {}

let timer: NodeJS.Timeout

const Popup: React.FC<PopupProps> = () => {
  const [scale1] = useState(new Animated.Value(0))
  const [scale2] = useState(new Animated.Value(0))
  const [countdown, setCountdown] = useState(3)

  useEffect(() => {
    Animated.parallel([
      Animated.timing(scale1, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
      Animated.timing(scale2, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }),
    ]).start()

    timer = setInterval(() => {
      setCountdown((prevCountdown) => prevCountdown - 1)
    }, 1000)

    return () => {
      clearInterval(timer)
    }
  }, [scale1, scale2])

  useEffect(() => {
    if (countdown === 0) {
      onButtonClick()
    }
  }, [countdown])

  useEffect(() => {
    // 任务中心-获得礼包弹窗  控件曝光
    xmlog.event(53938, 'slipPage', { currPage: '任务中心' })
  }, [])

  const onButtonClick = () => {
    // 任务中心-获得礼包弹窗  点击事件
    xmlog.click(53937, undefined, { currPage: '任务中心' })
    clearInterval(timer)
    store.dispatch.signInInfo.refresh({
      signInBigGiftPackageModalVisible: false,
    })
  }

  return (
    <Overlay>
      <Image1
        source={gift_package_title_img}
        style={{ transform: [{ scale: scale1 }] }}
      />
      <Image2
        source={gift_package_content_img}
        style={{ transform: [{ scale: scale2 }] }}
      />
      <Button onPress={onButtonClick}>
        <ButtonText>
          {countdown > 0 ? `立即打开 ${countdown}s` : '立即打开'}
        </ButtonText>
      </Button>
    </Overlay>
  )
}

export default Popup
