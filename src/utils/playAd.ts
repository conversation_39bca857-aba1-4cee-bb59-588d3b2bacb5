import { Toast } from "@xmly/rn-sdk";
import { isAndroid, isIOS } from "@xmly/rn-utils";
import customReportError from "utilsV2/customReportError";
import { listenEarnRewardCoin } from "utilsV2/native";

interface PlayAdParams {
  positionName: string;
  positionId: number;
  extInfo?: string;
  successInfo?: string;
  failInfo?: string;
  otherParams?: Record<string, any>;
}

export default async function playAd(params: PlayAdParams) {
  const {
    positionName,
    positionId,
    extInfo,
    successInfo,
    failInfo,
    otherParams
  } = params;
  try {
    const options = {
      positionName: positionName,
      slotId: positionId,
      extInfo, // 透传给服务端的数据
      rewardVideoStyle: 1, // 表示使用新的看视频逻辑，客户端完成任务。
      otherParams
    };
    const res = await listenEarnRewardCoin(options);
    console.info('debug_listenEarnRewardCoin', res);
    // iOS端clientCode的返回值为'', 则成功；若失败，会走catch
    // android端clientCode的返回值为'success', 则成功；若失败，会走catch
    if (
      (res.clientCode === '' && isIOS) ||
      (String(res.clientCode) === '10003' && isAndroid)
      || (res?.clientCode === 'fallBackReq' && isIOS) // iOS客户端超时兜底逻辑，会直接发放奖励
      || (String(res.clientCode) === '10004' && isAndroid) // android客户端超时兜底逻辑，会直接发放奖励
    ) {
      successInfo && Toast.info(successInfo);
      if (res?.clientCode === 'fallBackReq' || String(res?.clientCode) === '10004') {
        return 'fallback'
      }
      return true;
    } else {
      customReportError({ source: 'playAd.invalid_result', error: res?.toString() === '[object Object]' ? JSON.stringify(res) : res })
      failInfo && Toast.info(failInfo);
      return false;
    }
  } catch (err) {
    console.info('debug_listenEarnRewardCoin_err', err);
    customReportError({ source: 'playAd.catch', error: err })
    failInfo && Toast.info(failInfo)
    return false;
  }
}