/**
 * 检查值是否有效 (非 null 且非 undefined)
 */
function isValidValue<T>(value: T): boolean {
  return value !== null && value !== undefined;
}

/**
 * 从源对象中复制指定字段到目标对象
 */
export function assignFields<S extends object>(
  target: Record<keyof any, unknown>,
  source: S,
  fields: (keyof S)[]
): typeof target {
  fields.forEach(field => {
    const value = source[field];
    if (isValidValue(value)) {
      target[field] = value;
    }
  });
  return target;
}

/**
 * 从源对象中复制指定字段创建新对象
 */
export function pickFields<S extends object, K extends keyof S>(
  source: S,
  fields: K[]
): Pick<S, K> {
  return fields.reduce((obj, field) => {
    const value = source[field];
    if (isValidValue(value)) {
      obj[field] = value;
    }
    return obj;
  }, {} as Pick<S, K>);
} 