import { Image } from 'react-native';

/**
 * 预加载一组图片
 * @param sources 图片URL数组
 * @returns 预加载的Promise数组
 */
export const preloadImages = (sources: string[]): Promise<boolean>[] => {
  return sources.map(source => {
    return new Promise<boolean>((resolve, reject) => {
      try {
        if (!source) {
          resolve(true);
          return;
        }
        
        const img = Image.prefetch(source);
        img.then(() => {
          console.log(`图片预加载成功: ${source}`);
          resolve(true);
        }).catch(error => {
          console.error(`图片预加载失败: ${source}`, error);
          // 即使加载失败，我们也resolve为true，不阻止主流程
          resolve(true);
        });
      } catch (error) {
        console.error(`图片预加载出错: ${source}`, error);
        resolve(true);
      }
    });
  });
};

/**
 * 预加载多张图片并等待所有图片加载完成
 * @param sources 图片URL数组
 * @returns Promise，所有图片加载完成后resolve
 */
export const preloadImagesAll = async (sources: string[]): Promise<boolean> => {
  try {
    const loadPromises = preloadImages(sources);
    await Promise.all(loadPromises);
    return true;
  } catch (error) {
    console.error('预加载图片出错:', error);
    return false;
  }
}; 