import { Platform, Vibration } from 'react-native';
import ReactNativeHaptic, { HapticFeedbackType } from 'react-native-haptic';

export const haptic = (type: HapticFeedbackType) => {
  if (Platform.OS === 'ios') {
    ReactNativeHaptic.generate(type);
  } else {
    switch (type) {
      case 'impactLight':
        Vibration.vibrate(50);
        break;
      case 'impactMedium':
        Vibration.vibrate(100);
        break;
      case 'impactHeavy':
        Vibration.vibrate(200);
        break;
      case 'notificationSuccess':
        Vibration.vibrate(100);
        break;
      case 'notificationError':
        Vibration.vibrate(200);
        break;
      case 'notificationWarning':
        Vibration.vibrate(100);
        break;
      case 'selection':
        Vibration.vibrate(50);
        break;
      default:
        Vibration.vibrate(100);
        break;
    }
  }
};

export const hapticTap = () => {
  haptic('impactLight');
};

export const hapticSuccess = () => {
  haptic('notificationSuccess');
};

export const hapticError = () => {
  haptic('notificationError');
};