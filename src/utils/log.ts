import { NativeModules, Platform } from "react-native";

export default function log(...args: any) {
  if (Platform.OS !== 'android' && NativeModules?.XMApm?.localLog) {
    const logs = args.reduce((acc: string, cur: any) => {
      return acc += (acc ? ', ' : '') + (typeof cur === 'object' ? JSON.stringify(cur) : cur);
    }, '');

    NativeModules?.XMApm?.localLog(logs);
  } else {
    console.log(...args);
  }
}