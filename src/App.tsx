import { AppState, AppStateStatus, NativeEventEmitter, NativeModules, StatusBar } from 'react-native'
import React, { PureComponent, Suspense } from 'react'
import { isAndroid, isIOS } from '@xmly/rn-utils/dist/device'
import { safetyToString, setAndroidFullScreen, setAndroidStatusBar } from '@xmly/rn-utils'
import { NativeInfo } from 'typesV2/nativeInfo'
import { connect } from 'react-redux'
import { RootState, store } from './store'
import { NativeInfoContext } from 'contextV2/nativeInfoContext'
import scheduleTask, { scheduleTaskExecType } from 'modulesV2/scheduleTask'
import xmlog from 'utilsV2/xmlog'
import DelayTask from 'utilsV2/delayTask'
import ErrorBoundary from './componentsV2/common/ErrorBoundary'
import TimeRecorder from 'utilsV2/TimeRecorder'
import SentryUtils from 'utilsV2/sentryUtils'
import Router from './router'
import { RootView } from '@xmly/rn-sdk'
import { MonthlyTicketVoteModalSrcName } from './constantsV2'
import TaskAwardModal from './componentsV2/TaskAwardModal'
import { getConfig as EntrancePromptConfirmGetConfig } from './componentsV2/EntrancePromptConfirm'
import GlobalEventEmitter from './utilsV2/globalEventEmitter'
import goToLogin from './utilsV2/goToLogin'
import getNoLoginCheckInModalABTest from 'utilsV2/getNoLoginCheckInModalABTest'
import { ChannelUndertakeModalStatus } from 'typesV2/channelModal'
import EverydayChallengeMultiAwardModal from './componentsV2/EverydayChallengeV2/EverydayChallengeMultiAwardModal'
import { PageEventEmitter } from 'defs'
// import MambaConsoleEjector from './componentsV2/MambaConsoleEjector'

// 第三方任务悬浮窗
const ThirdpartyTaskExecutorLazy = React.lazy(() => import('./componentsV2/ThirdpartyTaskExecutor'))

const SuccessGetPopLazy = React.lazy(() => import('./componentsV2/SuccessGetPop'))

const NoLoginCheckInPopLazy = React.lazy(() => import('./componentsV2/NoLoginCheckInPopLazy'))
const CheckInPopLazy = React.lazy(() => import('./componentsV2/CheckInPop'))
const GiftPackagePopLazy = React.lazy(() => import('./componentsV2/GiftPackagePop'))

const TaskPcDialogLazy = React.lazy(() => import('./componentsV2/EarnCredit/TaskPcDialog'))

type AppSelfProps = {
  isLogin: boolean
  showNoLoginCheckInPop: boolean
  isDarkMode: boolean
  withMonthlyTicket: boolean
  modalStatus: ChannelUndertakeModalStatus,
  isLite: false
}
class App extends PureComponent<NativeInfo & AppSelfProps, { nativeInfo: NativeInfo }> {
  constructor(props: NativeInfo & AppSelfProps) {
    super(props)
    this.state = {
      nativeInfo: props,
    }
    // this.setAndroidProfile()
    this.pageResumeListener = PageEventEmitter.addListener('onResume', this.handlePageResume)
    TimeRecorder.start('enter')
    AppState.addEventListener('change', this.handleAppStateChange)
    this.preFetchData()

    this.onArgumentsUpdateUnsubscribe = GlobalEventEmitter.addListener('onArgumentsUpdate', (data: NativeInfo) => this.handleIting(data))

    if (this.props.isLogin) {
      store.dispatch.thirdpartyTask.judgeTaskExist({
        channelName: this.props.channelName,
        token: this.props.token,
        task: this.props.task,
      })
    }
  }

  handleNotLogin = async (showNoLoginCheckInPop: boolean, modalStatus: ChannelUndertakeModalStatus) => {
    console.log('❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️', '承接弹窗展示  结果:', modalStatus)
    console.log('❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️', '未登录弹窗展示  结果:', showNoLoginCheckInPop)

    // modalStatus 0: UNSET 1: need 2: noNeed
    if (modalStatus === ChannelUndertakeModalStatus.need) {
      // 需要展示承接弹窗
      store.dispatch.signInInfo.refresh({
        noLoginSigInModalVisible: false,
        hasShowOneModalBefore: true,
      })
    } else {
      // 当不展示承接弹窗，且满足未登录签到弹窗逻辑，则展示未登录签到弹窗
      if (showNoLoginCheckInPop) {
        store.dispatch.signInInfo.refresh({
          noLoginSigInModalVisible: true,
          hasShowOneModalBefore: true,
        })
      } else {
        store.dispatch.signInInfo.refresh({
          noLoginSigInModalVisible: false,
          hasShowOneModalBefore: false,
        })
      }
    }
  }

  handleIting = (data: NativeInfo) => {
    console.log('handleIting❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️❤️', data)
    if (data.srcChannel === 'widget') {
      this.setState(
        {
          nativeInfo: { ...this.state.nativeInfo, srcChannel: data.srcChannel },
        },
        () => {
          GlobalEventEmitter.emit('finishAddWidgetTaskEvent', true)
          console.log('finishAddWidgetTaskEvent ⌚️⌚️⌚️⌚️⌚️⌚️')
        },
      )
    }
  }

  preFetchData = async () => {
    await Promise.all([
      store.dispatch.signInInfo.getSignInInfo({
        withMonthlyTicket: this.props.withMonthlyTicket,
        isFromVoteModal: this.props.srcChannel === MonthlyTicketVoteModalSrcName,
      }),
      store.dispatch.topSection.fetchTopSectionInfo('global'),
    ])
    this.isInitFetched = true
    // 2023年7月4日 疑似没有对应的入口，所以注释掉这块逻辑
    // if (this.props.isLogin) {
    //   store.dispatch.taskCenter.handleDrawCoin({
    //     drawCoin: this.props.drawCoin,
    //   })
    // }
  }

  handleAppStateChange = (state: AppStateStatus) => {
    if (state === 'active') {
      scheduleTask.flushTask(scheduleTaskExecType.onAppChangeToActive)
    }
  }

  isInitFetched = false // 防止页面打开的时候page resume 重复触发请求
  pageResumeListener
  onArgumentsUpdateUnsubscribe
  // isFirstJoin: boolean = true
  //  为什么要设置一个变量区分首次onResume？，先给关掉，修复bug
  isFirstJoin: boolean = false

  handlePageResume = () => {
    if (this.isInitFetched) {
      if (this.isFirstJoin && isIOS) {
        this.isFirstJoin = false
        return
      } else {
        setTimeout(() => {
          console.log('=========onResume=======')
          DelayTask.addHandlers([
            store.dispatch.credit.getCredit,
            store.dispatch.taskCenter.getTaskList,
            store.dispatch.everydayChallenge.getTaskList,
            store.dispatch.monthlyTicket.refreshMonthlyTicketData.bind(null, {
              withMonthlyTicket: this.props.withMonthlyTicket,
            }),
            store.dispatch.goldCoin.getBalance,
            store.dispatch.chips.getChipsBalance,
            store.dispatch.notification.fetchNotificationStatus,
            () => {
              scheduleTask.flushTask(scheduleTaskExecType.onPageResume)
            },
          ])
        }, 800)
      }
    }
  }

  // 首页加载时长
  async homeLoaded({ duration }: { duration: number }) {
    try {
      xmlog.event(32465, 'others', {
        duration: safetyToString(duration),
      })
    } catch (error) {
      console.log(error)
      SentryUtils.captureException(error, {
        source: 'app.homeLoaded',
      })
    }
  }

  async componentDidMount() {
    // const sourceType = safetyToString(this.props.channelName || this.props.srcChannel || '')
    // xmlog.pageView(30532, '任务中心', {
    //   sourceType,
    // })

    !this.props?.isLogin && this.updateNoLoginCheckInModalStatus(this.props.modalStatus)
    const enterDuration = TimeRecorder.end('enter')
    this.homeLoaded({ duration: enterDuration })
    EntrancePromptConfirmGetConfig()
  }

  async componentDidUpdate(prevProps: Readonly<NativeInfo & AppSelfProps>) {
    console.log('❤️❤️❤️ App componentDidUpdate', 'prevProps', prevProps, 'this.props', this.props)
    !this.props?.isLogin && (await this.updateNoLoginCheckInModalStatus(this.props.modalStatus))
  }

  // 未登录签到弹窗逻辑
  async updateNoLoginCheckInModalStatus(modalStatus: ChannelUndertakeModalStatus) {
    console.log('updateNoLoginCheckInModalStatus:', modalStatus)
    // ABtest 1: 对照组 （保持原有逻辑不变） 2: 实验组 （最新逻辑）
    const noLoginAbTestResult = await getNoLoginCheckInModalABTest()
    // Toast.info(`未登录AB Test结果：${noLoginAbTestResult}`)
    store.dispatch.signInInfo.refresh({
      noLoginAbTestStatus: noLoginAbTestResult,
    })
    if (noLoginAbTestResult === '2') {
      if (modalStatus !== ChannelUndertakeModalStatus.UNSET) {
        this.handleNotLogin(this.props.showNoLoginCheckInPop, this.props.modalStatus)
      }
    } else {
      // 原有逻辑, 唤起登录
      goToLogin()
    }
  }

  componentWillUnmount() {
    // const sourceType = safetyToString(this.props.channelName || this.props.srcChannel || '')
    // xmlog.pageExit(30533, {
    //   currPage: '任务中心',
    //   sourceType,
    // })
    this.pageResumeListener && this.pageResumeListener.remove()
    this.onArgumentsUpdateUnsubscribe && this.onArgumentsUpdateUnsubscribe.remove()
    store.dispatch.taskCenter.reset()
    store.dispatch.everydayChallenge.reset()
    store.dispatch.commodity.reset()
    store.dispatch.notification.reset()
    store.dispatch.page.reset()
    store.dispatch.newUserSignIn.reset()
    AppState.removeEventListener('change', this.handleAppStateChange)
    SentryUtils.unmount()
  }

  /** 设置安卓端全屏和状态栏 */
  setAndroidProfile = async () => {
    try {
      // /** 安卓设置全屏 */
      isAndroid && setAndroidFullScreen(true)
      isAndroid && setAndroidStatusBar(this.props.isDarkMode ? false : true)
    } catch (err) {
      console.log(err)
    }
  }

  render() {
    return (
      <ErrorBoundary>
        <NativeInfoContext.Provider value={this.state.nativeInfo}>
          {isIOS ? <StatusBar barStyle={this.props.isDarkMode ? 'light-content' : 'dark-content'} /> : null}
          <Router />
          {/* {__DEV__ ? <MambaConsoleEjector /> : null} */}

          {/* <MambaConsoleEjector /> */}

          {/* 未登录签到弹窗 */}
          <Suspense fallback={null}>
            <NoLoginCheckInPopLazy />
          </Suspense>

          {/* 登录情况下的签到弹窗 */}
          {this.props?.isLogin && (
            <Suspense fallback={null}>
              <CheckInPopLazy />
            </Suspense>
          )}

          {/* 连签礼包 */}
          <Suspense fallback={null}>
            <GiftPackagePopLazy />
          </Suspense>

          {/* 成功领取积分弹窗  从播放页来的领取收听任务奖励的用户承接弹窗*/}
          <Suspense fallback={null}>
            <SuccessGetPopLazy />
          </Suspense>

          <Suspense fallback={null}>
            <ThirdpartyTaskExecutorLazy />
          </Suspense>

          <Suspense fallback={null}>
            <TaskPcDialogLazy />
          </Suspense>

          <TaskAwardModal />

          {/* 任务包发现金的多选一奖励弹窗 */}
          <EverydayChallengeMultiAwardModal />

          <RootView />
        </NativeInfoContext.Provider>
      </ErrorBoundary>
    )
  }
}

export default connect((state: RootState) => {
  return {
    modalStatus: state.channelUndertake.modalStatus,
  }
})(App)
