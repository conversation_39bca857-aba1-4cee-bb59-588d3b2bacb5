import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  tabBgColor: '#1B1B1B',
  tabActiveBgColor: 'rgba(255, 68, 68, 0.2)',
  tabTitleColor: '#ACACAF',
  itemTitleColor:'#ACACAF'
}

const lightTheme = {
  tabBgColor: '#FFF',
  tabActiveBgColor: 'rgba(255, 68, 68, 0.1)',
  tabTitleColor: '#333333',
  itemTitleColor:'#444'
}

const goodsThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default goodsThemeStyleAtom;