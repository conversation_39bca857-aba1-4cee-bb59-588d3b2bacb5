import React, { useState } from 'react'
import { LayoutRectangle, ScrollView, View } from 'react-native'
import isEqual from 'lodash.isequal'
import { ECommerceComponentVos } from '../../../typesV2/ECommerce'
import styled from 'styled-components'
import TabBarItem from '../../../componentsV2/Categories/TabBarItem'
import LinearGradient from 'react-native-linear-gradient'
import { ThemeStyle } from '../../../typesV2/themeInfo'

type Props = {
  componentList: ECommerceComponentVos[]
  currentTabIndex: number
  onPressTabBar: (index: number) => void
}

const Wrapper = styled(View)`
  height: 50px;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.bg_color};
`

const TabBarIndicatorWrapper = styled(View)`
  position: absolute;
  left: 0;
  bottom: 8px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`

const TabBarIndicator = styled(LinearGradient)`
  width: 15%;
  height: 4px;
  border-radius: 1px;
`

const TabBar: React.FC<Props> = ({
  componentList,
  currentTabIndex,
  onPressTabBar,
}) => {
  const handleItemPress = (index: number) => {
    onPressTabBar(index)
  }
  const [tabBarItemLayoutMap, setTabBarItemLayoutMap] = useState<{
    [index: number]: LayoutRectangle
  }>({})

  const handleTabBarItemLayout = ({
    index,
    data,
  }: {
    index: number
    data: LayoutRectangle
  }) => {
    const _tabBarItemLayoutMap = {
      ...tabBarItemLayoutMap,
      [index]: data,
    }
    setTabBarItemLayoutMap(_tabBarItemLayoutMap)
  }

  if (componentList.length > 1) {
    return (
      //@ts-ignore
      <Wrapper>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {componentList.map((component, index) => (
            <TabBarItem
              key={component.id}
              currentTabIndex={currentTabIndex}
              tabId={component.id}
              tabLabel={component.name}
              index={index}
              onPress={handleItemPress}
              setTabBarLayout={handleTabBarItemLayout}
            />
          ))}
          <TabBarIndicatorWrapper
            style={{
              width: tabBarItemLayoutMap[currentTabIndex]?.width || 0,
              left: tabBarItemLayoutMap[currentTabIndex]?.x || 0,
            }}
          >
            <TabBarIndicator
              start={{ x: 0.35, y: 0 }}
              end={{ x: 1, y: 0 }}
              colors={['#FF4C2E', '#ffa697']}
            />
          </TabBarIndicatorWrapper>
        </ScrollView>
      </Wrapper>
    )
  } else {
    return null
  }
}

export default React.memo(TabBar, isEqual)
