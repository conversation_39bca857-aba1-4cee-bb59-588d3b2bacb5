import { StyleSheet } from "react-native";
import { darkTheme } from "../theme";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  tab: {
    backgroundColor: theme.tabBgColor,
    paddingHorizontal: 16,
    marginRight: 8,
    borderRadius: 14,
    height: 28,
    justifyContent: 'center',
    alignItems: 'center'
  },
  tabActive: {
    backgroundColor: theme.tabActiveBgColor
  },
  tabTitle: {
    color: theme.tabTitleColor,
    includeFontPadding: false,
    textAlignVertical: 'center',
    fontSize: 13,
  },
  tabTitleActive: {
    color: '#FF4444',
    fontWeight: 'bold'
  }
});