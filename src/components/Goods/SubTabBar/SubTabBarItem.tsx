import React from 'react'
import { Text, Touch } from '@xmly/rn-components'
import isEqual from 'lodash.isequal'
import { useAtomValue } from 'jotai'
import goodsThemeStyleAtom from '../theme'
import { getStyles } from './style'

type Props = {
  currentSubTabIndex: number
  index: number
  tabId: number
  tabLabel: string
  onPress: (index: number) => void
}

const SubTabBarItem: React.FC<Props> = ({
  tabLabel,
  index,
  currentSubTabIndex,
  onPress,
}) => {
  const isCurrent = index === currentSubTabIndex
  const goodsThemeStyle = useAtomValue(goodsThemeStyleAtom);
  const styles = getStyles(goodsThemeStyle);

  const handlePress = () => {
    onPress(index)
  }

  return (
    <Touch style={[styles.tab, isCurrent ? styles.tabActive : null]} onPress={handlePress}>
      <Text
        style={[
          styles.tabTitle,
          isCurrent ? styles.tabTitleActive : null
        ]}>
        {tabLabel}
      </Text>
    </Touch>
  )
}

export default React.memo(SubTabBarItem, isEqual)
