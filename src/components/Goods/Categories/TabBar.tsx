import React, {
  Suspense,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react'
import { LayoutChangeEvent, LayoutRectangle, StyleSheet } from 'react-native'
import {
  PanGestureHandler,
  State as GestureState,
} from 'react-native-gesture-handler'
import LinearGradient from 'react-native-linear-gradient'
import Animated, {
  add,
  and,
  Clock,
  clockRunning,
  cond,
  eq,
  Extrapolate,
  floor,
  interpolate,
  multiply,
  neq,
  onChange,
  set,
  stopClock,
  useCode,
  useValue,
} from 'react-native-reanimated'

import { connect } from 'react-redux'

import { RootState } from 'store'
import styled from 'styled-components'
import { CATEGORIES_TAB_BAR_HEIGHT } from 'constantsV2/layout'
import TabBarItem from './TabBarItem'
import isEqual from 'lodash.isequal'
import {
  CommodityDisplayTabItem,
  CommodityDisplayTabs,
} from 'typesV2/commodity'
import clamp from '../../utilsV2/animations/clamp'
import decay from '../../utilsV2/animations/decay'

const BrowseTaskLazy = React.lazy(() => import('../BrowseTask'))

type Props = {
  displayTabs: CommodityDisplayTabs
  changeTab?: (index: number) => void
  thirdpartyExist: boolean
  commodityTabCurrentTab: number
}

const TabBarIndicatorWrapper = styled(Animated.View)`
  position: absolute;
  left: 0;
  bottom: 8px;
  flex-direction: row;
  align-items: center;
  justify-content: center;
`

const TabBarIndicator = styled(LinearGradient)`
  width: 20px;
  height: 4px;
  border-radius: 1px;
`

type TabBarUIProps = {}

const TabBarUI: React.FC<Props & TabBarUIProps> = React.memo((props) => {
  const { displayTabs, changeTab, commodityTabCurrentTab } = props
  const commodityTabScrollValue = useValue<number>(commodityTabCurrentTab)

  const translateX = useValue<number>(0)
  const offsetX = useValue<number>(0)
  const gestureX = useValue<number>(0)
  const velocityX = useValue<number>(0)
  const gestureState = useValue(GestureState.UNDETERMINED)

  const [containerWidth, setContainerWidth] = useState<number>(0)
  const [contentWidth, setContentWidth] = useState<number>(0)
  const [tabBarReady, setTabBarReady] = useState(false)
  const [tabBarIndicatorStyle, setTabBarIndicatorStyle] = useState<null | any>(
    null
  )
  const [translateXInterpolate, setTranslateXInterpolate] = useState<
    null | any
  >(useValue(0))

  const shouldDecay = useValue<0 | 1>(0)
  const decayClock = useRef(new Clock()).current
  const tabChanging = useValue<0 | 1>(0)
  const [scrollMax, setScrollMax] = useState(0)

  const tabBarItemLayoutData = useRef<{ [key: number]: LayoutRectangle }>({})

  const decayValue = clamp(
    decay({
      clock: decayClock,
      from: translateX,
      velocity: velocityX,
      deceleration: 0.998,
    }),
    scrollMax,
    0
  )

  useEffect(() => {
    commodityTabScrollValue.setValue(commodityTabCurrentTab)
  }, [commodityTabCurrentTab])

  const handlePress = useCallback((index: number) => {
    typeof changeTab === 'function' && changeTab(index)
  }, [])

  const handleContentLayout = (event: LayoutChangeEvent) => {
    setContentWidth(event.nativeEvent.layout.width)
  }

  useEffect(() => {
    if (containerWidth && contentWidth) {
      const diff = containerWidth - contentWidth
      if (diff > 0) {
        setScrollMax(0)
      } else {
        setScrollMax(diff)
      }
    }
  }, [containerWidth, contentWidth])

  const handleGestureEvent = Animated.event([
    {
      nativeEvent: {
        translationX: gestureX,
        velocityX: velocityX,
        state: gestureState,
      },
    },
  ])

  useCode(() => {
    return [
      onChange(
        [floor(multiply(commodityTabScrollValue, 10))],
        [
          set(shouldDecay, 0),
          cond(neq(velocityX, 0), set(velocityX, 0)),
          set(tabChanging, 1),
        ]
      ),
    ]
  }, [translateXInterpolate, scrollMax])

  useCode(() => {
    return [
      cond(eq(gestureState, GestureState.BEGAN), [
        cond(tabChanging, set(translateX, translateXInterpolate)),
        set(tabChanging, 0),
        set(offsetX, translateX),
      ]),
    ]
  }, [translateXInterpolate, scrollMax])

  useCode(() => {
    return [
      cond(
        [eq(gestureState, GestureState.BEGAN)],
        [
          set(velocityX, 0),
          cond(clockRunning(decayClock), stopClock(decayClock)),
        ]
      ),
      cond(
        [eq(gestureState, GestureState.END)],
        [
          cond(
            and(shouldDecay, neq(velocityX, 0)),
            set(translateX, decayValue)
          ),
          set(offsetX, translateX),
        ]
      ),
      cond(
        [eq(gestureState, GestureState.ACTIVE)],
        [
          cond(clockRunning(decayClock), stopClock(decayClock)),
          set(shouldDecay, 1),
          set(translateX, clamp(add(offsetX, gestureX), scrollMax, 0)),
        ]
      ),
    ]
  }, [containerWidth, contentWidth, scrollMax])

  const handleContainerLayout = (e: LayoutChangeEvent) => {
    setContainerWidth(e.nativeEvent.layout.width)
  }

  const setTabBarItemLayoutData = useCallback(
    ({ index, data }: { index: number; data: LayoutRectangle }) => {
      tabBarItemLayoutData.current[index] = data
      if (
        tabBarItemLayoutData.current &&
        Object.keys(tabBarItemLayoutData.current).length ===
          displayTabs.length &&
        Object.keys(tabBarItemLayoutData.current).every(
          (i) => typeof i !== 'undefined'
        )
      ) {
        setTabBarReady(true)
      }
    },
    []
  )

  useEffect(() => {
    if (tabBarReady) {
      const input: number[] = []
      const translateXOutput: number[] = []
      const widthOutput: number[] = []
      const translateXInterpolateOutput: number[] = []
      const containerCenterX = containerWidth / 2
      Object.entries(tabBarItemLayoutData.current).forEach(
        (layoutData, index) => {
          const _index = Number(layoutData[0])
          input[_index] = _index
          widthOutput[_index] = layoutData[1].width
          translateXOutput[_index] = layoutData[1].x
          translateXInterpolateOutput[_index] =
            containerCenterX >= layoutData[1].x
              ? 0
              : containerCenterX - layoutData[1].x
        }
      )
      if (Object.keys(tabBarItemLayoutData.current).length > 1) {
        // 指示器样式
        setTabBarIndicatorStyle({
          transform: [
            {
              translateX: interpolate(commodityTabScrollValue, {
                inputRange: input,
                outputRange: translateXOutput,
                extrapolate: Extrapolate.CLAMP,
              }),
            },
          ],
          width: interpolate(commodityTabScrollValue, {
            inputRange: input,
            outputRange: widthOutput,
          }),
        })

        // scrollView 样式
        setTranslateXInterpolate(
          clamp(
            interpolate(commodityTabScrollValue, {
              inputRange: input,
              outputRange: translateXInterpolateOutput,
            }),
            scrollMax,
            0
          )
        )
      } else {
        setTabBarIndicatorStyle({
          transform: [
            {
              translateX: translateXOutput[0],
            },
          ],
          width: widthOutput[0],
        })
        setTranslateXInterpolate(0)
      }
    }
  }, [tabBarReady, containerWidth, contentWidth, scrollMax])

  const renderTabBarItem = (
    displayTab: CommodityDisplayTabItem,
    index: number
  ) => {
    return (
      <TabBarItem
        key={displayTab.id}
        setTabBarLayout={setTabBarItemLayoutData}
        tabId={displayTab.id}
        tabLabel={displayTab.name}
        index={index}
        currentTabIndex={commodityTabCurrentTab}
        onPress={handlePress}
      />
    )
  }

  return (
    <PanGestureHandler
      onGestureEvent={handleGestureEvent}
      onHandlerStateChange={handleGestureEvent}
      minDist={10}
      failOffsetY={[-10, 10]}
    >
      <Animated.View
        onLayout={handleContainerLayout}
        collapsable={false}
        style={styles.containerWrapper}
      >
        <Animated.View
          onLayout={handleContentLayout}
          collapsable={false}
          style={[
            styles.contentWrapper,
            {
              transform: [
                {
                  translateX: cond(
                    tabChanging,
                    translateXInterpolate,
                    translateX
                  ),
                },
              ],
            },
          ]}
        >
          {displayTabs.map(renderTabBarItem)}
          <TabBarIndicatorWrapper style={tabBarIndicatorStyle}>
            <TabBarIndicator
              start={{ x: 0.35, y: 0 }}
              end={{ x: 1, y: 0 }}
              colors={['#FF4C2E', '#ffa697']}
            />
          </TabBarIndicatorWrapper>
        </Animated.View>
      </Animated.View>
    </PanGestureHandler>
  )
}, isEqual)

const TabBar: React.FC<Props> = (props) => {
  const { changeTab, thirdpartyExist } = props
  return (
    <>
      <TabBarUI {...props} changeTab={changeTab} />
      {!thirdpartyExist && (
        <Suspense fallback={null}>
          <BrowseTaskLazy />
        </Suspense>
      )}
    </>
  )
}

const styles = StyleSheet.create({
  contentWrapper: {
    height: '100%',
    alignItems: 'center',
    zIndex: 10,
    flexDirection: 'row',
    flexWrap: 'nowrap',
    position: 'absolute',
  },
  containerWrapper: {
    width: '100%',
    paddingLeft: 12,
    height: CATEGORIES_TAB_BAR_HEIGHT,
    zIndex: 10,
    overflow: 'hidden',
  },
})

const mapStateToProps = (state: RootState) => {
  return {
    displayTabs: state.commodity.displayTabs,
    thirdpartyExist: state.thirdpartyTask.isExist,
    commodityTabCurrentTab: state.commodity.currentTab,
  }
}

export default connect(mapStateToProps)(React.memo(TabBar, isEqual))
