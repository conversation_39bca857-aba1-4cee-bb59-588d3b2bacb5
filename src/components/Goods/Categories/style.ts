import { StyleSheet } from "react-native";
import { darkTheme } from "../theme";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  cover: {
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  itemTitle: {
    fontSize: 14,
    color: theme.itemTitleColor,
  },
  price: {
    fontFamily: 'XmlyNumber-Regular',
  },
  yuan: {
    fontSize: 16,
    fontFamily: 'XmlyNumber-Regular',
  }
});