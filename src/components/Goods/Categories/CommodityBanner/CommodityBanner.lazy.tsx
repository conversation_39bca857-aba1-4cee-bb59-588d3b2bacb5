import React, { useCallback, useEffect, useState } from 'react'
import { Image, StyleProp, View, ViewStyle } from 'react-native'
import isEqual from 'lodash.isequal'
import CommodityBannerItemUI from './CommodityBannerItem'
import { CommodityBannerItem } from '../typesV2/commodity'
import Carousel, { Pagination } from 'react-native-snap-carousel'
import { CommodityItemWidth } from '../../../constantsV2/layout'
import styled from 'styled-components'
import { useSelector } from 'react-redux'
import { RootState } from '../../../store'

type Props = {
  commodityBanner: CommodityBannerItem[]
}
const slideStyle: StyleProp<ViewStyle> = {
  paddingHorizontal: 32,
  alignItems: 'center',
  justifyContent: 'center',
}

const Wrapper = styled(View)`
  border-radius: 8px;
  overflow: hidden;
  margin: 0 0 12px 0;
`

const CommodityBannerLazy: React.FC<Props> = ({ commodityBanner }) => {
  const [itemHeight, setItemHeight] = useState(0)
  const currentTab = useSelector(
    (state: RootState) => state.commodity.currentTab
  )
  useEffect(() => {
    calcBannerHeight()
  }, [])

  const calcBannerHeight = async () => {
    try {
      const firstItemCover = commodityBanner[0].cover
      if (firstItemCover) {
        Image.getSize(
          firstItemCover,
          (width, height) => {
            console.log(width, height)
            const imageOriginScale = width / height
            const imageFitHeight = CommodityItemWidth / imageOriginScale
            setItemHeight(imageFitHeight)
          },
          (err) => {
            console.log(err)
          }
        )
      }
    } catch (err) {}
  }

  const _renderItem = ({ item }: { item: CommodityBannerItem }) => {
    return <CommodityBannerItemUI item={item} height={itemHeight} />
  }

  const handleGetItemLayout = useCallback(
    (data: CommodityBannerItem[], index: number) => ({
      length: CommodityItemWidth,
      offset: CommodityItemWidth * index,
      index,
    }),
    []
  )

  const wrapperStyle = { height: currentTab === 0 ? itemHeight : 0 }

  if (itemHeight > 0) {
    return (
      <Wrapper style={wrapperStyle}>
        <Carousel
          lockScrollWhileSnapping
          data={commodityBanner}
          renderItem={_renderItem}
          sliderWidth={CommodityItemWidth}
          sliderHeight={itemHeight}
          itemWidth={CommodityItemWidth}
          itemHeight={itemHeight}
          layout='stack'
          autoplay
          loop
          autoplayInterval={2000}
          removeClippedSubviews={false}
          slideStyle={slideStyle}
          getItemLayout={handleGetItemLayout}
        />
      </Wrapper>
    )
  }
  return null
}

export default React.memo(CommodityBannerLazy, isEqual)
