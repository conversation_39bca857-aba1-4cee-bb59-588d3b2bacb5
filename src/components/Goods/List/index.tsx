import React, { useCallback, useContext, useEffect, useRef } from 'react'
import isEqual from 'lodash.isequal'
import ListContent from './ListContent'
import {
  CommodityDisplayProductItem,
  CommodityDisplayProducts,
} from '../../../typesV2/commodity'
import StaticListFooter from '../../../componentsV2/common/StaticListFooter'
import { InViewPort } from '@xmly/rn-components'
import CommodityCoverLazyControl from '../../../componentsV2/Categories/CommodityCoverLazyControl'
import { FetchStatus } from '../../../typesV2/common'
import EmptyView from './EmptyView'
import ErrorView from '../../../componentsV2/common/ErrorView'
import xmlog from '../../../utilsV2/xmlog'
import { safetyToString } from '@xmly/rn-utils'
import { NativeInfoContext } from '../../../contextV2/nativeInfoContext'
import GlobalEventEmitter from 'utilsV2/globalEventEmitter'
import { HomeScrollViewEventName } from 'constantsV2'
import log from 'utils/log'

type Props = {
  commodities: CommodityDisplayProducts
  onLoadMore: () => void
  hasMore: boolean
  fetchStatus: FetchStatus
  onReload: () => void
  currentTabId: number
}

const inViewPortGroupName = 'commodityList'

const List: React.FC<Props> = ({
  commodities,
  onLoadMore,
  hasMore,
  fetchStatus,
  onReload,
  currentTabId,
}) => {
  const nativeInfo = useContext(NativeInfoContext)
  const mIsVip = nativeInfo?.initData?.account?.isVip
  const listFooterInViewChecker = useRef(
    InViewPort.getCheckBatcher(inViewPortGroupName)
  )

  const handleScroll = useCallback(() => {
    CommodityCoverLazyControl.checker.schedule()
    listFooterInViewChecker.current.schedule()
  }, [])

  useEffect(() => {
    const scrollListener = GlobalEventEmitter.addListener(HomeScrollViewEventName.onScroll, handleScroll)
    return () => {
      scrollListener.remove();
    }
  }, [])

  const handleCommodityClickEventReport = useCallback(
    (commodity: CommodityDisplayProductItem) => {
      // 任务中心_会员权益-独立商品  点击事件
      xmlog.click(46408, undefined, {
        currPage: '任务中心',
        productId: safetyToString(commodity.id),
        isVIP: mIsVip ? 'true' : 'false',
        tabId: safetyToString(currentTabId), // 组件的ID（一级tab）
      })
    },
    [mIsVip, currentTabId]
  )

  const handleCommodityShowEventReport = useCallback(
    (commodity: CommodityDisplayProductItem) => {
      // 任务中心_会员权益-独立商品  控件曝光
      xmlog.event(46409, 'slipPage', {
        currPage: '任务中心',
        productId: safetyToString(commodity.id),
        isVIP: mIsVip ? 'true' : 'false',
        tabId: safetyToString(currentTabId), // 组件的ID（一级tab）
      })
    },
    [mIsVip, currentTabId]
  )

  if (commodities.length === 0) {
    if (fetchStatus === FetchStatus.success) {
      return <EmptyView />
    }

    if (fetchStatus === FetchStatus.fail) {
      return (
        <ErrorView withoutHeader buttonLabel='请重试' onReload={onReload} />
      )
    }
  }

  return (
    <>
      <ListContent
        commodities={commodities}
        onClickEventReport={handleCommodityClickEventReport}
        onItemShowEventReport={handleCommodityShowEventReport}
      />
      <StaticListFooter
        hasMore={hasMore}
        inViewPortGroupName={inViewPortGroupName}
        onShow={onLoadMore}
      />
    </>
  )
}

export default React.memo(List, isEqual)
