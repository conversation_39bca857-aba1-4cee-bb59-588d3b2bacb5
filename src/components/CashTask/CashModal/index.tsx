import { BetterImage, Touch } from "@xmly/rn-components";
import { containerHeight, containerWidth, closeIcon, getStyles } from "./style";
import { Text, View } from "react-native";
import { useAtomValue } from "jotai";
import Pop from "../../../componentsV2/common/Pop";
import React from "react";
import StepAward from "../StepAward";
import themeStyleAtom from "../store/theme/modal";
import useShowCashModal from "../hook/useShowCashModal";
import ViewBtn, { ViewBtnLocation } from "../ViewBtn";
import { cashModalShowAtom } from "../store/modal";
import { modalClickReport, AreaType, ActionType } from "../utils";

function CashModal() {
  const themeStyles = useAtomValue(themeStyleAtom);
  const styles = getStyles(themeStyles);
  const { close } = useShowCashModal();
  const showModal = useAtomValue(cashModalShowAtom);

  function click(btnText: string, action: ActionType) {
    modalClickReport({ btnText, area: AreaType.Button, action });
  }

  function stepPressReport() {
    modalClickReport({ btnText: '今日可领', area: AreaType.Item, action: ActionType.Video });
  }

  return showModal ? <Pop
    handleClose={close}
  >
    <View style={styles.container}>
      <BetterImage
        resizeMode={'cover'}
        style={styles.bg}
        source={{ uri: themeStyles.modalBg }}
        imgWidth={containerWidth}
        imgHeight={containerHeight}
      />
      <View style={styles.head}>
        <Text style={styles.title}>每日赚现金</Text>
      </View>
      <View style={[styles.body, { backgroundColor: themeStyles.bodyBgColor }]}>
        <StepAward onPressReport={stepPressReport} />
        <View style={styles.btnWrapper}>
          <ViewBtn
            btnBg={themeStyles.btnBg}
            btnDisabledStyle={styles.btnDisabled}
            btnStyle={styles.btn}
            btnTextStyle={styles.btnTitle}
            onPress={click}
            position={ViewBtnLocation.Modal}
          />
        </View>
      </View>
      <Touch style={styles.close} onPress={close}>
        <BetterImage
          imgWidth={13}
          imgHeight={13}
          style={styles.closeIcon}
          source={{ uri: closeIcon }}
        />
      </Touch>
    </View>
  </Pop> : null
}

export default React.memo(CashModal);