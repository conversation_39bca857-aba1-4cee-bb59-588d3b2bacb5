import { StyleSheet } from 'react-native';
import type { darkTheme } from '../store/theme/modal';
import { gap } from '../style';

export const containerWidth = 303;
export const containerHeight = 290;
export const closeIcon = 'https://imagev2.xmcdn.com/storages/b9cc-audiofreehighqps/76/49/GKwRIMAKmw2-AAABCAMDQcYg.png';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    width: containerWidth,
    height: containerHeight,
    borderRadius: 8,
    position: 'relative',
    padding: 8,
    backgroundColor: theme.modalBgColor
  },
  bg: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: containerWidth,
    height: containerHeight,
  },
  head: {
    paddingTop: 12,
    paddingBottom: 17,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 22,
    fontWeight: '600',
    lineHeight: 30,
    fontFamily: 'PingFang SC',
    color: '#FFF',
  },
  close: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 12
  },
  closeIcon: {
    width: 13,
    height: 13,
  },
  body: {
    borderRadius: 5,
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 8,
    flex: 1,
  },
  btnWrapper: {
    marginHorizontal: gap,
    marginTop: 8,
  },
  btn: {
    width: '100%',
    aspectRatio: 263 / 46,
    marginLeft: 'auto',
    marginRight: 'auto',
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnDisabled: {
    opacity: 0.5,
  },
  btnTitle: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'PingFang SC',
    fontWeight: '500',
  }
});
