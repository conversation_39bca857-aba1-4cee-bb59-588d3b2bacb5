import React from "react";
import { useAtomValue } from "jotai";
import { getStyles } from "./style";
import { View } from "react-native";
import { BetterImage, Text } from "@xmly/rn-components";
import NoticeBar from "components/NoticeBar";
import withdrawNotice<PERSON>tom from "../store/withdrawNotice";
import { WithdrawNotice } from "../store/task";
import withdrawNoticeBarThemeStyleAtom from "../store/theme/withdrawNoticeBar";

export default function WithdrawNoticeBar() {
  const themeStyles = useAtomValue(withdrawNoticeBarThemeStyleAtom);
  const styles = getStyles(themeStyles);
  const noticeList = useAtomValue(withdrawNoticeAtom);

  function renderItem(item: WithdrawNotice) {
    return <View style={styles.notice}>
      {item.avatar ? <BetterImage
        source={{
          uri: item.avatar,
        }}
        imgHeight={16}
        imgWidth={16}
        style={[styles.avatar]}
      /> : null}
      <Text style={styles.text}>{`用户${item.nickName}刚刚`}</Text>
      <Text style={[styles.text, styles.highlight]}>{`成功提现${Number(item.amount) > 0 ? `${item.amount}元` : ''}`}</Text>
    </View>;
  }

  return noticeList.length > 0 ? <View style={styles.noticeBarWrapper}>
    <NoticeBar
      list={noticeList}
      renderItem={renderItem}
      itemHeight={20}
    />
  </View> : null;
}