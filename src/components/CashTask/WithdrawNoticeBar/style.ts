import { StyleSheet } from 'react-native';
import type { darkTheme } from '../store/theme/withdrawNoticeBar';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  noticeBarWrapper: {
    height: 36,
    top: 8,
    paddingTop: 4,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    backgroundColor: theme.noticeBarBgColor,
    overflow: 'hidden'
  },
  noticeBarBg: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    left: 0,
  },
  notice: {
    height: 20,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 8,
  },
  avatar: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 4
  },
  text: {
    fontFamily: 'PingFang SC',
    fontSize: 11,
    color: theme.textColor,
  },
  highlight: {
    color: theme.highlight,
    fontWeight: 'bold',
    marginLeft: 2
  }
});
