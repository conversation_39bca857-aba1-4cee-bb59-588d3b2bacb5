import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useAtomValue } from 'jotai';
import { ThemeStyle } from 'typesV2/themeInfo';
import { themeAtom } from 'atom/theme';
import themes from 'themeV2';

const getSkeletonStyles = (theme: ThemeStyle) => StyleSheet.create({
  block: {
    backgroundColor: theme.common.item_bg_color,
    borderRadius: 8,
    marginHorizontal: 'auto',
  },
  item: {
    borderRadius: 2,
    opacity: 0.15,
    backgroundColor: '#aaaaaa'
  },
  row: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  }
})

function CashBannerSkeleton() {
  const theme = useAtomValue(themeAtom);
  const skeletonStyle = getSkeletonStyles(themes[theme]);

  return (
    <View style={[skeletonStyle.block, {
      width: '100%',
      height: 284,
      marginBottom: 12
    }]}>
      <View style={[skeletonStyle.row, {
        margin: 12,
      }]}>
        <View
          style={[skeletonStyle.item, {
            width: 40,
            height: 40,
            borderRadius: 20,
            marginRight: 8
          }]}
        />
        <View>
          <View style={[skeletonStyle.item, {
            width: 80,
            height: 22,
          }]}></View>
          <View style={[skeletonStyle.item, {
            width: 120,
            height: 15,
            marginTop: 3
          }]}></View>
        </View>
      </View>
      <View style={[skeletonStyle.item, {
        width: 335,
        height: 200,
        marginLeft: 'auto',
        marginRight: 'auto',
        borderRadius: 8
      }]}>
      </View>
    </View>
  );
}

export default React.memo(CashBannerSkeleton);