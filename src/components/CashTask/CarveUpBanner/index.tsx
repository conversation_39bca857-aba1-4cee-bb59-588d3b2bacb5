import { BetterImage, Text } from '@xmly/rn-components';
import React from 'react';
import { View } from 'react-native';
import { useAtomValue } from 'jotai';
import bannerThemeStyleAtom from '../store/theme/banner';
import { getStyles } from './style';
import ViewBtn, { ViewBtnLocation } from '../ViewBtn';
import taskAtom from '../store/task';
//@ts-ignore
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import xmlog from 'utilsV2/xmlog';
import LinearGradient from 'react-native-linear-gradient';
import AwardA from '../AwardA';
import AwardB from '../AwardB';
import { ActionType, bannerClickReport } from '../utils';
import WithdrawNoticeBar from '../WithdrawNoticeBar';
import PostSignBtn from '../PostSignBtn';
import useCashTaskLocate from '../hook/useCashTaskLocate';

const moduleTitle = {
  a: '奖励A｜看视频领现金',
  b: '奖励B｜瓜分50万大奖',
};

function CarveUpBanner() {
  const themeStyles = useAtomValue(bannerThemeStyleAtom);
  const styles = getStyles(themeStyles);
  const cashTask = useAtomValue(taskAtom);
  const layoutProperty = useCashTaskLocate();

  function expose() {
    // 任务中心-福利中心-签到模块  控件曝光
    xmlog.event(65103, 'slipPage', { currPage: '任务中心', status: cashTask?.carveUpCashAward?.replenishSignIn ? '1' : '0' });
  }

  function click(btnText: string, action: ActionType.Video) {
    bannerClickReport({ btnText, action });
  }

  return (
    cashTask ? <View {...layoutProperty.onLayoutProperty}>
      <WithdrawNoticeBar />
      <ScrollAnalyticComp itemKey='CashTaskBanner' onShow={expose}>
        <LinearGradient
          {...themeStyles.bg}
          useAngle={true}
          style={styles.container}
        >
          <BetterImage
            source={{
              uri: themeStyles.headBg,
            }}
            imgHeight={135}
            imgWidth={351}
            style={styles.headBg}
          />
          <View style={styles.head}>
            <BetterImage
              source={{ uri: themeStyles.headIcon }}
              imgHeight={40}
              imgWidth={40}
              style={styles.headIcon}
            />
            <View>
              <View style={styles.titleWrapper}>
                <Text style={styles.title}>每日赚现金 7天领</Text>
                <BetterImage
                  source={{ uri: themeStyles.titleIcon }}
                  imgWidth={45}
                  imgHeight={29}
                  style={styles.titleIcon}
                />
              </View>
              {cashTask?.carveUpCashAward ? <Text style={styles.subtitle}>还有50万大奖等你来瓜分</Text> : null}
            </View>
          </View>
          <AwardA hideFold={!cashTask?.carveUpCashAward} title={moduleTitle.a} />
          <AwardB title={moduleTitle.b} />
          {cashTask?.carveUpCashAward?.replenishSignIn ? (
            <View style={styles.btnWrapper}>
              <PostSignBtn
                style={[styles.btn, styles.subBtn]}
                title={cashTask?.carveUpCashAward?.replenishSignIn.btnText}
                onPress={click}
                textStyle={styles.subBtnText}
              />
              <ViewBtn
                btnBg={themeStyles.btnBg}
                disabledBtnBg={themeStyles.disabledBtnBg}
                btnDisabledTextStyle={styles.btnTitleDisabled}
                btnStyle={[styles.btn, styles.mainBtn]}
                btnTextStyle={styles.mainBtnText}
                onPress={click}
                position={ViewBtnLocation.Banner}
                noAnimation
              />
            </View>
          ) : (
            <View style={styles.btnWrapper}>
              <ViewBtn
                btnBg={themeStyles.btnBg}
                disabledBtnBg={themeStyles.disabledBtnBg}
                btnDisabledTextStyle={styles.btnTitleDisabled}
                btnStyle={styles.singleBtn}
                btnTextStyle={styles.btnTitle}
                onPress={click}
                position={ViewBtnLocation.Banner}
                animationInitScale={.95}
                animationToScale={1}
              />
            </View>
          )}
        </LinearGradient>
      </ScrollAnalyticComp>
    </View> : null
  );
}

export default React.memo(CarveUpBanner);