import { StyleSheet } from 'react-native';
import type { darkTheme } from '../store/theme/banner';
import { gap } from '../style';

export const btnWidth = 311;
export const btnHeight = 46;

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
    borderRadius: 8,
    overflow: 'hidden',
    marginBottom: 12,
    paddingBottom: 12
  },
  headBg: {
    width: '100%',
    aspectRatio: 351 / 135,
    resizeMode: 'cover',
    position: 'absolute',
    top: 0,
    left: 0,
  },
  footBg: {
    width: '100%',
    aspectRatio: 351 / 117,
    resizeMode: 'cover',
    position: 'absolute',
    bottom: 0,
    left: 0,
  },
  headIcon: {
    width: 40,
    height: 40,
    marginRight: 8,
  },
  head: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 12,
    paddingLeft: 12,
  },
  titleWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontFamily: 'PingFang SC',
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 22,
    color: '#FEFFFE',
  },
  titleIcon: {
    width: 45,
    height: 29
  },
  subtitle: {
    color: theme.subtitleColor,
    fontFamily: 'PingFang SC',
    fontSize: 11,
    lineHeight: 15,
    top: -3,
  },
  foldBtnWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    marginTop: 2,
  },
  foldBtn: {
    width: 58,
    height: 18,
    backgroundColor: 'rgba(0,0,0,0.1)',
    position: 'absolute',
    bottom: -4
  },
  foldBtnIconWrapper: {
    position: 'relative',
    width: 42,
    height: 9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  foldBtnBg: {
    width: 42,
    height: 9,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  foldArrow: {
    width: 7,
    height: 4,
  },
  btnWrapper: {
    paddingHorizontal: gap,
    marginTop: 12,
    flexDirection: 'row',
  },
  mainBtn: {
    flex: 3
  },
  subBtn: {
    flex: 2,
    backgroundColor: theme.subBtnBgColor,
    marginRight: 8
  },
  subBtnText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.subBntTextColor,
  },
  mainBtnText: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.btnTextColor,
  },
  btn: {
    height: btnHeight,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 120
  },
  singleBtn: {
    width: '100%',
    height: btnHeight,
    marginLeft: 'auto',
    marginRight: 'auto',
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  btnTitle: {
    lineHeight: 32,
    fontFamily: 'PingFang SC',
    fontSize: 16,
    fontWeight: '500',
    color: theme.btnTextColor,
  },
  btnTitleDisabled: {
    color: 'rgba(255,255,255,.6)',
  },
});
