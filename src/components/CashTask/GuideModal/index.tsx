import { BetterImage, Touch } from "@xmly/rn-components";
import { closeIcon, getStyles } from "./style";
import { Text, View, Image } from "react-native";
import { useAtomValue } from "jotai";
import Pop from "../../../componentsV2/common/Pop";
import React from "react";
import themeStyleAtom from "../store/theme/guide";
import useGuideModal from "../hook/useGuideModal";
import taskAtom, { GuideType } from "../store/task";

const GuideSubtitleMap = {
  [GuideType.GuideForFreeListen]: '点首页右上角「+」按钮找签到入口、点底部导航栏「我的」找签到入口',
  [GuideType.Guide]: '点首页右上角「签」按钮找到、点击底部导航栏「我的」找到签到入口'
}

function GuideModal() {
  const themeStyles = useAtomValue(themeStyleAtom);
  const styles = getStyles(themeStyles);
  const { showModal, close } = useGuideModal();
  const taskInfo = useAtomValue(taskAtom);

  return showModal && taskInfo !== undefined ? <Pop
    handleClose={close}
  >
    <View style={styles.container}>
      <View style={styles.head}>
        <Text style={styles.title}>下次如何找到常驻入口</Text>
        <Text style={styles.subtitle}>{GuideSubtitleMap[(taskInfo?.guideType as GuideType)] || '后续可由福利中心常驻入口每日领现金'}</Text>
      </View>
      <View style={styles.guide}>
        {taskInfo?.guideType === GuideType.GuideForFreeListen ? <Image style={styles.img} source={{ uri: themeStyles.freeGif }} /> : null}
        {taskInfo?.guideType === GuideType.Guide ? <Image style={styles.img} source={{ uri: themeStyles.signInGif }} /> : null}
      </View>
      <Touch style={styles.btn} onPress={close}>
        <Text style={styles.btnText}>知道了</Text>
      </Touch>
      <Touch style={styles.close} onPress={close}>
        <BetterImage
          imgWidth={16}
          imgHeight={16}
          style={styles.closeIcon}
          source={{ uri: closeIcon }}
        />
      </Touch>
    </View>
  </Pop> : null
}

export default React.memo(GuideModal);