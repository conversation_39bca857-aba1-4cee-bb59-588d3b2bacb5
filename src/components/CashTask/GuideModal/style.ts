import { StyleSheet } from 'react-native';
import type { darkTheme } from '../store/theme/guide';

export const closeIcon = 'https://imagev2.xmcdn.com/storages/b8b5-audiofreehighqps/4D/56/GKwRIW4Ktxv6AAABCQMOMavJ.png';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    width: 275,
    height: 350,
    borderRadius: 10,
    position: 'relative',
    backgroundColor: theme.bgColor,

  },
  head: {
    paddingTop: 32,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 22,
    fontFamily: 'PingFang SC',
    color: theme.titleColor,
  },
  subtitle: {
    fontSize: 13,
    fontWeight: 'normal',
    lineHeight: 18,
    color: theme.subtitleColor,
    marginTop: 8,
    width: 227,
    textAlign: 'center'
  },
  close: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 8
  },
  closeIcon: {
    width: 16,
    height: 16,
  },
  guide: {
    width: 227,
    height: 156,
    backgroundColor: theme.guideBgColor,
    marginLeft: 'auto',
    marginRight: 'auto',
    borderRadius: 8,
    marginTop: 16,
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden'
  },
  img: {
    width: '100%',
    height: '100%'
  },
  btn: {
    width: 194,
    height: 40,
    backgroundColor: '#FF4444',
    borderRadius: 30,
    marginLeft: 'auto',
    marginRight: 'auto',
    marginTop: 18,
    justifyContent: 'center',
    alignItems: 'center'
  },
  btnText: {
    fontSize: 15,
    fontWeight: '500',
    color: '#FFF'
  }
});
