import { Platform, StyleSheet } from 'react-native';

const containerWidth = 301;

export const getStyles = () => StyleSheet.create({
  container: {
    position: 'relative',
    // backgroundColor: 'wheat'
    alignItems: 'center',
    justifyContent: 'center'
  },
  content: {
    position: 'absolute',
    zIndex: 2
  },
  bg: {
    width: containerWidth,
    height: 393
  },
  close: {
    width: 36,
    height: 36,

  },
  closeBtn: {
    position: 'absolute',
    top: 0,
    right: 0
  },
  amount: {
    width: containerWidth,
    alignItems: 'flex-end',
    justifyContent: 'center',
    position: 'absolute',
    flexDirection: 'row',
    top: 123,
  },
  amountNumber: {
    fontFamily: 'XmlyNumber',
    fontSize: 70,
    color: '#FE5355',
  },
  amountUnit: {
    fontFamily: 'PingFang SC',
    fontSize: 24,
    color: '#191C1B',
    marginLeft: 4,
    fontWeight: '600',
    top: Platform.OS === 'ios' ? -5 : -16
  },
  btnWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    width: containerWidth,
    bottom: 17,
    position: 'absolute',
  },
  btn: {
    width: 209,
    height: 64,
    position: 'relative',
    alignItems: 'center',
    // justifyContent: 'center',
  },
  btnBg: {
    width: 209,
    height: 60,
    position: 'absolute',
  },
  btnText: {
    marginTop: 14,
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 20,
    lineHeight: 24,
    color: '#C94F29'
  },
  awardIcon: {
    width: 105,
    height: 105,
  },
  award: {
    zIndex: 3,
    // backgroundColor: 'tomato'
  }
});