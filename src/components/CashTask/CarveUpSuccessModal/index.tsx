import { BetterImage, Text, Touch } from "@xmly/rn-components";
import Pop from "componentsV2/common/Pop";
import React, { useRef, useState } from "react";
import { View, Animated, Easing } from "react-native";
import type { LayoutChangeEvent } from 'react-native';
import { getStyles } from "./style";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { cashBalanceAtom } from "atom/refs";
import { carveUpModalShowAtom } from "../store/modal";
import { addMessageAtom } from "atom/push";
import { carveUpAwardAtom } from "../store/carveUpTask";
import { store } from "../../../store";

interface Coord {
  x: number;
  y: number;
  width?: number;
  height?: number;
}

export default function CarveUpSuccessModal() {
  const styles = getStyles();
  const inScale = new Animated.Value(0);
  const outScale = new Animated.Value(1);
  const left = new Animated.Value(0);
  const top = new Animated.Value(0);
  const cashNode = useAtomValue(cashBalanceAtom);
  const awardIconRef = useRef<View>();
  const [awardCoord, setAwardCoord] = useState<Coord>();
  const [modalShow, setModalShow] = useAtom(carveUpModalShowAtom);
  const awardInfo = useAtomValue(carveUpAwardAtom);
  const addMessage = useSetAtom(addMessageAtom);

  async function measureCashCoord(): Promise<Coord> {
    const coord: Coord = await new Promise((resolve) => {
      if (cashNode?.measureInWindow) {
        cashNode?.measureInWindow((x: number, y: number) => {
          resolve({ x, y });
        })
      } else {
        resolve({ x: 0, y: 0 });
      }
    });
    return coord;
  }

  async function close() {
    const cashCoord: Coord = await measureCashCoord();
    const toLeft = (cashCoord?.x ?? 0) - (awardCoord?.x ?? 0) - (awardCoord?.width ?? 0) / 3;
    const toTop = (cashCoord?.y ?? 0) - (awardCoord?.y ?? 0) - (awardCoord?.height ?? 0) / 4;
    const outAnimation = Animated.sequence([
      Animated.parallel([
        Animated.timing(inScale, {
          toValue: 1,
          duration: 600,
          easing: Easing.elastic(1.1),
          useNativeDriver: true
        }),
        Animated.timing(outScale, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true
        }),
      ]),
      Animated.parallel([
        Animated.timing(inScale, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true
        }),
        Animated.timing(left, {
          toValue: toLeft,
          duration: 300,
          useNativeDriver: true
        }),
        Animated.timing(top, {
          toValue: toTop,
          duration: 300,
          useNativeDriver: true
        }),
      ])
    ]);
    outAnimation.start(({ finished }) => {
      if (finished) {
        store.dispatch.goldCoin.getBalance();
        setModalShow(false);
        addMessage([{
          title: awardInfo?.title ?? '',
          subtitle: awardInfo?.subtitle ?? '',
        }]);
      }
    });
  };

  function awardIconLocate(e: LayoutChangeEvent) {
    awardIconRef?.current?.measureInWindow?.((x: number, y: number, width: number, height: number) => {
      setAwardCoord({ x, y, width, height });
    });
  }

  return modalShow === true ? <Pop handleClose={close}>
    <View style={styles.container}>
      <Animated.View style={[styles.content, {
        transform: [{ scale: outScale }]
      }]}>
        <BetterImage
          source={{ uri: 'https://imagev2.xmcdn.com/storages/578c-audiofreehighqps/28/4D/GAqhtYsK4B8CAACx6wMdrKDi.png' }}
          imgWidth={301}
          imgHeight={393}
          style={styles.bg}
        />
        <View style={styles.amount}>
          <Text style={styles.amountNumber}>{awardInfo?.amount}</Text>
          <Text style={styles.amountUnit}>元</Text>
        </View>
        <View style={styles.btnWrapper}>
          <Touch style={styles.btn} onPress={close}>
            <BetterImage
              source={{ uri: 'https://imagev2.xmcdn.com/storages/b2ca-audiofreehighqps/6C/60/GAqhjg0K4B-mAAAYTwMdrOs9.png' }}
              imgWidth={209}
              imgHeight={60}
              style={styles.btnBg}
              resizeMode="contain"
            />
            <Text style={styles.btnText}>立即收下</Text>
          </Touch>
        </View>
        <Touch style={styles.closeBtn} onPress={close}>
          <BetterImage
            source={{ uri: 'https://imagev2.xmcdn.com/storages/2524-audiofreehighqps/98/12/GKwRINsK2-H0AAADYAMb5GNL.png' }}
            imgWidth={36}
            imgHeight={36}
            style={styles.close}
          />
        </Touch>
      </Animated.View>
      <Animated.View
        style={[styles.award, {
          transform: [{ translateX: left }, { translateY: top }],
        }]}
        ref={awardIconRef}
        onLayout={awardIconLocate}
      >
        <Animated.View style={[{ transform: [{ scale: inScale }], }]}>
          <BetterImage
            source={{
              uri: 'https://imagev2.xmcdn.com/storages/0743-audiofreehighqps/DE/09/GKwRIJIK2-F9AAA2wAMb5B-d.png'
            }}
            imgWidth={105}
            imgHeight={105}
            style={styles.awardIcon}
          />
        </Animated.View>
      </Animated.View>
    </View>
  </Pop> : null
}