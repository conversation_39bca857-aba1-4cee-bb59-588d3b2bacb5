import React from "react";
import { Text, Touch } from "@xmly/rn-components";
import type { TextStyle, ViewStyle } from "react-native";
import { ActionType } from "../utils";
import usePlayBackdateAd from "../hook/usePlayBackdateAd";

interface PostSignBtnProps {
  onPress?: (btnText: string, action: ActionType) => void;
  title: string;
  style: ViewStyle | ViewStyle[];
  textStyle: TextStyle | TextStyle[];
}

export default function PostSignBtn(props: PostSignBtnProps) {
  const { onPress, title, style, textStyle } = props;
  const playAd = usePlayBackdateAd();

  function pressHandle() {
    onPress?.(title, ActionType.Video);
    playAd();
  }

  return <Touch
    onPress={pressHandle}
    activeOpacity={.5}
    style={style}
  >
    <Text style={textStyle}>{title}</Text>
  </Touch>
}