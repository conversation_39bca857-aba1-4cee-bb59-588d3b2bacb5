import React, { useState } from "react";
import { BetterImage, Text, Touch } from "@xmly/rn-components";
import AwardBox from "../AwardBox";
import { useAtomValue } from "jotai";
import { getStyles } from "./style";
import carveUpThemeStyleAtom from "../store/theme/carveUp";
import { View } from "react-native";
import type { LayoutChangeEvent } from "react-native";
import bannerThemeStyleAtom from "../store/theme/banner";
import taskAtom, { CarveUpTaskStatus } from "../store/task";
import usePlayAd from "../hook/usePlayAd";
import { Toast } from "@xmly/rn-sdk";
import { ActionType, bannerClickReport } from "../utils";
import LinearGradient from "react-native-linear-gradient";
import usePlayBackdateAd from "../hook/usePlayBackdateAd";

const days = [
  {
    day: '周一',
    letter: '看',
  },
  {
    day: '周二',
    letter: '视',
  },
  {
    day: '周三',
    letter: '频',
  },
  {
    day: '周四',
    letter: '瓜',
  },
  {
    day: '周五',
    letter: '分',
  },
  {
    day: '周六',
    letter: '大',
  },
  {
    day: '周日',
    letter: '奖',
  },
]

interface Props {
  title: string;
}

export default function AwardB(props: Props) {
  const { title } = props;
  const themeStyles = useAtomValue(carveUpThemeStyleAtom);
  const bannerThemeStyles = useAtomValue(bannerThemeStyleAtom);
  const styles = getStyles(themeStyles);
  const taskInfo = useAtomValue(taskAtom);
  const playAd = usePlayAd();
  const playBackDateAd = usePlayBackdateAd();
  const [dotLeft, setDotLeft] = useState(0);
  const [progressWidth, setProgressWidth] = useState(0);

  const carveUpTask = taskInfo?.carveUpCashAward;
  const subtitle = carveUpTask?.subTitle || '';
  const activeIndex = (carveUpTask?.dayOfWeek ?? 0) - 1;
  const progress = carveUpTask?.progress || 0;
  const completed = carveUpTask?.taskStatus === CarveUpTaskStatus.Completed;
  const backdateInfo = carveUpTask?.replenishSignIn;
  const unCompleted = carveUpTask?.taskStatus === CarveUpTaskStatus.Failed && !backdateInfo;
  const maxRewardTimes = taskInfo?.maxRewardTimes || 4;
  const alreadyRewardTimes = taskInfo?.alreadyRewardTimes || 0;

  const parseText = (text: string) => {
    const parts = text.split(/(\{\{.*?\}\})/);

    return parts.map((part, index) => {
      if (part.match(/\{\{.*?\}\}/)) {
        const emphasizedText = part.replace(/\{\{|\}\}/g, '');
        return <Text key={index} style={styles.bold}>{emphasizedText}</Text>;
      }
      return <Text key={index}>{part}</Text>;
    });
  };

  function play(index: number) {
    if (backdateInfo && index + 1 === backdateInfo?.replenishSignInDays) {
      // 补签
      playBackDateAd();
      bannerClickReport({ btnText: `待补签${backdateInfo?.alreadyTimes}/${backdateInfo?.totalTimes}`, action: ActionType.Video, moduleName: title });
    } else if (index === activeIndex && alreadyRewardTimes < maxRewardTimes) {
      playAd();
      bannerClickReport({ btnText: '看视频', action: ActionType.Video, moduleName: title });
    } else {
      Toast.info('未到领取时间');
    }
  }

  function onDotLayout(e: LayoutChangeEvent) {
    const { width, x } = e.nativeEvent.layout;
    const left = width / 2 + x;
    setDotLeft(left);
  }

  function onProgressLayout(e: LayoutChangeEvent) {
    const { width } = e.nativeEvent.layout;
    setProgressWidth(width);
  }

  return carveUpTask ? <AwardBox
    title={title}
    subtitle={<Text>需<Text style={styles.highlight}>连续7天</Text>看视频才可瓜分大奖</Text>}
  >
    <View>
      {unCompleted ?
        <View style={[styles.title, unCompleted ? styles.titleUncompleted : null]}>
          <Text style={styles.titleText}>瓜分</Text>
          <BetterImage
            source={{ uri: themeStyles.amountIcon }}
            style={styles.amountIcon}
          />
          <Text style={styles.titleText}>现金大奖敬请期待</Text>
        </View>
        :
        <View style={[styles.title, unCompleted ? styles.titleUncompleted : null]}>
          <Text style={styles.titleText}>瓜分</Text>
          <BetterImage
            source={{ uri: themeStyles.amountIcon }}
            style={styles.amountIcon}
          />
          <Text style={styles.titleText}>现金大奖</Text>
        </View>
      }
      {unCompleted ? null :
        <>
          <View style={[styles.subtitle, { position: 'relative' }]}>
            <BetterImage
              source={{
                uri: themeStyles.subtitleArrowIcon
              }}
              imgWidth={7.02}
              imgHeight={6.17}
              style={styles.subtitleArrowIcon}
            />
            <Text style={styles.subtitleText}>
              {parseText(subtitle)}
            </Text>
          </View>
          <View style={styles.days}>
            {days.map((item, index) => {
              const active = index === activeIndex;
              const completed = index < progress || (active && alreadyRewardTimes === maxRewardTimes);
              const isBackdate = backdateInfo && index === backdateInfo?.replenishSignInDays - 1;
              return <>
                <Touch
                  style={[styles.day]}
                  key={index}
                  onPress={() => play(index)}
                  disabled={completed}
                >
                  {isBackdate ? <BetterImage
                    source={{
                      uri: themeStyles.dayBackdateBg
                    }}
                    imgWidth={32}
                    imgHeight={33}
                    style={[styles.dayBg, active ? styles.active : null, completed ? styles.complete : null]}
                  /> : (active && !completed) ? <LinearGradient
                    style={[styles.dayBg]}
                    {...themeStyles.dayActiveBg}
                    useAngle={true}
                  /> : null}
                  {completed ?
                    <BetterImage
                      source={{
                        uri: themeStyles.rightIcon,
                      }}
                      imgWidth={18.5}
                      imgHeight={12.5}
                      style={styles.rightIcon}
                    />
                    :
                    isBackdate ?
                      <>
                        <Text style={[styles.activeTitle, styles.backdateTitle]}>{backdateInfo?.alreadyTimes}/{backdateInfo?.totalTimes}</Text>
                        <Text style={[styles.activeSubtitle, styles.backdateTitle]}>待补签</Text>
                      </>
                      :
                      active ?
                        <>
                          <Text style={styles.activeTitle}>{alreadyRewardTimes}/{maxRewardTimes}</Text>
                          <Text style={styles.activeSubtitle}>看视频</Text>
                        </>
                        :
                        <Text style={styles.letter}>{days[index]?.letter}</Text>
                  }
                </Touch>
                {index === days.length - 1 ? <BetterImage
                  source={{ uri: themeStyles.hintIcon }}
                  imgWidth={41}
                  imgHeight={41}
                  style={styles.hintIcon}
                /> : null}
              </>
            })}
          </View>
        </>
      }
      <View style={[styles.progress, unCompleted ? styles.progressUncompleted : null]}>
        <View style={styles.progressLine} onLayout={onProgressLayout}>
          <View style={[styles.progressTo, {
            width: `${completed ? 100 : 5 + 15 * activeIndex}%` // 两端5%的padding，中间每段（100-10）/6 = 15%
          }]} />
        </View>
        {days.map((item, index) => {
          const finished = index < progress;
          const active = activeIndex === index;
          const unCompletedStep = unCompleted && index === progress;
          const unCompletedRight = unCompleted && progress > 3;
          return <>
            {unCompletedStep ?
              <View
                style={[
                  styles.unCompleted,
                  unCompletedRight ? styles.unCompletedRight : null,
                  !carveUpTask?.subTitle ? styles.unCompletedNoText : null,
                  {
                    [unCompletedRight ? 'right' : 'left']: unCompletedRight ? progressWidth - dotLeft - 19 : dotLeft - 19,
                    opacity: dotLeft > 0 ? 1 : 0
                  }
                ]}
              >
                <View style={[styles.unCompletedIcon, unCompletedRight ? styles.unCompletedIconRight : styles.unCompletedIconLeft]}>
                  <Text style={styles.unCompletedText}>未完成</Text>
                  <BetterImage
                    source={{
                      uri: themeStyles.unCompletedArrowIcon
                    }}
                    imgWidth={4}
                    imgHeight={2}
                    style={styles.unCompletedArrowIcon}
                  />
                </View>
                {carveUpTask?.subTitle ? <Text
                  style={[styles.lastTurnText, unCompletedRight ? styles.lastTurnTextRight : styles.lastTurnTextLeft]}
                >
                  {carveUpTask?.subTitle.replace(/[{|}]/g, '')}
                </Text> : null}
              </View> : null
            }
            <View style={styles.step} onLayout={unCompletedStep ? onDotLayout : undefined}>
              <View style={[styles.dot, finished || active ? styles.finishedDot : null, {
                borderColor: bannerThemeStyles.bodyBgColor,
                opacity: index < activeIndex ? 0 : 1
              }]} />
              <Text style={[
                styles.progressTitle,
                index <= activeIndex ?
                  styles.progressTitleActive
                  :
                  styles.progressTitleFuture
              ]}>{days[index].day}</Text>
            </View>
          </>
        })}
      </View>
    </View>
  </AwardBox> : null
}