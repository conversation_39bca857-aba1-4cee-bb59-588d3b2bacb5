import { StyleSheet } from 'react-native';
import type { darkTheme } from '../store/theme/carveUp';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  highlight: {
    color: theme.highlightColor,
  },
  title: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 32,
  },
  titleUncompleted: {
    marginBottom: 4
  },
  titleText: {
    fontFamily: 'PingFang SC',
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.titleColor,
    textAlign: 'center',
  },
  amountText: {
    fontFamily: 'XmlyNumber',
    fontSize: 22,
    lineHeight: 32,
  },
  amountIcon: {
    // width: 74.34,
    height: 18.67,
    aspectRatio: 74.34 / 18.67,
    top: .5,
    marginHorizontal: 2
  },
  subtitle: {
    height: 23,
    marginLeft: 'auto',
    marginRight: 'auto',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.subtitleBgColor,
    borderRadius: 24,
    borderColor: theme.subtitleBorderColor,
    borderWidth: .5,
    position: 'absolute',
    paddingLeft: 8,
    paddingRight: 8,
  },
  subtitleArrowIcon: {
    width: 7.02,
    height: 6.17,
    position: 'absolute',
    top: -6,
    zIndex: 10,
  },
  subtitleText: {
    fontFamily: 'PingFang SC',
    fontSize: 11,
    color: theme.subtitleColor,
  },
  bold: {
    color: theme.subtitleBoldColor,
  },
  days: {
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: 16,
    marginBottom: 10,
  },
  day: {
    width: 32,
    height: 33,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    backgroundColor: theme.dayBgColor,
    borderRadius: 6
  },
  hintIcon: {
    width: 41,
    height: 41,
    opacity: 1,
    position: 'absolute',
    top: -38,
    right: -20,
  },
  complete: {
    opacity: .8
  },
  active: {
    opacity: 1
  },
  letter: {
    fontSize: 20,
    fontWeight: '500',
    fontFamily: 'PingFang SC',
    color: theme.dayLetterColor,
  },
  dayBg: {
    width: '100%',
    aspectRatio: 32 / 33,
    position: 'absolute',
    top: 0,
    left: 0,
    borderRadius: 6
  },
  rightIcon: {
    width: 18.5,
    height: 12.5,
    left: 1.25,
    top: .5
  },
  activeTitle: {
    fontFamily: 'XmlyNumber',
    fontSize: 11,
    fontWeight: '600',
    lineHeight: 15,
    top: 1,
    color: theme.activeTitleColor
  },
  activeSubtitle: {
    fontFamily: 'PingFang SC',
    fontSize: 8,
    fontWeight: '600',
    lineHeight: 15,
    top: -1,
    color: theme.activeTitleColor
  },
  backdateTitle: {
    color: theme.backdateTitleColor
  },
  progress: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'relative',
    marginHorizontal: 16,
    marginBottom: 8
  },
  progressUncompleted: {
    marginTop: 21
  },
  progressLine: {
    width: '100%',
    height: 2,
    borderRadius: 200,
    backgroundColor: theme.progressColor,
    position: 'absolute',
    left: 0
  },
  progressTo: {
    height: 2,
    borderRadius: 200,
    backgroundColor: theme.progressToColor,
    position: 'absolute',
    left: 0
  },
  progressTitle: {
    fontFamily: 'PingFang SC',
    fontSize: 11,
    lineHeight: 18
  },
  progressTitleActive: {
    color: theme.progressTitleActiveColor
  },
  progressTitleFuture: {
    color: theme.progressColor
  },
  step: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 5,
    position: 'relative',
  },
  dot: {
    backgroundColor: theme.progressColor,
    borderColor: '#FFF',
    borderWidth: 1,
    borderRadius: 200,
    width: 6,
    height: 6,
    top: -2,
    zIndex: 10
  },
  finishedDot: {
    backgroundColor: theme.progressToColor,
  },
  unCompleted: {
    borderRadius: 10,
    backgroundColor: theme.unCompletedBgColor,
    flexDirection: 'row',
    alignItems: 'center',
    position: 'absolute',
    top: -25,
    alignSelf: 'flex-start',
  },
  unCompletedNoText: {
    backgroundColor: 'transparent'
  },
  unCompletedRight: {
    flexDirection: 'row-reverse',
  },
  unCompletedIcon: {
    width: 38,
    paddingVertical: 2,
    borderRadius: 10,
    top: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.unCompletedIconBgColor,
  },
  unCompletedIconLeft: {
    left: 0,
  },
  unCompletedIconRight: {
    right: 0,
  },
  unCompletedArrowIcon: {
    width: 4,
    height: 2,
    position: 'absolute',
    bottom: -1.5,
  },
  unCompletedText: {
    color: '#FFF',
    fontFamily: 'PingFang SC',
    fontSize: 10,
  },
  lastTurnText: {
    fontFamily: 'PingFang SC',
    fontSize: 10,
    fontWeight: 'normal',
    color: theme.lastTurnTextColor,
  },
  lastTurnTextLeft: {
    paddingLeft: 4,
    paddingRight: 8,
    textAlign: 'left',
  },
  lastTurnTextRight: {
    textAlign: 'right',
    paddingRight: 4,
    paddingLeft: 8,
  }
});
