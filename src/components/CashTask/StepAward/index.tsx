import { BetterImage, Text, Touch } from "@xmly/rn-components";
import { bigAward } from "../style";
import { getStyles } from "./style";
import { StyleSheet, View } from "react-native";
import { useAtomValue } from "jotai";
import awardsAtom from "../store/award";
import awardThemeStyleAtom from "../store/theme/award";
import LinearGradient from 'react-native-linear-gradient';
import React from "react";
import type { darkTheme } from "../store/theme/award";
import usePlayAd from "../hook/usePlayAd";
import { Toast } from "@xmly/rn-sdk";
import { AwardStatus } from "../store/task";

interface Props {
  customThemeStyles?: Partial<typeof darkTheme>;
  fold?: boolean;
  onPressReport?: () => void;
}

function StepAward(props: Props) {
  const { customThemeStyles = {}, fold, onPressReport } = props;
  const awardThemeStyles = useAtomValue(awardThemeStyleAtom);
  const themeStyles = { ...awardThemeStyles, ...customThemeStyles };
  const styles = getStyles(themeStyles);
  const awards = useAtomValue(awardsAtom);
  const todayIndex = Array.isArray(awards) ? awards.findIndex(award => award.today) : -1;
  const firstBigStep = Array.isArray(awards) ? awards.findIndex((award, index) => award.bigAmount && index !== 3 && award.status !== AwardStatus.GOT && (!award.today || (index === awards.length - 1 && award.today))) : 0; // 今天不显示大额布局，除非今天是最后一天
  const playAd = usePlayAd();
  const todayCompleted = awards?.[todayIndex]?.status === AwardStatus.GOT;

  function play(status: AwardStatus, index: number) {
    if (index === todayIndex) {
      if (status === AwardStatus.CAN_GET) {
        playAd();
        onPressReport?.();
      }
    } else {
      Toast.info('未到领取时间');
    }
  }

  return <View
    style={[styles.steps, fold ? styles.foldSteps : null]}
  >
    {Array.isArray(awards) ? awards.map((award, index) => {
      if (fold) {
        if (todayIndex <= 3) { //展示第一排
          if (firstBigStep < 3) { // 第一排3个
            if (index > 2) {
              return null;
            }
          } else { // 第一排4个
            if (index > 3) {
              return null;
            }
          }
        } else { //展示第二排
          if (firstBigStep < 3) { // 第二排4个
            if (index < 3) {
              return null;
            }
          } else { // 第二排3个
            if (index < 4) {
              return null;
            }
          }
        }
      }
      const isBig = index === firstBigStep && (!award.today || (index === awards.length - 1 && award.today));
      let headBg: typeof darkTheme.stepHeadBg;
      let stepBodyStyle, stepBgStyle, stepHeadTextStyle, amountTextStyle, stepHeadStyle;
      const headText = award.today ? '今日可领' :
        (award.tomorrow && index === 0) || (todayCompleted && index === todayIndex + 1) ?
          '明日可领'
          :
          `第${award.stepNo}天`;

      if (award.status === AwardStatus.GOT) {
        headBg = themeStyles.stepHeadBgFinish;
        stepBgStyle = StyleSheet.flatten([styles.stepBg, styles.stepBgFinish]);
        stepBodyStyle = StyleSheet.flatten([styles.step, styles.stepFinish]);
        stepHeadTextStyle = StyleSheet.flatten([styles.stepHeadText, styles.stepHeadTextFinish]);
      } else if (award.today) {
        amountTextStyle = StyleSheet.flatten([styles.amountText, styles.amountTextActive]);
        headBg = themeStyles.stepHeadBgActive;
        stepBgStyle = StyleSheet.flatten([styles.stepBg, styles.stepBgActive]);
        stepBodyStyle = StyleSheet.flatten([styles.step, styles.stepActive]);
        stepHeadTextStyle = StyleSheet.flatten([styles.stepHeadText, styles.stepHeadTextActive]);
      } else {
        amountTextStyle = styles.amountText;
        headBg = themeStyles.stepHeadBg;
        stepBgStyle = styles.stepBg;
        stepBodyStyle = styles.step;
        stepHeadTextStyle = styles.stepHeadText;
        stepHeadStyle = styles.stepHeadBg;
      }

      return <Touch
        style={[
          stepBodyStyle,
          { flexBasis: isBig ? '47%' : '22%' },
          { zIndex: awards.length - index }
        ]}
        key={index}
        onPress={() => play(award.status, index)}
        disabled={award.status === AwardStatus.GOT}
      >
        <View style={stepBgStyle}>
          <LinearGradient
            style={[styles.stepHead, stepHeadStyle]}
            useAngle={true}
            {...headBg}
          />
        </View>
        <View
          style={styles.stepHead}
        >
          {award.today ?
            <Text style={stepHeadTextStyle}>
              {headText}
            </Text>
            :
            award.bigAmount ?
              <View style={styles.stepHeadTextWrap}>
                <Text style={stepHeadTextStyle}>
                  {headText}
                </Text>
                {award.status !== AwardStatus.GOT ?
                  <BetterImage
                    imgWidth={32}
                    imgHeight={16}
                    source={{ uri: bigAward }}
                    style={[styles.bigAward, headText.length === 4 ? { left: 52 } : null]}
                  />
                  :
                  null}
              </View>
              :
              <Text style={stepHeadTextStyle}>
                {headText}
              </Text>
          }
        </View>
        {award.status === AwardStatus.GOT ?
          <BetterImage
            source={{ uri: themeStyles.stepFinishBg }}
            imgWidth={47}
            imgHeight={43}
            style={styles.finishBg}
          />
          :
          <View style={styles.stepContent}>
            <Text style={styles.atMost}>最高</Text>
            <View style={styles.amount}>
              <Text style={[styles.amountNumber, amountTextStyle]}>{award.amount}</Text>
              <Text style={[styles.amountUnit, amountTextStyle]}>元</Text>
            </View>
          </View>
        }
      </Touch>
    }) : null}
  </View>
}

export default React.memo(StepAward);