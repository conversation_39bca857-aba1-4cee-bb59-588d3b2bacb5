import { countdownAtom } from 'components/TreasureBox/atom/countdown';
import { atom } from 'jotai';
import queryCashTaskInfo from 'servicesV2/queryCashTaskInfo';

const mockData = {
  "title": "连续签到7天，最高累计可获取22.5元", // 奖励标题
  "buttonText": "今日已领取，明日再来（4/4）",
  "enableReward": true, // 是否可以领取
  "alreadyRewardTimes": 1, // 已经领取的奖励次数
  "maxRewardTimes": 4, // 最多可领取次数
  "adPositionName": "", //资源位名称
  "adPositionId": "", // 资源位ID
}

export enum AwardStatus {
  CAN_GET = 1,
  GOT = 2,
  CANNOT_GET = 3
}

export interface AwardInfo {
  stepNo: number;
  amount: string;
  status: AwardStatus;
  bigAmount: boolean;
  today: boolean;
  tomorrow: boolean;
}

export enum CarveUpTaskStatus {
  InProgress = 1, // 任务进行中
  Failed = 2, // 任务失败
  Completed = 3, // 任务已完成
  Backdate = 4, // 可补签
}

export type CarveUpCashAward = {
  taskStatus: CarveUpTaskStatus;
  progress: number; // 任务进度，已完成天数 0-7
  dayOfWeek: number; // 今天是周几
  subTitle: string;
  replenishSignIn?: {
    replenishSignInDays: number;
    totalTimes: number;
    alreadyTimes: number;
    btnText: string;
  }
}

export type WithdrawNotice = {
  avatar: string;
  nickName: string;
  content?: string;
  amount: string;
}

export type TaskInfo = {
  success: boolean;
  failCode: number;
  title: string | null;
  buttonText: string;
  enableReward: boolean;
  alreadyRewardTimes: number;
  maxRewardTimes: number;
  adPositionName: string;
  adPositionId: number;
  awardInfo: AwardInfo[];
  guideType: GuideType;
  carveUpCashAward: CarveUpCashAward;
  treasureBox?: { calmSeconds: number };
  withdrawSwiper?: WithdrawNotice[];
}

export enum GuideType {
  Guide = 1,
  GuideForFreeListen = 2,
}

export type NullishTaskInfo = TaskInfo | null | undefined;

const taskAtom = atom<NullishTaskInfo>(undefined);

export const writeTaskAtom = atom(void 0, async (get, set) => {
  let taskInfo = null;
  let treasureBoxCountDown;
  try {
    const result = await queryCashTaskInfo();
    if (result?.data?.success) {
      taskInfo = result?.data;
      treasureBoxCountDown = taskInfo?.treasureBox?.calmSeconds;
    }
  } catch (e) {
    taskInfo = null;
  }
  set(taskAtom, taskInfo);
  if (Number.isFinite(treasureBoxCountDown)) {
    set(countdownAtom, treasureBoxCountDown);
  }
});

export default taskAtom;
