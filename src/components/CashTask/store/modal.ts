import storage from "../../../storage";
import { atom } from 'jotai';
import { atomWithStorage, createJSONStorage } from 'jotai/utils';

const nativeStorage = createJSONStorage(() => ({
  setItem: storage.set,
  getItem: storage.get,
  removeItem: storage.remove,
}))

export const modalShowDateAtom = atomWithStorage('cashModalShowDate', undefined, nativeStorage);
export const guideModalShowDateAtom = atomWithStorage('guideModalShowDate', undefined, nativeStorage);
export const cashModalShowAtom = atom<boolean | undefined>(undefined);
export const cashModalPendingStateAtom = atom<boolean>(true);
export const guideModalShowAtom = atom<boolean | undefined>(undefined);
export const carveUpModalShowAtom = atom<boolean | undefined>(undefined);