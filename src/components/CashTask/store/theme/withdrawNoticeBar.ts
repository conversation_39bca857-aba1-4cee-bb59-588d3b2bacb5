import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  noticeBarBgColor: '#341A1C',
  highlight: '#DF6774',
  textColor: '#ACACAF',
}

const lightTheme = {
  noticeBarBgColor: '#FFE4E5',
  highlight: '#FF4C5A',
  textColor: '#444444',
}

const withdrawNoticeBarThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default withdrawNoticeBarThemeStyleAtom;