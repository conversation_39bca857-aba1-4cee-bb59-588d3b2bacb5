import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  bgColor: '#282828',
  titleColor: '#FFF',
  subtitleColor: '#8D8D91',
  guideBgColor: '#4E5152',
  signInGif: 'https://imagev2.xmcdn.com/storages/e0b1-audiofreehighqps/68/20/GKwRIasKudCcABAAAAMPYLw5.gif',
  freeGif: 'https://imagev2.xmcdn.com/storages/a2e4-audiofreehighqps/14/FA/GKwRIW4KudCeABAAAAMPYL15.gif',
}

const lightTheme = {
  bgColor: '#FFF',
  titleColor: '#333',
  subtitleColor: '#666',
  guideBgColor: '#F7F8FA',
  signInGif: 'https://imagev2.xmcdn.com/storages/7357-audiofreehighqps/F5/55/GAqhubMKudCiABAAAAMPYL9L.gif',
  freeGif: 'https://imagev2.xmcdn.com/storages/ac20-audiofreehighqps/5A/48/GAqhSt4KudCjABAAAAMPYMCN.gif',
}

const themeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default themeStyleAtom;