import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  highlightColor: '#8D545A',
  titleColor: '#DF6774',
  amountIcon: 'https://imagev2.xmcdn.com/storages/29ac-audiofreehighqps/FF/EE/GAqhAwwK_fliAAAE0gMpkHcb.png',
  subtitleBgColor: '#504039',
  subtitleBorderColor: '#675247',
  subtitleColor: '#B08A73',
  subtitleBoldColor: '#D68F63',
  subtitleArrowIcon: 'https://imagev2.xmcdn.com/storages/1c1c-audiofreehighqps/95/A4/GKwRINsKyoAbAAABEgMVn8_F.png',
  dayBackdateBg: 'https://imagev2.xmcdn.com/storages/3d8d-audiofreehighqps/DB/D3/GKwRIMALg11_AAACVQNmDJK4.png',
  dayBgColor: '#502F34',
  dayActiveBg: {
    angle: 319,
    colors: ['#AF4954', '#B24750'],
    locations: [.05, .94]
  },
  dayLetterColor: '#6A4246',
  rightIcon: 'https://imagev2.xmcdn.com/storages/5cbd-audiofreehighqps/75/D1/GKwRIUELg11_AAABeQNmDJLS.png',
  activeTitleColor: '#D18990',
  backdateTitleColor: '#DF6774',
  progressTitleActiveColor: '#B24851',
  progressColor: '#502F34',
  progressToColor: '#B24851',
  hintIcon: 'https://imagev2.xmcdn.com/storages/43b0-audiofreehighqps/AA/C9/GKwRIasKyF63AAANuAMUuyPN.png',
  unCompletedArrowIcon: 'https://imagev2.xmcdn.com/storages/dc49-audiofreehighqps/56/60/GKwRIDoKyoAaAAAAugMVn893.png',
  unCompletedIconBgColor: '#8F4750',
  lastTurnTextColor: '#B55761',
  unCompletedBgColor: '#41292D',
}

const lightTheme = {
  highlightColor: '#FF7C7E',
  titleColor: '#FF4655',
  amountIcon: 'https://imagev2.xmcdn.com/storages/ea80-audiofreehighqps/25/E8/GAqhig8K_fliAAAIvQMpkHdU.png',
  subtitleBgColor: '#FFF0DD',
  subtitleBorderColor: '#F9DCB7',
  subtitleColor: '#CB8C43',
  subtitleBoldColor: '#BE6D22',
  subtitleArrowIcon: 'https://imagev2.xmcdn.com/storages/29c6-audiofreehighqps/05/9D/GKwRIJEKyYNaAAABDQMVL4fA.png',
  dayBackdateBg: 'https://imagev2.xmcdn.com/storages/b61c-audiofreehighqps/80/24/GKwRIDoLg11_AAACWgNmDJJ8.png',
  dayBgColor: '#FFEAEB',
  dayActiveBg: {
    angle: 135,
    colors: ['#FF4857', '#FF8387'],
    locations: [0, 1]
  },
  dayLetterColor: 'rgba(255, 182, 185, .3)',
  rightIcon: 'https://imagev2.xmcdn.com/storages/805f-audiofreehighqps/56/6C/GKwRIDoLg366AAABmwNmIh40.png',
  activeTitleColor: '#FFD8DA',
  backdateTitleColor: '#FF7680',
  progressTitleActiveColor: '#FF5E70',
  progressColor: '#FBD9DA',
  progressToColor: '#FF5E70',
  hintIcon: 'https://imagev2.xmcdn.com/storages/f6be-audiofreehighqps/7F/49/GKwRIW4KyF63AAAO-gMUuyOV.png',
  unCompletedArrowIcon: 'https://imagev2.xmcdn.com/storages/f57f-audiofreehighqps/39/AA/GKwRIJIKyoAbAAAAugMVn8-q.png',
  unCompletedIconBgColor: '#FF8F9B',
  lastTurnTextColor: '#F97181',
  unCompletedBgColor: '#FCE4E5',
}

const carveUpThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default carveUpThemeStyleAtom;