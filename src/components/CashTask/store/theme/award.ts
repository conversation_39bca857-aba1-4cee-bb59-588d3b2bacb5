import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  bodyBgColor: '#1B1B1B',
  bodyBgColorActive: '#1B1B1B',
  bodyBgColorFinish: '#2E1C1E',
  stepTextColor: '#92725E',
  stepTextColorActive: '#FFEFF1',
  stepTextColorFinish: '#6F4D50',
  stepHeadBg: {
    angle: 270,
    colors: ['#816658', '#6D5149'],
  },
  stepHeadBgOpacity: .5,
  stepHeadBgActive: {
    angle: 103,
    colors: ['#7B373F', '#63262B'],
    locations: [0.01, 0.95]
  },
  stepHeadBgFinish: {
    angle: 94,
    colors: ['#3B292B', '#3D2527'],
    locations: [0.04, 0.99]
  },
  stepContentBgColor: '#4D4039',
  stepContentBgColorActive: '#6F2D34',
  stepContentBgColorFinish: '#2E1C1E',
  stepBorderColor: '#4D4039',
  stepBorderColorActive: '#6F2D34',
  stepBorderColorFinish: '#503437',
  stepFinishBg: 'https://imagev2.xmcdn.com/storages/5ca5-audiofreehighqps/D5/24/GAqh2R4Km36OAAANbwMDfrZr.png',
  atMostColor: 'rgba(255, 255, 255, 0.2)',
  stepAwardTextColor: '#92725E',
  stepAwardTextColorActive: '#DF6774',
}

const lightTheme = {
  bodyBgColor: '#FFFAF5',
  bodyBgColorActive: '#FAFAFA',
  bodyBgColorFinish: '#FFF8F8',
  stepTextColor: '#CB8C43',
  stepTextColorActive: '#FFF',
  stepTextColorFinish: '#F0A4A4',
  stepHeadBg: {
    angle: 270,
    colors: ['rgba(254, 230, 201, 0.98)', '#FEDAB3'],
  },
  stepHeadBgOpacity: 1,
  stepHeadBgActive: {
    angle: 110,
    colors: ['#FF6E7F', '#FF4554'],
    locations: [0.14, 0.95]
  },
  stepHeadBgFinish: {
    angle: 100,
    colors: ['#FFE6E6', '#FFD0D0'],
    locations: [0.01, 0.99]
  },
  stepContentBgColor: '#FEDCB7',
  stepContentBgColorActive: '#FAFAFA',
  stepContentBgColorFinish: '#FFF8F8',
  stepBorderColor: '#FEDCB7',
  stepBorderColorActive: '#FF4250',
  stepBorderColorFinish: '#FCC3C3',
  stepFinishBg: 'https://imagev2.xmcdn.com/storages/cdfa-audiofreehighqps/14/31/GKwRIJEKm36OAAANgAMDfra5.png',
  atMostColor: '#DCDCDC',
  stepAwardTextColor: '#BE6D22',
  stepAwardTextColorActive: '#FF424F',
}

const awardThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default awardThemeStyleAtom;