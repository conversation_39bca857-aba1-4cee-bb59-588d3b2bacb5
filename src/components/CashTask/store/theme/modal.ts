import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  modalBg: 'https://imagev2.xmcdn.com/storages/d3bf-audiofreehighqps/F8/E2/GArMJnIKmdCZAABu9QMCwFGk.png',
  modalBgColor: '#3B1D1F',
  bodyBgColor: '#1B1B1B',
  btnBg: {
    angle: 90,
    colors: ['#AF4955', '#B2474F'],
    locations: [0, 1]
  },
}

const lightTheme = {
  modalBg: 'https://imagev2.xmcdn.com/storages/4089-audiofreehighqps/7B/7D/GArMejYKmdCaAACuOgMCwFHj.png',
  modalBgColor: '#F03841',
  bodyBgColor: '#FFF',
  btnBg: {
    angle: 89,
    colors: ['#FF6C7E', '#FF4A5C'],
    locations: [0, 0.99]
  },
}

const themeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default themeStyleAtom;