import { StyleSheet } from 'react-native';
import type { darkTheme } from '../store/theme/banner';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    marginHorizontal: 8,
    marginTop: 8,
    borderRadius: 8,
    // overflow: 'hidden',
    position: 'relative',
    // height: 111
  },
  head: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 2,
    alignItems: 'center',
  },
  title: {
    position: 'relative',
    textAlign: 'center',
    width: 124,
    height: 30,
  },
  titleBg: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
  },
  titleText: {
    fontFamily: 'PingFang SC',
    fontSize: 11,
    fontWeight: '600',
    lineHeight: 16,
    letterSpacing: 0,
    color: theme.awardBoxTitleColor,
    textAlign: 'center',
    marginTop: 4
  },
  body: {
    paddingHorizontal: 4,
  },
  subtitle: {
    fontFamily: 'PingFang SC',
    fontSize: 11,
    fontWeight: 'normal',
    color: theme.awardBoxSubtitleColor,
    marginRight: 8
  }
});
