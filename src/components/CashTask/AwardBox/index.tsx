import { BetterImage, Text } from "@xmly/rn-components";
import { useAtomValue } from "jotai";
import React from "react";
import type { PropsWithChildren } from "react";
import { View } from "react-native";
import type { ViewStyle } from 'react-native';
import bannerThemeStyleAtom from "../store/theme/banner";
import { getStyles } from "./styles";

interface Props {
  title: string;
  subtitle?: string | React.ReactNode;
  containerStyle?: ViewStyle;
}

export default function AwardBox(props: PropsWithChildren<Props>) {
  const { children, title, subtitle, containerStyle } = props;
  const themeStyles = useAtomValue(bannerThemeStyleAtom);
  const styles = getStyles(themeStyles);

  return <View style={[styles.container, { backgroundColor: themeStyles.bodyBgColor }, containerStyle]}>
    <View style={styles.head}>
      <View style={styles.title}>
        <BetterImage
          source={{ uri: themeStyles.awardBoxTitleBg }}
          imgWidth={124}
          imgHeight={30}
          style={styles.titleBg}
          resizeMode={'stretch'}
        />
        <Text style={styles.titleText}>{title}</Text>
      </View>
      <Text style={styles.subtitle}>{subtitle}</Text>
    </View>
    <View style={styles.body}>
      {children}
    </View>
  </View>
}