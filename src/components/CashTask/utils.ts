import xmlog from "utilsV2/xmlog";

interface BannerClickReportParams {
  btnText: string;
  action: ActionType;
  moduleName?: string;
}

export enum ActionType {
  Calendar = 'calendar',
  Video = 'video',
}

// Banner 点击
export function bannerClickReport(params: BannerClickReportParams) {
  const { btnText, action = '', moduleName = '' } = params;
  // 任务中心-福利中心-签到模块  点击事件
  xmlog.click(65102, '', { currPage: '任务中心', Item: btnText, moduleName, action });
}

export enum AreaType {
  Item = 'item',
  Button = 'button',
}

interface ModalClickReportParams {
  btnText: string;
  area: AreaType;
  action: ActionType;
}

// Modal 点击
export function modalClickReport(params: ModalClickReportParams) {
  const { btnText, area, action } = params;
  // 任务中心-福利中心-签到弹窗  点击事件
  xmlog.click(65081, '', { currPage: '任务中心', Item: btnText, area, action });
}