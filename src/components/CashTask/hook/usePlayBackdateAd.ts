import { useSet<PERSON>tom } from "jotai";
import playAd from "../../../utils/playAd";
import { useCallback, useRef } from "react";
import throttle from "utilsV2/throttle";
import { writeTaskAtom } from "../store/task";
import { PageEventEmitter } from "defs";

export default function usePlayBackdateAd() {
  const queryCashTask = useSetAtom(writeTaskAtom);
  const paused = useRef(false);

  async function handler() {
    const pagePauseListener = PageEventEmitter.addListener('onPause', () => {
      paused.current = true;
    });
    const result = await playAd({
      positionName: 'integral_center_inspire_video',
      positionId: 254,
      extInfo: JSON.stringify({ sourceName: 'postSign' }),
      failInfo: '未完成任务，暂未补签成功'
    });
    if (result === false && !paused.current) { // 播放失败且未打开（广告位失败可能直接发奖励）更新任务状态
      setTimeout(queryCashTask, 800);
      paused.current = false;
    }
    pagePauseListener?.remove?.();
  }

  const playFunction = useCallback(throttle(handler, 300), []);

  return playFunction;
}