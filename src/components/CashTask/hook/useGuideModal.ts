import { NativeInfoContext } from "contextV2/nativeInfoContext";
import { useAtom } from "jotai";
import { useContext, useEffect, useState } from "react";
import { guideModalShowAtom, guideModalShowDateAtom } from "../store/modal";
import dayjs from "dayjs";
import { cashTaskTouchChannel } from "constantsV2";

const duration = 3;

export default function useGuideModal() {
  const nativeInfo = useContext(NativeInfoContext);
  const srcChannel = nativeInfo?.srcChannel;
  const [guideShow, setGuideShow] = useAtom(guideModalShowDateAtom);
  const [showDate, showTimes = 0] = typeof guideShow === 'string' ? guideShow?.split?.('_') : ['', 0];
  const [showModal, setShowModal] = useAtom(guideModalShowAtom);
  const today = dayjs().format('YYYY-MM-DD');
  const times = Number(showTimes);

  useEffect(() => {
    if (
      srcChannel === cashTaskTouchChannel
      && guideShow !== undefined
      && showDate !== today
      && Number.isFinite(times) && times < duration
    ) {
      setShowModal(true);
      setGuideShow(`${today}_${times + 1}`);
    }
  }, [srcChannel, guideShow, showDate, times, today])

  function close() {
    setShowModal(false);
  }

  return { showModal, close, newbie: Number.isFinite(times) && (times < duration || (times === duration && showDate === today)) };
}