import { addMessage<PERSON>tom } from "atom/push";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect } from "react"
import { throttleCarveUpCashAward as carveUpCashAward } from "servicesV2/queryCashTaskInfo"
import { carveUpModalShowAtom, cashModalPendingStateAtom, cashModalShowAtom, guideModalShowAtom, guideModalShowDateAtom, modalShowDateAtom } from "../store/modal";
import taskAtom from "../store/task";
import useTaskCenterLoaded from "hooks/useTaskCenterLoaded";
import { carveUpAwardAtom } from "../store/carveUpTask";
import { store } from "../../../store";

export default function useCarveUpAward() {
  const showCashModal = useAtomValue(cashModalShowAtom);
  const guideShow = useAtomValue(guideModalShowAtom);
  const taskInfo = useAtomValue(taskAtom);
  const showDate = useAtomValue(modalShowDateAtom);
  const guideModalShowDate = useAtomValue(guideModalShowDateAtom);
  const addMessage = useSetAtom(addMessageAtom);
  const taskCenterLoaded = useTaskCenterLoaded();
  const setCarveUpModalShow = useSetAtom(carveUpModalShowAtom);
  const setCarveUpAward = useSetAtom(carveUpAwardAtom);
  const cashModalPending = useAtomValue(cashModalPendingStateAtom);

  async function carveUp() {
    try {
      const result = await carveUpCashAward();
      if (result?.data?.success && result?.data?.toast) {
        const title = '现金到账通知';
        const subtitle = result?.data?.toast;
        const amount = result?.data?.amount;
        if (amount) {
          setCarveUpModalShow(true);
          setCarveUpAward({ title, subtitle, amount });
        } else {
          store.dispatch.goldCoin.getBalance();
          addMessage([{ title, subtitle }]);
        }
      }
    } catch (error) {
      console.info('debug_carveUpCashAward_error', error);
    }
  }

  useEffect(() => {
    if (taskInfo !== undefined
      && guideModalShowDate !== undefined
      && !guideShow
      && taskCenterLoaded
      && !showCashModal
      && !cashModalPending
    ) {
      carveUp();
    }
  }, [guideShow, guideModalShowDate, showCashModal, cashModalPending, taskInfo, showDate, taskCenterLoaded])

}