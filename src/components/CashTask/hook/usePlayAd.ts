import { useAtomValue } from "jotai";
import playAd from "../../../utils/playAd";
import { useSelector } from "react-redux";
import type { RootState } from "store";
import taskAtom from "../store/task";
import awardsAtom from "../store/award";
import { useCallback } from "react";
import throttle from "utilsV2/throttle";

export default function usePlayAd() {
  const taskInfo = useAtomValue(taskAtom);
  const award = useAtomValue(awardsAtom);
  const cashBalance = useSelector((state: RootState) => state.goldCoin.balance);

  async function handler() {
    const stepNo = award.find((item) => item.today)?.stepNo;
    if (taskInfo?.adPositionName && taskInfo?.adPositionId) {
      await playAd({
        positionName: taskInfo.adPositionName,
        positionId: Number(taskInfo.adPositionId),
        otherParams: {
          cashBalance,
          stepNo,
          maxRewardTimes: taskInfo.maxRewardTimes,
          rewardTimes: taskInfo.alreadyRewardTimes + 1,
        },
        extInfo: JSON.stringify({ sourceName: 'dailyReward' })
      });
    }
  }

  const playFunction = useCallback(throttle(handler, 300), [award, cashBalance, taskInfo]);

  return playFunction;
}