import { NativeInfoContext } from "contextV2/nativeInfoContext";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import { useContext, useEffect } from "react";
import { cashModalPendingStateAtom, cashModalShowAtom, modalShowDateAtom } from "../store/modal";
import dayjs from "dayjs";
import taskAtom from "../store/task";
import { cashTaskTouchChannel } from "constantsV2";
import xmlog from "utilsV2/xmlog";
import useGuideModal from "./useGuideModal";

export default function useShowCashModal() {
  const nativeInfo = useContext(NativeInfoContext);
  const srcChannel = nativeInfo?.srcChannel;
  const [showDate, setShowDate] = useAtom(modalShowDateAtom);
  const today = dayjs().format('YYYY-MM-DD');
  const taskInfo = useAtomValue(taskAtom);
  const { showModal: guideShow, newbie } = useGuideModal();
  const setShowModal = useSetAtom(cashModalShowAtom);
  const setCashModalPending = useSetAtom(cashModalPendingStateAtom);
  const guideShowDate = useAtomValue(modalShowDateAtom);

  useEffect(() => {
    if (
      guideShowDate !== undefined
      && taskInfo !== undefined
      && showDate !== undefined
    ) {
      setCashModalPending(false);
      if (
        !guideShow && (!newbie || srcChannel !== cashTaskTouchChannel) // 前n天且从touch进入显示引导弹窗不显示领现金弹窗
        && taskInfo?.enableReward
        && showDate !== today
      ) {
        setShowDate(today);
        setShowModal(true);
        // 任务中心-福利中心-签到弹窗  控件曝光
        xmlog.event(65082, 'slipPage', { currPage: '任务中心' });
      }
    }
  }, [guideShowDate, showDate, srcChannel, taskInfo, guideShow])

  function close() {
    setShowModal(false);
  }

  return { close };
}