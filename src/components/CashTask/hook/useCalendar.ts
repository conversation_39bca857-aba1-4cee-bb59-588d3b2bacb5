import { useAtom } from 'jotai';
import { useCallback, useEffect, useState } from 'react';
import { calendarAtom } from '../store/calendar';
import dayjs from 'dayjs';
import { NativeModules, Platform } from 'react-native';
import { Toast } from '@xmly/rn-sdk';
import storage from "../../../storage";

function getToAddDays() {
  const weekday = dayjs().day(); // 0-6, 0 is Sunday
  const addDays = 7 - weekday;
  // const addDays = 7;
  const toAddDays = [];
  for (let i = 1; i <= addDays; i++) {
    toAddDays.push(dayjs().add(i, 'day').format('YYYY-MM-DD'));
  }
  return toAddDays;
}

async function addSchedule(toAddDays: string[]) {
  await Promise.all(toAddDays.map(async day => {
    const result = await NativeModules.Business.calendarWrite({
      title: '看视频，领现金，瓜分50万大奖',
      desc: '',
      startDate: `${day} 10:00:00`,
      endDate: `${day} 11:00:00`,
      url: itingUrl,
      notes: `连续7天看视频，领现金，瓜分50万大奖 ${Platform.OS === 'android' ? `https://m.ximalaya.com/applink?open_xm=${encodeURIComponent(itingUrl)}` : itingUrl}`,
    });
    if (result?.code !== 0) {
      throw new Error(result?.msg || '日历添加失败');
    }
  }));
}

const itingUrl = 'iting://open?msg_type=94&bundle=rn_credit_center&srcChannel=cash_task_calendar&reuse=1';

export default function useCalendar() {
  const [added, setAdded] = useState(true);
  const [calendarInfo = '', setCalendarInfo] = useAtom(calendarAtom);
  const calendarDays = String(calendarInfo).split(',');
  const toAddDays = getToAddDays(); // 本周要添加的日期，更新至本地存储

  useEffect(() => {
    const inCalendar = toAddDays.every(day => calendarDays.indexOf(day) !== -1)
    if (inCalendar) {
      setAdded(true);
    } else {
      setAdded(false);
    }
  }, [calendarInfo])

  const addToCalendar = useCallback(async () => {
    try {
      await addSchedule(toAddDays.slice(0, 1)); // 只添加第一个，防止重复触发权限申请
      if (toAddDays.length > 1) {
        await addSchedule(toAddDays.slice(1));
      }
      Toast.info('已开启日历提醒');
      setAdded(true);
      setCalendarInfo(toAddDays.join(','));
    } catch (e) {
      console.info('debug_calendarWrite_err', e);
      Toast.info(e.message || '日历添加失败');
    }
  }, [toAddDays]);

  return {
    added,
    addToCalendar,
  }
}