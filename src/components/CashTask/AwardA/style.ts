import { StyleSheet } from 'react-native';
import { gap } from '../style';

export const getStyles = () => StyleSheet.create({
  foldBtnWrapper: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '100%',
    marginTop: 2,
  },
  foldBtn: {
    width: 58,
    height: 36,
    position: 'absolute',
    bottom: -4,
    zIndex: 1,
  },
  foldBtnIconWrapper: {
    position: 'relative',
    width: 42,
    height: 9,
    justifyContent: 'center',
    alignItems: 'center',
  },
  foldBtnBg: {
    width: 42,
    height: 9,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  foldArrow: {
    width: 7,
    height: 4,
    opacity: .4,
  },
  btnWrapper: {
    paddingHorizontal: gap,
    marginTop: 12,
  },
});
