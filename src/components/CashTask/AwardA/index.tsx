import { BetterImage, Touch } from '@xmly/rn-components';
import React, { useState } from 'react';
import { View, Animated } from 'react-native';
import StepAward from '../StepAward';
import { useAtomValue } from 'jotai';
import bannerThemeStyleAtom from '../store/theme/banner';
import { getStyles } from './style';
import AwardBox from '../AwardBox';
import { ActionType, bannerClickReport } from '../utils';

interface Props {
  hideFold?: boolean;
  title: string;
}

function CarveUpBanner(props: Props) {
  const { title, hideFold = false } = props;
  const themeStyles = useAtomValue(bannerThemeStyleAtom);
  const styles = getStyles();
  const [fold, setFold] = useState(!hideFold); // 同时展示奖励B时默认折叠

  function toggleFold() {
    setFold((fold) => !fold);
  }

  function onPressReport() {
    bannerClickReport({ btnText: '今日可领', action: ActionType.Video, moduleName: title });
  }

  return (
    <AwardBox
      title={hideFold ? '看视频领现金' : title}
      subtitle='断签会重新计算奖励哦'
    >
      <StepAward onPressReport={onPressReport} fold={fold} customThemeStyles={themeStyles.award} />
      <View style={styles.foldBtnWrapper}>
        <View style={styles.foldBtnIconWrapper}>
          {hideFold ? null :
            <>
              <BetterImage
                source={{ uri: 'https://imagev2.xmcdn.com/storages/0ffb-audiofreehighqps/1F/DC/GKwRIDoKx9s-AAAA_QMUeITi.png' }}
                imgHeight={9}
                imgWidth={42}
                style={styles.foldBtnBg}
              />
              <Animated.View style={{
                transform: [{ rotate: fold ? '0deg' : '180deg' }]
              }}>
                <BetterImage
                  source={{ uri: themeStyles.foldArrow }}
                  imgHeight={4}
                  imgWidth={7}
                  style={[styles.foldArrow]}
                />
              </Animated.View>
              <Touch onPress={toggleFold} style={styles.foldBtn} />
            </>
          }
        </View>
      </View>
    </AwardBox>
  );
}

export default React.memo(CarveUpBanner);