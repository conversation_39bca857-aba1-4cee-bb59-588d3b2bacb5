import React from 'react';
import RecordList from '../RecordList';
import { WithdrawRecord } from 'services/welfare/types';
import { useRecordList } from '../hooks/useRecordList';
import { TransactionType } from 'constants/ad';
export default function WithdrawRecords() {
  const { list, loading, onLoadMore, onRefresh } = useRecordList(TransactionType.WITHDRAW);

  return (
    <RecordList 
      data={list as WithdrawRecord[]}
      type={TransactionType.WITHDRAW}
      unit="元"
      loading={loading}
      onLoadMore={onLoadMore}
      onRefresh={onRefresh}
    />
  );
} 