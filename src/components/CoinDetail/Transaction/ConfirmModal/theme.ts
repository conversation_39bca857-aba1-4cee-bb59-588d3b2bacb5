import { atom } from 'jotai';
import { themeAtom } from 'atom/theme';

export const darkTheme = {
  contentBgColor: '#131313',
  descColor: '#DCDCDC',
  closeIconColor: '#8D8D91',
  cancelButtonBgColor: '#131313',
  cancelTextColor: '#8D8D91',
  borderColor: '#000000',
};

export const lightTheme = {
  contentBgColor: '#FFFFFF',
  descColor: '#393942',
  closeIconColor: '#666666',
  cancelButtonBgColor: '#FFFFFF',
  cancelTextColor: '#666666',
  borderColor: '#EEEEEE',
};

export const confirmModalThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default confirmModalThemeAtom; 