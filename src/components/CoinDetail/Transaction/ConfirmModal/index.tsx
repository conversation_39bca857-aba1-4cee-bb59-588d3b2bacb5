import React, { useEffect } from 'react';
import { View, Modal, Text } from 'react-native';
import { Touch } from '@xmly/rn-components';
import { getStyles } from './styles';
import { useAtomValue } from 'jotai';
import confirmModalThemeAtom from './theme';
import CloseIcon from 'componentsV2/common/CloseIcon';
import { BannerCard } from 'services/welfare/types';
import xmlog from 'utilsV2/xmlog';

interface ConfirmModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  card?: BannerCard;
}

export default function ConfirmModal({
  visible,
  onClose,
  onConfirm,
  card,
}: ConfirmModalProps) {
  const theme = useAtomValue(confirmModalThemeAtom);
  const styles = getStyles(theme);
  const dialogTitle = `是否使用${card?.coins}金币${card?.title}？`;
  const dialogType = card?.consumeType === 1 ? '金币换现金' : '金币换时长';
  const titleType = card?.consumeType === 1 ? '提现' : '畅听';

  function handleClose() {
    // 任务中心_收支兑换明细-金币换XX弹窗  点击事件
    xmlog.click(67690, '', { currPage: 'point_exchange', dialogType, dialogTitle, Item: '取消', titleType, from: '福利中心' });
    onClose();
  }

  function handleConfirm() {
    // 任务中心_收支兑换明细-金币换XX弹窗  点击事件
    xmlog.click(67690, '', { currPage: 'point_exchange', dialogType, dialogTitle, Item: '确认', titleType, from: '福利中心' });
    onConfirm();
  }

  useEffect(() => {
    if (card && visible) {
      // 任务中心_收支兑换明细-金币换XX弹窗  控件曝光
      xmlog.event(67691, 'slipPage', { currPage: 'point_exchange', dialogType, dialogTitle, titleType, from: '福利中心 ' });
    }
  }, [visible, card])

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <View style={styles.container}>
        <Touch style={styles.overlay} onPress={onClose} />
        <View style={styles.content}>
          <Touch style={[styles.closeButton]} onPress={onClose}>
            <CloseIcon size={12} color={theme.closeIconColor} />
          </Touch>
          <Text style={styles.desc}>{dialogTitle}</Text>
          <View style={styles.buttons}>
            <Touch style={styles.cancelButton} onPress={handleClose}>
              <Text style={styles.cancelText}>取消</Text>
            </Touch>
            <Touch style={styles.confirmButton} onPress={handleConfirm}>
              <Text style={styles.confirmText}>确认</Text>
            </Touch>
          </View>
        </View>
      </View>
    </Modal>
  );
} 