import { StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  content: {
    width: px(275),
    backgroundColor: theme.contentBgColor,
    borderRadius: px(10),
    paddingTop: px(32),
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: px(12),
    top: px(12),
    padding: px(8),
  },
  desc: {
    fontSize: px(16),
    fontWeight: '500',
    color: theme.descColor,
    marginBottom: px(24),
    fontFamily: 'XmlyNumber',
  },
  buttons: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  cancelButton: {
    flex: 1,
    height: px(45),
    backgroundColor: theme.cancelButtonBgColor,
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomLeftRadius: px(10),
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: theme.borderColor,
  },
  confirmButton: {
    flex: 1,
    height: px(45),
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
    borderBottomRightRadius: px(10),
  },
  cancelText: {
    fontSize: px(16),
    color: theme.cancelTextColor,
  },
  confirmText: {
    fontSize: px(16),
    fontWeight: '500',
    color: '#FFFFFF',
  },
}); 