import { StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginBottom: px(20),
  },
  card: {
    backgroundColor: theme.backgroundColor,
    borderRadius: px(2),
    padding: px(12),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: px(14),
    color: theme.titleColor,
    fontFamily: 'XmlyNumber',
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  value: {
    fontSize: px(10),
    color: theme.valueColor,
    fontFamily: 'XmlyNumber',
  },
  icon: {
    width: px(5),
    height: px(8),
    marginLeft: px(4),
  },
}); 