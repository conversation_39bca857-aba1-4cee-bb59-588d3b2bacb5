import React, { useEffect } from 'react';
import { useState } from 'react';
import { View, Text } from 'react-native';
import { BetterImage, Touch } from '@xmly/rn-components';
import { getStyles } from './styles';
import { useAtomValue, useSetAtom } from 'jotai';
import bannerThemeAtom from './theme';
import { coinInfoAtom, updateCoinDetailAtom } from '../atom';
import ConfirmModal from './ConfirmModal';
import { BannerCard } from 'services/welfare/types';
import { exchangeGoldCoin } from 'services/welfare';
import { Toast } from '@xmly/rn-sdk';
import { TransactionType } from 'constants/ad';
import { balanceAtom, updateWelfareAtom } from 'atom/welfare';
import xmlog from 'utilsV2/xmlog';

export default function Transaction() {
  const { bannerCards } = useAtomValue(coinInfoAtom);
  const theme = useAtomValue(bannerThemeAtom);
  const styles = getStyles(theme);
  const [selectedCard, setSelectedCard] = useState<BannerCard>();
  const [modalVisible, setModalVisible] = useState(false);
  const updateCoinDetail = useSetAtom(updateCoinDetailAtom);
  const { balance } = useAtomValue(coinInfoAtom);
  const updateWelfare = useSetAtom(updateWelfareAtom);

  const handleCardPress = (card: BannerCard) => {
    // 任务中心_收支兑换明细-金币换XX入口  点击事件
    xmlog.click(67692, '', { currPage: 'point_exchange', title: card.title, titleType: card.consumeType === 1 ? '畅听' : '提现', dialogTitle: card.title, from: '福利中心' });
    if (balance < card.coins) {
      Toast.info('金币不足');
      return;
    }
    setSelectedCard(card);
    setModalVisible(true);
  };

  const handleConfirm = async () => {
    if (!selectedCard) return;

    try {
      const response = await exchangeGoldCoin({
        consumeType: 1,
        coins: selectedCard.coins,
      });

      if (response?.data?.success) {
        Toast.info('兑换成功');
        updateCoinDetail({ type: TransactionType.EXPENSE, refresh: true });
        updateWelfare();
      } else if (response?.data?.toast) {
        Toast.info(response.data.toast);
      }
    } catch (error) {
      console.error('Exchange failed:', error);
      Toast.info('系统繁忙，请稍后再试');
    } finally {
      setModalVisible(false);
    }
  };

  useEffect(() => {
    bannerCards.forEach((card) => {
      // 任务中心_收支兑换明细-金币换XX入口  控件曝光
      xmlog.event(67693, 'slipPage', { currPage: 'point_exchange', title: card.title, titleType: card.consumeType === 1 ? '畅听' : '提现', dialogTitle: card.title, from: '福利中心' });
    });
  }, [bannerCards.length]);

  return (
    <>
      <View style={styles.container}>
        {bannerCards.map((card) => (
          <Touch
            key={card.title}
            style={styles.card}
            onPress={() => handleCardPress(card)}
          >
            <Text style={styles.title}>{card.title}</Text>
            <View style={styles.valueContainer}>
              <Text style={styles.value}>需{card.coins}金币兑换</Text>
              <BetterImage
                source={{ uri: theme.icon }}
                style={styles.icon}
                imgHeight={8}
                imgWidth={5}
              />
            </View>
          </Touch>
        ))}
      </View>
      <ConfirmModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onConfirm={handleConfirm}
        card={selectedCard}
      />
    </>
  );
} 