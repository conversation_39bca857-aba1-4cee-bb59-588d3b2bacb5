import React from 'react';
import { View, Text } from 'react-native';
import { BetterImage } from '@xmly/rn-components';
import { getStyles } from './styles';
import { useAtomValue } from 'jotai';
import { coinInfoAtom } from '../atom';
import coinsThemeAtom from './theme';

const coinsIcon = 'https://imagev2.xmcdn.com/storages/98cc-audiofreehighqps/15/20/GAqhQ6cLwRvnAAAS5QOMbJxy.png';

export default function Coins() {
  const theme = useAtomValue(coinsThemeAtom);
  const styles = getStyles(theme);
  const { balance } = useAtomValue(coinInfoAtom);

  return (
    <View style={styles.container}>
      <View style={styles.coinsWrapper}>
        <View style={styles.coinsIconWrapper}>
          <BetterImage
            source={{ uri: coinsIcon }}
            style={styles.coinsIcon}
            imgHeight={52}
            imgWidth={52}
          />
        </View>
        <Text style={styles.coins}>{balance}</Text>
      </View>
    </View>
  );
} 