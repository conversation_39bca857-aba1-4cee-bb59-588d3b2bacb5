import React from 'react';
import RecordList from '../RecordList';
import { TransactionType } from 'constants/ad';
import { useRecordList } from '../hooks/useRecordList';

export default function ExpenseRecords() {
  const { list, loading, onLoadMore, onRefresh } = useRecordList(TransactionType.EXPENSE);

  return (
    <RecordList 
      data={list}
      type={TransactionType.EXPENSE}
      loading={loading}
      onLoadMore={onLoadMore}
      onRefresh={onRefresh}
    />
  );
} 