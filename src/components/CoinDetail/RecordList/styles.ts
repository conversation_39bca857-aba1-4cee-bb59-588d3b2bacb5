import { Platform, StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: px(20),
  },
  info: {
    flex: 1,
  },
  value: {
    alignItems: 'flex-end',
  },
  coinContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    color: 'rgba(255, 68, 68, .5)',
    fontSize: px(11),
    lineHeight: px(15)
  },
  title: {
    fontSize: px(13),
    color: theme.titleColor,
    fontFamily: 'XmlyNumber',
    fontWeight: Platform.select({ ios: '500', android: 'bold' })
  },
  time: {
    fontSize: px(12),
    color: theme.timeColor,
    marginTop: px(4),
    fontFamily: 'XmlyNumber'
  },
  coins: {
    fontSize: px(16),
    color: theme.incomeColor,
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    fontFamily: 'XmlyNumber'
  },
  coinText: {
    fontSize: px(12),
    marginLeft: px(4),
  },
  empty: {
    alignItems: 'center',
    marginTop: px(121),
  },
  emptyText: {
    fontSize: px(14),
    color: '#B0B3B8',
  },
  emptyImage: {
    width: px(200),
    height: px(108),
    marginBottom: px(26),
  },
}); 