import { atom } from 'jotai';
import { themeAtom } from 'atom/theme';

export const darkTheme = {
  titleColor: '#FFFFFF',
  timeColor: '#DCDCDC',
  incomeColor: '#FF4B4B',
};

export const lightTheme = {
  titleColor: '#111111',
  timeColor: '#999999',
  incomeColor: '#FF4B4B',
};

export const recordListThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default recordListThemeAtom; 