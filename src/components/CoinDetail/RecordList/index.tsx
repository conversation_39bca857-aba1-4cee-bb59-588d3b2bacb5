import React from 'react';
import { View, FlatList, Text } from 'react-native';
import { BetterImage } from '@xmly/rn-components';
import { getStyles } from './styles';
import { useAtomValue } from 'jotai';
import recordListThemeAtom from './theme';
import { GoldCoinHistoryItem, CashFlowItem, BaseRecord, WithdrawRecord, WithdrawStatus } from 'services/welfare/types';
import { px } from 'utils/px';
import { TransactionType } from 'constants/ad';
interface RecordListProps {
  data: BaseRecord[];
  loading?: boolean;
  onLoadMore?: () => void;
  onRefresh?: () => void;
  type: TransactionType;
  unit?: string;
}

const emptyImage = 'https://imagev2.xmcdn.com/storages/a024-audiofreehighqps/60/A7/GKwRIW4Lt9e2AAA5hQOFlxIS.png';

export default function RecordList({
  data,
  loading,
  onLoadMore,
  onRefresh,
  type,
  unit = '金币',
}: RecordListProps) {
  const theme = useAtomValue(recordListThemeAtom);
  const styles = getStyles(theme);

  function WithdrawStatusText({ status }: { status: WithdrawStatus }) {
    const hintStatus = [WithdrawStatus.INIT, WithdrawStatus.PROCESSING, WithdrawStatus.FAILED];
    const processingStatus = [WithdrawStatus.INIT, WithdrawStatus.PROCESSING];
    if (hintStatus.includes(status)) {
      return <Text style={styles.statusText}>{processingStatus.includes(status) ? '提现中' : '提现失败'}</Text>;
    }
    return null;
  }

  const renderItem = ({ item }: { item: BaseRecord }) => (
    <View style={styles.item}>
      <View style={styles.info}>
        <Text style={styles.title}>{item.title}</Text>
        <Text style={styles.time}>{item.createTime}</Text>
      </View>
      <View style={styles.value}>
        <View style={styles.coinContainer}>
          {type === TransactionType.WITHDRAW ? (
            <Text style={[
              styles.coins,
            ]}>
              -{item.amount}
            </Text>
          ) : (
            <Text style={[
              styles.coins,
            ]}>
              {type === TransactionType.INCOME ? '+' : '-'}{item.amount}
            </Text>
          )}
          <Text style={[styles.coins, styles.coinText]}>{unit}</Text>
        </View>
        {type === TransactionType.WITHDRAW ? <View>
          <WithdrawStatusText status={(item as WithdrawRecord).status} />
        </View> : null}
      </View>
    </View>
  );

  return (
    <FlatList
      data={data}
      renderItem={renderItem}
      onEndReached={onLoadMore}
      onRefresh={onRefresh}
      refreshing={loading}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={
        <View style={styles.empty}>
          <BetterImage
            source={{ uri: emptyImage }}
            style={styles.emptyImage}
            imgHeight={styles.emptyImage.height}
            imgWidth={styles.emptyImage.width}
          />
          <Text style={styles.emptyText}>暂无数据</Text>
        </View>
      }
    />
  );
} 