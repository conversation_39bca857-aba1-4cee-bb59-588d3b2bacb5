import { StyleSheet, Dimensions } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: px(39),
  },
  tabs: {
    flexDirection: 'row',
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.borderColor,
    paddingLeft: 16,
  },
  tab: {
    alignItems: 'center',
    paddingVertical: px(12),
    marginRight: px(34),
  },
  bottomLine: {
    width: px(20),
    height: px(3),
    backgroundColor: theme.activeColor,
    opacity: 0,
    position: 'absolute',
    bottom: 0,
    left: px(6),
    right: 0,
  },
  activeBottomLine: {
    opacity: 1,
  },
  activeTab: {
  },
  tabText: {
    fontSize: px(16),
    color: theme.titleColor,
    opacity: 0.5,
  },
  activeTabText: {
    opacity: 1,
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: px(16),
  },
  icon: {
    width: px(5),
    height: px(8),
    marginLeft: px(4),
    transform: [{
      rotate: '90deg',
    }],
  },
  monthSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: px(8),
    borderRadius: px(4),
  },
  monthSelectorText: {
    fontSize: px(14),
    color: theme.titleColor,
    marginRight: px(4),
  },
  monthSelectorArrow: {
    width: px(12),
    height: px(12),
    tintColor: theme.titleColor,
  },
  monthPickerContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(63, 62, 62, 0.5)',
    justifyContent: 'flex-end',
  },
  monthPickerContent: {
    width: SCREEN_WIDTH,
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: px(16),
    borderTopRightRadius: px(16),
    paddingBottom: px(16),
    // borderStyle: 'dashed',
    // borderWidth: 1,
    // borderColor: '#666',
  },
  wheelPickerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: px(5),
    paddingBottom: px(16),
    // position: 'relative',
    height: px(173),

    // borderStyle: 'dashed',
    // borderWidth: 1,
    // borderColor: '#666',
  },
  selectedItemBackground: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: px(30),
    backgroundColor: '#F5F5F5',
    top: '50%',
    marginTop: px(-15),
    zIndex: -1,
  },
  yearWheelPicker: {
    width: px(85),
    height: px(150),
  },
  monthWheelPicker: {
    width: px(83),
    height: px(150),
  },
  yearItemTextStyle: {
    fontSize: px(18),
    // color: '#999999',
    textAlign: 'center',
    color: theme == darkTheme ? '#FFFFFF' : '#000000',
  },
  monthItemTextStyle: {
    fontSize: px(18),
    // color: '#999999',
    textAlign: 'center',
    color: theme == darkTheme ? '#FFFFFF' : '#000000',
  },
  yearOverlayItemStyle: {
    fontSize: px(22),
    fontWeight: 'bold',
    color: theme == darkTheme ? '#FFFFFF' : '#000000',
    textAlign: 'center',
  },
  monthOverlayItemStyle: {
    fontSize: px(22),
    fontWeight: 'bold',
    color: theme == darkTheme ? '#FFFFFF' : '#000000',
    textAlign: 'center',
  },
  confirmButton: {
    width: px(275),
    height: px(44),
    backgroundColor: '#FF4444',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: px(22),
    marginTop: px(32),
    marginBottom: px(50),
    alignSelf: 'center',
  },
  confirmButtonText: {
    color: '#FFFFFF',
    fontSize: px(16),
    fontWeight: 'bold',
  },
  handleBar: {
    width: px(40),
    height: px(4),
    backgroundColor: '#E8E8E8',
    borderRadius: px(2),
    alignSelf: 'center',
    marginTop: px(8),
    marginBottom: px(8),
  },
  handleBarContainer: {
    width: '100%',
    paddingVertical: px(8),
  },
}); 