import React from 'react';
import { View } from 'react-native';
import WheelPicker from '@quidone/react-native-wheel-picker';
import dayjs from 'dayjs';
import { getStyles } from '../styles';
interface MonthPickerModalProps {
  styles: ReturnType<typeof getStyles>;
  selectedYear: number;
  selectedMonth: number;
  onYearChange: (year: number) => void;
  onMonthChange: (month: number) => void;
}

export default function IOSMonthPickerModal({
  styles,
  selectedYear,
  selectedMonth,
  onYearChange,
  onMonthChange,
}: MonthPickerModalProps) {
  // 计算最近12个月
  const now = dayjs();
  const monthsList: { year: number; month: number }[] = [];
  for (let i = 0; i < 12; i++) {
    const date = now.subtract(i, 'month');
    monthsList.push({ year: date.year(), month: date.month() + 1 });
  }
  monthsList.reverse();

  // 年份和月份数据
  const years = Array.from(new Set(monthsList.map(item => item.year)));
  const getMonthsByYear = (year: number) =>
    monthsList.filter(item => item.year === year).map(item => item.month);

  return (
    <View style={{ flexDirection: 'row', justifyContent: 'center' }}>
      <WheelPicker
        style={styles.yearWheelPicker}
        data={years.map(year => ({ value: year, label: `${year}年年` }))}
        itemTextStyle={styles.yearItemTextStyle}
        // overlayItemStyle={styles.yearOverlayItemStyle as any}
        value={selectedYear}
        onValueChanged={({ item: { value } }) => onYearChange(value)}
      />
      <WheelPicker
        style={styles.monthWheelPicker}
        data={getMonthsByYear(selectedYear).map(month => ({ value: month, label: `${month}月` }))}
        itemTextStyle={styles.monthItemTextStyle}
        // overlayItemStyle={styles.monthOverlayItemStyle as any}
        value={selectedMonth}
        onValueChanged={({ item: { value } }) => onMonthChange(value)}
      />
    </View>
  );
}