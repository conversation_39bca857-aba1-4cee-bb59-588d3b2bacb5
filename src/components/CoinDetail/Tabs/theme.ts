import { atom } from 'jotai';
import { themeAtom } from 'atom/theme';

export const darkTheme = {
  titleColor: '#FFF',
  activeColor: '#FF4B4B',
  borderColor: 'rgba(255, 255, 255, 0.08)',
  icon: 'https://imagev2.xmcdn.com/storages/94a5-audiofreehighqps/02/B1/GAqh9sALtt1aAAAAzAOE4-Qc.png',
};

export const lightTheme = {
  titleColor: '#131313',
  activeColor: '#FF4B4B',
  borderColor: 'rgba(0, 0, 0, 0.08)',
  icon: 'https://imagev2.xmcdn.com/storages/7960-audiofreehighqps/39/DC/GKwRIDoLtt1aAAAA9QOE4-RN.png',
};

export const tabsThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default tabsThemeAtom; 