import React from 'react';
import { getStyles } from './styles';
import { BetterImage } from '@xmly/rn-components';
import { useAtomValue } from 'jotai';
import coinDetailThemeAtom from '../../../pages/CoinDetail/theme';

export default function TopBg() {
  const theme = useAtomValue(coinDetailThemeAtom);
  const styles = getStyles();
  return <BetterImage source={{ uri: theme.headerBg }} key={theme.headerBg} style={styles.container} />;
} 