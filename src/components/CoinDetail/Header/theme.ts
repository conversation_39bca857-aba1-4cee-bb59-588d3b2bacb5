import { themeAtom } from 'atom/theme';
import commonThemeAtom from 'components/CoinCenter/common/theme';
import { atom } from 'jotai';

export const darkTheme = {
  titleColor: '#FFFFFF',
};

export const lightTheme = {
  titleColor: '#131313',
};

export const headersThemeAtom = atom((get) => { 
  const theme = get(themeAtom);
  const commonTheme = get(commonThemeAtom);
  return {
    ...commonTheme,
    ...(theme === 'dark' ? darkTheme : lightTheme)
  };
})

export default headersThemeAtom;