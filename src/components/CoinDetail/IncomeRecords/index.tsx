import React from 'react';
import RecordList from '../RecordList';
import { TransactionType } from 'constants/ad';
import { useRecordList } from '../hooks/useRecordList';

export default function IncomeRecords() {
  // 获取收入记录状态
  const { list, loading, onLoadMore, onRefresh } = useRecordList(TransactionType.INCOME);
  
  return (
    <RecordList 
      data={list}
      type={TransactionType.INCOME}
      loading={loading}
      onLoadMore={onLoadMore}
      onRefresh={onRefresh}
    />
  );
}