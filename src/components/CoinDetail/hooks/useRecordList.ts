import { useAtomValue, useSet<PERSON>tom } from 'jotai';
import { coinDetail<PERSON>tom, updateDetailList<PERSON>tom } from '../atom';
import { TransactionType } from 'constants/ad';

export function useRecordList(type: TransactionType) {
  const { [type]: records } = useAtomValue(coinDetailAtom);
  const updateDetailList = useSetAtom(updateDetailListAtom);

  const handleLoadMore = () => {
    if (!records.loading && records.hasMore) {
      updateDetailList({
        type,
        refresh: false,
        month: records.month
      });
    }
  };

  const handleRefresh = () => {
    updateDetailList({
      type,
      refresh: true,
      month: records.month
    });
  };

  return {
    list: records.list,
    loading: records.loading,
    onLoadMore: handleLoadMore,
    onRefresh: undefined
  };
}
