import React, { useEffect, useState } from "react";
import { View, Animated } from "react-native";
import getStyles from "./style";
//@ts-ignore
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics';
import log from "utils/log";

interface Props {
  renderItem: (item: any, index?: number) => React.ReactNode;
  list: any[];
  itemHeight: number;
}

export default function NoticeBar(props: Props) {
  const styles = getStyles();
  const { list: noticeList, renderItem, itemHeight } = props;
  const list = Array.isArray(noticeList) && noticeList.length > 0 ? [...noticeList, noticeList[0]] : [];
  const slideAnim = new Animated.Value(0);
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (list?.length > 0 && visible) {
      const slideAnimations = list.map((item, index) => {
        return Animated.timing(slideAnim, {
          toValue: -itemHeight * index,
          duration: 300,
          useNativeDriver: true,
          isInteraction: false,
        });
      });
      const animations = Animated.loop(Animated.sequence([Animated.stagger(3000, slideAnimations), Animated.timing(slideAnim, {
        toValue: 0,
        duration: 0,
        useNativeDriver: true,
        isInteraction: false,
      })]));
      animations.start();

      return () => {
        animations.stop();
      }
    }
    return () => { }
  }, [list, visible])

  function onShow() {
    setVisible(true)
  }

  function onHide() {
    setVisible(false)
  }

  return <ScrollAnalyticComp
    key='notice-bar'
    allowRepeatExpose
    onShow={onShow}
    onHide={onHide}
    scrollEventThrottle={60}
  >
    <View style={[styles.container, { height: itemHeight }]}>
      <Animated.View style={[styles.list, {
        transform: [{ translateY: slideAnim }],
      }]}>
        {list.map((item, index) => {
          return renderItem(item, index);
        })}
      </Animated.View>
    </View>
  </ScrollAnalyticComp>
}