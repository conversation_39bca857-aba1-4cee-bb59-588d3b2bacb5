import React from 'react';
import { Text, View, NativeModules } from 'react-native';
import { Touch } from '@xmly/rn-components';
import { getStyles } from './style';
import { useAtomValue } from 'jotai';
import ruleThemeAtom from './theme';
import { Page } from '@xmly/rn-sdk';
import { coinCenterRuleUrl, userExperienceSurveyUrl } from 'constantsV2';
import getUrlToOpen from 'utilsV2/getUrlToOpen';

export default function Rule() {
  const theme = useAtomValue(ruleThemeAtom);
  const styles = getStyles(theme);

    const handleRulesPress = () => {
      NativeModules.Page.start(getUrlToOpen(coinCenterRuleUrl, {newIting: true}));
    };

    const handleQuestionnairePress = () => {
      NativeModules.Page.start(getUrlToOpen(userExperienceSurveyUrl, {newIting: true}));
    };

  return (
      <View style={[styles.container]}>
        <Touch onPress={handleQuestionnairePress}>
          <Text style={styles.text}>满意度调研</Text>
        </Touch>
        <View style={styles.line} />
        <Touch onPress={handleRulesPress}>
          <Text style={styles.text}>活动规则</Text>
        </Touch>
    </View>
  );
} 