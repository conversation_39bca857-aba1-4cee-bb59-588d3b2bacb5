import { StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: px(30),
    paddingBottom: px(56),
  },
  line: {
    width: 1,
    height: 14,
    backgroundColor: '#E5E5E5',
    marginHorizontal: 12,
  },
  text: {
    fontSize: px(15),
    color: theme.textColor,
    textAlign: 'center',
  },
  pureNumber: {
    fontFamily: 'XmlyNumber',
    fontSize: px(24),
  },
  number: {
    fontFamily: 'XmlyNumber',
    fontSize: px(24),
  },
  textNumber: {
    fontFamily: 'XmlyNumber',
    fontSize: px(24),
  },
  numberText: {
    fontFamily: 'XmlyNumber',
    fontSize: px(24),
  },
  string: {
    fontFamily: 'PingFangSC',
    fontSize: px(24),
  },
}); 