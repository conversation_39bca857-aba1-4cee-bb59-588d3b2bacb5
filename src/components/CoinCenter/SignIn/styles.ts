import { Platform, StyleSheet } from "react-native";
import { darkTheme } from "./theme";
import { px } from "utils/px";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  signInProgress: {
    // paddingTop: px(26),
    backgroundColor: theme.contentBgColor,
    borderBottomLeftRadius: px(2),
    borderBottomRightRadius: px(2),
    paddingBottom: px(20),
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: px(12),
    paddingLeft: px(16),
    paddingTop: px(26),
  },
  progressLine: {
    width: '100%',
    height: px(2),
    backgroundColor: theme.progressLineColor,
    position: 'absolute',
    top: px(31),
    left: px(52)
  },
  progressTitle: {
    fontSize: px(14),
    color: theme.titleColor,
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    fontFamily: 'XmlyNumber',
  },
  icon: {
    width: px(60),
    height: px(60),
  },
  todayAward: {
    width: px(60),
    height: px(60),
    alignItems: 'center',
    justifyContent: 'center',
  },
  todayIcon: {
    position: 'absolute'
  },
  detailBtn: {
    padding: px(4),
  },
  progressDays: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  progressDaysScroll: {
    paddingLeft: px(8),
    paddingRight: px(0),
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexGrow: 1,
  },
  dayItem: {
    alignItems: 'center',
    width: px(60),
    marginRight: px(6),
  },
  dayText: {
    fontSize: px(17),
    lineHeight: px(21),
    color: '#C9AF67',
    fontFamily: 'XmlyNumber',
  },
  todayText: {
    color: theme.todayTextColor,
    fontWeight: Platform.select({ ios: '600', android: 'bold' }),
  },
  dateText: {
    fontSize: px(12),
    color: theme.dateTextColor,
    fontFamily: 'XmlyNumber',
  },
  todayItem: {
    // 今天的特殊样式
  },
  shadow: {
    position: 'absolute',
    top: 0,
    right: px(60 + 6),
    width: px(36),
    height: px(76),
    zIndex: 1000,
  },
  lotteryItem: {
    width: px(60),
    // paddingRight: px(8),
  },
  signInButtonWrapper: {
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: px(27),
    marginBottom: px(8),
  },
  signInButton: {
    width: px(227),
    height: px(34),
    backgroundColor: '#FF4444',
    borderRadius: px(58),
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 'auto',
  },
  signInText: {
    fontSize: px(13),
    color: '#FFF',
    fontWeight: '600',
    fontFamily: 'XmlyNumber',
  },
  bottomText: {
    fontSize: px(12),
    color: theme.bottomTextColor,
    textAlign: 'center',
    fontFamily: 'XmlyNumber',
  },
  signInTextButton: {
    marginTop: px(7),
  },
  receivedIcon: {
    width: px(16),
    height: px(16),
    position: 'absolute',
    top: px(8),
    right: px(8),
  },
  giftIcon: {
    width: px(54),
    height: px(60),
  }
});
