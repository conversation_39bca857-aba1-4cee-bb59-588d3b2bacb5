import React, { useEffect, useRef, useState, useCallback } from 'react';
import { View, Modal, Animated, Easing } from 'react-native';
import { Touch } from '@xmly/rn-components';
import AlphaVideo from 'components/common/AlphaVideo';
import { useVideoResources } from 'hooks/useVideoResources';
import { getStyles } from './styles';
import CloseIcon from 'componentsV2/common/CloseIcon';
import RewardModalContent from 'components/CoinCenter/RewardModalContent';
import ConfettiAnimation from 'components/CoinCenter/common/ConfettiAnimation';
import { awardBgStartVideo, awardBgLoopVideo } from 'constants/assets';
import CloseButton from 'components/CoinCenter/common/CloseButton';

const customEasing = Easing.bezier(0.66, 0, 0.34, 1);

interface LotteryModalProps {
  visible: boolean;
  onClose: () => void;
  coins?: number;
}

// 视频资源链接 - 导出以便其他组件可以预加载
export const VIDEOS = {
  LOTTERY: 'https://aod.cos.tx.xmcdn.com/storages/518f-audiofreehighqps/8F/70/GKwRIaILzkBnAA9ncQOUatQa.mp4',
  BACKGROUND_START: awardBgStartVideo,
  BACKGROUND_LOOP: awardBgLoopVideo,
};

// 背景开始视频预计时长（毫秒）
const BACKGROUND_START_DURATION = 2000; // 根据实际视频长度调整
// 预加载背景循环的提前时间（毫秒）
const PRELOAD_LOOP_TIME = 100;

enum VideoPlayState {
  LOTTERY,
  BACKGROUND_START_AND_CONFETTI,
  BACKGROUND_LOOP,
  FINISHED,
}

// 导出预加载资源的hook
export const useLotteryVideos = () => {
  return useVideoResources(VIDEOS, { autoLoad: false });
};

const LotteryModal: React.FC<LotteryModalProps> = ({ visible, onClose, coins = 0 }) => {
  const styles = getStyles();
  const [playState, setPlayState] = useState<VideoPlayState>(VideoPlayState.LOTTERY);
  const [showContent, setShowContent] = useState(false);
  const [backgroundLoopVisible, setBackgroundLoopVisible] = useState(false);

  // 添加一个定时器引用
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  // 背景视频开始加载的时间
  const bgStartLoadTimeRef = useRef<number>(0);

  // 创建动画值
  const scaleAnim = useRef(new Animated.Value(0)).current;

  // 使用 useVideoResources hook 加载视频资源
  const { paths } = useVideoResources(VIDEOS, {
    autoLoad: visible, // 当弹窗显示时自动加载资源
  });

  // 背景开始视频加载完成回调
  const handleBgStartLoad = useCallback(() => {
    // 记录背景视频开始加载的时间
    bgStartLoadTimeRef.current = Date.now();

    // 计算预加载时间
    const preloadDelay = Math.max(BACKGROUND_START_DURATION - PRELOAD_LOOP_TIME, 0);

    // 清除可能存在的旧定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 设置新的定时器，提前预加载BACKGROUND_LOOP
    timerRef.current = setTimeout(() => {
      setBackgroundLoopVisible(true);
    }, preloadDelay);
  }, []);

  // 背景视频播放完成回调
  const handleBgVideoComplete = useCallback(() => {
    // 如果还没有显示背景循环视频，立即显示
    if (!backgroundLoopVisible) {
      setBackgroundLoopVisible(true);
    }

    // 清除定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, [backgroundLoopVisible]);

  // 初始抽奖动画播放完成回调
  const handleLotteryComplete = useCallback(() => {
    // LOTTERY结束后进入同时播放背景和彩带的状态
    setPlayState(VideoPlayState.BACKGROUND_START_AND_CONFETTI);
    setShowContent(true);

    // 启动缩放动画
    Animated.timing(scaleAnim, {
      toValue: 1,
      duration: 500,
      easing: customEasing,
      useNativeDriver: true
    }).start();
  }, [scaleAnim]);

  // 当弹窗关闭时重置状态和清除定时器
  useEffect(() => {
    if (!visible) {
      setPlayState(VideoPlayState.LOTTERY);
      setShowContent(false);
      scaleAnim.setValue(0);
      setBackgroundLoopVisible(false);
      bgStartLoadTimeRef.current = 0;

      // 清除定时器
      if (timerRef.current) {
        clearTimeout(timerRef.current);
        timerRef.current = null;
      }
    }
  }, [visible, scaleAnim]);

  // 组件卸载时清除定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // 渲染视频内容
  const renderVideo = () => {
    switch (playState) {
      case VideoPlayState.LOTTERY:
        return paths.LOTTERY ? (
          <AlphaVideo
            source={paths.LOTTERY}
            style={styles.fullVideo}
            repeat={false}
            muted={false}
            onComplete={handleLotteryComplete}
          />
        ) : null;

      case VideoPlayState.BACKGROUND_START_AND_CONFETTI:
        return (
          <>
            {/* 背景开始视频 */}
            {paths.BACKGROUND_START && (
              <AlphaVideo
                source={paths.BACKGROUND_START}
                style={[
                  styles.fullVideo,
                  { opacity: backgroundLoopVisible ? 0 : 1 }
                ]}
                repeat={false}
                onLoad={handleBgStartLoad}
                onComplete={handleBgVideoComplete}
              />
            )}

            {/* 背景循环视频 - 提前加载但开始时透明度为0，需要时再显示 */}
            {paths.BACKGROUND_LOOP && (
              <AlphaVideo
                source={paths.BACKGROUND_LOOP}
                style={[
                  styles.fullVideo,
                  { opacity: backgroundLoopVisible ? 1 : 0 }
                ]}
                repeat={true}
              />
            )}

            <ConfettiAnimation
              autoPlay
              loop={false}
              onComplete={() => console.log('彩带动画播放完成')}
            />
          </>
        );

      default:
        return null;
    }
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <View style={styles.container}>
        <View style={styles.modalContainer}>
          {/* 关闭按钮 */}
          <CloseButton onPress={onClose} />

          {/* 渲染视频内容 */}
          {renderVideo()}

          {/* 显示内容 */}
          {showContent ? (
            <RewardModalContent
              coins={coins}
              onPress={onClose}
              scaleAnim={scaleAnim}
              btnText='开心收下'
            />
          ) : null}
        </View>
      </View>
    </Modal>
  );
};

export default LotteryModal; 