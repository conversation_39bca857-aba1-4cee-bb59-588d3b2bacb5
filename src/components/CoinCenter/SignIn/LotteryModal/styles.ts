import { StyleSheet } from "react-native";
import { px } from "utils/px";

export const getStyles = () => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    ...StyleSheet.absoluteFillObject,
  },
  modalContainer: {
    // 完全覆盖整个屏幕
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    right: px(16),
    top: px(44),
    padding: px(4),
    width: px(32),
    height: px(32),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 100,
  },
  closeIcon: {
    width: px(24),
    height: px(24),
  },
  fullVideo: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
  }
});