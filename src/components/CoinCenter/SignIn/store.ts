import { atom } from 'jotai';
import { querySignInInfo, SignInStatus } from 'services/welfare';

export interface SignInTaskInfo {
  responseId: number;
  title: string;
  enableResignIn: boolean;
  enableWakeUp: boolean;
  awardInfo: {
    day: number;
    today: boolean;
    status: SignInStatus;
    award: number;
    upgradeAward: number;
    alreadyAward?: number;
    hasLottery?: boolean;
  }[];
}

export const signInTaskAtom = atom<SignInTaskInfo | null>(null);

export const writeSignInTaskAtom = atom(
  null,
  async (get, set) => {
    try {
      const response = await querySignInInfo();
      console.log('querySignInInfo response:::', JSON.stringify(response));
      if (response?.ret === 0 && response.data.success) {
        set(signInTaskAtom, {
          responseId: response.responseId!,
          title: response.data.title,
          enableResignIn: response.data.enableResignIn,
          enableWakeUp: response.data.enableWakeUp?? true,
          awardInfo: response.data.awardInfo,
        });
      }
    } catch (error) {
      console.error('Failed to fetch sign in task:', error);
    }
  }
);

// export const updateSignInTaskAtom = atom(
//   null,
//   (get, set, dayIndex: number) => {
//     const currentTask = get(signInTaskAtom);
//     if (!currentTask) return;

//     const updatedAwardInfo = [...currentTask.awardInfo];
//     if (updatedAwardInfo[dayIndex]) {
//       updatedAwardInfo[dayIndex] = {
//         ...updatedAwardInfo[dayIndex],
//         status: SignInStatus.COMPLETED,
//         alreadyAward: updatedAwardInfo[dayIndex].upgradeAward,
//       };
//     }

//     set(signInTaskAtom, {
//       ...currentTask,
//       awardInfo: updatedAwardInfo,
//     });
//   }
// ); 