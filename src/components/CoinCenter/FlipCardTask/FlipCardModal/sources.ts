import { ResourcePriority } from "atom/videoResources";
import { awardBgStartVideo, awardBgLoopVideo } from "constants/assets";
import { Dimensions, Platform } from "react-native";

// const dpr = Dimensions.get('window').scale;

export const videoSources = {
  awardBgStart: awardBgStartVideo,
  awardBg: awardBgLoopVideo,
  flipCard: {
    // url: dpr >= 3 ? 'https://aod.cos.tx.xmcdn.com/storages/fc69-audiofreehighqps/EA/40/GAqhqKwL3msAAAS8ZAOd28RL.mp4' : 'https://aod.cos.tx.xmcdn.com/storages/ca7c-audiofreehighqps/AE/79/GAqh4zILzX4RAAHeVgOUBd7a.mp4',
    url: Platform.select({
      ios: 'https://aod.cos.tx.xmcdn.com/storages/fc69-audiofreehighqps/EA/40/GAqhqKwL3msAAAS8ZAOd28RL.mp4',
      android: 'https://aod.cos.tx.xmcdn.com/storages/ca7c-audiofreehighqps/AE/79/GAqh4zILzX4RAAHeVgOUBd7a.mp4',
    }) || '',
    priority: ResourcePriority.HIGH
  },
}
