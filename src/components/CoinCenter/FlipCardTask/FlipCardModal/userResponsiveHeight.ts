import { useCallback } from "react";
import { Dimensions } from "react-native";

interface UseResponsiveHeightProps {
  width?: number;
  aspectRatio?: number;
  maxWidth?: number;
}

export default function useResponsiveHeight({
  width,
  aspectRatio,
  maxWidth
}: UseResponsiveHeightProps) {
  // 基准设计高度
  const DESIGN_HEIGHT = 827;
  const videoWidth = Math.min(width || Dimensions.get('window').width, maxWidth || 600);
  const videoAspectRatio = aspectRatio || 544 / 1200;
  const videoHeight = videoWidth / videoAspectRatio;

  const pv = useCallback((value: number) => {
    return value * (videoHeight / DESIGN_HEIGHT);
  }, [videoHeight, DESIGN_HEIGHT]);

  return { pv, videoHeight, videoWidth };
}