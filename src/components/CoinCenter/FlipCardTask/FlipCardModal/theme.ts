import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  contentBgColor: '#131313',
  titleColor: '#FFFFFF',
  descColor: '#8D8D91',
  buttonBgColor: '#FF4B4B',
  buttonTextColor: '#FFFFFF',
  retryButtonTextColor: '#FFFFFF',
  closeIconColor: '#8D8D91',
}

const lightTheme = {
  contentBgColor: '#FFFFFF',
  titleColor: '#131313',
  descColor: '#999999',
  buttonBgColor: '#FF4B4B',
  buttonTextColor: '#FFFFFF',
  retryButtonTextColor: '#131313',
  closeIconColor: '#666666',
}

const flipCardModalThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default flipCardModalThemeAtom; 