import { atom } from 'jotai';
import { queryFlipCardInfo, FlipCardStatus } from 'services/welfare';

export interface FlipCardTaskInfo {
  responseId: number;
  status: FlipCardStatus;
  title: string;
  subTitle: string;
  currentCoins: number;
}

export const flipCardTaskAtom = atom<FlipCardTaskInfo | null>(null);

export const writeFlipCardTaskAtom = atom(
  null,
  async (get, set) => {
    try {
      const response = await queryFlipCardInfo();
      if (response?.ret === 0 && response.data.success) {
        set(flipCardTaskAtom, {
          responseId: response.responseId!,
          status: response.data.status,
          title: response.data.title,
          subTitle: response.data.subTitle,
          currentCoins: response.data.currentCoins
        });
      }
    } catch (error) {
      console.error('Failed to fetch flip card task:', error);
    }
  }
);

export const updateFlipCardTaskAtom = atom(
  null,
  (get, set, currentCoins: number) => {
    const currentTask = get(flipCardTask<PERSON>tom);
    if (!currentTask) return;

    set(flipCardTask<PERSON>tom, {
      ...currentTask,
      currentCoins,
      status: currentTask.status === FlipCardStatus.COMPLETED 
        ? FlipCardStatus.COMPLETED 
        : FlipCardStatus.AVAILABLE
    });
  }
); 