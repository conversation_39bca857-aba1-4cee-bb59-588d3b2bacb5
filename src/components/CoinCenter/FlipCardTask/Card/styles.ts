import { StyleSheet } from "react-native";
import { px } from "utils/px";

export const getStyles = () => StyleSheet.create({
  titleIcon: {
    width: px(41),
    height: px(13),
  },
  bgIcon: {
    width: px(71),
    height: px(98),
    position: 'absolute',
    top: 0,
    left: 0,
  },
  questionIcon: {
    width: px(61),
    height: px(61),
    marginTop: px(7),
  },
  card: {
    width: px(71),
    height: px(98),
    alignItems: 'center',
  },
  cardAmount: {
    fontSize: px(52),
    lineHeight: px(61),
    color: '#D95757',
    fontWeight: 'bold',
    fontFamily: 'XmlyNumber',
    marginTop: px(7),
  }
}); 