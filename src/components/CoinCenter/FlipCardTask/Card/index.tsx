import React from "react";
import { View, Text, Image, ViewStyle, StyleProp } from "react-native";
import { BetterImage } from "@xmly/rn-components";
import FlipCard from "componentsV2/FlipCard";
import { getStyles } from "./styles";

interface CardProps {
  titleIcon: string;
  amount?: number;
  fire: boolean;
  cardStyle?: StyleProp<ViewStyle>;
}

const bgIcon = 'https://imagev2.xmcdn.com/storages/3ecd-audiofreehighqps/7B/AC/GAqh4zILsHO-AAAEMAOBpMTH.png';
const questionIcon = 'https://imagev2.xmcdn.com/storages/5884-audiofreehighqps/D6/C3/GKwRIDoLsHO-AAAHWQOBpMT2.png';

export default function Card({ titleIcon, amount, fire = false, cardStyle }: CardProps) {
  const styles = getStyles();

  const frontSlot = (
    <View style={[styles.card, cardStyle]}>
      <Image
        source={{ uri: bgIcon }}
        key={bgIcon}
        style={styles.bgIcon}
      />
      <BetterImage
        source={{ uri: questionIcon }}
        key={questionIcon}
        imgHeight={61}
        imgWidth={61}
        style={styles.questionIcon}
      />
      <BetterImage
        source={{ uri: titleIcon }}
        key={titleIcon}
        imgHeight={13}
        imgWidth={41}
        style={styles.titleIcon}
      />
    </View>
  );

  const backSlot = (
    <View style={[styles.card, cardStyle]}>
      <Image
        source={{ uri: bgIcon }}
        key={bgIcon}
        style={styles.bgIcon}
      />
      {amount ? (
        <Text style={styles.cardAmount}>{amount}</Text>
      ) : (
        <BetterImage
          source={{ uri: questionIcon }}
          key={questionIcon}
          imgHeight={61}
          imgWidth={61}
          style={styles.questionIcon}
        />
      )}
      <BetterImage
        source={{ uri: titleIcon }}
        key={titleIcon}
        imgHeight={13}
        imgWidth={41}
        style={styles.titleIcon}
      />
    </View>
  );

  return (
    <FlipCard
      frontSlot={frontSlot}
      backSlot={backSlot}
      duration={600}
      fire={fire}
    />
  );
}