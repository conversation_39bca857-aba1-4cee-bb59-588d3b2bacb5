import React, { useEffect, useState, useCallback, useRef } from "react";
import { View, Text, LayoutChangeEvent } from "react-native";
import { BetterImage, Touch } from "@xmly/rn-components";
import { getStyles } from "./styles";
import Title from "../common/Title";
import ModuleCard from "../common/ModuleCard";
import Subtitle from "../common/Subtitle";
import TaskButton from "../common/TaskButton";
import FlipCardModal, { useFlipCardVideos } from "./FlipCardModal";
import watchAd from "utils/watchAd";
import { FallbackReqType } from "constants/ad";
import { flipCard, FlipCardStatus } from "services/welfare";
import { AD_POSITION, AD_SOURCE, RewardType } from "constants/ad";
import Card from "./Card";
import { flipCardTaskAtom, writeFlipCardTaskAtom } from "./store";
import { useAtom, useAtomValue, useSet<PERSON>tom } from "jotai";
import { Toast } from "@xmly/rn-sdk";
import customReportError from "utilsV2/customReportError";
import useRewardGoldCoin from "hooks/useRewardGoldCoin";
import flipCardTaskThemeAtom from './theme';
import { ScrollAnalyticComp } from "@xmly/react-native-page-analytics";
import xmlog from "utilsV2/xmlog";
import { px } from "utils/px";
import log from "utils/log";

const detailIcon = 'https://imagev2.xmcdn.com/storages/59c4-audiofreehighqps/9C/C4/GKwRIMALs_CXAAAAkwODRqUc.png';

const thousandIcon = 'https://imagev2.xmcdn.com/storages/5305-audiofreehighqps/4E/4F/GAqhF9kLsHO9AAADZQOBpMRl.png';
const hundredIcon = 'https://imagev2.xmcdn.com/storages/3c46-audiofreehighqps/F1/2F/GKwRIW4LsHO9AAADZgOBpMQy.png';
const tenIcon = 'https://imagev2.xmcdn.com/storages/0e2a-audiofreehighqps/9F/AB/GAqhntALsHO9AAADUwOBpMSI.png';
const unitIcon = 'https://imagev2.xmcdn.com/storages/4415-audiofreehighqps/4D/3A/GKwRIRwLsHO8AAADwwOBpMQC.png';

const cardList = [
  hundredIcon,
  tenIcon,
  unitIcon,
];

const positionText = ['个', '十', '百'];
// const positionText = ['个', '十', '百'];

export default function FlipCardTask() {
  const theme = useAtomValue(flipCardTaskThemeAtom);
  const styles = getStyles(theme);
  const [modalVisible, setModalVisible] = useState(false);
  const [cardsNumber, setCardsNumber] = useState<string[]>(['0', '0', '0']);
  const [currentIndex, setCurrentIndex] = useState(1); // 当前翻转的位置 1-个位 2-十位 3-百位
  const [flipCardTaskInfo, setFlipCardTaskInfo] = useAtom(flipCardTaskAtom);
  const queryFlipCardTaskInfo = useSetAtom(writeFlipCardTaskAtom);
  const { title = '', subTitle = '', currentCoins = 0, status } = flipCardTaskInfo || {};
  const reward = useRewardGoldCoin();
  const flipCompleted = currentCoins > 100;
  const rewardBtnText = '直接领取当前奖励';
  const taskBtnText = flipCompleted ? '直接领取' : currentCoins > 0 ? '继续翻卡' : '去翻卡';
  const [cardScale, setCardScale] = useState(1);
  const [flipping, setFlipping] = useState(false);

  // 获取视频资源加载函数的引用
  const videoResourcesRef = useRef(useFlipCardVideos());

  const handleFlip = useCallback(async (retry = false) => {
    const flipPosition = currentCoins === 0 ? 1 : currentCoins.toString().length + 1;
    if (flipCompleted && !retry) {
      return handleReward();
    }
    const res = await watchAd({
      positionName: AD_POSITION.positionName,
      slotId: AD_POSITION.slotId,
      sourceName: AD_SOURCE.FLIP,
    });
    const position = retry === true ? flipPosition - 1 : flipPosition;

    const params = {
      position,
      adId: res.adId,
      adResponseId: res.adResponseId,
      encryptType: res.encryptType,
      ecpm: res.ecpm,
      fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL,
    }
    if (res.success) {
      try {
        const result = await flipCard(params);
        if (result?.data?.curCoins) {
          // updateFlipCardTaskInfo(result?.data?.curCoins);
          // setCurrentIndex(prev => Math.min(prev + 1, 4));
          // flipPosition.current = result?.data?.curCoins.toString().length;
          setModalVisible(true);
          queryFlipCardTaskInfo();
        } else {
          Toast.fail(result?.data?.toast || result?.data?.msg || '翻卡失败，请稍后再试');
          customReportError({ source: 'flipCard_result', error: result });
          customReportError({ source: 'flipCard_params', error: params });
        }
      } catch (error) {
        console.error('Failed to flip card:', error);
        Toast.fail('翻卡失败，请稍后再试');
        customReportError({ source: 'flipCard_error', error });
        customReportError({ source: 'flipCard_params', error: params });
      }
    }
  }, [currentIndex, currentCoins, flipCompleted, handleReward]);

  const handleFlipNext = useCallback(async () => {
    setModalVisible(false);
    if (currentIndex < 3) {
      setCardsNumber(currentCoins.toString().padStart(3, '0').split(''));
      setFlipping(false);
      await handleFlip();
    } else {
      setFlipping(false);
      setCardsNumber(currentCoins.toString().padStart(3, '0').split(''));
      handleReward();
    }
  }, [currentIndex, currentCoins, handleFlip]);

  const handleRetry = useCallback(async () => {
    setModalVisible(false);
    setFlipping(true);
    await handleFlip(true);
  }, [currentCoins, handleFlip]);

  function closeModal() {
    setFlipping(false);
    setModalVisible(false);
    queryFlipCardTaskInfo();
  }

  async function handleReward() {
    await reward({
      rewardType: RewardType.FLIP,
      coins: currentCoins,
      sourceName: AD_SOURCE.FLIP,
      fallbackReq: FallbackReqType.NORMAL,
    });
    queryFlipCardTaskInfo();
  }

  function textBtnOnClick() {
    setFlipping(true);
    clickReport(rewardBtnText, positionText[currentIndex - 1]);
    handleReward();
  }

  function taskBtnOnClick() {
    clickReport(taskBtnText, currentCoins === 0 ? '' : positionText[currentIndex - 1]);
    handleFlip();
  }

  useEffect(() => {
    queryFlipCardTaskInfo();
    return () => {
      setFlipCardTaskInfo(null);
    }
  }, [])

  useEffect(() => {
    if (currentCoins > 0 && !modalVisible && !flipping) {
      setCardsNumber(currentCoins.toString().padStart(3, '0').split(''));
      setCurrentIndex(currentCoins.toString().length);
    }
  }, [currentCoins, modalVisible, flipping]);

  function onShow() {
    // 福利中心-翻卡模块  控件曝光
    xmlog.event(67698, 'slipPage', { currPage: 'welfareCenter' });

    // 在组件显示时预加载视频资源
    if (videoResourcesRef.current) {
      videoResourcesRef.current.load();
    }
  }

  function clickReport(btnText: string, status: string) {
    // 福利中心-翻卡模块  点击事件
    xmlog.click(67697, 'FlipCardTask', { currPage: 'welfareCenter', Item: btnText, status: status });
  }

  function handleCardListLayout(event: LayoutChangeEvent) {
    const gap = px(9);
    const cardWidth = px(71);
    const paddingHorizontal = px(16)
    const listPadding = paddingHorizontal * 2 + gap * 2;
    const cardListWidth = cardWidth * 3 + listPadding;

    if (event.nativeEvent.layout.width < cardListWidth - px(4)) {
      setCardScale((event.nativeEvent.layout.width - listPadding) / 3 / cardWidth);
    }
  }

  return (
    <ScrollAnalyticComp
      itemKey={'FlipCardTask'}
      onShow={onShow}
    >
      <ModuleCard style={styles.container}>
        <View style={styles.header}>
          <View>
            <Title title={title} />
            <View style={styles.subtitle}>
              <Subtitle text={subTitle} />
              {status === FlipCardStatus.AVAILABLE ?
                <View style={styles.textButtonWrapper}>
                  <Touch style={styles.textButton} onPress={textBtnOnClick}>
                    <Text style={styles.textButtonTitle}>{rewardBtnText}</Text>
                    <BetterImage
                      source={{ uri: detailIcon }}
                      key={detailIcon}
                      imgHeight={8}
                      imgWidth={5}
                      style={styles.detailIcon}
                    />
                  </Touch>
                </View> : null}
            </View>
          </View>
          <View style={cardScale < 1 ? {
            position: 'absolute',
            right: px(16),
            top: -px(4),
          } : null}>
            {status === FlipCardStatus.RECEIVED ?
              <Text style={styles.disabledText}>明日再来</Text>
              :
              <TaskButton text={taskBtnText} onPress={taskBtnOnClick} />
            }
          </View>
        </View>
        <View style={[styles.cardList, cardScale < 1 ? { justifyContent: 'space-evenly' } : null]} onLayout={handleCardListLayout}>
          {cardList.map((titleIcon, index) => {
            return (
              <Card
                key={index}
                titleIcon={titleIcon}
                amount={Number(cardsNumber[index])}
                fire={index >= 3 - currentIndex && currentCoins > 0 && !flipping}
                cardStyle={{ transform: [{ scale: cardScale }] }}
              />
            )
          })}
        </View>
      </ModuleCard>
      <FlipCardModal
        visible={modalVisible}
        onClose={closeModal}
        onFlipNext={handleFlipNext}
        onRetry={handleRetry}
        currentAmount={currentCoins}
      />
    </ScrollAnalyticComp>
  );
} 