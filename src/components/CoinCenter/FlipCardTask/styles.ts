import { StyleSheet } from "react-native";
import { px } from "utils/px";
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme = darkTheme) => StyleSheet.create({
  container: {
    marginTop: px(20),
    paddingVertical: px(20),
  },
  header: {
    marginBottom: px(12),
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: px(16),
  },
  subtitle: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: px(4),
  },
  textButtonWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  textButtonTitle: {
    fontSize: px(12),
    color: '#5A7EA7',
    lineHeight: px(17),
    fontFamily: 'XmlyNumber',
  },
  detailIcon: {
    marginLeft: px(2),
    width: px(5),
    height: px(8),
  },
  cardList: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: px(16)
  },
  button: {
    backgroundColor: '#FF4B4B',
    borderRadius: px(22),
    height: px(44),
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: px(16),
    color: '#FFFFFF',
    fontWeight: '600',
  },
  disabledText: {
    fontSize: px(11),
    color: theme.disabledTextColor,
  },
}); 