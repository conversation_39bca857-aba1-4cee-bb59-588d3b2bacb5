import React, { useEffect, useState } from "react";
import { View, Text, Image, LayoutChangeEvent } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue, useSetAtom } from "jotai";
import valuableTaskThemeAtom from "./theme";
import useTaskWithStatus from "./hooks/useTaskWithStatus";
import { valuableTaskAtom, ValuableTaskInfo, writeValuableTaskAtom, updateValuableTask<PERSON>tom } from "./store";
import { AdAppTaskInfo } from "components/AdAppTask/store/task";
import HeaderTitle from "../common/HeaderTitle";
import TaskButton from "../common/TaskButton";
import ModuleCard from "../common/ModuleCard";
import { ScrollAnalyticComp } from "@xmly/react-native-page-analytics";
import adDPTaskCtrl from "utilsV2/adDPTask";
import xmlog from "utilsV2/xmlog";
import { px } from "utils/px";
import { TruncatedText } from "components/common/TruncatedText";
import CountDown from "./CountDown";
import log from "utils/log";

const atMostIcon = 'https://imagev2.xmcdn.com/storages/a7d9-audiofreehighqps/22/96/GKwRIRwLwqBZAAAJPgONZwd5.png';

function formatCoins(coins: number) {
  if (coins > 10000) {
    const number = coins / 10000;
    const showNumber = Math.floor(number);
    return `${showNumber}万${showNumber < number ? '+' : ''}`;
  }
  return coins;
}

export default function ValuableTask() {
  const theme = useAtomValue(valuableTaskThemeAtom);
  const styles = getStyles(theme);
  const getValuableTask = useSetAtom(writeValuableTaskAtom);
  const { btnText, onClick } = useTaskWithStatus();
  const valuableTaskInfo = useAtomValue(valuableTaskAtom) as ValuableTaskInfo;
  const updateValuableTask = useSetAtom(updateValuableTaskAtom);
  const task = (valuableTaskInfo as AdAppTaskInfo)?.data?.[0];
  const moduleTitle = '惊喜任务';

  // 任务名称的字体大小
  const marginLeft = px(6); // atMostContainer的marginLeft，与styles.ts中保持一致

  // 状态管理
  const [parentWidth, setParentWidth] = useState(0);
  const [atMostWidth, setAtMostWidth] = useState(0);

  // 处理父容器宽度变化
  const handleTaskNameContainerLayout = (e: LayoutChangeEvent) => {
    const { width } = e.nativeEvent.layout;
    setParentWidth(width);
  };

  // 处理atMost容器宽度变化
  const handleAtMostLayout = (e: LayoutChangeEvent) => {
    const { width } = e.nativeEvent.layout;
    setAtMostWidth(width);
  };

  // 计算主文本可用宽度
  const calculateAvailableWidth = () => {
    if (!parentWidth) return 0;
    // 如果已经测量到atMost容器宽度，使用实际值；否则使用估计值
    const atMostSpace = atMostWidth > 0 ? atMostWidth + marginLeft : px(120);
    return Math.min(Math.max(0, parentWidth - atMostSpace), px(130));
  };

  useEffect(() => {
    getValuableTask({ init: true });
  }, []);

  function onShow() {
    xmlog.event(67696, 'slipPage', {
      moduleTitle,
      taskId: `${task?.adid}`,
      taskTitle: task?.name || '',
      currPage: 'welfareCenter',
      xmRequestId: valuableTaskInfo?.requestId || '',
      Item: btnText,
      positionNew: '1',
      responseId: `${valuableTaskInfo?.responseId}`
    })
  }

  // 构建最高金币提示组件
  const renderAtMostContainer = () => {
    if (!task?.priceSecondBanner || task.priceSecondBanner <= 0) return null;

    return (
      <View
        style={styles.atMostContainer}
        onLayout={handleAtMostLayout}
      >
        <Image
          source={{ uri: atMostIcon }}
          key={atMostIcon}
          style={styles.atMostIcon}
        />
        <Text style={styles.atMost}>最高{formatCoins(task.priceSecondBanner)}金币</Text>
      </View>
    );
  };

  return (
    task?.name ? (
      <ScrollAnalyticComp
        itemKey={'ValuableTask'}
        onShow={onShow}
      >
        <ModuleCard style={styles.container}>
          <HeaderTitle
            title={moduleTitle}
            titleIcon={theme.titleIcon}
          >
            <CountDown />
          </HeaderTitle>
          <View style={styles.taskItem}>
            <View style={styles.taskInfo}>
              <View
                style={styles.taskNameContainer}
                onLayout={handleTaskNameContainerLayout}
              >
                {task?.priceSecondBanner > 0 ? (
                  <>
                    <TruncatedText
                      text={task.name}
                      style={styles.taskName}
                      maxWidth={calculateAvailableWidth()}
                    />
                    {renderAtMostContainer()}
                  </>
                ) : (
                  <Text style={styles.taskName} numberOfLines={1}>
                    {task.name}
                  </Text>
                )}
              </View>
              <Text style={styles.taskDesc}>{task?.description}</Text>
            </View>
            <TaskButton style={styles.taskButton} text={btnText} onPress={onClick} />
          </View>
        </ModuleCard>
      </ScrollAnalyticComp>
    ) : null
  )
} 