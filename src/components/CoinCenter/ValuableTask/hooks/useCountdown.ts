import { use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import { useEffect, useState } from "react";
import { countdownTimer<PERSON><PERSON>, writeValuableTask<PERSON>tom } from "../store";

export default function useCountdown() {
  const [countdown, setCountdown] = useAtom(countdownTimerAtom);
  const [clock, setClock] = useState('');
  const getValuableTask = useSetAtom(writeValuableTaskAtom);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(prev => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
    if (countdown === 0) {
      getValuableTask();
    }
    return () => { }
  }, [countdown]);

  useEffect(() => {
    if (countdown && countdown > 0) {
      const hours = Math.floor(countdown / 3600);
      const min = Math.floor((countdown % 3600) / 60);
      const second = countdown % 60;
      setClock(`${hours > 9 ? hours : '0' + hours}:${min > 9 ? min : '0' + min}:${second > 9 ? second : '0' + second}`);
    } else if (countdown === 0) {
      setClock('');
    }
    return () => {

    }
  }, [countdown])

  return clock;
}
