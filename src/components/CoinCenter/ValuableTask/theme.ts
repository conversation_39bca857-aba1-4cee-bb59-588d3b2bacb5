import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  headerBgColor: '#131313',
  tabColor: 'rgba(255, 255, 255, .5)',
  activeTabColor: '#FFFFFF',
  titleIcon: 'https://imagev2.xmcdn.com/storages/48f9-audiofreehighqps/5B/5B/GAqhqKwLsHO8AAAB5gOBpMOZ.png',
  countdownLabelColor: '#8D8D91',
  taskNameColor: '#FFFFFF',
  taskDescColor: '#8D8D91',
  headerBorderColor: '#000000',
  contentBgColor: 'rgba(255, 255, 255, 0.05)',
  titleColor: '#8D8D91',
}

const lightTheme = {
  headerBgColor: '#FFFFFF',
  tabColor: 'rgba(36, 0, 0, .5)',
  activeTabColor: '#240000',
  titleIcon: 'https://imagev2.xmcdn.com/storages/ba4a-audiofreehighqps/90/97/GAqhQ6cLsHO8AAAB3gOBpMN9.png',
  countdownLabelColor: '#666666',
  taskNameColor: '#111111',
  taskDescColor: '#999999',
  headerBorderColor: '#F0F0F0',
  contentBgColor: '#FFFFFF',
  titleColor: '#666666',
}

const valuableTaskThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default valuableTaskThemeAtom;