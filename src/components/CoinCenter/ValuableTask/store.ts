import { atom } from "jotai";
import { AdAppTaskStatus, AdDPTaskItem } from "typesV2/adDPTask";
import requestAdDPTaskList from "servicesV2/requestAdDPTaskList";
import getXMRequestId from "utilsV2/getXMRequestId";
import { AD_SOURCE } from "constants/ad";
import { AdAppTaskInfo } from "components/AdAppTask/store/task";
import { checkAppInstalled } from "components/AdAppTask/utils";

type AttachedStates = {
  requestId?: string;
  completedAt?: number;
  exposed?: boolean;
  installed?: boolean;
}

async function sleep(duration = 0) {
  return new Promise(resolve => setTimeout(resolve, duration));
}

export type ValuableTaskInfo = AdAppTaskInfo & AttachedStates | null;

export const valuableTaskAtom = atom<ValuableTaskInfo | undefined | 'pending'>(null);

interface WriteValuableTaskParams {
  init?: boolean;
}

export const countdownTimerAtom = atom(-1);

export const writeValuableTaskAtom = atom(null, async (get, set, params?: WriteValuableTaskParams) => {
  try {
    const [result, requestId] = await Promise.all([
      requestAdDPTaskList(AD_SOURCE.VALUABLE),
      getXMRequestId()
    ]);
    const task = result?.data?.[0];
    set(countdownTimerAtom, task?.highModeExpireTime || -1);
    let newState = null;
    if (params?.init) {
      newState = { ...result, requestId };
    } else {
      const prevTask = get(valuableTaskAtom);

      if (typeof prevTask !== 'string' && prevTask?.data?.[0]?.adid === task?.adid && prevTask?.responseId === result?.responseId) {
        newState = { ...prevTask, ...result, requestId };
      } else {
        set(valuableTaskAtom, 'pending');
        await sleep();
        newState = { ...result, requestId };
      }
    }

    if (task?.name) {
      if (task?.dpMarketUrl && task?.appPackageName) { //拉新任务
        if (task?.taskStatus !== AdAppTaskStatus.Finished) { // 任务未完成，检测是否安装
          const installed = await checkAppInstalled(task?.appPackageName);
          newState.installed = installed;
        }
      }
      return set(valuableTaskAtom, newState);
    }
  } catch (e) {
    console.info('debug_valuable_task_error', e);
  }
  return set(valuableTaskAtom, null);
});

export const updateValuableTaskAtom = atom(null, async (get, set, params: Partial<AttachedStates>) => {
  const task = get(valuableTaskAtom);
  if (task && typeof task !== 'string' && params) {
    set(valuableTaskAtom, { ...task, ...params });
  }
}); 