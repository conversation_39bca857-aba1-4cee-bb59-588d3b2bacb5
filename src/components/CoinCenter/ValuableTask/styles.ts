import { Platform, StyleSheet } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    marginTop: px(20),
    backgroundColor: theme.contentBgColor,
    borderRadius: px(2),
    paddingHorizontal: px(16),
    paddingTop: px(12),
    paddingBottom: px(14),
  },
  countdownContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'nowrap',
  },
  countdownLabel: {
    fontSize: px(12),
    lineHeight: px(17),
    color: theme.countdownLabelColor,
    marginRight: px(2),
  },
  countdown: {
    fontSize: px(10),
    color: '#FF4444',
    textAlign: 'center',
    fontFamily: 'XmlyNumber',
    lineHeight: px(10)
  },
  countdownWrapper: {
    width: px(8),
    height: px(12),
    backgroundColor: 'rgba(255, 68, 68, 0.1)',
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: px(2)
  },
  colon: {
    width: px(3),
    height: px(12),
    backgroundColor: 'rgba(255, 68, 68, 0)',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: px(8.5),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.headerBorderColor,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: px(12),
    fontWeight: '500',
    color: theme.titleColor,
  },
  titleIcon: {
    marginRight: px(4),
    width: px(14),
    height: px(14),
  },
  taskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: px(4),
    paddingTop: px(12),
  },
  taskInfo: {
    flex: 1,
    maxWidth: px(240),
    overflow: 'hidden'
  },
  taskName: {
    fontSize: px(14),
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    color: theme.taskNameColor,
  },
  taskDesc: {
    fontSize: px(12),
    color: theme.taskDescColor,
    marginTop: px(2),
    fontFamily: 'XmlyNumber',
  },
  taskNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  atMost: {
    fontSize: px(12),
    fontFamily: 'XmlyNumber',
    fontWeight: '500',
    color: '#FF4444',
    marginLeft: px(2),
  },
  atMostContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: px(6),
  },
  atMostIcon: {
    width: 14,
    aspectRatio: 1,
    marginRight: px(2),
  },
  taskButton: {
    marginLeft: px(8),
  }
});
