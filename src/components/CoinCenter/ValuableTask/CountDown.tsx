import React from "react";
import { useAtomValue } from "jotai";
import { View, Text } from "react-native";
import { getStyles } from "./styles";
import valuableTaskThemeAtom from "./theme";
import useCountdown from "./hooks/useCountdown";

export default function CountDown() {
  const clock = useCountdown();
  const theme = useAtomValue(valuableTaskThemeAtom);
  const styles = getStyles(theme);
  return (
    <View style={[styles.countdownContainer, { opacity: clock ? 1 : 0 }]}>
      <Text style={styles.countdownLabel}>限时</Text>
      {clock.split('').map((item, index) => {
        return (
          <View style={[styles.countdownWrapper, item === ':' ? styles.colon : null]} key={index}>
            <Text style={styles.countdown}>{item}</Text>
          </View>
        )
      })}
    </View>
  );
}