import { StyleSheet, Platform } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";


export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({

  contentContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  rewardContainer: {
    alignItems: 'center',
  },
  popupImageContainer: {
    width: px(241),
    aspectRatio: 241 / 293,
    alignItems: 'center',
  },
  popupImage: {
    width: px(241),
    aspectRatio: 241 / 293,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  coinIcon: {
    width: px(96),
    aspectRatio: 96 / 75,
  },
  congratsText: {
    fontSize: px(22),
    lineHeight: px(28),
    color: theme.congratsTextColor,
    marginBottom: px(15),
    marginTop: px(30),
    fontWeight: 'bold',
    fontFamily: 'Microsoft YaHei',
  },
  coinsText: {
    fontSize: px(38),
    fontWeight: 'bold',
    color: '#FF4444',
    fontFamily: 'XmlyNumber',
  },
  confirmButton: {
    position: 'absolute',
    bottom: 30,
  },
});