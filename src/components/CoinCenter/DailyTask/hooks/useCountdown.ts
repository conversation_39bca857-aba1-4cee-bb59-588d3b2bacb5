import { useEffect, useRef } from 'react';
import { useSet<PERSON>tom } from 'jotai';
import { updateDailyTask<PERSON>tom, updateCountdownAtom } from '../atom';

interface UseCountdownProps {
  key: string;
  positionId: number;
  initialSeconds: number;
}

export function useCountdown({ key, positionId, initialSeconds }: UseCountdownProps) {
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);
  const updateCountdown = useSetAtom(updateCountdownAtom);
  const timerRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (initialSeconds <= 0) return;

    function tick() {
      updateCountdown({
        key,
        countdown: initialSeconds - 1,
      });

      if (initialSeconds <= 1) {
        updateDailyTask(key);
      } else {
        timerRef.current = setTimeout(tick, 1000);
      }
    }

    timerRef.current = setTimeout(tick, 1000);

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [initialSeconds, key]);
} 