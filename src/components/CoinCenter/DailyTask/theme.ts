import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  titleIcon: 'https://imagev2.xmcdn.com/storages/a9d0-audiofreehighqps/12/FB/GAqh9sALsHO8AAACGwOBpMO7.png',
}

const lightTheme = {
  titleIcon: 'https://imagev2.xmcdn.com/storages/176c-audiofreehighqps/E5/87/GKwRIJELsHO8AAACBwOBpMPl.png',
}

const dailyTaskThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default dailyTaskThemeAtom; 