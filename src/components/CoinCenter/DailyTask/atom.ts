import { atom } from 'jotai';
import { DailyTaskItem, queryDailyTask } from 'services/welfare';

interface DailyTaskState {
  loading: boolean;
  list: DailyTaskItem[];
  countdowns: Record<string, number>; // 使用 positionId+title 作为 key
  updatingPositions: string[]; // 记录正在更新的任务ID
}

const initialState: DailyTaskState = {
  loading: false,
  list: [],
  countdowns: {},
  updatingPositions: [],
};

export const dailyTaskAtom = atom(initialState);

export const updateDailyTaskAtom = atom(
  null,
  async (get, set, key?: string) => {
    const state = get(dailyTaskAtom);
    if (state.loading) return;

    // 如果是特定任务触发的更新，先标记该任务为更新中
    if (key) {
      set(dailyTaskAtom, {
        ...state,
        updatingPositions: [...state.updatingPositions, key],
      });
    } else {
      set(dailyTaskAtom, { ...state, loading: true });
    }

    try {
      const response = await queryDailyTask();
      if (response?.data?.success) {
        const list = response.data.list;
        const countdowns = Object.fromEntries(
          list.map(item => [item.positionId + item.title, item.calmSeconds])
        );
        set(dailyTaskAtom, {
          loading: false,
          list,
          countdowns,
          updatingPositions: [],
        });
      }
    } catch (error) {
      console.error('Failed to fetch daily tasks:', error);
    } finally {
        if (key) {
        set(dailyTaskAtom, prev => ({
          ...prev,
          updatingPositions: prev.updatingPositions.filter(id => id !== key),
        }));
      } else {
        set(dailyTaskAtom, prev => ({ ...prev, loading: false }));
      }
    }
  }
);

export const updateCountdownAtom = atom(
  null,
  (get, set, payload: { key: string; countdown: number }) => {
    const state = get(dailyTaskAtom);
    set(dailyTaskAtom, {
      ...state,
      countdowns: {
        ...state.countdowns,
        [payload.key]: payload.countdown,
      },
    });
  }
); 