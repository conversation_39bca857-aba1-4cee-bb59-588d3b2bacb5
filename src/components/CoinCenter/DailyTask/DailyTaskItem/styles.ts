import { Platform, StyleSheet } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  taskItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: px(17),
  },
  firstTaskItem: {
    marginTop: px(12),
  },
  taskInfo: {
    flex: 1,
  },
  taskName: {
    fontSize: px(14),
    color: theme.taskNameColor,
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
  },
  taskReward: {
    fontSize: px(12),
    color: theme.taskRewardColor,
    marginTop: px(4),
    fontFamily: 'XmlyNumber',
  },
  taskButton: {
    backgroundColor: theme.buttonBgColor,
    borderRadius: px(15),
    paddingHorizontal: px(12),
    height: px(24),
    alignItems: 'center',
    justifyContent: 'center'
  },
  buttonText: {
    fontSize: px(11),
    color: theme.buttonTextColor,
  },
  countdown: {
    fontSize: px(12),
    color: theme.countdownColor,
    fontFamily: 'XmlyNumber',
  },
  loadingButton: {
    opacity: 0.5,
  },
}); 