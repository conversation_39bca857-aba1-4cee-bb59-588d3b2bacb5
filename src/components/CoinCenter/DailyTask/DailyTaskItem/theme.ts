import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  taskNameColor: '#FFFFFF',
  taskRewardColor: '#8D8D91',
  buttonBgColor: '#FF4B4B',
  buttonTextColor: '#FFFFFF',
  countdownColor: 'rgba(255, 255, 255, 0.3)',
}

const lightTheme = {
  taskNameColor: '#111111',
  taskRewardColor: '#999999',
  buttonBgColor: '#FF4B4B',
  buttonTextColor: '#FFFFFF',
  countdownColor: 'rgba(44, 44, 60, 0.3)',
}

const dailyTaskItemThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default dailyTaskItemThemeAtom; 