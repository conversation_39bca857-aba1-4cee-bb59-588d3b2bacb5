import { StyleSheet, Platform } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";


export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({

  contentContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  rewardContainer: {
    alignItems: 'center',
  },
  popupImageContainer: {
    width: px(271),
    aspectRatio: 271 / 326,
    alignItems: 'center',
  },
  popupImage: {
    width: px(271),
    aspectRatio: 271 / 326,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  congratsText: {
    fontSize: px(22),
    color: theme.congratsTextColor,
    marginBottom: px(18),
    marginTop: px(30),
    fontWeight: 'bold',
    fontFamily: 'Microsoft YaHei',
  },
  highlightText: {
    fontSize: px(24),
    fontWeight: 'bold',
    letterSpacing: px(1),
    color: '#FF4444',
    fontFamily: 'XmlyNumber',
  },
  coinsText: {
    fontSize: px(38),
    fontWeight: 'bold',
    color: '#FF4444',
    fontFamily: 'XmlyNumber',
  },
  confirmButton: {
    width: px(223),
    marginTop: px(24),
  },
  banner: {
    position: 'relative',
  },
  bannerIcon: {
    width: px(215),
    aspectRatio: 215 / 114,
  },
  coinContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 20,
    height: '100%',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    flexDirection: 'row',
  },
  textContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  coinText: {
    fontSize: px(16),
    fontWeight: 'bold',
    color: '#75919E',
    fontFamily: 'XmlyNumber',
  },
  adText: {
    fontSize: px(16),
    fontWeight: 'bold',
    color: '#FF7B9C',
    fontFamily: 'XmlyNumber',
  },
  closeButton: {
    marginTop: px(20),
  },
  closeText: {
    fontSize: px(16),
    lineHeight: px(20),
    color: theme.closeTextColor,
  },
  buttonContainer: {
    position: 'absolute',
    bottom: 28,
    left: 0,
    right: 0,
    alignItems: 'center',
    justifyContent: 'center',
  }
});