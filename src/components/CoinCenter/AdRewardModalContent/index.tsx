import React, { useRef } from "react";
import { View, Animated, Text, Image } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import rewardModalContentThemeAtom, { darkTheme, lightTheme } from "./theme";
import { BetterImage, Touch, FastImage } from "@xmly/rn-components";
import { preloadImages } from "utils/preloadImages";
import { themeAtom } from "atom/theme";
import ConfirmButton from "components/CoinCenter/common/ConfirmButton";

// 导出金币图片以便预加载
export const COINS_IMAGE = 'https://imagev2.xmcdn.com/storages/fd64-audiofreehighqps/70/56/GAqh1QQL0sgjAAAyPwOXPuoW.png';

export const useLotteryImages = () => {
  const theme = useAtomValue(themeAtom);
  const PRELOAD_IMAGES = theme === 'dark' ? [
    darkTheme.popupImage,
    COINS_IMAGE,
  ] : [
    lightTheme.popupImage,
    COINS_IMAGE,
  ];

  return () => preloadImages(PRELOAD_IMAGES);
};


// 导出所有需要预加载的图片
export const PRELOAD_IMAGES = [
  COINS_IMAGE,
  darkTheme.popupImage,
  lightTheme.popupImage
];

interface RewardModalContentProps {
  coins: number;
  upgradeCoins: number;
  btnText?: string;
  title?: string;
  onPress: () => void;
  onClose: () => void;
  scaleAnim?: Animated.Value;
  closeBtnText?: string;
}

export default function AdRewardModalContent({
  coins,
  upgradeCoins,
  btnText = '看广告翻倍领取',
  title = '恭喜获得',
  scaleAnim,
  onPress,
  onClose,
  closeBtnText = '直接领取'
}: RewardModalContentProps) {
  const theme = useAtomValue(rewardModalContentThemeAtom);
  const styles = getStyles(theme);
  // const scaleAnim = useRef(new Animated.Value(0)).current;
  return (
    <View style={styles.contentContainer}>
      <Animated.View
        style={[
          styles.popupImageContainer,
          {
            transform: [
              { scale: scaleAnim || 1 }
            ]
          }
        ]}
      >
        {/* Image组件可以直接使用预加载资源 */}
        <Image
          source={{ uri: theme.popupImage }}
          style={styles.popupImage}
        />

        <View style={styles.rewardContainer}>
          <Text style={styles.congratsText}>{title}<Text style={styles.highlightText}>{coins}</Text>金币</Text>
          <View style={styles.banner}>
            <BetterImage
              source={{ uri: COINS_IMAGE }}
              style={styles.bannerIcon}
              imgHeight={114}
              imgWidth={216}
            />
            <View style={styles.coinContainer}>
              <View style={styles.textContainer}>
                <Text style={styles.coinText}>已获得</Text>
                <Text style={styles.coinText}>{coins}金币</Text>
              </View>
              <View style={styles.textContainer}>
                <Text style={styles.adText}>看广告领</Text>
                <Text style={styles.adText}>{upgradeCoins}金币</Text>
              </View>
            </View>
          </View>
        </View>
        <View style={styles.buttonContainer}>
          <ConfirmButton
            text={btnText}
            onPress={onPress}
            style={styles.confirmButton}
          />
          <Touch style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText}>{closeBtnText}</Text>
          </Touch>
        </View>
      </Animated.View>
    </View>
  );
}