import { useAtom, useSet<PERSON>tom } from "jotai";
import { useEffect, useRef, useState } from "react";
import { countdownAtom, writeTouchTaskAtom } from "../store";

export default function useCountDown() {
  const [countdown, setCountdown] = useAtom(countdownAtom);
  const [clock, setClock] = useState('');
  const queryTouchTask = useSetAtom(writeTouchTaskAtom);
  
  // 记录目标结束时间
  const endTimeRef = useRef<number | null>(null);
  // 存储定时器引用
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  // 记录是否正在倒计时
  const isCountingRef = useRef<boolean>(false);

  // 计算剩余时间
  const calculateRemainingTime = () => {
    if (!endTimeRef.current) return 0;
    const now = Date.now();
    const remaining = Math.max(0, Math.floor((endTimeRef.current - now) / 1000));
    console.log('calculateRemainingTime======>====!--!', remaining, now, endTimeRef.current);
    return remaining;
  };

  // 格式化时间显示
  const formatTime = (seconds: number): string => {
    if (seconds <= 0) return '';
    const min = Math.floor(seconds / 60);
    const second = seconds % 60;
    return `${min > 0 ? min + '分' : ''}${second > 0 ? second + '秒' : ''}`;
  };

  // 更新倒计时显示
  const updateCountdown = () => {
    const remaining = calculateRemainingTime();
    setCountdown(remaining);
    setClock(formatTime(remaining));

    console.log('updateCountdown======>====!--!', remaining);

    if (remaining <= 0) {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      isCountingRef.current = false;
      queryTouchTask();
    }
  };

  // 初始化倒计时
  useEffect(() => {
    if (countdown > 0 && !isCountingRef.current) {
      endTimeRef.current = Date.now() + countdown * 1000;
      isCountingRef.current = true;
    }
      
      // 清除可能存在的旧定时器
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      
      // 使用 setInterval 每秒更新一次
      timerRef.current = setInterval(updateCountdown, 1000);
      // 立即执行一次更新
      updateCountdown();

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [countdown, queryTouchTask]);

  return clock;
}