import { use<PERSON>tom, useSet<PERSON>tom } from "jotai";
import { useEffect, useRef, useState } from "react";
import { countdownAtom, writeTouchTaskAtom } from "../store";

export default function useCountDown() {
  const [countdown, setCountdown] = useAtom(countdownAtom);
  const timer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [clock, setClock] = useState('');
  const queryTouchTask = useSetAtom(writeTouchTaskAtom);

  useEffect(() => {
    if (countdown > 0) {
      timer.current = setTimeout(() => {
        setCountdown(prevCountdown => prevCountdown !== undefined && prevCountdown > 0 ? prevCountdown - 1 : 0);
      }, 1000);
    }
    if (countdown === 0) {
      queryTouchTask();
    }

    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
    };
  }, [countdown])

  useEffect(() => {
    if (countdown && countdown > 0) {
      const min = Math.floor(countdown / 60);
      const second = countdown % 60;
      setClock(`${min > 0 ? min + '分' : ''}${second > 0 ? second + '秒' : ''}`);
    } else if (countdown === 0) {
      setClock('');
    }
    return () => {

    }
  }, [countdown])


  return clock;
}