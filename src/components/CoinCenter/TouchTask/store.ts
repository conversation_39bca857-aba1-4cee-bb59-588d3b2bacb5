import { atom } from "jotai";
import { queryTouchTask, TouchTaskInfo } from "services/welfare/touch";


export const touchTaskAtom = atom<TouchTaskInfo>();
export const countdownAtom = atom<number>(-1);
export const touchTaskVisibleAtom = atom<boolean>(false);

interface WriteTouchTaskParams {
  init?: boolean;
}

export const writeTouchTaskAtom = atom(null, async (get, set, params?: WriteTouchTaskParams) => {
  const result = await queryTouchTask();
  if (result?.data.success) {
    set(touchTaskAtom, result.data);
    if (!(params?.init && result.data.calmSeconds === 0)) {
      set(countdownAtom, result.data.calmSeconds);
    }
    set(touchTaskVisibleAtom, true);
  }
});
