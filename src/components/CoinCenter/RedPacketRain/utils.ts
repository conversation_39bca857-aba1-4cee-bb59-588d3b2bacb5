import { Dimensions, Animated } from 'react-native';

const { width, height } = Dimensions.get('window');

// 游戏常量配置
export const GAME_CONFIG = {
  PREP_DURATION: 3000, // 3秒准备时间
  GAME_DURATION: 10000, // 10秒游戏时间
  TOTAL_DURATION: 13000, // 总时长13秒
  PACKET_INTERVAL: 0, // 红包生成间隔 - 设为0避免一阵一阵的效果
  MIN_DURATION: 3500, // 最快速度 - 增加最小持续时间使红包下落更慢
  MAX_DURATION: 5000, // 最慢速度 - 增加最大持续时间使红包下落更慢
  MAX_PACKETS: 6, // 最大红包数量减少到6个
  SPEED_VARIATIONS: [2.4, 2.6, 2.8, 3.0], // 增大速度系数，使下落更慢，变化也更小
  FIXED_DURATION: 4000, // 固定红包下落时间为4秒
};

// 游戏状态枚举
export enum GameState {
  PREP = 'prep',
  PLAYING = 'playing',
  END = 'end'
}

// 视频播放状态枚举
export enum VideoPlayState {
  NONE,
  BG_AND_COUNT,
  PLAYING_GAME,
  AWARD_BG_START,
  AWARD_BG_LOOP,
  CONFETTI
}

// 红包类型定义
export interface Packet {
  id: number;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  progress: Animated.Value;
  animation?: Animated.CompositeAnimation;
  disappearAnimation?: Animated.CompositeAnimation;
  scale: number;
  animatedScale: Animated.Value;
  opacity: number;
  animatedOpacity: Animated.Value;
}

// 生成随机范围内的数字
export const random = (min: number, max: number): number => {
  return Math.random() * (max - min) + min;
};

// 添加权重随机函数
export const weightedRandom = (min: number, max: number, weight = 1): number => {
  // weight > 1 会使结果偏向最大值，< 1 会偏向最小值
  const value = Math.random();
  return min + (max - min) * Math.pow(value, weight);
};

// 计算终点位置（垂直下落，不偏转）
export const getEndPosition = (startX: number, startY: number) => {
  // 计算所需的落点Y坐标，确保红包完全穿过屏幕
  const targetY = height + 150; // 终点在屏幕底部以下，完全离开屏幕
  
  // 对于静态红包，返回相同的X坐标实现垂直下落
  return {
    endX: startX, // 保持X坐标不变实现垂直下落
    endY: targetY
  };
};

// 生成一个新红包
export const createPacket = (startX: number, startY: number, speedVariation = 1): Packet => {
  const { endX, endY } = getEndPosition(startX, startY);

  // 80%概率正常大小，20%概率随机大小
  const scale = Math.random() > 0.1
    ? 1
    : weightedRandom(0.8, 1.5, 2);

  // 90%概率完全不透明，10%概率半透明
  const opacity = Math.random() > 0.1 ? 1 : weightedRandom(0.7, 0.9, 1);

  const packet: Packet = {
    id: Date.now() + Math.random(),
    startX,
    startY,
    endX,
    endY,
    progress: new Animated.Value(0),
    scale,
    opacity,
    animatedScale: new Animated.Value(scale),
    animatedOpacity: new Animated.Value(opacity),
  };

  // 使用固定时间代替随机速度
  const duration = GAME_CONFIG.FIXED_DURATION;

  packet.animation = Animated.timing(packet.progress, {
    toValue: 1,
    duration,
    useNativeDriver: true,
  });

  packet.disappearAnimation = Animated.parallel([
    Animated.timing(packet.animatedScale, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }),
    Animated.timing(packet.animatedOpacity, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    })
  ]);

  return packet;
};

// 生成红包组
export const createPacketGroup = (scattered = false): Packet[] => {
  const packets: Packet[] = [];
  // 散布模式下生成更多红包，增加视觉效果
  const count = scattered ?
    Math.floor(random(1, 3)) : // 1-2个，更分散
    Math.floor(random(1, 2)); // 1个，更集中在轴线上

  // 散布模式下增加生成概率，确保屏幕均匀覆盖
  const spawnRandom = Math.random();
  const shouldUseHighSpawnArea = scattered && spawnRandom < 0.7; // 散布模式70%概率使用更多种区域

  // 用于跟踪已生成红包的位置，防止重叠
  const occupiedAreas: Array<{ x1: number, y1: number, x2: number, y2: number }> = [];

  // 红包大小估算（用于防止重叠计算）
  const PACKET_WIDTH = 100; // 增加宽度以考虑UI实际大小
  const PACKET_HEIGHT = 120; // 增加高度以考虑UI实际大小
  const SAFETY_MARGIN = 20; // 额外的安全间距，防止红包视觉上太靠近

  // 生成不重叠的红包
  for (let i = 0; i < count; i++) {
    // 最多尝试10次生成不重叠的位置
    let attempts = 0;
    let validPosition = false;
    let startX = 0, startY = 0;

    while (!validPosition && attempts < 10) {
      attempts++;

      // 随机选择生成位置：右上、上方、中部
      // 散布模式下增加更多的随机性
      const spawnArea = shouldUseHighSpawnArea ? 
        Math.floor(random(0, 5)) : // 散布模式使用全部区域
        Math.floor(random(0, 3));  // 非散布模式只使用基本区域

      switch (spawnArea) {
        case 0: // 右上角区域（屏幕外右上）
          startX = random(width + 20, width + 100);
          startY = random(-150, -20);
          break;
        case 1: // 上方区域（屏幕顶部偏右）
          startX = random(width * 0.6, width * 0.9);
          startY = random(-150, -30);
          break;
        case 2: // 上方中部区域
          startX = random(width * 0.3, width * 0.6);
          startY = random(-150, -20);
          break;
        case 3: // 散布模式：右侧区域（屏幕外右侧中部）
          startX = random(width + 10, width + 80);
          startY = random(0, height * 0.4);
          break;
        case 4: // 散布模式：更广泛的右上区域
          startX = random(width * 0.5, width + 100);
          startY = random(-180, -50);
          break;
        default:
          startX = random(width * 0.5, width + 50);
          startY = random(-150, -30);
      }

      // 计算红包的区域范围
      const packetArea = {
        x1: startX - PACKET_WIDTH / 2 - SAFETY_MARGIN,
        y1: startY - PACKET_HEIGHT / 2 - SAFETY_MARGIN,
        x2: startX + PACKET_WIDTH / 2 + SAFETY_MARGIN,
        y2: startY + PACKET_HEIGHT / 2 + SAFETY_MARGIN
      };

      // 检查与现有红包是否重叠
      validPosition = true; // 默认位置有效

      // 检查是否与任何已存在的红包重叠
      for (const area of occupiedAreas) {
        // 检查两个矩形是否重叠
        // 如果两个矩形不重叠，则至少在一个轴上有间隙
        const noOverlap =
          packetArea.x2 < area.x1 || // 当前红包在已有红包左侧
          packetArea.x1 > area.x2 || // 当前红包在已有红包右侧
          packetArea.y2 < area.y1 || // 当前红包在已有红包上方
          packetArea.y1 > area.y2;   // 当前红包在已有红包下方

        // 如果有重叠，则位置无效
        if (!noOverlap) {
          validPosition = false;
          break;
        }
      }

      // 如果找到有效位置，添加到已占用区域
      if (validPosition) {
        occupiedAreas.push(packetArea);
      }
    }

    // 如果多次尝试后仍无法找到不重叠的位置，直接使用最后一次生成的位置
    // 但增加一个位置偏移，尽量减少重叠
    if (!validPosition && attempts >= 10) {
      // 如果屏幕上红包过多，可能找不到合适位置
      // 此时尝试将红包放在屏幕边缘较远的位置
      const randomShift = random(50, 150);
      if (Math.random() > 0.5) {
        // 水平方向远离当前红包
        startX = startX < width / 2 ?
          startX - randomShift : // 如果在左侧，则更左
          startX + randomShift;  // 如果在右侧，则更右
      } else {
        // 垂直方向远离当前红包
        startY = startY < 0 ?
          startY - randomShift : // 如果在上方，则更上
          startY + randomShift;  // 如果在下方，则更下
      }
    }

    // 使用固定速度，不再应用速度变化
    const packet = createPacket(startX, startY, 1);
    packets.push(packet);
  }

  return packets;
}; 