import { useLottieResource } from "hooks/useLottieResources";
import { View, StyleSheet, Animated, Platform } from "react-native";
import AnimatedLottieView from "lottie-react-native";
import React, { useEffect, useRef } from 'react';
import type { ViewStyle, StyleProp } from 'react-native';
import { isAndroid } from "../../../../../rnEnv";

interface CountProps {
  style?: StyleProp<ViewStyle>;
  bgStyle?: StyleProp<ViewStyle>;
  loop?: boolean;
  onComplete?: () => void;
  onLoad?: () => void;
  countVideo?: React.ReactNode;
}

export default function Count({
  style,
  bgStyle,
  loop = false,
  onComplete,
  onLoad,
  countVideo
}: CountProps) {
  const countLottie = useLottieResource('https://aod.cos.tx.xmcdn.com/storages/ff3b-audiofreehighqps/50/7D/GAqhQ6cL328zAAMTwwOefTd3.json');
  const bgLottie = useLottieResource('https://aod.cos.tx.xmcdn.com/storages/080e-audiofreehighqps/4B/C0/GKwRIUEL328yAAiU0QOefTcr.json');

  const bgOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isAndroid && countLottie) {
      onLoad?.();
    } else {
      onLoad?.();
    }
  }, [countLottie, onLoad]);

  useEffect(() => {
    if (countLottie || !isAndroid) {
      const timer = setTimeout(() => {
        Animated.timing(bgOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }).start();
      }, Platform.OS === 'android' ? 2900 : 3300);

      return () => {
        clearTimeout(timer);
      };
    }
  }, [countLottie, bgOpacity]);

  return (
    <>
      {Platform.select({
        ios: countVideo, android: <>
          {countLottie ? <View style={[
            {
              zIndex: 2,
            },
            style
          ]} pointerEvents="none">
            <AnimatedLottieView
              source={countLottie}
              style={{ width: '100%', height: '100%' }}
              autoPlay
              loop={loop}
              onAnimationFinish={onComplete}
            />
          </View> : null}
        </>
      })}
      {bgLottie ? <Animated.View style={[
        {
          zIndex: 1,
          position: 'absolute',
          width: '100%',
          aspectRatio: 1080 / 2400,
          left: 0,
          top: 0,
          opacity: bgOpacity,
        },
        bgStyle
      ]} pointerEvents="none">
        <AnimatedLottieView
          source={bgLottie}
          style={{
            width: '100%',
            height: '100%',
          }}
          autoPlay
          loop={true}
        />
      </Animated.View> : null}
    </>
  );
}