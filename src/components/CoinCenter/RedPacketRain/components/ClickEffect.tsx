import { useLottieResource } from "hooks/useLottieResources";
import { View } from "react-native";
import AnimatedLott<PERSON>Vie<PERSON> from "lottie-react-native";
import React from 'react';
import type { ViewStyle, StyleProp } from 'react-native';

interface ClickEffectProps {
  style: StyleProp<ViewStyle>;
  loop?: boolean;
}

export default function ClickEffect({ style, loop = false }: ClickEffectProps) {
  const lottie = useLottieResource('https://aod.cos.tx.xmcdn.com/storages/688f-audiofreehighqps/DE/92/GAqhfD0L07-OAAx7SgOXs4ok.json');

  return (
    lottie ? <View style={[style]} pointerEvents="none">
      <AnimatedLottieView
        source={lottie}
        style={{ width: '100%', height: '100%' }}
        autoPlay
        loop={loop}
      />
    </View> : null
  );
}