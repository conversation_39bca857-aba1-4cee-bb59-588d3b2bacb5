import React from 'react';
import { Animated, StyleSheet } from 'react-native';
import { BetterImage, Touch } from '@xmly/rn-components';
import { px } from 'utils/px';

interface EndPacketProps {
  endPacketScale: Animated.Value;
  splitLineAnimatedValue: Animated.Value;
  onPress: () => void;
}

/**
 * 结束红包组件
 */
export const EndPacket: React.FC<EndPacketProps> = ({ 
  endPacketScale, 
  splitLineAnimatedValue,
  onPress
}) => {
  return (
    <Touch style={styles.endPacket} onPress={onPress}>
      <Animated.View style={[
        styles.endPacket,
        {
          transform: [{ scale: endPacketScale }]
        }
      ]}>
        <Animated.View style={[
          styles.endPacketTop,
          {
            transform: [
              { 
                translateY: splitLineAnimatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -800]
                }) 
              },
              { 
                rotate: splitLineAnimatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '-20deg']
                }) 
              },
              { 
                translateX: splitLineAnimatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, -150]
                })
              }
            ]
          }
        ]}>
          <BetterImage
            source={{ uri: 'https://imagev2.xmcdn.com/storages/4f04-audiofreehighqps/3E/00/GAqh1QQL4ROnAAGWlQOfpUkZ.png' }}
            style={styles.endPacketTop}
            quality={10}
          />
        </Animated.View>
        <Animated.View style={[
          styles.endPacketBottom,
          {
            transform: [
              { 
                translateY: splitLineAnimatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 800]
                }) 
              },
              { 
                rotate: splitLineAnimatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '20deg']
                }) 
              },
              { 
                translateX: splitLineAnimatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0, 150]
                })
              }
            ]
          }
        ]}>
          <BetterImage
            source={{ uri: 'https://imagev2.xmcdn.com/storages/6117-audiofreehighqps/14/BE/GAqh4zIL4ROoAAG3PgOfpUlP.png' }}
            style={styles.endPacketBottom}
            quality={10}
          />
        </Animated.View>
      </Animated.View>
    </Touch>
  );
};

const styles = StyleSheet.create({
  endPacket: {
    justifyContent: 'center',
    alignItems: 'center',
    height: px(307),
    width: px(241),
  },
  endPacketTop: {
    width: px(241),
    height: px(175),
    position: 'absolute',
    top: 0,
    zIndex: 10,
  },
  endPacketBottom: {
    width: px(241),
    height: px(225),
    position: 'absolute',
    bottom: 0,
  },
}); 