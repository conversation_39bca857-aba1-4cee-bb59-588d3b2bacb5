import React, { memo, useCallback, useEffect, useRef } from 'react';
import { Animated, GestureResponderEvent, StyleSheet, TouchableWithoutFeedback } from 'react-native';
import { BetterImage } from '@xmly/rn-components';
import { Packet } from '../utils';

// 红包图片URL
const PACKET_IMAGE = 'https://imagev2.xmcdn.com/storages/fc8f-audiofreehighqps/AA/61/GAqhQ6cLyprGAACMEQOSUw6Y.png';

// 可能需要预加载图片，但根据组件库不同而异
// 这里假设BetterImage有自动缓存功能

interface PacketItemProps {
  packet: Packet;
  onPress: (packetId: number, event: GestureResponderEvent) => void;
}

/**
 * 单个红包组件 - 使用memo减少重渲染
 */
export const PacketItem = memo(({ packet, onPress }: PacketItemProps) => {
  // 使用ref跟踪红包点击状态
  const isClickedRef = useRef(false);
  const lastClickTimeRef = useRef(0);
  const CLICK_THROTTLE = 100; // 点击节流阈值(毫秒)
  
  // 增强版点击处理函数，添加点击拦截
  const handlePress = useCallback((event: GestureResponderEvent) => {
    // 检查红包是否已被点击
    if (isClickedRef.current) {
      return; // 已点击过，直接忽略
    }
    
    // 点击节流：避免过快点击
    const now = Date.now();
    if (now - lastClickTimeRef.current < CLICK_THROTTLE) {
      return; // 点击过快，忽略本次点击
    }
    lastClickTimeRef.current = now;
    
    // 立即标记为已点击，防止重复处理
    isClickedRef.current = true;
    
    // 调用原始点击处理函数
    onPress(packet.id, event);
  }, [packet.id, onPress]);

  // 预先计算动画样式，避免在渲染时计算
  const animatedStyle = {
    transform: [
      {
        translateX: packet.progress.interpolate({
          inputRange: [0, 1],
          outputRange: [packet.startX, packet.endX],
          extrapolate: 'clamp' // 防止超出范围的值
        })
      },
      {
        translateY: packet.progress.interpolate({
          inputRange: [0, 1],
          outputRange: [packet.startY, packet.endY],
          extrapolate: 'clamp'
        })
      },
      { scale: packet.animatedScale },
    ],
    opacity: packet.animatedOpacity
  };

  return (
    <TouchableWithoutFeedback
      key={packet.id}
      onPress={handlePress}
    >
      <Animated.View style={[styles.packetContainer, animatedStyle]}>
        <BetterImage
          source={{ uri: PACKET_IMAGE }}
          style={styles.packetImage}
          resizeMode="contain"
        />
      </Animated.View>
    </TouchableWithoutFeedback>
  );
}, (prevProps, nextProps) => {
  // 对比消失状态，确保在状态变化时重新渲染
  return (
    prevProps.packet.id === nextProps.packet.id &&
    prevProps.packet.animation === nextProps.packet.animation &&
    prevProps.packet.disappearAnimation === nextProps.packet.disappearAnimation
  );
});

const styles = StyleSheet.create({
  packetContainer: {
    position: 'absolute',
    width: 86,
    height: 108,
  },
  packetImage: {
    width: '100%',
    height: '100%',
  }
}); 