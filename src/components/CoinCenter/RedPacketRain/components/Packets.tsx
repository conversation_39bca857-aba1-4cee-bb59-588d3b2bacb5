import React, { useEffect, useRef } from "react";
import { Animated, GestureResponderEvent, StyleSheet, Dimensions, Easing, StyleProp, ViewStyle } from "react-native";
import { Packet } from "../utils";
import { PacketItem } from "./PacketItem";

// 获取屏幕尺寸用于确定中心点
const { width, height } = Dimensions.get('window');

// 动画的起点和终点
const START_POSITION_X = 0; // 起始X位置
const START_POSITION_Y = -height * 5; // 起始Y位置，适应五倍屏幕高度
const END_POSITION_X = 0; // 终点X位置
const END_POSITION_Y = height * 5; // 终点Y位置，适应五倍屏幕高度

// 动画持续时间(毫秒)
const ANIMATION_DURATION = 20000; // 20秒完成一次平移，适应五倍高度

interface PacketsProps {
  packets: Packet[];
  handlePacketPress: (packetId: number, event: GestureResponderEvent) => void;
  style?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;
}

function Packets(props: PacketsProps) {
  const { packets, handlePacketPress, style } = props;

  // 创建动画值引用
  const translateY = useRef(new Animated.Value(START_POSITION_Y)).current;

  // 设置并启动动画
  useEffect(() => {
    // 创建平移动画 - 同时控制X和Y坐标
    const animation = Animated.parallel([
      // Y轴从上向下移动
      Animated.timing(translateY, {
        toValue: END_POSITION_Y,
        duration: ANIMATION_DURATION,
        easing: Easing.linear,
        useNativeDriver: true,
      }),
    ]);

    console.log('动画起始位置:', START_POSITION_X, START_POSITION_Y);
    console.log('动画终点位置:', END_POSITION_X, END_POSITION_Y);

    // 启动动画
    // setTimeout(() => {
    if (packets.length > 0) {
      animation.start();
    }
    // }, 1000);

  }, [packets]);

  return (
    <Animated.View
      style={[
        styles.container,
        style,
        {
          // 定位容器中心点，考虑到五屏高度
          left: 0,
          top: 0,
          height: height * 5, // 容器高度为五屏
          width: width, // 宽度不变
          transform: [
            { translateY },
          ],
        }
      ]}
    >
      {packets.map(packet => (
        <PacketItem
          key={packet.id}
          packet={packet}
          onPress={handlePacketPress}
        />
      ))}
    </Animated.View>
  );
}

export default React.memo(Packets);

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
  }
});