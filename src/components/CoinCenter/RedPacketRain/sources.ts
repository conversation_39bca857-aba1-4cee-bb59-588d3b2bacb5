import { ResourcePriority } from "atom/videoResources";
import { awardBgStartVideo, awardBgLoopVideo } from "constants/assets";
import { Platform } from "react-native";

const countUrl = Platform.select({
  ios: 'https://aod.cos.tx.xmcdn.com/storages/f75b-audiofreehighqps/D2/DE/GKwRIDoL44uXAAiYHwOhLTHZ.mp4',
  android: '',
}) || '';

export const videoSources = {
  count: { url: countUrl, priority: ResourcePriority.HIGH },
  awardBgStart: awardBgStartVideo,
  awardBg: awardBgLoopVideo,
  click: { url: 'https://aod.cos.tx.xmcdn.com/storages/a4c5-audiofreehighqps/3A/5B/GKwRIW4LzX4gAABfMgOUBe1b.mp4', priority: ResourcePriority.MEDIUM },
}
