import React, { useCallback, useEffect } from 'react';
import { View, TouchableWithoutFeedback, Text, Platform, Image } from 'react-native';
import { getStyles } from './styles';
import { useVideoResources } from 'hooks/useVideoResources';
import AlphaVideo from 'components/common/AlphaVideo';
import { BetterImage } from '@xmly/rn-components';
import AnimatedLottieView from 'lottie-react-native';
import { useLottieResource } from 'hooks/useLottieResources';
import { px } from 'utils/px';
import ClickEffect from './components/ClickEffect';
import { transform } from '@babel/core';
import Count from './components/Count';
// import AlphaVideo from 'components/common/AlphaVideo';

interface Props {
  visible: boolean;
  onGameEnd?: (score: number) => void;
}

export default function RedPacketRain({ visible, onGameEnd }: Props) {
  const styles = getStyles();
  const lottie = useLottieResource('https://aod.cos.tx.xmcdn.com/storages/688f-audiofreehighqps/DE/92/GAqhfD0L07-OAAx7SgOXs4ok.json');

  // 使用hook加载视频
  const { resources, ready, isLoading, load } = useVideoResources({
    lottery: 'https://aod.cos.tx.xmcdn.com/storages/f75b-audiofreehighqps/D2/DE/GKwRIDoL44uXAAiYHwOhLTHZ.mp4'
  }, {
    autoLoad: visible // 当组件可见时自动加载
  });

  // 资源加载完成后的处理
  useEffect(() => {
    if (ready) {
      console.log('Video resources loaded successfully');
    }
  }, [ready]);

  // 渲染视频组件，确保资源已加载
  const renderVideo = useCallback((resourceKey: string, props: any) => {
    const resourceInfo = resources.find(resource => resource.key === resourceKey);

    if (!resourceInfo || !resourceInfo.isReady) {
      // 资源未加载完成，返回null或加载状态
      return null;
    }

    return <AlphaVideo source={resourceInfo.path} {...props} />;
  }, [resources]);

  if (!visible) return null;

  return (
    <TouchableWithoutFeedback>
      <View style={styles.container}>
        {/* 使用renderVideo确保资源已加载 */}
        {/* {lottie ? <ClickEffect
          style={[styles.video,
          {
            left: px(40), top: px(40)
          },
          // {
          //   transform: [{ translateX: px(-60) }, { translateY: px(-60) }]
          // }
          ]}
          loop={true}
        /> : null} */}
        {/* <Count style={styles.video} loop={true} /> */}
        {renderVideo('lottery', {
          style: styles.video,
          loop: true,
        })}


        {/* 加载中显示 */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>资源加载中...</Text>
          </View>
        )}
      </View>
    </TouchableWithoutFeedback>
  );
} 