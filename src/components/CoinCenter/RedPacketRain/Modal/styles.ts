import { StyleSheet, Dimensions } from 'react-native';
import { px } from 'utils/px';

const { width } = Dimensions.get('window');

export const getStyles = (theme: 'light' | 'dark') => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'transparent',
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  modalContainer: {
    // 完全覆盖整个屏幕
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullVideo: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
  }
}); 