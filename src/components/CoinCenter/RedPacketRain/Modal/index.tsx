import React, { useEffect, useState, useRef } from 'react';
import { View, Animated, Easing } from 'react-native';
import { getStyles } from './styles';
import { useAtomValue } from 'jotai';
import { themeAtom } from 'atom/theme';
import RewardModalContent from 'components/CoinCenter/RewardModalContent';
import ConfettiAnimation from 'components/CoinCenter/common/ConfettiAnimation';
import AdRewardModalContent from 'components/CoinCenter/AdRewardModalContent';
import xmlog from 'utilsV2/xmlog';
import log from 'utils/log';
const customEasing = Easing.bezier(0.66, 0, 0.34, 1);


enum VideoPlayState {
  BACKGROUND_START_AND_CONFETTI,
  BACKGROUND_LOOP,
  FINISHED
}

interface ModalProps {
  visible: boolean;
  onClose: () => void;
  onPress?: () => void;
  children?: React.ReactNode;
  coins?: number;
  type?: 'withAd' | 'normal';
  upgradeCoins?: number;
}

export default function Modal({
  visible,
  onClose,
  onPress,
  coins = 0,
  type = 'normal',
  upgradeCoins = 0,
}: ModalProps) {
  const theme = useAtomValue(themeAtom);
  const styles = getStyles(theme === 'dark' ? 'dark' : 'light');
  const [playState, setPlayState] = useState<VideoPlayState>(VideoPlayState.BACKGROUND_START_AND_CONFETTI);
  const [showContent, setShowContent] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const modalTitle = '恭喜获得';
  const adBtnText = '看广告翻倍领取';
  const adCloseBtnText = '直接领取';
  const normalCloseBtnText = '开心收下';
  const dialogType = type === 'withAd' ? '奖励弹窗' : '看视频后弹窗';
  const from = '红包雨';
  const closeBtnText = type === 'withAd' ? adCloseBtnText : normalCloseBtnText;

  // 创建动画值
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const hasReported = useRef(false);
  const prevType = useRef(type);

  // 初始动画已经不需要，直接在visible变化时触发动画
  useEffect(() => {
    if (visible) {
      // 显示内容并启动缩放动画
      setShowContent(true);
      setShowConfetti(true);

      // 启动缩放动画
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 1000,
        easing: customEasing,
        useNativeDriver: true
      }).start();
    }
  }, [visible, scaleAnim]);

  // 当弹窗关闭时重置状态和清除定时器
  useEffect(() => {
    if (!visible) {
      setPlayState(VideoPlayState.BACKGROUND_START_AND_CONFETTI);
      setShowContent(false);
      setShowConfetti(false);
      scaleAnim.setValue(0);
    }
  }, [visible, scaleAnim]);

  // 渲染视频内容
  const renderVideo = () => {
    switch (playState) {
      case VideoPlayState.BACKGROUND_START_AND_CONFETTI:
        return (
          <>
            {showConfetti && (
              <ConfettiAnimation
                autoPlay
                loop={false}
              />
            )}
          </>
        );

      default:
        return null;
    }
  };

  useEffect(() => {
    if (showContent && coins > 0 && !hasReported.current) {
      xmlog.event(68281, 'dialogView', { currPage: 'welfareCenter', dialogType, from, dialogTitle: `${modalTitle}${coins}金币` });
      hasReported.current = true;
    }
    
    return () => {
      // 当showContent为false或type变化时，重置hasReported状态
      if (!showContent || type !== prevType.current) {
        hasReported.current = false;
        prevType.current = type;
      }
    };
  }, [showContent, coins, dialogType, from, modalTitle, type]);

  function onCloseBtnPress() {
    xmlog.event(68282, 'dialogClick', { currPage: 'welfareCenter', dialogType, from, dialogTitle: `${modalTitle}${coins}金币`, Item: closeBtnText });
    onClose();
  }

  function onAdBtnPress() {
    xmlog.event(68282, 'dialogClick', { currPage: 'welfareCenter', dialogType, from, dialogTitle: `${modalTitle}${coins}金币`, Item: adBtnText });
    onPress?.();
  }

  if (!visible) return null;

  return (
    <View style={styles.container}>
      <View style={styles.modalContainer}>
        {/* 渲染视频内容 */}
        {renderVideo()}

        {/* 显示内容 */}
        {showContent ? type === 'withAd' ? (<AdRewardModalContent
          coins={coins}
          upgradeCoins={upgradeCoins}
          onClose={onCloseBtnPress}
          onPress={onAdBtnPress}
          scaleAnim={scaleAnim}
          btnText={adBtnText}
          closeBtnText={adCloseBtnText}
        />
        ) : (
          <RewardModalContent
            coins={coins}
            onPress={onCloseBtnPress}
            scaleAnim={scaleAnim}
            btnText={normalCloseBtnText}
          />
        ) : null}
      </View>
    </View>
  );
} 