import { useState, useEffect, useCallback, useRef } from 'react';
import { GestureResponderEvent, InteractionManager, Platform } from 'react-native';
import { GameState, Packet, createPacketGroup } from '../utils';
import { hapticTap } from 'utils/haptic';

// 针对不同平台调整参数
const PLATFORM_CONFIG = {
  // iOS上调整参数
  ios: {
    BATCH_SIZE: 1, // 每次生成1个红包
    GENERATION_INTERVAL: 0, // 生成间隔设很小，避免一阵一阵的效果
    MAX_PACKETS: 6, // 最大红包数(每屏5-6个)
    SPAWN_PROBABILITY: 1, // 每次生成的概率
  },
  // Android平台参数
  android: {
    BATCH_SIZE: 1, // 每次生成1个红包
    GENERATION_INTERVAL: 0, // 生成间隔设很小，避免一阵一阵的效果
    MAX_PACKETS: 6, // 最大红包数(每屏5-6个)
    SPAWN_PROBABILITY: 1, // 每次生成的概率
  }
};

// 根据平台选择配置
const currentConfig = Platform.OS === 'ios' ? PLATFORM_CONFIG.ios : PLATFORM_CONFIG.android;

/**
 * 管理红包逻辑的hook
 */
export const usePackets = (gameState: GameState, isPaused = false) => {
  const [packets, setPackets] = useState<Packet[]>([]);
  const [score, setScore] = useState(0);
  const [clickedPositions, setClickedPositions] = useState<Array<{ x: number, y: number }>>([]);

  // 使用refs来跟踪动画帧和上次生成红包的时间
  const requestRef = useRef<number | null>(null);
  const lastTimeRef = useRef<number>(0);
  const isGeneratingRef = useRef<boolean>(false);
  const packetsRef = useRef<Packet[]>(packets); // 使用ref跟踪packets，减少不必要的重渲染
  const pendingRemovalRef = useRef<Set<number>>(new Set()); // 跟踪待移除的红包ID
  const batchUpdateInteractionRef = useRef<{ cancel: () => void } | null>(null); // 批量更新交互引用

  // 更新ref值
  useEffect(() => {
    packetsRef.current = packets;
  }, [packets]);

  // 使用批量更新来减少状态更新频率
  const scheduleBatchUpdate = useCallback(() => {
    if (batchUpdateInteractionRef.current) return; // 已经有一个批量更新计划

    batchUpdateInteractionRef.current = InteractionManager.runAfterInteractions(() => {
      if (pendingRemovalRef.current.size > 0) {
        // 直接使用Set过滤，不需要创建中间数组
        setPackets(current => current.filter(p => !pendingRemovalRef.current.has(p.id)));
        pendingRemovalRef.current.clear();
      }
      batchUpdateInteractionRef.current = null;
    });
  }, []);

  // 标记红包需要移除，但不立即更新状态
  const markPacketForRemoval = useCallback((packetId: number) => {
    pendingRemovalRef.current.add(packetId);
    scheduleBatchUpdate();
  }, [scheduleBatchUpdate]);

  // 生成红包的函数
  const generatePackets = useCallback((timestamp: number) => {
    // 第一帧初始化lastTime
    if (!lastTimeRef.current) {
      lastTimeRef.current = timestamp;
    }

    // 计算距离上次生成红包经过的时间
    const elapsed = timestamp - lastTimeRef.current;

    // 尝试生成红包（间隔很小时几乎每帧都会尝试）
    if (elapsed > currentConfig.GENERATION_INTERVAL) {
      // 更新上次生成时间，小幅随机变化，但保持极短间隔
      const randomVariation = Math.random() * 10 - 5; // 小范围变化，避免严格同步
      lastTimeRef.current = timestamp + randomVariation;

      // 使用函数式更新，避免依赖packets状态
      setPackets(prev => {
        // 如果红包数量已达上限，不再生成
        if (prev.length >= currentConfig.MAX_PACKETS) {
          return prev;
        }

        // 控制生成频率，避免即使帧率很高也不会生成太密集
        // 为了避免红包生成太密集，增加一个基于概率的限制
        if (Math.random() < currentConfig.SPAWN_PROBABILITY * 0.15) {
          // 增加散布模式的概率，确保红包在屏幕宽度范围内分布更均匀
          const useScattered = Math.random() < 0.8; // 80%概率使用散布模式
          const packetGroup = createPacketGroup(useScattered);
          
          // 预先设置动画完成回调，使用标记待移除而不是立即更新状态
          packetGroup.forEach(packet => {
            packet.animation?.start(({ finished }) => {
              if (finished) {
                markPacketForRemoval(packet.id);
              }
            });
          });

          return [...prev, ...packetGroup];
        }

        return prev;
      });
    }

    // 如果游戏仍在进行且未暂停，继续请求下一帧
    if (isGeneratingRef.current) {
      requestRef.current = requestAnimationFrame(generatePackets);
    }
  }, [markPacketForRemoval]);

  // 开始生成红包
  const startGenerating = useCallback(() => {
    if (!isGeneratingRef.current) {
      isGeneratingRef.current = true;
      lastTimeRef.current = 0; // 重置上次生成时间
      requestRef.current = requestAnimationFrame(generatePackets);
    }
  }, [generatePackets]);

  // 停止生成红包
  const stopGenerating = useCallback(() => {
    if (isGeneratingRef.current) {
      isGeneratingRef.current = false;
      if (requestRef.current !== null) {
        cancelAnimationFrame(requestRef.current);
        requestRef.current = null;
      }

      // 清理批量更新任务
      if (batchUpdateInteractionRef.current) {
        batchUpdateInteractionRef.current.cancel();
        batchUpdateInteractionRef.current = null;
      }
    }
  }, []);

  // 监听游戏状态变化
  useEffect(() => {
    // 当游戏开始且未暂停时，开始生成红包
    if (gameState === GameState.PLAYING && !isPaused) {
      // 确保UI线程不会被阻塞
      InteractionManager.runAfterInteractions(() => {
        startGenerating();
      });
    } else {
      // 其他情况停止生成红包
      stopGenerating();
    }

    // 当游戏暂停时，停止所有红包动画
    if (isPaused) {
      packetsRef.current.forEach(packet => packet.animation?.stop());
    }

    // 组件卸载时清理资源
    return () => {
      stopGenerating();
    };
  }, [gameState, isPaused, startGenerating, stopGenerating]);

  // 当取消暂停时，恢复所有红包动画
  useEffect(() => {
    if (gameState === GameState.PLAYING && !isPaused && packetsRef.current.length > 0) {
      // 使用InteractionManager确保UI渲染优先
      InteractionManager.runAfterInteractions(() => {
        packetsRef.current.forEach(packet => {
          // 创建新的动画实例，因为之前的可能已经被停止
          packet.animation?.reset();
          packet.animation?.start(({ finished }) => {
            if (finished) {
              markPacketForRemoval(packet.id);
            }
          });
        });
      });
    }
  }, [isPaused, gameState, markPacketForRemoval]);

  // 优化点击处理，使用防抖技术，避免连续点击造成性能问题
  const lastClickTime = useRef(0);
  const CLICK_THROTTLE = 50; // 点击节流时间，毫秒

  // 在handlePacketPress中实现最大同时动画限制
  const MAX_CONCURRENT_ANIMATIONS = 5;

  // 点击红包处理 - 使用防抖和记忆化
  const handlePacketPress = useCallback((packetId: number, event: GestureResponderEvent) => {
    // 节流点击处理
    const now = Date.now();
    if (now - lastClickTime.current < CLICK_THROTTLE) {
      return; // 忽略过于频繁的点击
    }
    lastClickTime.current = now;

    // 如果已暂停，不处理点击
    if (isPaused) return;

    // 从点击事件中获取坐标
    const { pageX, pageY } = event.nativeEvent;

    // 使用InteractionManager确保UI响应优先
    InteractionManager.runAfterInteractions(() => {
      hapticTap();
      // 记录点击，增加分数
      setScore(prev => prev + 1);

      // 限制最大同时存在的点击动画数量
      if (clickedPositions.length >= MAX_CONCURRENT_ANIMATIONS) {
        setClickedPositions(prev => [...prev.slice(1), { x: pageX, y: pageY }]);
      } else {
        setClickedPositions(prev => [...prev, { x: pageX, y: pageY }]);
      }

      // 找到红包并应用消失动画
      const packet = packetsRef.current.find(p => p.id === packetId);
      if (packet) {
        // 停止原有的动画
        if (packet.animation) {
          packet.animation.stop();
        }
        
        // 确保值已重置到初始状态
        packet.animatedScale.setValue(packet.scale);
        packet.animatedOpacity.setValue(packet.opacity);
        
        // 启动消失动画
        if (packet.disappearAnimation) {
          packet.disappearAnimation.start();
        }

        // 在动画完成后延迟移除红包
        setTimeout(() => {
          markPacketForRemoval(packetId);
        }, 300); // 300ms后移除，与动画时长一致
      } else {
        // 如果找不到红包，直接标记移除
        markPacketForRemoval(packetId);
      }

      // 延迟移除点击动画
      setTimeout(() => {
        setClickedPositions(pos => pos.filter(p => p.x !== pageX || p.y !== pageY));
      }, 560); // 缩短动画时间，减少同时存在的动画对象
    });
  }, [isPaused, markPacketForRemoval]);

  // 重置状态
  const resetPackets = useCallback(() => {
    console.log('重置红包状态');
    stopGenerating();

    packetsRef.current.forEach(packet => {
      packet.animation?.stop();
      // 移除所有引用
      packet.animation = undefined;
    });

    // 清理批量更新任务
    if (batchUpdateInteractionRef.current) {
      batchUpdateInteractionRef.current.cancel();
      batchUpdateInteractionRef.current = null;
    }

    pendingRemovalRef.current.clear();
    setPackets([]);
    setScore(0);
    setClickedPositions([]);
  }, [stopGenerating]);

  return {
    packets,
    score,
    clickedPositions,
    handlePacketPress,
    resetPackets
  };
}; 