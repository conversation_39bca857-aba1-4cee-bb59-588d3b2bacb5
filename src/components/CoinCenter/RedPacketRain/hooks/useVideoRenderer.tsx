import React, { useCallback } from 'react';
import { Platform, View } from 'react-native';
import { px } from 'utils/px';
import AlphaVideo from 'components/common/AlphaVideo';
import { GameState } from '../utils';
import { videoSources } from '../sources';
import ClickEffect from '../components/ClickEffect';
import { transform } from '@babel/core';
import Count from '../components/Count';

interface UseVideoRendererProps {
  resources: Array<{
    key: string;
    path: string;
    isReady: boolean;
  }>;
  sources: Record<keyof typeof videoSources, string>;
  styles: Record<string, any>;
  gameState: GameState;
  handleCountVideoLoad: () => void;
  handleCountVideoComplete: () => void;
  handleAwardBgStartComplete: () => void;
}

/**
 * 管理视频渲染的hook
 */
export const useVideoRenderer = ({
  resources,
  sources,
  styles,
  gameState,
  handleCountVideoLoad,
  handleCountVideoComplete,
  handleAwardBgStartComplete
}: UseVideoRendererProps) => {

  // 渲染视频组件，确保资源已加载
  const renderVideo = useCallback((resourceKey: keyof typeof videoSources, props: any = {}) => {
    const resourceInfo = resources.find(resource => resource.key === resourceKey);

    // 支持样式合并，以传入的样式为优先
    const mergedStyle = props.style ? (
      Array.isArray(props.style) ? props.style : [props.style]
    ) : [styles.video];

    // 添加错误处理回调和默认样式
    const enhancedProps = {
      ...props,
      style: mergedStyle, // 使用合并后的样式
      onError: (error: any) => {
        console.error(`视频播放错误 ${resourceKey}:`, error);
        if (props.onError) {
          props.onError(error);
        }
      }
    };

    if (!resourceInfo || !resourceInfo.isReady) {
      // 对于关键视频，如果资源未加载完成但路径存在，仍然尝试渲染
      if (sources[resourceKey]) {
        console.log(`使用原始路径渲染视频: ${resourceKey}`);
        return <AlphaVideo source={sources[resourceKey]} {...enhancedProps} />;
      }
      console.log(`无法渲染视频: ${resourceKey}，资源未准备好`);
      return null;
    }

    return <AlphaVideo source={resourceInfo.path} {...enhancedProps} />;
  }, [resources, sources, styles]);

  // 渲染点击特效
  const renderClickEffects = useCallback((clickedPositions: Array<{ x: number, y: number }>) => {
    // 只显示最新的点击
    return clickedPositions.slice(-1).map((pos, index) => {
      const style = [
        styles.clicked,
        {
          left: pos.x - px(45),
          top: pos.y - px(45),
        }
      ];

      const videoEffect = <ClickEffect
        key={`click-${index}-${pos.x}-${pos.y}`}
        style={style}
      />;

      return videoEffect;
    });
  }, [renderVideo, styles]);

  // 渲染倒计时视频
  const renderCountdown = useCallback(() => {
    // 只在准备阶段或游戏进行中阶段显示倒计时视频，结束阶段不显示
    if (gameState === GameState.END) {
      return null;
    }

    // 渲染倒计时视频，这个视频包含了准备阶段和游戏阶段的完整倒计时
    const countVideo = renderVideo('count', {
      onLoad: handleCountVideoLoad,
      onComplete: handleCountVideoComplete,
      style: styles.countdown
    });

    return <Count
      style={styles.countdown}
      onComplete={handleCountVideoComplete}
      countVideo={Platform.OS === 'ios' ? countVideo : null}
    />;
  }, [gameState, handleCountVideoLoad, handleCountVideoComplete, renderVideo]);

  return {
    renderVideo,
    renderClickEffects,
    renderCountdown
  };
}; 