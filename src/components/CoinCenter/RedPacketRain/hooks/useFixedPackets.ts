import { useState, useEffect, useCallback, useRef } from 'react';
import { GestureResponderEvent, InteractionManager, Dimensions } from 'react-native';
import { GameState, Packet } from '../utils';
import { hapticTap } from 'utils/haptic';
import { Animated } from 'react-native';

// 获取屏幕尺寸
const { width, height } = Dimensions.get('window');

// 红包配置
const STATIC_PACKET_CONFIG = {
  STATIC_PACKET_COUNT: 50, // 增加到50个红包，每屏10个
  PACKET_WIDTH: 86, // 红包宽度 - 与PacketItem样式一致
  PACKET_HEIGHT: 108, // 红包高度 - 与PacketItem样式一致
  SAFETY_MARGIN: 20, // 减小安全间距，确保能放置更多红包
  MIN_ATTEMPTS: 30, // 每个区域最少尝试次数
  MAX_ATTEMPTS: 100, // 最大尝试次数
};

/**
 * 生成一个静态红包
 */
const createStaticPacket = (x: number, y: number): Packet => {
  // 大小和透明度独立随机
  const hasRandomScale = Math.random() < 0.1; // 10%几率有随机大小
  const hasRandomOpacity = Math.random() < 0.1; // 10%几率有随机透明度
  
  // 默认值
  let scale = 1;
  let opacity = 1;
  
  // 随机大小 (0.8-1.5倍)
  if (hasRandomScale) {
    scale = 0.8 + Math.random() * 0.7;
  }
  
  // 随机透明度 (0.7-1)
  if (hasRandomOpacity) {
    opacity = 0.7 + Math.random() * 0.3;
  }

  return {
    id: Date.now() + Math.random(),
    startX: x,
    startY: y,
    endX: x, // 静态红包，起点和终点相同
    endY: y,
    progress: new Animated.Value(0), // 保留但不使用
    scale,
    opacity,
    animatedScale: new Animated.Value(scale),
    animatedOpacity: new Animated.Value(opacity),
  };
};

/**
 * 计算屏幕上不重叠的红包位置 - 完全随机版本
 */
const generateStaticPacketPositions = (count: number) => {
  const positions: Array<{ x: number, y: number }> = [];
  const occupiedAreas: Array<{ x1: number, y1: number, x2: number, y2: number }> = [];
  
  const { PACKET_WIDTH, PACKET_HEIGHT, SAFETY_MARGIN } = STATIC_PACKET_CONFIG;
  
  // 定义可用的屏幕区域，扩展为五屏高度
  const marginX = PACKET_WIDTH / 2; // 最小边距，刚好让红包完全可见
  const marginY = PACKET_HEIGHT / 2; // 最小边距，刚好让红包完全可见
  const safeAreaX1 = marginX;
  const safeAreaY1 = marginY;
  const safeAreaX2 = width - marginX;
  const safeAreaY2 = height * 5 - marginY - 30; // 扩展为五倍屏幕高度
  
  // 计算屏幕可容纳的网格数量（仅用于后备解决方案）
  const availableWidth = safeAreaX2 - safeAreaX1;
  const availableHeight = safeAreaY2 - safeAreaY1;
  
  // 修改区域划分策略 - 适应五屏高度
  const sectionRows = 25; // 增加到25行，对应五屏高度
  const sectionCols = 5; // 保持5列宽度
  const sectionWidth = availableWidth / sectionCols;
  const sectionHeight = availableHeight / sectionRows;
  
  // 调整安全区域，允许红包贴边
  const safeRightBoundary = width - PACKET_WIDTH/2; // 允许红包贴右边缘
  
  // 记录已使用的区域，并确保左右平衡
  const usedSections = new Set<string>();
  
  // 创建左右两侧的区域列表，以便交替选择
  const leftSections = [];
  const rightSections = [];
  
  // 定义屏幕中间线
  const midX = width / 2;
  
  // 将区域分为左右两组
  for (let row = 0; row < sectionRows; row++) {
    for (let col = 0; col < sectionCols; col++) {
      const section = `${row}-${col}`;
      const sectionCenterX = safeAreaX1 + col * sectionWidth + sectionWidth / 2;
      
      if (sectionCenterX < midX) {
        leftSections.push(section);
      } else {
        rightSections.push(section);
      }
    }
  }
  
  // 打乱左右区域顺序，增加随机性
  leftSections.sort(() => Math.random() - 0.5);
  rightSections.sort(() => Math.random() - 0.5);
  
  // 尝试对每个红包生成位置
  for (let i = 0; i < count; i++) {
    let validPosition = false;
    let x = 0, y = 0;
    
    // 第一步：尝试在未使用的区域中随机放置，且保证左右平衡
    if (usedSections.size < sectionRows * sectionCols && Math.random() < 0.99) {
      // 根据偶数/奇数选择左侧或右侧区域，确保左右平衡
      const preferLeft = i % 2 === 0 || leftSections.length > rightSections.length;
      
      // 寻找未使用的区域，优先选择左侧或右侧
      let section = '';
      let row = 0, col = 0;
      
      // 首先尝试获取首选侧的未使用区域
      const primarySections = preferLeft ? leftSections : rightSections;
      const secondarySections = preferLeft ? rightSections : leftSections;
      
      // 从首选区域中寻找未使用的
      for (const sect of primarySections) {
        if (!usedSections.has(sect)) {
          section = sect;
          usedSections.add(section);
          
          // 解析section字符串，获取row和col
          [row, col] = section.split('-').map(Number);
          break;
        }
      }
      
      // 如果首选侧没有可用区域，尝试另一侧
      if (!section && secondarySections.length > 0) {
        for (const sect of secondarySections) {
          if (!usedSections.has(sect)) {
            section = sect;
            usedSections.add(section);
            
            // 解析section字符串，获取row和col
            [row, col] = section.split('-').map(Number);
            break;
          }
        }
      }
      
      // 如果仍未找到区域，使用随机选择（回退到原始逻辑）
      if (!section) {
        const maxAttempts = 20;
        let sectAttempts = 0;
        
        while (sectAttempts < maxAttempts) {
          row = Math.floor(Math.random() * sectionRows);
          col = Math.floor(Math.random() * sectionCols);
          section = `${row}-${col}`;
          
          if (!usedSections.has(section)) {
            usedSections.add(section);
            break;
          }
          sectAttempts++;
        }
      }
      
      // 在选定区域内随机选择位置，允许贴近边缘
      const margin = SAFETY_MARGIN / 3; // 减少内边距，允许更贴近区域边缘
      let minX = safeAreaX1 + col * sectionWidth + margin;
      let maxX = safeAreaX1 + (col + 1) * sectionWidth - margin;
      
      // 防止红包超出屏幕，但允许贴边
      maxX = Math.min(maxX, safeRightBoundary);
      
      // 确保红包不会超出左边缘，但允许贴边
      minX = Math.max(minX, safeAreaX1);
      
      const minY = safeAreaY1 + row * sectionHeight + PACKET_HEIGHT/2 + margin;
      const maxY = safeAreaY1 + (row + 1) * sectionHeight - PACKET_HEIGHT/2 - margin;
      
      // 如果区域太小，则调整
      if (maxX > minX && maxY > minY) {
        // 对于左侧区域，向左倾斜随机位置
        if (col < sectionCols / 2) {
          // 左侧区域更偏向于左边，增加左侧密度
          const leftBias = 0.3; // 30%的倾向性
          x = minX + Math.random() * (maxX - minX) * (1 - leftBias * Math.random());
        } else {
          x = minX + Math.random() * (maxX - minX);
        }
        y = minY + Math.random() * (maxY - minY);
        
        // 检查与现有红包是否重叠
        const packetArea = {
          x1: x - PACKET_WIDTH / 2 - SAFETY_MARGIN/2, // 减小边距计算
          y1: y - PACKET_HEIGHT / 2 - SAFETY_MARGIN/2,
          x2: x + PACKET_WIDTH / 2 + SAFETY_MARGIN/2,
          y2: y + PACKET_HEIGHT / 2 + SAFETY_MARGIN/2
        };
        
        validPosition = true;
        
        for (const area of occupiedAreas) {
          // 检测红包视觉上的重叠，而不是安全区域的重叠
          // 允许安全区域有一定重叠，但不允许视觉区域重叠
          const visualPacket = {
            x1: x - PACKET_WIDTH / 2,
            y1: y - PACKET_HEIGHT / 2,
            x2: x + PACKET_WIDTH / 2,
            y2: y + PACKET_HEIGHT / 2
          };
          
          const visualArea = {
            x1: area.x1 + SAFETY_MARGIN/2,
            y1: area.y1 + SAFETY_MARGIN/2,
            x2: area.x2 - SAFETY_MARGIN/2,
            y2: area.y2 - SAFETY_MARGIN/2
          };
          
          // 检查视觉区域是否重叠
          const noVisualOverlap =
            visualPacket.x2 <= visualArea.x1 ||
            visualPacket.x1 >= visualArea.x2 ||
            visualPacket.y2 <= visualArea.y1 ||
            visualPacket.y1 >= visualArea.y2;
            
          if (!noVisualOverlap) {
            validPosition = false;
            usedSections.delete(section); // 放弃这个区域
            break;
          }
        }
        
        if (validPosition) {
          positions.push({ x, y });
          occupiedAreas.push(packetArea);
          continue; // 成功，处理下一个红包
        }
      }
    }
    
    // 第二步：如果区域策略失败，尝试完全随机位置
    const maxRandomAttempts = STATIC_PACKET_CONFIG.MAX_ATTEMPTS; // 正确引用配置中的MAX_ATTEMPTS
    let attempts = 0;
    
    while (!validPosition && attempts < maxRandomAttempts) {
      attempts++;
      
      // 生成随机位置，改进横向分布
      const leftWeight = Math.random() < 0.6; // 60%概率偏向左侧
      
      // 计算水平位置，改进左右平衡并允许贴边
      let xPosition;
      
      if (leftWeight) {
        // 偏向左侧的位置生成，允许更贴左
        xPosition = Math.random() * (width * 0.5);
        // 有10%几率完全贴左边
        if (Math.random() < 0.1) {
          xPosition = marginX;
        }
      } else {
        // 偏右并允许贴右边
        xPosition = width * 0.5 + Math.random() * (width * 0.5 - marginX);
        // 有10%几率完全贴右边
        if (Math.random() < 0.1) {
          xPosition = width - marginX;
        }
      }
      
      // 添加小幅抖动
      const jitterX = (Math.random() - 0.5) * 25; // 减小抖动幅度，避免完全贴边的红包超出屏幕
      const jitterY = (Math.random() - 0.5) * 40;
      
      x = xPosition + jitterX;
      y = safeAreaY1 + Math.random() * (safeAreaY2 - safeAreaY1) + jitterY;
      
      // 确保不会超出安全边界，但允许贴边
      x = Math.max(safeAreaX1, Math.min(safeRightBoundary, x));
      y = Math.max(safeAreaY1, Math.min(safeAreaY2, y));
      
      // 计算红包区域
      const packetArea = {
        x1: x - PACKET_WIDTH / 2 - SAFETY_MARGIN/2, // 减小边距计算
        y1: y - PACKET_HEIGHT / 2 - SAFETY_MARGIN/2,
        x2: x + PACKET_WIDTH / 2 + SAFETY_MARGIN/2,
        y2: y + PACKET_HEIGHT / 2 + SAFETY_MARGIN/2
      };
      
      // 检查是否与现有红包重叠
      validPosition = true;
      
      for (const area of occupiedAreas) {
        // 检测红包视觉上的重叠，而不是安全区域的重叠
        // 允许安全区域有一定重叠，但不允许视觉区域重叠
        const visualPacket = {
          x1: x - PACKET_WIDTH / 2,
          y1: y - PACKET_HEIGHT / 2,
          x2: x + PACKET_WIDTH / 2,
          y2: y + PACKET_HEIGHT / 2
        };
        
        const visualArea = {
          x1: area.x1 + SAFETY_MARGIN/2,
          y1: area.y1 + SAFETY_MARGIN/2,
          x2: area.x2 - SAFETY_MARGIN/2,
          y2: area.y2 - SAFETY_MARGIN/2
        };
        
        // 检查视觉区域是否重叠
        const noVisualOverlap =
          visualPacket.x2 <= visualArea.x1 ||
          visualPacket.x1 >= visualArea.x2 ||
          visualPacket.y2 <= visualArea.y1 ||
          visualPacket.y1 >= visualArea.y2;
          
        if (!noVisualOverlap) {
          validPosition = false;
          break;
        }
      }
      
      // 找到有效位置
      if (validPosition) {
        positions.push({ x, y });
        occupiedAreas.push(packetArea);
      }
    }
    
    // 第三步：如果仍然失败，跳过此红包，减少总数而不是允许重叠
    if (!validPosition) {
      console.log(`无法为红包 #${i+1} 找到不重叠位置，跳过生成`);
      // 不添加此红包，继续尝试下一个
      continue;
    }
  }
  
  return positions;
};

/**
 * 管理静态红包的hook
 */
export const useFixedPackets = (gameState: GameState) => {
  const [packets, setPackets] = useState<Packet[]>([]);
  const [score, setScore] = useState(0);
  const [clickedPositions, setClickedPositions] = useState<Array<{ x: number, y: number }>>([]);
  const packetsRef = useRef<Packet[]>(packets);
  const pendingRemovalRef = useRef<Set<number>>(new Set());
  const hasGeneratedRef = useRef<boolean>(false);

  // 更新ref值
  useEffect(() => {
    packetsRef.current = packets;
  }, [packets]);

  // 标记红包需要移除
  const markPacketForRemoval = useCallback((packetId: number) => {
    pendingRemovalRef.current.add(packetId);
    
    InteractionManager.runAfterInteractions(() => {
      setPackets(current => current.filter(p => !pendingRemovalRef.current.has(p.id)));
      pendingRemovalRef.current.clear();
    });
  }, []);

  // 一次性生成静态红包
  const generateStaticPackets = useCallback(() => {
    if (hasGeneratedRef.current) return; // 确保只生成一次
    
    // 增加最大尝试次数
    let attempts = 0;
    let positions: Array<{ x: number, y: number }> = [];
    
    // 如果生成的红包数量太少，尝试再次生成
    while (attempts < 3) {
      positions = generateStaticPacketPositions(STATIC_PACKET_CONFIG.STATIC_PACKET_COUNT);
      
      // 如果生成的红包数量足够，或者已尝试多次，则接受当前结果
      if (positions.length >= 12 || attempts >= 2) { // 提高最小接受数量为12个
        break;
      }
      
      attempts++;
    }
    
    const newPackets = positions.map(pos => createStaticPacket(pos.x, pos.y));
    
    // 记录实际生成的红包数量
    console.log(`成功生成 ${positions.length} 个红包，无重叠 (尝试次数: ${attempts + 1})`);
    
    setPackets(newPackets);
    hasGeneratedRef.current = true;
  }, []);

  // 游戏开始时生成红包
  useEffect(() => {
    if (gameState === GameState.PLAYING && !hasGeneratedRef.current) {
      InteractionManager.runAfterInteractions(() => {
        generateStaticPackets();
      });
    } else if (gameState === GameState.PREP) {
      // 游戏准备阶段重置状态
      hasGeneratedRef.current = false;
    }
  }, [gameState, generateStaticPackets]);

  // 添加已点击红包的引用，防止重复点击
  const clickedPacketsRef = useRef<Set<number>>(new Set());
  const lastClickTimeRef = useRef<number>(0);
  const CLICK_THROTTLE = 100; // 点击节流阈值(毫秒)

  // 点击红包处理 - 添加防重复点击机制
  const handlePacketPress = useCallback((packetId: number, event: GestureResponderEvent) => {
    // 检查红包是否已被点击过
    if (clickedPacketsRef.current.has(packetId)) {
      return; // 已点击过，直接忽略
    }
    
    // 点击节流：避免过快点击导致性能问题
    const now = Date.now();
    if (now - lastClickTimeRef.current < CLICK_THROTTLE) {
      return; // 点击过快，忽略本次点击
    }
    lastClickTimeRef.current = now;
    
    // 立即标记为已点击，防止重复处理
    clickedPacketsRef.current.add(packetId);
    
    // 从点击事件中获取坐标
    const { pageX, pageY } = event.nativeEvent;

    InteractionManager.runAfterInteractions(() => {
      hapticTap();
      // 增加分数
      setScore(prev => prev + 1);

      // 添加点击位置用于显示动画
      setClickedPositions(prev => {
        const MAX_ANIMATIONS = 5;
        return prev.length >= MAX_ANIMATIONS
          ? [...prev.slice(1), { x: pageX, y: pageY }]
          : [...prev, { x: pageX, y: pageY }];
      });

      // 找到红包并应用消失动画
      const packet = packetsRef.current.find(p => p.id === packetId);
      if (packet) {
        // 应用消失动画
        Animated.parallel([
          Animated.timing(packet.animatedScale, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(packet.animatedOpacity, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          })
        ]).start();

        // 在动画完成后移除红包
        setTimeout(() => {
          markPacketForRemoval(packetId);
        }, 300);
      }

      // 清理点击动画
      setTimeout(() => {
        setClickedPositions(pos => pos.filter(p => p.x !== pageX || p.y !== pageY));
      }, 560);
    });
  }, [markPacketForRemoval]);

  // 重置状态
  const resetPackets = useCallback(() => {
    hasGeneratedRef.current = false;
    pendingRemovalRef.current.clear();
    clickedPacketsRef.current.clear(); // 清空已点击红包集合
    lastClickTimeRef.current = 0; // 重置点击时间
    setPackets([]);
    setScore(0);
    setClickedPositions([]);
  }, []);

  return {
    packets,
    score,
    clickedPositions,
    handlePacketPress,
    resetPackets
  };
}; 