import { useState, useEffect, useCallback, useRef } from 'react';
import { GameState, VideoPlayState, GAME_CONFIG } from '../utils';
import log from 'utils/log';

interface UseGameStateProps {
  visible: boolean;
  ready: boolean;
  onGameEnd: (score: number) => void;
}

/**
 * 管理游戏状态的hook
 */
export const useGameState = ({ visible, ready, onGameEnd }: UseGameStateProps) => {
  const [gameState, setGameState] = useState<GameState>(GameState.PREP);
  const [videoPlayState, setVideoPlayState] = useState<VideoPlayState>(VideoPlayState.NONE);
  const [countdown, setCountdown] = useState(3);
  const [awardBgShow, setAwardBgShow] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);

  // 添加一个状态记录游戏是否正在进行中
  const isPlayingRef = useRef(false);

  // 定时器引用
  const gameTimerRef = useRef<NodeJS.Timeout | null>(null);
  const animTimerRef = useRef<NodeJS.Timeout | null>(null);
  const countdownTimerRef = useRef<NodeJS.Timeout | null>(null);
  const safetyTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 重置游戏状态
  const resetGameState = useCallback(() => {
    console.log('重置游戏状态');
    setGameState(GameState.PREP);
    setVideoPlayState(VideoPlayState.NONE);
    setCountdown(3);
    setAwardBgShow(false);
    setShowConfetti(false);
    setModalVisible(false);
    isPlayingRef.current = false;

    // 清除所有定时器
    if (gameTimerRef.current) {
      clearTimeout(gameTimerRef.current);
      gameTimerRef.current = null;
    }

    if (animTimerRef.current) {
      clearTimeout(animTimerRef.current);
      animTimerRef.current = null;
    }

    if (countdownTimerRef.current) {
      clearTimeout(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }

    if (safetyTimerRef.current) {
      clearTimeout(safetyTimerRef.current);
      safetyTimerRef.current = null;
    }
  }, []);

  // 当组件可见且资源已加载完成时，开始游戏
  useEffect(() => {
    if (visible && ready && videoPlayState === VideoPlayState.NONE) {
      // 初始化为背景和倒计时状态，但不更改游戏状态，让视频加载时处理
      console.log('资源准备就绪，开始倒计时');
      setVideoPlayState(VideoPlayState.BG_AND_COUNT);
    }
  }, [visible, ready, videoPlayState]);

  // 准备阶段倒计时
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (gameState === GameState.PREP && visible) {
      if (countdown > 0) {
        timer = setTimeout(() => {
          setCountdown(prev => prev - 1);
        }, 1000);
      } else {
        setGameState(GameState.PLAYING);
      }
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [gameState, visible, countdown]);

  // 游戏时间倒计时
  useEffect(() => {
    if (gameState === GameState.PLAYING && visible) {
      // 清除可能存在的旧定时器
      if (gameTimerRef.current) {
        clearTimeout(gameTimerRef.current);
        gameTimerRef.current = null;
      }

      console.log('游戏开始，设置结束计时器');
      isPlayingRef.current = true;

      // 设置游戏结束定时器
      const timer = setTimeout(() => {
        if (visible) { // 确保游戏还在可见状态时才执行
          console.log('游戏结束计时器触发');
          setGameState(GameState.END);
          setVideoPlayState(VideoPlayState.AWARD_BG_START);
          isPlayingRef.current = false;

          // 触发游戏结束回调
          onGameEnd(0); // 实际分数会在调用组件传入
        }
      }, GAME_CONFIG.GAME_DURATION);

      gameTimerRef.current = timer;

      return () => {
        clearTimeout(timer);
        gameTimerRef.current = null;
      };
    }
  }, [gameState, onGameEnd, visible]);

  // 在gameState变为end时处理
  useEffect(() => {
    if (gameState === GameState.END && visible) {
      // 设置结束状态的视频状态
      isPlayingRef.current = false;
      // console.log('游戏状态变为END');
      setVideoPlayState(VideoPlayState.AWARD_BG_START);
    }
  }, [gameState, visible]);

  // 组件卸载或不可见时清除定时器和重置状态
  useEffect(() => {
    if (!visible) {
      console.log('游戏不可见，重置状态');
      // 重置游戏状态
      resetGameState();
    }

    return () => {
      // 组件卸载时清理所有定时器
      resetGameState();
    };
  }, [visible, resetGameState]);

  // 处理倒计时视频加载
  const handleCountVideoLoad = useCallback(() => {
    if (!visible) return; // 如果不可见，不执行后续逻辑

    console.log('倒计时视频加载完成');
    setGameState(GameState.PREP);

    // 做一个安全保障，即使视频的onComplete事件没触发，也能在特定时间后进入游戏结束状态
    const totalDuration = GAME_CONFIG.PREP_DURATION + GAME_CONFIG.GAME_DURATION + 1000; // 额外添加1秒的容错时间

    // 先清除之前可能的定时器
    if (safetyTimerRef.current) {
      clearTimeout(safetyTimerRef.current);
      safetyTimerRef.current = null;
    }

    const timer = setTimeout(() => {
      // 如果此时游戏状态还没到END，强制设为END
      if (gameState !== GameState.END && visible) {
        console.log('视频播放超时保护触发，强制结束游戏');
        setGameState(GameState.END);
        setVideoPlayState(VideoPlayState.AWARD_BG_START);
        isPlayingRef.current = false;
      }
    }, totalDuration);

    safetyTimerRef.current = timer;

  }, [gameState, visible]);

  // 处理倒计时视频完成
  const handleCountVideoComplete = useCallback(() => {
    if (!visible) return; // 如果不可见，不执行后续逻辑

    // 整个视频结束后，游戏结束
    console.log('倒计时视频播放完成，游戏结束');

    // 强制确保结束状态
    setGameState(GameState.END);
    setVideoPlayState(VideoPlayState.AWARD_BG_START);
    isPlayingRef.current = false;

    // 清除可能存在的保护性定时器
    if (safetyTimerRef.current) {
      clearTimeout(safetyTimerRef.current);
      safetyTimerRef.current = null;
    }
  }, [visible]);

  // 处理背景开始动画完成
  const handleAwardBgStartComplete = useCallback(() => {
    if (!visible) return; // 如果不可见，不执行后续逻辑

    // 设置标志位，让循环背景变为可见
    setAwardBgShow(true);
    // 更新视频播放状态
    setVideoPlayState(VideoPlayState.AWARD_BG_LOOP);
  }, [visible]);

  // 处理红包打开
  const handleOpenRedPacket = useCallback(() => {
    if (!visible) return; // 如果不可见，不执行后续逻辑

    setModalVisible(true);

    // 设置视频状态为打开彩带
    setVideoPlayState(VideoPlayState.CONFETTI);

    // 点击后333ms显示confetti动画
    if (animTimerRef.current) {
      clearTimeout(animTimerRef.current);
      animTimerRef.current = null;
    }

    const timer = setTimeout(() => {
      if (visible) {
        setShowConfetti(true);
      }
    }, 333);
    animTimerRef.current = timer;

  }, [visible]);

  // 关闭弹窗
  const handleCloseModal = useCallback(() => {
    setModalVisible(false);
  }, []);

  return {
    gameState,
    videoPlayState,
    countdown,
    awardBgShow,
    showConfetti,
    modalVisible,
    gameTimerRef,
    animTimerRef,
    isPlaying: isPlayingRef.current,
    handleCountVideoLoad,
    handleCountVideoComplete,
    handleAwardBgStartComplete,
    handleOpenRedPacket,
    resetGameState,
    handleCloseModal
  };
}; 