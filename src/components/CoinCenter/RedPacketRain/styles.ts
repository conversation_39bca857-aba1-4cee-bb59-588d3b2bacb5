import { Dimensions, StyleSheet } from 'react-native';
import { px } from 'utils/px';

const videoWidth = Math.min(Dimensions.get('window').width, 420);
const aspectRatio = 544 / 1200;

export const getStyles = () => StyleSheet.create({
  container: {
    zIndex: 9999,
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  closeButton: {
    position: 'absolute',
    right: px(16),
    top: px(44),
    padding: px(4),
    width: px(32),
    height: px(32),
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 9999,
  },
  // 视频样式
  video: {
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  clicked: {
    width: px(120),
    height: px(120),
    position: 'absolute',
    zIndex: 9999,
  },
  // 倒计时视频层
  countdown: {
    zIndex: 1001,
    position: 'absolute',
    width: videoWidth,
    aspectRatio,
    left: '50%',
    top: 0,
    transform: [
      { translateX: -videoWidth / 2 },
    ]
  },
  // 游戏内容容器样式
  contentContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1000,
  },
  packetsContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  score: {
    position: 'absolute',
    top: px(40),
    left: px(20),
    fontSize: px(24),
    color: '#fff',
  },
  packet: {
    position: 'absolute',
    width: px(86),
    height: px(108),
  },
  endContainer: {
    ...StyleSheet.absoluteFillObject,
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1009,
  },
  endPacket: {
    justifyContent: 'center',
    alignItems: 'center',
    height: px(307),
    width: px(241),
  },
  endPacketTop: {
    width: px(241),
    height: px(175),
    position: 'absolute',
    top: 0,
    zIndex: 10,
  },
  endPacketBottom: {
    width: px(241),
    height: px(225),
    position: 'absolute',
    bottom: 0,
  },
  // 加载中样式
  loadingContainer: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    zIndex: 1000,
  },
  loadingText: {
    color: '#ffffff',
    fontSize: px(18),
    fontWeight: 'bold',
  },
  debugText: {
    position: 'absolute',
    bottom: px(180),
    left: px(20),
    fontSize: px(12),
    color: '#fff',
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: px(4),
    zIndex: 1000,
  },
}); 