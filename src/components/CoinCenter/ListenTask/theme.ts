import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  headerBgColor: '#131313',
  tabColor: 'rgba(255, 255, 255, .5)',
  activeTabColor: '#FFFFFF',
  titleIcon: 'https://imagev2.xmcdn.com/storages/48f9-audiofreehighqps/5B/5B/GAqhqKwLsHO8AAAB5gOBpMOZ.png',
  countdownLabelColor: '#8D8D91',
  taskNameColor: '#FFFFFF',
  taskDescColor: '#8D8D91',
  headerBorderColor: '#000000',
  contentBgColor: 'rgba(255, 255, 255, 0.05)',
  titleColor: '#ffffff',
  tagColor: '#352323',
  color: '#FFFFFF',
  amountColor: '#FFFFFF',
  coinBubbleColor: '#622a2a',
  unfinishedAmountColor: '#ffffff',
  claimedOpacity: 0.1,
  tipsColor: '#ffffff',
  unfinishedDotColor: '#363434'
}

const lightTheme = {
  headerBgColor: '#FFFFFF',
  tabColor: 'rgba(36, 0, 0, .5)',
  activeTabColor: '#240000',
  titleIcon: 'https://imagev2.xmcdn.com/storages/ba4a-audiofreehighqps/90/97/GAqhQ6cLsHO8AAAB3gOBpMN9.png',
  countdownLabelColor: '#666666',
  taskNameColor: '#111111',
  taskDescColor: '#999999',
  headerBorderColor: '#F0F0F0',
  contentBgColor: '#FFFFFF',
  titleColor: '#111111',
  tagColor: '#FFDADA',
  amountColor: '#ffffff',
  coinBubbleColor: '#FFF4F6',
  unfinishedAmountColor: '#464646',
  claimedOpacity: 1,
  tipsColor: '#2c2c3c',
  unfinishedDotColor: '#FFEDF0'
}

const valuableTaskThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  console.log('-------------- theme ------------->', theme)
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default valuableTaskThemeAtom;