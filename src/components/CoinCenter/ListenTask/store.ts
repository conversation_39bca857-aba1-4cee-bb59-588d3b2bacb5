import { atom } from 'jotai';
import { queryListenTaskInfo, ListenTaskInfo } from 'services/welfare/listenTask';

// 初始状态
const initialListenTaskInfo: ListenTaskInfo = {
  success: true,
  currentStep: 0,
  stepInfo: [],
  totalCoins: 0,
  listenDuration: 0,
  nextRewardTime: 30,
  title: '',
  btnText: '',
  status: 0 // 添加初始状态
};

// 创建状态原子
export const listenTaskAtom = atom<ListenTaskInfo>(initialListenTaskInfo);

// 写入/更新任务状态的原子
export const writeListenTaskAtom = atom(
  null,
  async (get, set) => {
    const task = get(listenTaskAtom)
    try {
      const response = await queryListenTaskInfo();
      set(listenTaskAtom, {...task, ...response?.data});
    } catch (error) {
      set(listenTaskAtom, {...task, success: false})
      console.error('Failed to update listen task:', error);
    }
  }
);

// 更新听书进度的原子
export const updateListenProgressAtom = atom(
  null,
  async (get, set) => {
    const currentTask = get(listenTaskAtom);
    if (!currentTask) return;

    // 更新听书时长和进度
    const newDuration = currentTask.listenDuration + 1; // 每次增加1秒
    const currentStep = currentTask.currentStep;
    const stepInfo = [...currentTask.stepInfo];

    // 检查是否达到下一个奖励节点
    if (newDuration >= currentTask.nextRewardTime && currentStep < stepInfo.length - 1) {
      stepInfo[currentStep].status = 1; // 更新为待领取状态
      stepInfo[currentStep + 1].status = 0; // 下一个任务初始化为未完成状态
      
      set(listenTaskAtom, {
        ...currentTask,
        currentStep: currentStep + 1,
        stepInfo,
        listenDuration: newDuration,
        nextRewardTime: getNextRewardTime(currentStep + 1)
      });
    } else {
      set(listenTaskAtom, {
        ...currentTask,
        listenDuration: newDuration
      });
    }
  }
);


// 获取下一个奖励所需时间
function getNextRewardTime(stepIndex: number): number {
  const rewardTimes = [30, 60, 300, 600, 900]; // 30秒, 1分钟, 5分钟, 10分钟, 15分钟
  return rewardTimes[stepIndex] || 0;
}

