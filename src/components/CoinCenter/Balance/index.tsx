import React, { useEffect } from "react";
import { View, Text, NativeModules } from "react-native";
import { BetterImage, Touch } from "@xmly/rn-components";
import { getStyles } from "./styles";
import { useAtomValue, useSetAtom } from "jotai";
import balanceThemeStyleAtom from "./theme";
import { balanceAtom } from "atom/welfare";
import { coinsAtom } from 'atom/welfare'
import { Page } from "@xmly/rn-sdk";
import { useNavigation } from "@react-navigation/native";
import { ScrollAnalyticComp } from "@xmly/react-native-page-analytics";
import xmlog from "utilsV2/xmlog";
import getDomainEnvSync from "utilsV2/getEnvSync";
import { API_DEFAULT } from "constantsV2/apiConfig";

const wechatPayIcon = 'https://imagev2.xmcdn.com/storages/23f1-audiofreehighqps/50/36/GKwRIJELsYEfAAAC1AOCIOmO.png';

export default function Balance({ showSignModal }: { showSignModal: boolean }) {
  const theme = useAtomValue(balanceThemeStyleAtom);
  const balance = useAtomValue(balanceAtom);
  const coins = useAtomValue(coinsAtom)
  const styles = getStyles(theme);
  const navigation = useNavigation();

  function withdraw() {
    // 福利中心-金币/现金余额入口  点击事件
    xmlog.click(67699, 'CashBalance', { currPage: 'welfareCenter', title: '现金余额' });
    // Page.start('iting://open?msg_type=94&bundle=rn_gold_coin_withdraw')
    NativeModules.Page.start('iting://rnopen?name=H5&url=' + 'http://' + getDomainEnvSync(API_DEFAULT.test,API_DEFAULT.uat, API_DEFAULT.prod) + 'growth-ssr-speed-welfare-center/page/withdraw?_full_with_transparent_bar=1')
    // window.location.href = 'iting://rnopen?name=H5&url=' + encodeURIComponent(getDomainEnvSync(API_DEFAULT.test,API_DEFAULT.uat, API_DEFAULT.prod ) + '/growth-ssr-speed-welfare-center/page/withdraw?_full_with_transparent_bar=1')
    // NativeModules.Page.start(getDomainEnvSync(API_DEFAULT.test,API_DEFAULT.uat, API_DEFAULT.prod ) + '/growth-ssr-speed-welfare-center/page/withdraw?_full_with_transparent_bar=1')
  }

  function onCoinBalanceClick() {
    // 福利中心-金币/现金余额入口  点击事件
    xmlog.click(67699, 'CoinBalance', { currPage: 'welfareCenter', title: '金币余额' });
    navigation.navigate('CoinDetail');
  }

  function onCoinBalanceShow() {
    // 福利中心-金币/现金余额入口  控件曝光
    xmlog.event(67700, 'slipPage', { currPage: 'welfareCenter', title: '金币余额' });
  }

  function onCashBalanceShow() {
    // 福利中心-金币/现金余额入口  控件曝光
    xmlog.event(67700, 'slipPage', { currPage: 'welfareCenter', title: '现金余额' });
  }

  return (
    <View style={[styles.container, { 
        borderBottomLeftRadius: showSignModal ? 0 : 2 , 
        borderBottomRightRadius: showSignModal ? 0 : 2 
      }]}>
      <ScrollAnalyticComp
        itemKey="CoinBalance"
        onShow={onCoinBalanceShow}
      >
        <View style={styles.balanceItem}>
          <Text style={styles.label}>金币</Text>
          <Touch style={styles.valueWrapper} onPress={onCoinBalanceClick}>
            <Text style={styles.value}>{balance.coins}</Text>
            <Text style={styles.unit}>个</Text>
            <BetterImage
              source={{
                uri: theme.detailIcon
              }}
              key={theme.detailIcon}
              imgHeight={12}
              imgWidth={6}
              style={styles.detailIcon}
            />
          </Touch>
        </View>
      </ScrollAnalyticComp>
      <ScrollAnalyticComp
        itemKey="CashBalance"
        onShow={onCashBalanceShow}
      >
        {balance.enableCash && (
          <View style={styles.balanceItem}>
            <View style={styles.cashTitle}>
              <Text style={styles.label}>现金余额</Text>
              {/* <View style={styles.hint}>
                <BetterImage
                  source={{
                    uri: wechatPayIcon
                  }}
                  key={wechatPayIcon}
                  imgHeight={16}
                  imgWidth={16}
                  style={styles.wechatPayIcon}
                />
                <Text style={styles.cashHint}>可微信提现</Text>
              </View> */}
            </View>
            <Touch style={styles.valueWrapper} onPress={withdraw}>
              <Text style={[styles.value,styles.cashValue]}>{balance.cash.toFixed(2)}</Text>
              <Text style={styles.unit}>元</Text>
              <BetterImage
                source={{
                  uri: theme.detailIcon
                }}
                key={theme.detailIcon}
                imgHeight={12}
                imgWidth={6}
                style={styles.detailIcon}
              />
            </Touch>
          </View>
        )}
      </ScrollAnalyticComp>
    </View>
  );
} 