import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';
import commonThemeAtom from '../common/theme';

export const darkTheme = {
  coinTitleColor: 'rgba(255, 255, 255, .5)',
  coinBalanceColor: '#FFFFFF',
  cashHintColor: 'rgba(255, 255, 255, .3)',
  detailIcon: 'https://imagev2.xmcdn.com/storages/82e4-audiofreehighqps/1B/6C/GKwRIW4LsCbgAAAA4gOBbY_E.png',
  cashHintBgColor: 'rgba(244, 229, 229, 0.05)',
  contentBgColor: 'rgba(255, 255, 255, 0.05)',
}

const lightTheme = {
  coinTitleColor: 'rgba(36, 0, 0, .5)',
  coinBalanceColor: '#240000',
  cashHintColor: 'rgba(36, 0, 0, .3)',
  detailIcon: 'https://imagev2.xmcdn.com/storages/8da2-audiofreehighqps/67/5A/GAqhp50LsCbhAAAA4QOBbY_x.png',
  cashHintBgColor: '#F4E5E5',
  contentBgColor: '#FBF1F2',
}

const balanceThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  const commonTheme = get(commonThemeAtom);
  return {
    ...commonTheme,
    ...(theme === 'dark' ? darkTheme : lightTheme)
  };
})

export default balanceThemeStyleAtom;