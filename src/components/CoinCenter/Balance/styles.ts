import { StyleSheet } from "react-native";
import { darkTheme } from "./theme";
import { px } from "utils/px";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingLeft: px(16),
    backgroundColor: theme.contentBgColor,
    paddingBottom: px(15),
    paddingTop: px(16),
    borderTopLeftRadius: px(2),
    borderTopRightRadius: px(2),
    paddingRight: px(20),
  },
  balanceItem: {
    flex: 1,
  },
  valueWrapper: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  label: {
    fontSize: px(11),
    color: theme.coinTitleColor,
    lineHeight: px(15),
  },
  value: {
    fontSize: px(28),
    color: theme.coinBalanceColor,
    fontFamily: 'XmlyNumber',
    fontWeight: '600',
    lineHeight: px(34),
    marginTop: px(3),
  },
  cashValue: {
    marginTop: px(0),
  },
  unit: {
    fontSize: px(12),
    color: theme.coinBalanceColor,
    lineHeight: px(28),
    marginLeft: px(3),
  },
  detailIcon: {
    width: px(6),
    height: px(12),
    marginLeft: px(3),
    marginBottom: px(8),
  },
  cashTitle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  hint: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: px(2),
    paddingVertical: px(1),
    paddingHorizontal: px(3),
    backgroundColor: theme.cashHintBgColor,
    marginLeft: px(5),
  },
  cashHint: {
    fontSize: px(10),
    color: theme.cashHintColor,
    lineHeight: px(15),
  },
  wechatPayIcon: {
    marginRight: px(2),
    width: px(12),
    height: px(12),
  }
}); 