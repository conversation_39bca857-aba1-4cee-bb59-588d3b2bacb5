import React, { useRef, useEffect } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import Svg, { Circle, Text } from '@xmly/react-native-svg';
import { useAtomValue } from 'jotai'
import { ShareCoinsTaskDayStatus } from 'services/welfare/shareCoinsTask'
import shareCoinsThemeAtom from '../theme'
import { px } from 'utils/px'
import { isAndroid, isIOS } from '@xmly/rn-utils/dist/device'

const SIZE = px(33); // 组件大小
const STROKE_WIDTH = px(2);
const RADIUS = (SIZE - STROKE_WIDTH) / 2;
const CIRCUMFERENCE = px(2 * 3 * 16);

export default function ProgressCircle({ progress = 0.25, segment = 4, status }) {
  // progress: 0~1
  const animated = useRef(new Animated.Value(0)).current;
  const theme = useAtomValue(shareCoinsThemeAtom)
  const styles = getStyles(theme)

  useEffect(() => {
    Animated.timing(animated, {
      toValue: progress,
      duration: 1200,
      useNativeDriver: true,
    }).start();
  }, [progress]);

  // 计算每段的角度
  const segmentAngle = 360 / segment;
  const gapAngle = 18; // 每段之间的间隙角度

  // 生成分段圆环
  const renderSegments = () => {
    let segments = [];
    for (let i = 0; i < segment; i++) {
      const startAngle = -90 - gapAngle/2 + i * segmentAngle; // 从-90度减去一半间隙角度开始
      const endAngle = -90 - gapAngle/2 + (i + 1) * segmentAngle - gapAngle; // 计算结束角度
      const arcLength = (endAngle - startAngle) / 360 * CIRCUMFERENCE;

      segments.push(
        <Circle
          key={i}
          cx={SIZE / 2}
          cy={SIZE / 2}
          r={RADIUS}
          stroke="rgba(255, 68, 68, 0.2)"
          strokeWidth={STROKE_WIDTH}
          strokeDasharray={`${arcLength},${CIRCUMFERENCE - arcLength}`}
          strokeDashoffset={CIRCUMFERENCE * (startAngle / 360)}
          strokeLinecap="round"
          fill="none"
        />
      );
    }
    return segments;
  };

  // 动态高亮当前进度段
  const renderActiveSegment = () => {
    // 只高亮前progress*SEGMENTS段
    const activeCount = Math.ceil(progress * segment);
    let segments = [];
    // 从最高点开始点亮，但段落从右边开始排列
    for (let i = 0; i < activeCount; i++) {
      // 计算实际位置：从右边开始，顺时针排列
      let arcLength
      let startAngle
      let endAngle 
      if(segment <= 2){
        // 当 segment <= 2 时，从最高点开始，但优先激活右边的段落
        const position = segment - 1 - i; // 从右边开始，逆序排列
        startAngle = -90 - gapAngle/2 + position * segmentAngle; // 从-90度减去一半间隙角度开始
        endAngle = -90 - gapAngle/2 + (position + 1) * segmentAngle - gapAngle; // 计算结束角度
        arcLength = (endAngle - startAngle) / 360 * CIRCUMFERENCE;
      } else {
        const position = (segment - 2 - i) % segment; // 从右边开始，逆序排列
        startAngle = -90 - gapAngle/2 + position * segmentAngle; // 从-90度减去一半间隙角度开始
        endAngle = -90 - gapAngle/2 + (position + 1) * segmentAngle - gapAngle; // 计算结束角度
        arcLength = (endAngle - startAngle) / 360 * CIRCUMFERENCE;
      }
      
      segments.push(
        <Circle
          key={i}
          cx={SIZE / 2}
          cy={SIZE / 2}
          r={RADIUS}
          stroke="#FF4444"
          strokeWidth={STROKE_WIDTH}
          strokeDasharray={`${arcLength},${CIRCUMFERENCE - arcLength}`}
          strokeDashoffset={CIRCUMFERENCE * (startAngle / 360)}
          strokeLinecap="round"
          fill="none"
        />
      );
    }
    return segments;
  };

  return (
    <View style={styles.container}>
      <Svg width={px(SIZE)} height={px(SIZE)} >
        {renderSegments()}
        {renderActiveSegment()}
       
          <Circle
            cx={SIZE / 2}      // 圆心x坐标
            cy={SIZE / 2}      // 圆心y坐标
            r={12}       // 半径
            fill={ status === ShareCoinsTaskDayStatus.COMPLEMENT ? 'rgba(255, 68, 68, 0.2)' :'#FF4444'} // 填充红色
          />
          <Text
            x={SIZE / 2 - 7}
            y={SIZE / 2 + 4} // y略微下移以视觉居中
            fontSize="12"
            fill={status === ShareCoinsTaskDayStatus.COMPLEMENT ? '#FF7680' :'#FFFFFF'}
            fontWeight="bold"
            textAnchor="middle" // 水平居中
            fontFamily='XmlyNumber'
          >
            {Math.round(progress * segment)}
          </Text>
          <Text
            x={SIZE / 2 - 1}
            y={SIZE / 2 + 4} // y略微下移以视觉居中
            fontSize="12"
            fill={status === ShareCoinsTaskDayStatus.COMPLEMENT ? '#FF7680' :'#FFD1D4'}
            fontWeight="bold"
            textAnchor="middle" // 水平居中
            fontFamily='XmlyNumber'
          >
            /
          </Text>
          <Text
            x={SIZE / 2 + 5}
            y={SIZE / 2 + 4} // y略微下移以视觉居中
            fontSize="12"
            fill={status === ShareCoinsTaskDayStatus.COMPLEMENT ? '#FF7680' :'#FFD1D4'}
            fontWeight="bold"
            textAnchor="middle" // 水平居中
            fontFamily='XmlyNumber'
          >
            {segment}
          </Text>
      </Svg>
      {/* <View style={styles.text}>
        
      </View> */}
     
      {/* <View style={styles.centerText}> */}
        {/* <Text style={[styles.text, { backgroundColor: status === ShareCoinsTaskDayStatus.COMPLEMENT ? 'rgba(255, 68, 68, 0.2)' :'#FF4444'}]}>
          <Text style={{color: status === ShareCoinsTaskDayStatus.COMPLEMENT ? 'rgba(255, 118, 128, 1)' : '#ffffff'}}>
            {Math.round(progress * segment)}
          </Text>
          <Text style={{color: status === ShareCoinsTaskDayStatus.COMPLEMENT ? '#ff7680' : '#FFD1D4'}}>
            /{segment}
          </Text>
        </Text> */}
      {/* </View> */}
    </View>
  );
}

const getStyles = (theme: any) => StyleSheet.create({
  container: {
    // width: SIZE,
    // height: SIZE,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    // backgroundColor: 'lightblue'
  },
  centerText: {
    position: 'absolute',
    width: SIZE,
    height: SIZE,
    top: '50%',
    left: '50%',
    // marginLeft: px(- SIZE/2),
    // marginTop: px(- SIZE/2),
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    position: 'absolute',
    width: '73%',
    height: '73%',
    top: '50%',
    left: '50%',
    marginLeft: px(- 12),
    marginTop: px(- 12),
    // justifyContent: 'center',
    // alignItems: 'center',

    fontSize: 12,
    display: 'flex',
    color: '#ff7680',
    fontWeight: 'bold',
    backgroundColor: '#FF4444',
    borderRadius: px(12),
    // width: px(24),
    // height: px(24),
    // marginRight: isAndroid ? px(1.5) : px(0.5),
    // marginBottom: isAndroid ? px(1.5) : px(0),
    textAlign: 'center',
    // justifyContent: 'center',
    // alignItems: 'center',
    overflow: 'hidden',
    paddingTop: isAndroid ? px(4) : px(5),
    fontFamily: 'XmlyNumber',
  },
});

