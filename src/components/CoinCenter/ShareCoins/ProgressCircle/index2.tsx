import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

const SIZE = 33; // 外圈直径
const STROKE = 2; // 圆环宽度
const SEGMENTS = 2; // 分段数
const ACTIVE_INDEX = 0; // 当前高亮段（0~3）

const getSegmentStyle = (index, active) => {
  const angle = 360 / SEGMENTS;
  return {
    position: 'absolute',
    width: SIZE,
    height: SIZE,
    transform: [
      { rotate: `${index * angle}deg` }
    ],
    justifyContent: 'center',
    alignItems: 'center',
  };
};

const ProgressCircle = ({ segment = 4, activeIndex = 0 }) => {
  const angle = 360 / segment;
  const gap = 12; // 每段之间的间隙角度
  const arcLength = angle - gap;

  return (
    <View style={styles.container}>
      {/* 外圈分段 */}
      {Array.from({ length: segment }).map((_, i) => (
        <View key={i} style={getSegmentStyle(i, i === activeIndex)}>
          <View
            style={[
              styles.arc,
              {
                borderColor: i === activeIndex ? '#FF4D4F' : '#FFD6D6',
                // 控制每段的长度和间隙
                borderLeftWidth: 0,
                borderRightWidth: 0,
                borderTopWidth: STROKE,
                borderBottomWidth: 0,
                width: SIZE,
                height: SIZE,
                borderRadius: SIZE / 2,
                // 通过裁剪和旋转实现分段
                transform: [
                  { rotate: `${-angle / 2 + gap / 2}deg` },
                  { scaleX: arcLength / 180 }, // 只显示一段弧
                ],
              },
            ]}
          />
        </View>
      ))}

      {/* 中间红色圆 */}
      <View style={styles.centerCircle}>
        <Text style={styles.text}>1/4</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: SIZE,
    height: SIZE,
    justifyContent: 'center',
    alignItems: 'center',
  },
  arc: {
    position: 'absolute',
    borderWidth: STROKE,
    borderColor: '#FFD6D6',
    borderRadius: SIZE / 2,
    width: SIZE,
    height: SIZE,
    backgroundColor: 'transparent',
  },
  centerCircle: {
    position: 'absolute',
    width: SIZE - STROKE * 2,
    height: SIZE - STROKE * 2,
    borderRadius: (SIZE - STROKE * 2) / 2,
    backgroundColor: '#FF4D4F',
    justifyContent: 'center',
    alignItems: 'center',
  },
  text: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 12,
  },
});

export default ProgressCircle;