import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  headerBgColor: '#131313',
  tabColor: 'rgba(255, 255, 255, .5)',
  activeTabColor: '#FFFFFF',
  titleIcon: 'https://imagev2.xmcdn.com/storages/48f9-audiofreehighqps/5B/5B/GAqhqKwLsHO8AAAB5gOBpMOZ.png',
  countdownLabelColor: '#8D8D91',
  taskNameColor: '#FFFFFF',
  taskDescColor: '#8D8D91',
  headerBorderColor: '#000000',
  contentBgColor: 'rgba(255, 255, 255, 0.05)',
  titleColor: '#ffffff',
  tagColor: '#352323',
  color: '#FFFFFF',
  amountColor: '#FFFFFF',
  coinBubbleColor: '#622a2a',
  unfinishedAmountColor: '#ffffff',
  claimedOpacity: 0.1,
  tipsColor: '#ffffff',
  unfinishedDotColor: '#363434',
  subtitleBoldColor: '#D68F63',
  btnLeftBgColor: 'rgba(248, 248, 248, 0.1)',
  btnLeftColor: '#FFFFFF',
  circleBgColor: '#282828',
  circleTextColorOpacity: 0.2,
  circleCheckedBgColor: 'rgba(255, 68, 68, 0.1)',
  stepDotBorderColor: '#1F1F1F',
  stepDotColor: '#282828',
  stepInfoColor: '#8d8d91',
  highlightStepInfoColor: '#ffffff',
  failedTitleColor: '#ffffff',
  circleTextColor: '#E5E6EB',
  nextTextColor: 'rgba(255, 255, 255, 0.3)',
  collapseSubTitleColor: '#8d8d91',
  collapseTitleColor: '#ffffff',
  maskBg: 'https://imagev2.xmcdn.com/storages/2e3d-audiofreehighqps/DF/21/GKwRIDoL-2sAAAArmgOvmraL.jpg',
  blurType: 'regular',
  blurColor: 'rgba(31,31, 31, 0.1)'
}

const lightTheme = {
  headerBgColor: '#FFFFFF',
  tabColor: 'rgba(36, 0, 0, .5)',
  activeTabColor: '#240000',
  titleIcon: 'https://imagev2.xmcdn.com/storages/ba4a-audiofreehighqps/90/97/GAqhQ6cLsHO8AAAB3gOBpMN9.png',
  countdownLabelColor: '#666666',
  taskNameColor: '#111111',
  taskDescColor: '#999999',
  headerBorderColor: '#F0F0F0',
  contentBgColor: '#FFFFFF',
  titleColor: '#240000',
  tagColor: '#FFDADA',
  amountColor: '#ffffff',
  coinBubbleColor: '#FFF4F6',
  unfinishedAmountColor: '#464646',
  claimedOpacity: 1,
  tipsColor: '#2c2c3c',
  unfinishedDotColor: '#FFEDF0',
  subtitleBoldColor: '#BE6D22',
  btnLeftBgColor: 'rgba(248, 248, 248, 1)',
  btnLeftColor: '#111111',
  circleBgColor: '#F6F7FD',
  circleTextColorOpacity: 1,
  circleCheckedBgColor: 'rgba(255, 68, 68, 0.1)',
  stepDotBorderColor: '#ffffff',
  stepDotColor: 'rgba(246, 247, 253, 1)',
  stepInfoColor: '#999999',
  highlightStepInfoColor: '#111111',
  failedTitleColor: '#111111',
  circleTextColor: 'rgba(83, 85, 184, 0.15)',
  nextTextColor: 'rgba(44,44,60,0.3)',
  collapseSubTitleColor: '#999999',
  collapseTitleColor: '#111111',
  maskBg: 'https://imagev2.xmcdn.com/storages/f7a0-audiofreehighqps/8A/FB/GKwRIMAL-2sEAAAokQOvmrl5.jpg',
  blurType: 'light',
  blurColor: 'rgba(255, 255, 255, 0.1)'
}

const shareCoinsThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default shareCoinsThemeAtom;