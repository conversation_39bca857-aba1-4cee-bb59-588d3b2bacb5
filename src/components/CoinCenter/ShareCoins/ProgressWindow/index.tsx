import React, { FC } from 'react'
import { BetterImage } from "@xmly/rn-components";
import { View, Text, TouchableOpacity, Image } from 'react-native'
import { useAtomValue } from 'jotai'
import LinearGradient from 'react-native-linear-gradient'
import { getStyles } from '../styles'
import { DayType, ShareCoinsTaskInfo } from '../store'
import { ShareCoinsTaskDayStatus } from 'services/welfare/shareCoinsTask'
import { BlurView } from '@react-native-community/blur'
import shareCoinsThemeAtom from '../theme'
import ProgressCircle from '../ProgressCircle'
import { px } from 'utils/px'

const markIcon = 'https://imagev2.xmcdn.com/storages/dce5-audiofreehighqps/28/BF/GKwRIJIL-0waAAAD1QOvgabR.png'
const maskBg = 'https://imagev2.xmcdn.com/storages/9427-audiofreehighqps/A8/AA/GKwRIRwL-3RLAAFQwAOvoSr0.png'

interface ProgressWindowProp {
  taskFailed: boolean
  shareConinsTaskInfo: ShareCoinsTaskInfo
  onComplementClick: () => void
  onSignClick: () => void
}

const ProgressWindow: FC<ProgressWindowProp> = ({ taskFailed, shareConinsTaskInfo, onComplementClick, onSignClick }) => {
  const theme = useAtomValue(shareCoinsThemeAtom)
  const styles = getStyles(theme)

  const parseText = (text: string) => {
    const parts = text.split(/(\{\{.*?\}\})/)
    return parts.map((part, index) => {
      if (part.match(/\{\{.*?\}\}/)) {
        const emphasizedText = part.replace(/\{\{|\}\}/g, '')
        return (
          <Text
            key={index}
            style={styles.bold}
          >
            {emphasizedText}
          </Text>
        )
      }
      return <Text key={index}>{part}</Text>
    })
  }

  const renderFailWindow = () => {
    if (taskFailed) {
      return (
        <>
          <BlurView blurType={theme.blurType} blurAmount={6} style={styles.mask} overlayColor={theme.blurColor} />
          {/* <BetterImage
            source={{ uri: maskBg}}
            
            // width={'100%'}
            // height={'100%'}
            style={styles.mask}
            blurRadius={100}
            resizeMode='cover'
          /> */}
          <View style={styles.failedContainer}>
            <Text style={styles.failedTitle}>连签任务未完成，下周继续</Text>
            <Text style={styles.failedSubTitle}>{parseText(shareConinsTaskInfo.subTitle)}</Text>
          </View>
        </>
      )
    }
    return null
  }

  return (
    <View style={styles.progressContainer}>
      {/* 失败状态 */}
      {renderFailWindow()}
      <View style={styles.row}>
        {shareConinsTaskInfo.dayInfo.map((day: DayType, index: number) => {
          const bgColor = day.status === ShareCoinsTaskDayStatus.UNFINISHED ? theme.stepDotColor : 'rgba(255, 68, 68, 1)'
          return (
            <TouchableOpacity style={styles.itemContainer}>
              <View
                key={index}
                style={styles.item}
              >
                {day.status === ShareCoinsTaskDayStatus.DONE && (
                  <View style={styles.circleChecked}>
                    <Image
                      source={{ uri: markIcon }}
                      style={{ width: px(30), height: px(20) }}
                    />
                  </View>
                )}
                {(day.status === ShareCoinsTaskDayStatus.COMPLEMENT || day.status === ShareCoinsTaskDayStatus.PENDING) && (
                  day.totalTimes === day.progress ? <View style={styles.circleChecked}>
                    <Image
                      source={{ uri: markIcon }}
                      style={{ width: px(30), height: px(20) }}
                    />
                  </View> :
                  <TouchableOpacity
                    onPress={() => {
                      if (day.totalTimes !== day.progress) {
                        if (day.status === ShareCoinsTaskDayStatus.COMPLEMENT) {
                          onComplementClick()
                        }
                        if (day.status === ShareCoinsTaskDayStatus.PENDING) {
                          onSignClick()
                        }
                      }
                    }}
                  >
                    {!!day.totalTimes && (
                      <ProgressCircle
                        segment={day.totalTimes}
                        progress={day.progress as number / day.totalTimes}
                        status={day.status}
                      />
                    )}
                  </TouchableOpacity>
                )}
                {day.status === ShareCoinsTaskDayStatus.UNFINISHED && (
                  <View style={styles.circleUnfinished}>
                    <Text style={styles.circleText}>{day.text}</Text>
                  </View>
                )}
              </View>
              <View style={styles.stepProgress}>
                <View style={[styles.stepDot, { backgroundColor: bgColor }]} />
                {index === 0 ? (
                  <LinearGradient
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    colors={['transparent', 'transparent', bgColor, bgColor]}
                    locations={[0, 0.5, 0.5, 1]}
                    style={styles.stepLine}
                  />
                ) : index === shareConinsTaskInfo.dayInfo.length - 1 ? (
                  <LinearGradient
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                    colors={[bgColor, bgColor, 'transparent', 'transparent']}
                    locations={[0, 0.5, 0.5, 1]}
                    style={styles.stepLine}
                  />
                ) : (
                  <View
                    style={[
                      styles.stepLine,
                      {
                        backgroundColor: bgColor,
                      },
                    ]}
                  />
                )}
              </View>
              <View style={styles.stepInfo}>
                <Text
                  style={[
                    styles.stepTime,
                    {
                      color:
                        (day.status === ShareCoinsTaskDayStatus.COMPLEMENT || day.status === ShareCoinsTaskDayStatus.PENDING) && day.progress !== day.totalTimes ? theme.highlightStepInfoColor : theme.stepInfoColor,
                    },
                  ]}
                >
                  {day.label}
                </Text>
              </View>
            </TouchableOpacity>
          )
        })}
      </View>
    </View>
  )
}

export default ProgressWindow
