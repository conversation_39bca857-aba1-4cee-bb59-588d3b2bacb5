import React, { useEffect, useState, useCallback } from "react";
import { View, Text, Platform, PixelRatio, Image } from "react-native";
import Modal from "./Modal";
import useThrottleCallback from 'hooks/useThrottleCallback';
import { Touch } from "@xmly/rn-components";
import { getStyles } from "./styles";
import { useAtomValue, useSetAtom } from "jotai";
import signInThemeStyleAtom from "./theme";
import LinearGradient from "react-native-linear-gradient";
import useRewardGoldCoin from 'hooks/useRewardGoldCoin';
import { FallbackReqType, AD_SOURCE, RewardType } from "constants/ad";
import { signInTaskAtom, writeSignInTaskAtom } from "../SignIn/store";
import { SignInStatus } from "services/welfare";
import { Toast } from "@xmly/rn-sdk";
import xmlog from "utilsV2/xmlog";
// import { Svga } from "components/Svga";
import NativeInspireAdView from '../../common/InspireAdView/index';
import bg_credit_reward from 'appImagesV2/bg_credit_reward';
import {bg_ribbon_left, bg_ribbon_right} from 'appImagesV2/bg_credit_reward';
import { NativeEventEmitter, NativeModules } from 'react-native';
import { px } from 'utils/px';
import LottieView from 'lottie-react-native';

const {InspireAd} = NativeModules;

export default function AdPopWindow({ adHeight, setAdHeight }: { adHeight: number | undefined, setAdHeight: (height: number | undefined) => void }) {
  const theme = useAtomValue(signInThemeStyleAtom);
  const styles = getStyles(theme);
  const signInTask = useAtomValue(signInTaskAtom);
  const { awardInfo = [] } = signInTask || {};
  const querySignInTask = useSetAtom(writeSignInTaskAtom);
  const rewardGoldCoinHook = useRewardGoldCoin();

  // 直接在组件中使用 updateWelfareAtom 和 useDebounceUpdate

  const todayIndex = awardInfo.findIndex(item => item.today);
  const todayAward = awardInfo[todayIndex];

  // 签到弹窗状态
  const [showAdModal, setShowAdModal] = useState(true);
  const [firstIncompleteSignStatus, setFirstIncompleteSignStatus] = useState<undefined | boolean>(undefined);
  
//   const firstIncompleteSignStatus = [SignInStatus.INCOMPLETE].includes(todayAward?.status);
  
  
  // const [nativeAdHeight, setNativeAdHeight] = useState(0);

  // 添加加载状态
  const [isLoading, setIsLoading] = useState(true);
  // 记录用户是否点击广告
  const [adClicked, setAdClicked] = useState(false);

  // 添加奖励提示状态
  const [showRewardToast, setShowRewardToast] = useState(false);

  // 添加高度转换函数
  const convertNativeHeightToRN = (nativeHeight: number) => {
    if (Platform.OS === 'ios') {
      // iOS: points 到 pixels 的转换
      // 1. 获取设备的像素密度比
      const pixelRatio = PixelRatio.get();
      // 2. 将 points 转换为 pixels
      const pixels = nativeHeight / pixelRatio;
      console.log('pixelRatio=', pixelRatio, ', nativeHeight=', nativeHeight, ', pixels=', pixels);
      // 3. 将 pixels 转换为 React Native 的 px
      // return px(pixels);
      return pixels + 20;
    } else {
      // Android: pixels 到 dp 的转换
      const pixelRatio = PixelRatio.get();
      // 将像素转换为 dp
      const dpHeight = nativeHeight / pixelRatio;
      // 将 dp 转换为 px
      return px(dpHeight);
    }
  };

  function clickReport(btnText: string) {
    // 福利中心-签到模块  点击事件
    xmlog.click(68285, 'SignInTask', { currPage: 'welfareCenter', dialogType: '签到弹窗', dialogTitle: '签到弹窗广告点击', Item: btnText });
  }

  function showReport(item: '签到弹窗') {
    // 福利中心-签到模块  控件曝光
    xmlog.event(68286, 'dialogView', { currPage: 'welfareCenter', dialogType: '签到弹窗', dialogTitle: '签到弹窗曝光', item });
  }

  const handleAutoSignIn = useThrottleCallback(async () => {
    // clickReport(signInBtnText);
    await rewardGoldCoinHook({
      rewardType: RewardType.SIGN_IN,
      coins: todayAward?.award,
      sourceName: AD_SOURCE.SIGN_IN_WAKE_UP,
      fallbackReq: FallbackReqType.NORMAL,
    });
    querySignInTask();
  });

  const handleRewardSuccess = useThrottleCallback(async (adId: number, adResponseId: number, rewardCoin: number = 0, ecpm: string = "", encryptType: string = "1") => {
    try {
      // 1. 检查数据是否存在
      if (!rewardCoin) {
        console.log('handleRewardSuccess rewardCoin is 0');
        return;
      }

      console.log('广告奖励成功:', adId, adResponseId, ecpm, encryptType, "rewardCoin=", rewardCoin);
      
      // 2. 发放奖励
      await rewardGoldCoinHook({
        rewardType: RewardType.AD_SIGN_IN,
        coins: rewardCoin,
        sourceName: AD_SOURCE.SIGN_IN_WAKE_UP,
        adId,
        adResponseId,
        encryptType,
        ecpm,
        fallbackReq: FallbackReqType.NORMAL,
      });

      // 3. 更新签到状态
      await querySignInTask();
      
      // 4. 更新UI状态
      setShowAdModal(false);
      setShowRewardToast(true);
    } catch (error) {
      console.error('处理广告奖励失败:', error);
      Toast.info('领取奖励失败，请稍后重试');
    }
  });

  // 添加事件监听
  useEffect(() => {
    // 检查 InspireAd 模块是否存在
    if (!NativeModules.InspireAd) {
      setShowAdModal(false);
      setShowRewardToast(false);
      return;
    }

    try {
      // 创建事件发射器
      const eventEmitter = new NativeEventEmitter(InspireAd);
      
      // 监听奖励成功事件
      const rewardSuccessSubscription = eventEmitter?.addListener(
        'onRewardSuccess',
        (event) => {
          console.log('收到原生奖励成功事件:', event);
          setShowAdModal(false);
          setShowRewardToast(true);
          handleRewardSuccess(event.adId, event.adResponseId, event.rewardCoin, event.ecpm, event.encryptType);
        }
      );

      // 监听奖励失败事件
      const rewardFailSubscription = eventEmitter?.addListener(
        'onRewardFail',
        (event) => {
          console.log('收到原生奖励失败事件:', event);
          setShowAdModal(true);
          setShowRewardToast(false);
        }
      );

      // 清理订阅
      return () => {
        console.log('清理订阅');
        rewardSuccessSubscription?.remove();
        rewardFailSubscription?.remove();
      };
    } catch (error) {
      console.error('设置事件监听器失败:', error);
      // 降级处理：直接显示奖励提示
      setShowAdModal(false);
      setShowRewardToast(false);
      return () => {};
    }
  }, []);

  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       await querySignInTask();
  //     } catch (error) {
  //       console.error('获取签到数据失败:', error);
  //       // setIsLoading(false);
  //     }
  //   };
    
  //   fetchData();
  // }, []);

  // 监听 signInTask 变化
    useEffect(() => {
    if (signInTask) {
      const awardInfo = signInTask.awardInfo || [];
      const todayAward = awardInfo.find(item => item.today);
      
      if (todayAward && firstIncompleteSignStatus === undefined) {
        setFirstIncompleteSignStatus(
          [SignInStatus.INCOMPLETE].includes(todayAward.status)
        );
        setIsLoading(false);
      }
    }
  }, [signInTask]);

    // 处理广告点击
    const handleAdClick = useCallback(() => {
        setAdClicked(true);
        clickReport('签到弹窗');
      }, []);
    
      // 处理高度变化
    const handleHeightChange = useCallback((e: any) => {
    try {
        const nativeHeight = e.nativeEvent?.height + 200;

          console.log('isay_------- nativeHeight ------->' ,nativeHeight)

        if (typeof nativeHeight === 'number' && nativeHeight > 0) {
          handleAutoSignIn();
          
          setAdHeight(nativeHeight);
          setShowAdModal(true);
          setShowRewardToast(false);
          console.log('handleHeightChange setAdHeight', nativeHeight);
          
          showReport('签到弹窗');
        } else {
          setAdHeight(0);
          setShowAdModal(false);
        }
    } catch (error) {
        console.error('处理高度变化错误:', error);
        setAdHeight(0);
        setShowAdModal(false);
    }
    }, [handleAutoSignIn]);
  

  // 渲染奖励提示
  const renderRewardToast = () => {
    if (!showRewardToast) return null;
    return (
          <View style={styles.rewardToastContent}>
            <Touch 
              style={styles.closeButton} 
              onPress={() => {
                console.log('closeRewardToast==========');
                setShowRewardToast(false);
                if (!adClicked && NativeModules.InspireAd) {
                  try {
                    NativeModules.InspireAd.onAdClose();
                  } catch (error) {
                    console.error('调用 onAdClose 失败:', error);
                  }
                }
              }}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Image
                source={theme.iconClose}
                height={16}
                width={16}
                style={styles.closeIcon}
              />
            </Touch>
            <LinearGradient
              colors={['rgba(226, 160, 160, 0.2)', 'rgba(226, 160, 160, 0)']}
              style={styles.windowGradient}
            />
            <LottieView
                source={bg_ribbon_left}
                autoPlay
                loop={false}
                style={styles.toastRibbonLeft}
            />
            <View style={styles.rewardToastTitleContainer}>
              <Text style={styles.rewardToastTitle}>
                恭喜获得
              </Text>
              <View style={styles.rewardToastSubTitleContainer}>
                <Text style={styles.rewardToastLeftSubTitle}>
                    {todayAward?.upgradeAward} 
                </Text>
                <Text style={styles.rewardToastRightSubTitle}>
                    金币
                </Text>
              </View>
            </View>
            <LottieView
                source={bg_ribbon_right}
                autoPlay
                loop={false}
                style={styles.toastRibbonRight}
            />
            <Touch 
              style={styles.rewardToastButton}
              onPress={() => setShowRewardToast(false)}
            >
              <Text style={styles.rewardToastButtonText}>我知道了</Text>
            </Touch>
          </View>
    );
  };
  

// 渲染广告弹窗
const renderAdModal = () => {
    console.log('isay_ 拦截判断', isLoading);
    if (isLoading) {
        return null;
    }

    if (!showAdModal) {
       // return null;
    }

    if (!signInTask?.enableWakeUp) {
        return null;
    }

    if (firstIncompleteSignStatus === undefined) {
        return null;
    }

    if (!firstIncompleteSignStatus) {
      setShowAdModal(false);
      setShowRewardToast(false);
      return null;
    }

      console.log('isay_ 成功 准备展示 ',true);
    return (
        <View style={[
            styles.modalContent,
            adHeight && adHeight > 0 ? styles.modalContentVisible : styles.modalContentInitial
          ]}>
            <Touch 
              style={styles.closeButton} 
              onPress={() => {
                setShowAdModal(false)
                if (!adClicked) {
                    InspireAd.onAdClose()
                }
            }}
              hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
            >
              <Image
                source={theme.iconClose}
                height={16}
                width={16}
                style={styles.closeIcon}
              />
            </Touch>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                签到成功！已获得{' '}
                <Text style={styles.coinText}>{todayAward?.award}</Text>
                {' '}金币
              </Text>
            </View>
            <View style={styles.adRewardContainer}>
                <LottieView
                    source={bg_ribbon_left}
                    autoPlay
                    loop={false}
                    style={styles.ribbonLeft}
                />
                <View style={styles.rewardContainer}>
                    <Image
                        source={bg_credit_reward}
                        style={styles.rewardBg}
                    />
                    <View style={styles.rewardTextContainer}>
                        <View style={styles.rewardItem}>
                        <Text style={styles.rewardTextLeft}>已获取</Text>
                        <Text style={styles.rewardTextLeft}>{todayAward?.award}金币</Text>
                        </View>
                        <View style={styles.rewardItem}>
                        <Text style={styles.rewardTextRight}>看广告领</Text>
                        <Text style={styles.rewardTextRight}>{todayAward?.award + todayAward?.upgradeAward}金币</Text>
                        </View>
                    </View>
                </View>
                <LottieView
                    source={bg_ribbon_right}
                    autoPlay
                    loop={false}
                    style={styles.ribbonRight}
                />
            </View>
            <View style={[styles.adContainer, { 
              height: adHeight > 0 ? convertNativeHeightToRN(adHeight) : 0,
            }]}>
              <NativeInspireAdView
                style={[styles.adView]}
                positionName='lite_incentive_welfare_inspire'
                // positionName="fuliyehuanduanguanggao"
                // TODO: 极速版广告位福利页唤端 id 原308
                slotId={310}
                sourceName={JSON.stringify({"SourceName": AD_SOURCE.SIGN_IN_WAKE_UP})}
                rewardCoin={todayAward?.upgradeAward}
                onAdClick={handleAdClick}
                onHeightChange={handleHeightChange}
              />
            </View>
          </View>
    );
  };

  // 处理关闭按钮点击
  const handleClose = useCallback(() => {
    setShowAdModal(false);
    setShowRewardToast(false);
    if (!adClicked && NativeModules.InspireAd) {
      try {
        NativeModules.InspireAd.onAdClose();
      } catch (error) {
        console.error('调用 onAdClose 失败:', error);
      }
    }
  }, [adClicked]);

  const enableSignInWakeUp = signInTask?.enableWakeUp ?? true;

  return (<Modal
        visible={enableSignInWakeUp && (showAdModal || showRewardToast)}
        overlayVisible={adHeight !== 0 }
        onClose={handleClose}
      >
        {renderRewardToast()}
        {renderAdModal()}
      </Modal>
  );

}