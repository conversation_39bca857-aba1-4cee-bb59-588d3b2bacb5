import { Touch } from "@xmly/rn-components";
import BackBtn from "componentsV2/common/BackBtn";
import React, { useCallback } from "react";
import { Animated, NativeModules, View, Text, LayoutChangeEvent } from "react-native";
import { useAtomValue } from 'jotai';
import { scrollValueAtom } from '../../../pages/CoinCenter/store/scroll';
import { getStyles } from './style';
import headerThemeAtom from './theme';
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { isAndroid } from "@xmly/rn-utils";

interface Props {
  onTabPress: () => void;
  onLayout?: (height: number) => void;
  singleTab?: boolean;
  hideTitle?: string;
}

export default function Header({ onTabPress, onLayout, singleTab, hideTitle }: Props) {
  const theme = useAtomValue(headerThemeAtom);
  const styles = getStyles(theme);
  const scrollValue = useAtomValue(scrollValueAtom);
  const safeArea = useSafeAreaInsets()
  const paddingTop = isAndroid ? 52 : safeArea.top
  const handleBack = useCallback(async function () {
    try {
      // await EntrancePromptConfirm(channelName || srcChannel || '')
      NativeModules.Page.finish(NativeModules.Page.OK, '')
    } catch (err) { }
  }, [])

  const handleLayout = useCallback((event: LayoutChangeEvent) => {
    const { height } = event.nativeEvent.layout;
    onLayout?.(height);
  }, [onLayout]);

  return (
    <>
      <View
        style={[styles.header, {
          paddingTop,
          height: isAndroid ? undefined : 44 + paddingTop,
          paddingBottom: isAndroid ? 16 : 0,
        }]}
        onLayout={handleLayout}
      >
        <Animated.View
          style={[
            styles.headerBg,
            {
              opacity: scrollValue.interpolate({
                inputRange: [0, 100],
                outputRange: [0, 1],
                extrapolate: 'clamp'
              })
            }
          ]}
        />
        {hideTitle ? null : <BackBtn onPress={handleBack} />}
        <View style={[styles.tabs, { opacity: hideTitle ? 0 : 1 }]}>
          {singleTab ? <Text style={styles.activeTab}>福利中心</Text> : <>
            <Text style={styles.activeTab}>福利中心</Text>
            <Touch onPress={onTabPress}>
              <Text style={styles.tab}>积分中心</Text>
            </Touch>
          </>}
        </View>
      </View>
    </>
  );
}