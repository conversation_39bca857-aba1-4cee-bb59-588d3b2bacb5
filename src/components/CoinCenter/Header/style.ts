import { Platform, StyleSheet } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: px(16),
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0)',
    opacity: 1,
  },
  headerBg: {
    backgroundColor: theme.headerBgColor,
    position: 'absolute',
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabs: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tab: {
    fontSize: px(15),
    color: theme.tabColor,
    marginLeft: px(60),
    fontFamily: 'PingFang SC, sans-serif-light',
  },
  activeTab: {
    fontSize: px(16),
    fontWeight: Platform.select({ ios: '600', android: 'bold' }),
    color: theme.activeTabColor,
    fontFamily: 'PingFang SC',
  },
});