import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  headerBgColor: '#131313',
  tabColor: 'rgba(255, 255, 255, .55)',
  activeTabColor: '#FFFFFF',
}

const lightTheme = {
  headerBgColor: '#FFFFFF',
  tabColor: 'rgba(36, 0, 0, 0.55)',
  activeTabColor: '#240000',
}

const headerThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default headerThemeAtom;