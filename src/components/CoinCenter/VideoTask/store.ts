import { atom } from 'jotai';
import { queryAdTask } from 'services/welfare';

export interface VideoTaskInfo {
  responseId: number;
  list: {
    coins: number;
    status: number;
  }[];
}

export const videoTaskAtom = atom<VideoTaskInfo | null>(null);

export const writeVideoTaskAtom = atom(
  null,
  async (get, set) => {
    try {
      const response = await queryAdTask();
      if (response?.ret === 0 && response.data.success) {
        set(videoTaskAtom, {
          responseId: response.responseId!,
          list: response.data.list
        });
      }
    } catch (error) {
      console.error('Failed to fetch video tasks:', error);
    }
  }
);

export const updateVideoTaskAtom = atom(
  null,
  (get, set, taskIndex: number) => {
    const currentTasks = get(videoTaskAtom);
    if (!currentTasks) return;

    const updatedList = [...currentTasks.list];
    if (updatedList[taskIndex]) {
      updatedList[taskIndex] = {
        ...updatedList[taskIndex],
        status: 1 // 标记为已完成
      };
    }

    set(videoTask<PERSON>tom, {
      ...currentTasks,
      list: updatedList
    });
  }
); 