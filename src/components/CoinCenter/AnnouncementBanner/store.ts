import { atom } from 'jotai';
import { queryAnnouncement } from 'services/welfare/announcement';
import userInfoDetail from 'modulesV2/userInfoDetail';

interface AnnoncementInfo {
    valid: boolean;
    content: string;
    link?: string | null | undefined;
}

export const announcementInfoAtom = atom<AnnoncementInfo | null>(null);


export const writeannouncementInfoAtom = atom(
    null,
    async (get, set) => {
      const uid = userInfoDetail.getDetail().uid || -1
      try {
        const response = await queryAnnouncement(uid);
        console.log('writeannouncementInfoAtom response::::::', response);
        if (response?.ret === 0 && response.data.success) {
          set(announcementInfoAtom, {
            valid: response.data.valid,
            content: response.data.content,
            link: response.data.link || ''
          });
        }
      } catch (error) {
        console.error('Failed to fetch announcement:', error);
      }
    }
  );