import { themeAtom } from '../../../atom/theme';
import { atom } from 'jotai';
import icon_megaphone, { banner_close_dark, banner_close_light } from '../../../appImagesV2/icon_announcement_banner';

export const darkTheme = {
  titleColor: '#FFFFFF',
  icon_megaphone,
  icon_close: banner_close_dark
}

export const lightTheme = {
  titleColor: '#240000',  
  icon_megaphone,
  icon_close: banner_close_light
}

const announcementBannerThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default announcementBannerThemeStyleAtom;