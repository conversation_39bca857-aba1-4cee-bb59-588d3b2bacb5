import React, { useState, useEffect } from 'react';
import { View, TouchableOpacity, Image, Linking } from 'react-native';
import { useAtomValue, useSetAtom } from 'jotai';
import announcementBannerThemeStyleAtom from './theme';
import getStyles from './styles';
import { announcementInfoAtom, writeannouncementInfoAtom } from './store';
import icon_megaphone from '../../../appImagesV2/icon_announcement_banner';
import TextTicker from 'react-native-text-ticker';

export default function AnnouncementBanner() {
  const theme = useAtomValue(announcementBannerThemeStyleAtom);
  const announcementInfo = useAtomValue(announcementInfoAtom);
  const queryAnnouncementInfo = useSetAtom(writeannouncementInfoAtom);
  const styles = getStyles(theme);
  const [visible, setVisible] = useState(true);
  const [containerWidth, setContainerWidth] = useState(0);

  const handleClose = () => {
    setVisible(false);
  }

  useEffect(() => {
    queryAnnouncementInfo();
  }, []);

  // 计算跑马灯滚动时长
  const textLength = announcementInfo?.content?.length || 0;
  const baseCount = 20;
  const baseDuration = 10000; // 10秒
  const extraPerCount = 5;
  const extraPerDuration = 2000; // 2秒

  const extra = textLength > baseCount
    ? Math.ceil((textLength - baseCount) / extraPerCount) * extraPerDuration
    : 0;
  const duration = baseDuration + extra;

  return (
    announcementInfo?.valid && visible ? (
      <View
        style={styles.container}
        onLayout={e => setContainerWidth(e.nativeEvent.layout.width)}
      >
        <Image
          source={icon_megaphone}
          style={styles.icon}
          resizeMode="contain"
        />
        <TouchableOpacity
          activeOpacity={0.8}
          onPress={() => {
            if (announcementInfo.link) {
              Linking.openURL(announcementInfo.link);
            }
          }}
          style={[styles.titleContainer, { width: containerWidth - 80 }]}
        >
          <TextTicker
            style={[styles.title, { width: containerWidth - 80 }]}
            duration={duration}
            loop
            bounce={false}
            repeatSpacer={50}
            marqueeDelay={2000}
          >
            {announcementInfo.content}
          </TextTicker>
        </TouchableOpacity>
        {/* <MarqueeText content={announcementInfo.content} /> */}
        {/* <MarqueeHtml htmlContent={announcementInfo.content} style={styles.webViewTitle}/> */}
        {/* <MarqueeMarkdown markdown={announcementInfo.content} style={styles.webViewTitle} contentStyle={styles.webViewTitle}/> */}
        <TouchableOpacity onPress={handleClose} activeOpacity={0.8}>
          <Image
            source={theme.icon_close}
            style={styles.closeIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
      </View>
    ) : null
  );
}