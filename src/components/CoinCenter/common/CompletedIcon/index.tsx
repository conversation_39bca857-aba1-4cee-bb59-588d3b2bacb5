import React from "react";
import { BetterImage } from "@xmly/rn-components";
import { ImageStyle, StyleProp } from "react-native";
import { themeAtom } from "atom/theme";
import { useAtomValue } from "jotai";

interface CompletedIconProps {
  size?: number;
  style?: StyleProp<ImageStyle>;
}

const COMPLETED_ICON = 'https://imagev2.xmcdn.com/storages/27a7-audiofreehighqps/F3/36/GKwRIasLsXGzAAAC0QOCGWWI.png';
const COMPLETED_ICON_DARK = 'https://imagev2.xmcdn.com/storages/0774-audiofreehighqps/E1/ED/GKwRIUELwVD-AAAC3AOMlX0p.png';

export default function CompletedIcon({ size = 16, style }: CompletedIconProps) {
  const theme = useAtomValue(themeAtom);
  const icon = theme === 'dark' ? COMPLETED_ICON_DARK : COMPLETED_ICON;
  
  return (
    <BetterImage
      source={{ uri: icon }}
      key={icon}
      imgHeight={size}
      imgWidth={size}
      style={style as any}
    />
  );
} 