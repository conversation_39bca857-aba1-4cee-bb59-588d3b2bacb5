import React from 'react';
import { View, TouchableWithoutFeedback } from 'react-native';
import CloseIcon from 'componentsV2/common/CloseIcon';
import { styles } from './styles';

interface CloseButtonProps {
  onPress: () => void;
  size?: number;
  color?: string;
}

const CloseButton: React.FC<CloseButtonProps> = ({
  onPress,
  size = 16,
  color = '#FFFFFF',
}) => {
  return (
    <TouchableWithoutFeedback onPress={onPress}>
      <View style={styles.closeButton}>
        <CloseIcon size={size} color={color} />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default CloseButton; 