import React from "react";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import titleThemeAtom from "./theme";
import { StyleProp, TextStyle, Text } from "react-native";
interface TitleProps {
  title: string;
  style?: StyleProp<TextStyle>;
}

export default function Title({ title, style }: TitleProps) {
  const theme = useAtomValue(titleThemeAtom);
  const styles = getStyles(theme);

  return <Text style={[styles.title, style]}>{title}</Text>;
} 