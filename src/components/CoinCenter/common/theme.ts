import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  contentBgColor: 'rgba(255, 255, 255, 0.05)',
  headerBorderColor: '#000000',
  titleColor: '#8D8D91',
}

const lightTheme = {
  contentBgColor: '#FFFFFF',
  headerBorderColor: '#F0F0F0',
  titleColor: '#666666',
}

const commonThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default commonThemeAtom; 