import { StyleSheet, Platform } from 'react-native';
import { px } from 'utils/px';

export const getStyles = () => StyleSheet.create({
  confirmButton: {
    width: px(175),
    height: px(44),
    borderRadius: px(22),
    overflow: 'hidden',
  },
  confirmButtonBg: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: px(22),
  },
  buttonText: {
    fontSize: px(16),
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    fontFamily: 'PingFang SC',
    color: '#FFF',
  },
}); 