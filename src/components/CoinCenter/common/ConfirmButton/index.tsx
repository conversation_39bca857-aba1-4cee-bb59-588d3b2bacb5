import React from 'react';
import { Text, StyleProp, ViewStyle, TextStyle } from 'react-native';
import { Touch } from '@xmly/rn-components';
import LinearGradient from 'react-native-linear-gradient';
import { getStyles } from './styles';

interface ConfirmButtonProps {
  /**
   * 按钮文本
   */
  text: string;
  /**
   * 按钮点击回调
   */
  onPress: () => void;
  /**
   * 按钮样式
   */
  style?: StyleProp<ViewStyle>;
  /**
   * 文本样式
   */
  textStyle?: StyleProp<TextStyle>;
  /**
   * 渐变颜色
   */
  colors?: string[];
  /**
   * 渐变角度
   */
  angle?: number;
  /**
   * 是否禁用
   */
  disabled?: boolean;
}

/**
 * 通用确认按钮组件
 * 支持自定义样式和渐变颜色
 */
const ConfirmButton: React.FC<ConfirmButtonProps> = ({
  text,
  onPress,
  style,
  textStyle,
  colors = ['#FF4444', '#FF7272'],
  angle = 270,
  disabled = false,
}) => {
  const styles = getStyles();
  
  return (
    <Touch style={[styles.confirmButton, style]} onPress={onPress} disabled={disabled}>
      <LinearGradient
        colors={colors}
        useAngle={true}
        angle={angle}
        style={styles.confirmButtonBg}
      >
        <Text style={[styles.buttonText, textStyle]}>{text}</Text>
      </LinearGradient>
    </Touch>
  );
};

export default ConfirmButton; 