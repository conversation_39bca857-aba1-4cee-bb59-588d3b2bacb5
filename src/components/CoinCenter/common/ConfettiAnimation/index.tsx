import React, { useRef, useEffect, useState } from 'react';
import { StyleSheet, Animated, Dimensions } from 'react-native';
import AnimatedLottieView from 'lottie-react-native';
import { useLottieResource } from 'hooks/useLottieResources';
import { confettiLottie } from 'constants/assets';

export interface ConfettiAnimationProps {
  /**
   * 自定义彩带 Lottie 动画的 URL
   * @default confettiLottie
   */
  source?: string;

  /**
   * 是否自动播放动画
   * @default true
   */
  autoPlay?: boolean;

  /**
   * 是否循环播放动画
   * @default false
   */
  loop?: boolean;

  /**
   * 动画容器的样式
   */
  style?: any;

  /**
   * 动画完成时的回调
   */
  onComplete?: () => void;

  /**
   * 是否禁用指针事件（点击穿透）
   * @default true
   */
  pointerEvents?: boolean;
}

/**
 * 可重用的彩带动画组件（使用 Lottie）
 */
const ConfettiAnimation: React.FC<ConfettiAnimationProps> = ({
  source = confettiLottie,
  autoPlay = true,
  loop = false,
  style,
  onComplete,
  pointerEvents = true,
}) => {
  const aspectRatio = 1125 / 2436;
  const height = Dimensions.get('window').height;
  const width = height * aspectRatio;
  // 获取动画资源
  const lottieAnimation = useLottieResource(source);
  const lottieRef = useRef<AnimatedLottieView>(null);
  const [isPlaying, setIsPlaying] = useState(false);

  // 确保组件卸载时停止动画
  useEffect(() => {
    const currentRef = lottieRef.current;

    return () => {
      if (currentRef) {
        currentRef.reset();
      }
    };
  }, []);

  // 动画完成处理函数
  const handleAnimationFinish = () => {
    setIsPlaying(false);
    if (onComplete) {
      onComplete();
    }
  };

  // 动画资源就绪时自动播放
  useEffect(() => {
    if (lottieAnimation && autoPlay && !isPlaying) {
      setIsPlaying(true);
    }
  }, [lottieAnimation, autoPlay, isPlaying]);

  if (!lottieAnimation) return null;

  return (
    <Animated.View
      pointerEvents={pointerEvents ? 'none' : 'auto'}
      style={[styles.container, {
        height: height,
        aspectRatio: aspectRatio,
        transform: [{
          translateX: -width / 2,
        }]
      }, style]}
    >
      <AnimatedLottieView
        ref={lottieRef}
        source={lottieAnimation}
        style={styles.animation}
        autoPlay={autoPlay}
        loop={loop}
        onAnimationFinish={handleAnimationFinish}
        resizeMode="cover"
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    zIndex: 10,
    position: 'absolute',
    bottom: 0,
    left: '50%',
  },
  animation: {
    width: '100%',
    height: '100%',
  },
});

export default ConfettiAnimation;