import React from "react";
import { View, StyleProp, ViewStyle } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import moduleCardThemeAtom from "./theme";

interface ModuleCardProps {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
}

export default function ModuleCard({ children, style }: ModuleCardProps) {
  const theme = useAtomValue(moduleCardThemeAtom);
  const styles = getStyles(theme);

  return (
    <View style={[styles.container, style]}>
      {children}
    </View>
  );
} 