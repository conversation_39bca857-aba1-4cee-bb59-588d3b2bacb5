import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  headerBorderColor: '#000000',
  titleColor: '#8D8D91',
}

const lightTheme = {
  headerBorderColor: '#F0F0F0',
  titleColor: '#666666',
}

const headerTitleThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default headerTitleThemeAtom; 