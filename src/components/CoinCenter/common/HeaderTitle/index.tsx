import React from "react";
import { View, Text } from "react-native";
import { BetterImage } from "@xmly/rn-components";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import headerTitleThemeAtom from "./theme";

interface HeaderTitleProps {
  title: string;
  titleIcon: string;
  children?: React.ReactNode;
}

export default function HeaderTitle({ title, titleIcon, children }: HeaderTitleProps) {
  const theme = useAtomValue(headerTitleThemeAtom);
  const styles = getStyles(theme);

  return (
    <View style={styles.header}>
      <View style={styles.titleContainer}>
        <BetterImage
          source={{ uri: titleIcon }}
          style={styles.titleIcon}
          imgHeight={16}
          imgWidth={16}
        />
        <Text style={styles.title}>{title}</Text>
      </View>
      {children}
    </View>
  );
} 