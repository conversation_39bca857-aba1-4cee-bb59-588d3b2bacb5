import { StyleSheet } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: px(8.5),
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: theme.headerBorderColor,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    fontSize: px(12),
    fontWeight: '500',
    color: theme.titleColor,
  },
  titleIcon: {
    marginRight: px(4),
    width: px(14),
    height: px(14),
  },
}); 