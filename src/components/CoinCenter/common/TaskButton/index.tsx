import React from "react";
import { Style<PERSON>rop, TextStyle, ViewStyle, Text } from "react-native";
import { Touch } from "@xmly/rn-components";
import { styles } from "./styles";

interface TaskButtonProps {
  text: string;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  onPress?: () => void;
  disabled?: boolean;
}

export default function TaskButton({ text, style, textStyle, onPress, disabled }: TaskButtonProps) {
  return (
    <Touch style={[styles.button, style]} onPress={onPress} disabled={disabled}>
      <Text style={[styles.text, textStyle]}>{text}</Text>
    </Touch>
  );
} 