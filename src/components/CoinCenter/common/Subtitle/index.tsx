import React from "react";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import subtitleThemeAtom from "./theme";
import { StyleProp, TextStyle, Text } from "react-native";

interface SubtitleProps {
  text: string;
  style?: StyleProp<TextStyle>;
}

export default function Subtitle({ text, style }: SubtitleProps) {
  const theme = useAtomValue(subtitleThemeAtom);
  const styles = getStyles(theme);

  return <Text style={[styles.text, style]}>{text}</Text>;
} 