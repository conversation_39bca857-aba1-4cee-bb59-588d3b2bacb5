import { StyleSheet, Platform } from "react-native";
import { darkTheme } from "./theme";
import { px } from "utils/px";


export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
//   container: {
//     padding: px(16),
//     backgroundColor: theme.contentBgColor,
//     borderRadius: px(8),
//     marginHorizontal: px(16),
//     marginTop: px(16),
//   },
//   text: {
//     fontSize: px(14),
//     color: theme.textColor,
//     textAlign: 'center',
//   },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: px(12),
    paddingHorizontal: px(16),
    height: px(62),
    backgroundColor: theme == darkTheme ? '#1F1F1F' : '#fff',
    borderRadius: px(2),
    marginHorizontal: px(0),
    marginTop: px(20),
  },
  left: {
    flex: 1,
    marginHorizontal: px(0),
    marginVertical: px(0),
    justifyContent: 'center',
    // borderWidth: px(1),
    // borderColor: '#e5e5e5',
    // borderStyle: 'dashed',
  },
  right: {
    justifyContent: 'center',
    alignItems: 'flex-end',
    minWidth: px(90),
    // borderWidth: px(1),
    // borderColor: 'red',
    // borderStyle: 'dashed',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: px(4),
    // borderWidth: px(1),
    // borderColor: '#e5e5e5',
    // borderStyle: 'dashed',
  },
  title: {
    fontSize: px(14),
    color: theme == darkTheme ? '#FFFFFF' : '#111',
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
  },
  button: {
    backgroundColor: '#FF4444',
    borderRadius: px(95),
    height: px(24),
    width: px(64),
    justifyContent: 'center',
    alignItems: 'center',
    // marginLeft: px(8),
    // paddingVertical: px(2),
    // paddingHorizontal: px(10),
  },
  buttonText: {
    color: '#fff',
    fontSize: px(11),
    // fontWeight: 'bold',
  },
  subText: {
    fontSize: px(12),
    color: '#999999',
    marginTop: px(2),
    fontFamily: 'XmlyNumber',
  },
  planText: {
    fontSize: px(11),
    color: theme == darkTheme ? '#FFFFFF' : '#2C2C3C',
    opacity: 0.3,
    justifyContent: 'flex-end',
  },
}); 