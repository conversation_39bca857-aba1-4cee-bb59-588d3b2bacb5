import React, { useCallback } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import signInThemeStyleAtom from "./theme";
import { signInTaskAtom } from "../SignIn/store";
import { SignInStatus } from "services/welfare";
import { useSignInActions } from "hooks/useSignInActions";
import { Toast } from "@xmly/rn-sdk";
import xmlog from "utilsV2/xmlog";
import useThrottleCallback from 'hooks/useThrottleCallback';

function clickReport(btnText: string) {
    // 福利中心-签到模块  点击事件
    xmlog.click(67701, 'SignInTask', { currPage: 'welfareCenter', Item: btnText });
  }

/**
 * 签到折叠组件
 * @returns 
 */
export default function CheckInCollapse({ adHeight }: { adHeight: number | undefined}) {
    const theme = useAtomValue(signInThemeStyleAtom);
    const styles = getStyles(theme);
    const signInTask = useAtomValue(signInTaskAtom);
    const { awardInfo = [], enableResignIn } = signInTask || {};
    const todayIndex = awardInfo.findIndex(item => item.today);
    const todayAward = awardInfo[todayIndex];

    // 当天有抽奖，且签到状态为未开奖，则显示折叠组件
    const disableCollapseOfLottery = todayAward !== undefined && todayAward.today && todayAward.hasLottery && todayAward.status != SignInStatus.LOTTERY;
    const disableCollapseOfAd = todayAward !== undefined && todayAward.today && todayAward.status != SignInStatus.AD_COMPLETED;

    const hasIncompleteCheckIn = useCallback(() => {
        if (!awardInfo) return false;
        if (todayIndex === -1) return false;
        
        // 检查到今天为止的所有签到状态
        return awardInfo.slice(0, todayIndex + 1).some(item => item.status === SignInStatus.INCOMPLETE);
    }, [awardInfo, todayIndex]);


    // 计算本周已连续签到天数
    const getContinuousDays = useCallback(() => {
        if (!awardInfo) return {checkInCount: 0, reSignInDay: -1};
        let checkInCount = 0;
        let reSignInDay = -1;
        for (let i = 0; i <= todayIndex; i++) {
            if (awardInfo[i].status > 0) {
                checkInCount++;
            } else {
                if (reSignInDay === -1) {
                    reSignInDay = awardInfo[i].day;
                }
            }
        }
        return {checkInCount, reSignInDay};
    }, [awardInfo, todayIndex]);

    const { checkInCount, reSignInDay } = getContinuousDays();
    const { handleReSignInAction, handleAdSignInAction } = useSignInActions();

    const handleAdSignIn = useThrottleCallback(async () => {
        clickReport("折叠看广告签到");
        handleAdSignInAction(todayAward?.upgradeAward);
    });

    const handleReSignIn = useThrottleCallback(async (reSignInDay: number) => {
        if (reSignInDay === -1) {
            return Toast.info('不能补签');
        }
        clickReport(reSignInDay > 0 ? '补' : '去补签');
        if (!signInTask?.enableResignIn) {
        return Toast.info('每天只能补签一次');
        }
        handleReSignInAction(reSignInDay);
    });
    
    // TODO: 不显示折叠的签到组件
    return null;

    if (adHeight === 0) {
        console.log('adHeight is 0!!!!!!');
        return null;
    }

    if (disableCollapseOfLottery || disableCollapseOfAd) {
        console.log('disableCollapse is true', todayAward);
        return null;
    }

    if (todayAward?.status === SignInStatus.INCOMPLETE) {
        return null;
    }

    console.log('todayAward', todayAward);

    // 按业务逻辑渲染按钮文案
    let buttonType = 'none'; // 例如: 'double', 'resign', 'none'
    let buttonText = '';
    let subTitleText = '';

    if (todayAward?.status === SignInStatus.COMPLETED) {
        buttonType = 'doubleGolds';
        buttonText = '更多金币';
        subTitleText = '看广告额外获得' + todayAward?.upgradeAward + '金币';
    } else if (todayAward?.status >= SignInStatus.AD_COMPLETED) {
        if (hasIncompleteCheckIn() && enableResignIn) {
            buttonType = 'resign';
            buttonText = '去补签';
        } else {
            buttonType = 'none';
            buttonText = '';
        }
        subTitleText = '签到天数（' + checkInCount + '/7）';
    }
    console.log('buttonText=', buttonText);

    const onButtonPress = () => {
        if (buttonType === 'doubleGolds') {
            handleAdSignIn();
        } else if (buttonType === 'resign') {
            handleReSignIn(reSignInDay);
        }
        // 'none' 不做处理
    };

    return (
        <View style={styles.card}>
            <View style={styles.left}>
                <Text style={styles.title}>{signInTask?.title}</Text>
                <Text style={styles.subText}>{subTitleText}</Text>
            </View>
            <View style={styles.right}>
                {buttonType !== 'none' ? (
                    <TouchableOpacity style={styles.button} onPress={onButtonPress}>
                        <Text style={styles.buttonText}>{buttonText}</Text>
                    </TouchableOpacity>
                ) : <Text style={styles.planText}>明天再来</Text>}
            </View>
        </View>
    );
}