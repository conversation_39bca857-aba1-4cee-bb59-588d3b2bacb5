import { themeAtom } from '../../../atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  contentBgColor: '#1A1A1A',
  textColor: '#FFFFFF',
}; 

export const lightTheme = {
    contentBgColor: '#FFFFFF',
    textColor: '#000000',
}; 

const signInThemeStyleAtom = atom((get) => {
    const theme = get(themeAtom);
    return theme === 'dark' ? darkTheme : lightTheme;
  })
  
  export default signInThemeStyleAtom;