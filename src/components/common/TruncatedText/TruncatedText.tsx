import React, { useEffect, useState } from "react";
import { Text } from "react-native";
import log from "utils/log";
import CharWidthCalibrator from "./CharWidthCalibrator";

// 通用截断文本组件接口
export interface TruncatedTextProps {
  text: string;
  maxWidth: number;
  style?: any;
  charWidths?: Record<string, number>;
  numberOfLines?: number;
  ellipsisChar?: string;
  onTruncate?: (isTruncated: boolean) => void;
  disableCalibrator?: boolean; // 新增属性，是否禁用宽度校准器
}

function TruncatedText({
  text,
  maxWidth,
  style,
  charWidths: externalCharWidths, // 重命名为 externalCharWidths，表示外部传入
  numberOfLines = 1,
  ellipsisChar = '…',
  onTruncate,
  disableCalibrator = false
}: TruncatedTextProps) {
  const [truncatedText, setTruncatedText] = useState(text);
  const [isTruncated, setIsTruncated] = useState(false);
  const [internalCharWidths, setInternalCharWidths] = useState<Record<string, number> | null>(null);

  // 最终使用的字符宽度数据：优先使用外部传入的，否则使用内部计算的
  const charWidths = externalCharWidths || internalCharWidths;

  // 从样式中提取字体大小
  const getFontSize = () => {
    let fontSize = 14; // 默认字体大小
    if (style && typeof style === 'object') {
      fontSize = style.fontSize || 14;
    }
    return fontSize;
  };

  // 处理字符宽度收集完成
  const handleWidthsCollected = (widths: Record<string, number>) => {
    setInternalCharWidths(widths);
  };

  // 根据真实测量的字符宽度进行文本截断
  const truncateTextWithRealWidths = (text: string, maxWidth: number, charWidths: Record<string, number>) => {
    if (!text || !maxWidth || !charWidths) return text;

    const chars = Array.from(text);
    let currentWidth = 0;
    let i = 0;
    const ellipsisWidth = 6; // 估算的省略号宽度

    // 根据字符类型确定宽度
    function getCharacterCategory(char: string) {
      if (/[\u4e00-\u9fa5]/.test(char)) return 'chinese'; // 中文
      if (/[0-9]/.test(char)) return 'numbers'; // 数字
      if (/[A-Z]/.test(char)) return 'upperCaseLetters'; // 大写字母
      if (/[a-z]/.test(char)) return 'lowerCaseLetters'; // 小写字母
      if (/[，。、；：'"'"【】《》？！]/.test(char)) return 'chinesePunctuation'; // 中文标点
      if (/[,.;:'""`"\[\](){}?!]/.test(char)) return 'englishPunctuation'; // 英文标点
      if (/[-_+=<>|/\\]/.test(char)) return 'narrowPunctuation'; // 窄标点
      if (/\s/.test(char)) return 'spaces'; // 空格
      return 'chinese'; // 默认作为中文字符处理
    }

    // 根据测量数据决定每个字符的宽度
    while (i < chars.length && currentWidth + ellipsisWidth <= maxWidth) {
      const category = getCharacterCategory(chars[i] as string);
      const charWidth = charWidths[category] || charWidths.chinese; // 如果没有匹配的类别，使用中文字符宽度

      currentWidth += charWidth;
      if (currentWidth + ellipsisWidth > maxWidth) break;
      i++;
    }

    if (i === chars.length) return text;
    return chars.slice(0, i).join('') + ellipsisChar;
  };

  // 根据字体大小估算字符宽度进行文本截断
  const truncateTextEstimated = (text: string, maxWidth: number, fontSize: number) => {
    if (!text || !maxWidth) return text;

    // 从样式中提取字体大小
    const effectiveFontSize = fontSize || 14;

    // 估算不同类型字符的宽度（单位：像素）
    const charWidthEstimates = {
      chinese: effectiveFontSize, // 中文字符
      default: effectiveFontSize * 0.6, // 默认英文字符
      narrow: effectiveFontSize * 0.4, // 窄字符
      wide: effectiveFontSize * 0.8, // 宽字符
    };

    const ellipsisWidth = charWidthEstimates.default; // 省略号宽度估计

    const chars = Array.from(text);
    let currentWidth = 0;
    let i = 0;

    // 字符宽度估计函数
    function estimateCharWidth(char: string) {
      if (/[\u4e00-\u9fa5]/.test(char)) return charWidthEstimates.chinese; // 中文
      if (/[iIl|.,:;'`]/.test(char)) return charWidthEstimates.narrow; // 窄字符
      if (/[mwWM]/.test(char)) return charWidthEstimates.wide; // 宽字符
      return charWidthEstimates.default; // 默认字符宽度
    }

    // 计算截断位置
    while (i < chars.length && currentWidth + ellipsisWidth <= maxWidth) {
      const charWidth = estimateCharWidth(chars[i] as string);
      currentWidth += charWidth;

      if (currentWidth + ellipsisWidth > maxWidth) break;
      i++;
    }

    if (i === chars.length) return text;

    return chars.slice(0, i).join('') + ellipsisChar;
  };

  // 更新截断文本
  useEffect(() => {
    if (!text || !maxWidth) {
      setTruncatedText(text);
      setIsTruncated(false);
      return;
    }

    const fontSize = getFontSize();

    let result;
    if (charWidths) {
      result = truncateTextWithRealWidths(text, maxWidth, charWidths);
    } else {
      result = truncateTextEstimated(text, maxWidth, fontSize);
    }

    const wasTruncated = result !== text;
    setTruncatedText(result);
    setIsTruncated(wasTruncated);

    if (onTruncate && wasTruncated !== isTruncated) {
      onTruncate(wasTruncated);
    }
  }, [text, maxWidth, charWidths, style, ellipsisChar, onTruncate, isTruncated]);

  return (
    <>
      <Text
        style={[style]}
        numberOfLines={numberOfLines}
      >
        {truncatedText}
      </Text>

      {/* 只有当没有外部传入的字符宽度和未禁用校准器时才添加测量组件 */}
      {!externalCharWidths && !disableCalibrator && (
        <CharWidthCalibrator
          onWidthsCollected={handleWidthsCollected}
          fontSize={getFontSize()}
        />
      )}
    </>
  );
}

export default React.memo(TruncatedText);