import React, { useEffect, useState } from "react";
import { View, Text, LayoutChangeEvent } from "react-native";
import { px } from "utils/px";


export interface CharWidthCalibratorProps {
  onWidthsCollected: (widths: Record<string, number>) => void;
  fontSize?: number;
}

// 字符宽度测量组件
export default function CharWidthCalibrator({
  onWidthsCollected,
  fontSize = 14
}: CharWidthCalibratorProps) {
  // 常见字符样本 - 包含各类字符
  const characterSamples = {
    chinese: '中文汉字',
    numbers: '0123456789',
    upperCaseLetters: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
    lowerCaseLetters: 'abcdefghijklmnopqrstuvwxyz',
    chinesePunctuation: `，。、；："\'"【】《》？！`,
    englishPunctuation: ',.;:\'"`"[](){}?!',
    narrowPunctuation: '-_+=<>|/\\',
    spaces: '   '  // 三个空格
  };

  const [widths, setWidths] = useState<Record<string, number>>({});
  const [samplesCollected, setSamplesCollected] = useState(false);

  // 处理样本宽度收集
  const handleSampleLayout = (category: string, e: LayoutChangeEvent) => {
    const { width } = e.nativeEvent.layout;
    const textLength = characterSamples[category as keyof typeof characterSamples].length;
    const avgWidth = width / textLength;

    setWidths(prev => ({
      ...prev,
      [category]: avgWidth
    }));
  };

  // 当所有样本都收集完成，通知父组件
  useEffect(() => {
    const allCategories = Object.keys(characterSamples);
    const collectedCategories = Object.keys(widths);

    if (allCategories.length === collectedCategories.length && !samplesCollected) {
      setSamplesCollected(true);
      onWidthsCollected(widths);
    }
  }, [widths, samplesCollected, onWidthsCollected]);

  return (
    <View style={{ position: 'absolute', opacity: 0, top: -1000, flexDirection: 'row' }} pointerEvents="none">
      {Object.entries(characterSamples).map(([category, sample], index) => (
        <Text
          key={category}
          onLayout={(e) => handleSampleLayout(category, e)}
          style={{ alignSelf: 'flex-start', fontSize: px(fontSize) }}
        >
          {sample}
        </Text>
      ))}
    </View>
  );
} 