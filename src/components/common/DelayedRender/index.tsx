import React, { useEffect, useState } from 'react';

interface DelayedRenderProps {
  delay: number;
  children: React.ReactNode;
}

export default function DelayedRender({ delay, children }: DelayedRenderProps) {
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShouldRender(true);
    }, delay);

    return () => {
      clearTimeout(timer);
    };
  }, [delay]);

  return shouldRender ? <>{children}</> : null;
} 