import React from "react";
import { ViewStyle, StyleProp, View, StyleSheet, Platform } from "react-native";
import { requireNativeComponent } from 'react-native';

const AlphaVideoPlayer = requireNativeComponent('XMRNAlphaVideoPlayer');

interface GameCountProps {
  pointerEvents?: 'none' | 'auto' | 'box-none' | 'box-only';
  repeat?: boolean;
  muted?: boolean;
  style: StyleProp<ViewStyle>;
  source: string;
  onLoad?: () => void;
  onComplete?: () => void;
  onError?: (error: any) => void;

}

export default function AlphaVideo(props: GameCountProps) {
  const {
    source,
    style,
    repeat = false,
    muted = true,
    pointerEvents = 'none',
    onLoad,
    onComplete,
    onError
  } = props;
  return (
    source ? (
      <View pointerEvents={pointerEvents} style={style}>
        <AlphaVideoPlayer
          //@ts-ignore
          style={StyleSheet.absoluteFill}
          source={source}
          repeat={repeat}
          muted={muted}
          //@ts-ignore  
          resizeMode={Platform.select({ ios: 'center', android: 2 })}
          onError={onError}
          onLoad={onLoad}
          onComplete={onComplete}
        />
      </View>
    ) : null
  );
}