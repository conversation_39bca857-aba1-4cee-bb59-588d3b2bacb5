import useSkeletonStyle from "hooks/useSkeletonStyle";
import getStyles from "./style";
import themes from "themeV2";
import { useAtomValue } from "jotai";
import { themeAtom } from "atom/theme";
import { View } from "react-native";
import React from "react";

export default function TaskSkeleton() {
  const skeletonStyles = useSkeletonStyle();
  const theme = useAtomValue(themeAtom);
  const taskThemeStyle = themes[theme].task.item;
  const creditThemeStyle = themes[theme].credit;
  const styles = getStyles({ ...taskThemeStyle, ...creditThemeStyle });

  return <View style={[styles.container, { height: 62.8 }]}>
    <View style={styles.content}>
      <View style={styles.title}>
        <View style={[skeletonStyles.item, {
          width: 120,
          height: 19
        }]}></View>
        <View style={[styles.value, skeletonStyles.item, {
          width: 80,
          height: 19
        }]}></View>
      </View>
      <View style={[skeletonStyles.item, styles.desc, { height: 19, width: 150 }]}>
      </View>
    </View>
    <View style={[
      styles.btn
    ]}></View>
  </View>
}