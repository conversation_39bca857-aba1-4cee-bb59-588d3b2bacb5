import { BetterImage, Text, Touch } from "@xmly/rn-components";
import React from "react";
import { View } from "react-native";
import getStyles from "./style";
import { TaskStatus } from "typesV2/taskList";
import { themeAtom } from "atom/theme";
import { useAtomValue } from "jotai";
import themes from "themeV2";

interface Props {
  name: string;
  desc: string;
  btnText: string;
  status: TaskStatus,
  value?: string;
  onPress?: () => void;
}

export default function Task(props: Props) {
  const {
    name,
    desc,
    btnText,
    value,
    status,
    onPress
  } = props;
  const theme = useAtomValue(themeAtom);
  const taskThemeStyle = themes[theme].task.item;
  const creditThemeStyle = themes[theme].credit;
  const styles = getStyles({ ...taskThemeStyle, ...creditThemeStyle });
  const receivable = status === TaskStatus.finished;
  const completed = status === TaskStatus.received;

  return <View style={styles.container}>
    <View style={styles.content}>
      <View style={styles.title}>
        <Text style={styles.titleText} numberOfLines={1}>
          {name}
        </Text>
        {value ? <View style={styles.value}>
          <BetterImage
            style={styles.valueIcon}
            source={{
              uri: 'https://imagev2.xmcdn.com/storages/e61a-audiofreehighqps/E4/18/GAqhtYsK8UT1AAADdQMkkga9.png'
            }}
            imgHeight={16}
            imgWidth={16}
          />
          <Text style={styles.valueText}>{value}</Text>
        </View> : null}
      </View>
      <View>
        <Text style={styles.desc}>{desc}</Text>
      </View>
    </View>
    <Touch
      onPress={onPress}
      style={[
        styles.btn,
        receivable ? styles.btnReceivable : null,
        completed ? styles.btnFinished : null
      ]}>
      <Text style={[
        styles.btnText,
        receivable ? styles.btnTextReceivable : null,
        completed ? styles.btnTextFinished : null,
      ]}>
        {btnText}
      </Text>
    </Touch>
  </View>
}