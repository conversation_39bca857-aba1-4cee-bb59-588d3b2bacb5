import { StyleSheet } from "react-native";
import themes from "themeV2";

const getStyles = (theme: typeof themes.dark.task.item & typeof themes.dark.credit) => StyleSheet.create({
  container: {
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 0.5,
    borderBottomColor: theme.line_color,
  },
  content: {
    flex: 1,
  },
  title: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  titleText: {
    fontSize: 14,
    color: theme.title_color,
    fontFamily: 'PingFangSC-Regular',
    flexShrink: 1,
  },
  desc: {
    fontSize: 11,
    color: theme.sub_title_color,
    fontFamily: 'PingFangSC-Regular',
    marginTop: 4,
  },
  btn: {
    width: 58,
    height: 28,
    borderRadius: 14,
    backgroundColor: theme.actionButton.not_finish_bg,
    borderColor: theme.actionButton.not_finish_border_color,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: .5,
    marginLeft: 12,
  },
  btnReceivable: {
    backgroundColor: theme.actionButton.can_receive_bg,
    borderColor: theme.actionButton.can_receive_border_color,
  },
  btnFinished: {
    backgroundColor: theme.actionButton.finished_bg,
    borderColor: theme.actionButton.finished_border_color,
  },
  btnText: {
    fontWeight: 'bold',
    fontFamily: 'PingFangSC-Regular',
    fontSize: 12,
    color: theme.actionButton.not_finish_label_color,
  },
  btnTextReceivable: {
    color: theme.actionButton.can_receive_label_color,
  },
  btnTextFinished: {
    color: theme.actionButton.finished_label_color,
  },
  valueIcon: {
    width: 16,
    height: 16,
  },
  value: {
    flexDirection: 'row',
    marginLeft: 6,
    flexShrink: 0,
    flexGrow: 1
  },
  valueText: {
    fontSize: 13,
    color: '#ff4646',
    fontWeight: '600',
  }
});

export default getStyles;