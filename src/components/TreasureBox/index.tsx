import React, { useEffect, useRef, useState } from "react";
import { BetterImage, Text, Touch } from "@xmly/rn-components";
import { Animated, Dimensions, Image } from "react-native";
import getStyles from "./style";
import useCountDown from "./hooks/useCountDown";
import { useAtomValue } from "jotai";
import { countdownAtom } from "./atom/countdown";
import usePlayTreasureAd from "./hooks/usePlayTreasureAd";
import { treasureClickReport } from "./utils";
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
import { HomeScrollViewEventName } from "constantsV2";

const boxPic = 'https://imagev2.xmcdn.com/storages/d1cc-audiofreehighqps/0F/FE/GAqhig8LFfspAAAjPAMzuKc1.png';
const treasurePic = 'https://imagev2.xmcdn.com/storages/e982-audiofreehighqps/57/51/GAqhm44LFhHlABAAAAMzx1VP.gif';

export default function TreasureBox() {
  const playAd = usePlayTreasureAd();
  const countdown = useAtomValue(countdownAtom);
  const clock = useCountDown();
  const styles = getStyles();
  const show = countdown !== undefined;
  const [scrolling, setScrolling] = useState(false);
  const animatedValue = useRef(new Animated.Value(0)).current;
  const finishScrollTimer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const momentumScrollStarted = useRef(false);
  const scale = Math.max(Math.min(Dimensions.get('window').width / 375, 1.1), 1);

  function onPress() {
    treasureClickReport();
    playAd();
  }

  useEffect(() => {
    if (show) {
      const onScrollBeginDragListener = GlobalEventEmitter.addListener(HomeScrollViewEventName.onScrollBeginDrag, () => {
        setScrolling(true);
      });
      const onScrollEndDragListener = GlobalEventEmitter.addListener(HomeScrollViewEventName.onScrollEndDrag, () => {
        setTimeout(() => {
          if (momentumScrollStarted.current) return;
          if (finishScrollTimer.current) clearTimeout(finishScrollTimer.current);
          finishScrollTimer.current = setTimeout(() => { setScrolling(false); }, 1000);
        }, 500);
      });
      const onMomentumScrollBegin = GlobalEventEmitter.addListener(HomeScrollViewEventName.onMomentumScrollBegin, () => {
        momentumScrollStarted.current = true;
      });
      const onMomentumScrollEnd = GlobalEventEmitter.addListener(HomeScrollViewEventName.onMomentumScrollEnd, () => {
        momentumScrollStarted.current = false;
        if (finishScrollTimer.current) clearTimeout(finishScrollTimer.current);
        finishScrollTimer.current = setTimeout(() => { setScrolling(false); }, 1000);
      });
      return () => {
        if (finishScrollTimer.current) clearTimeout(finishScrollTimer.current);
        onScrollBeginDragListener.remove();
        onScrollEndDragListener.remove();
        onMomentumScrollBegin.remove();
        onMomentumScrollEnd.remove();
      }
    }
    return () => { }
  }, [show])

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: scrolling ? 1 : 0,
      duration: 300,
      useNativeDriver: true
    }).start();
  }, [scrolling, animatedValue])

  return show ? <Animated.View style={[styles.container, {
    opacity: animatedValue.interpolate({
      inputRange: [0, 1],
      outputRange: [1, .3],
    }),
    transform: [{
      translateX: animatedValue.interpolate({
        inputRange: [0, 1],
        outputRange: [0, 40],
      })
    },
    {
      scale
    }]
  }]}>
    <Touch
      disabled={!!countdown && countdown > 0}
      onPress={onPress}
      style={styles.touch}
    >
      <Image
        source={{
          uri: treasurePic
        }}
        style={[styles.treasure, countdown <= 0 ? null : styles.hide]}
      />
      <BetterImage
        source={{
          uri: boxPic,
        }}
        key={`${boxPic}_${countdown > 0 ? 1 : 0}`} // 清FastImage缓存，使样式切换生效
        imgHeight={64.3}
        imgWidth={71}
        style={[styles.box, countdown <= 0 ? styles.hide : null]}
      />
      <Text style={[styles.titleText, countdown <= 0 ? styles.unlock : null]}>{clock ? `${clock}后再领` : '看广告领现金'}</Text>
    </Touch>
  </Animated.View> : null
}