import { useAtomValue } from "jotai";
import playAd from "../../../utils/playAd";
import { useSelector } from "react-redux";
import type { RootState } from "store";
import taskAtom from "../../CashTask/store/task";
import awardsAtom from "../../CashTask/store/award";
import { useCallback } from "react";
import throttle from "utilsV2/throttle";

export default function usePlayTreasureAd() {
  const taskInfo = useAtomValue(taskAtom);
  const award = useAtomValue(awardsAtom);
  const cashBalance = useSelector((state: RootState) => state.goldCoin.balance);

  async function handler() {
    const stepNo = award.find((item) => item.today)?.stepNo;
    if (taskInfo?.adPositionName && taskInfo?.adPositionId) {
      await playAd({
        positionName: taskInfo.adPositionName,
        positionId: Number(taskInfo.adPositionId),
        otherParams: {
          cashBalance,
          stepNo,
          // 宝箱广告传入固定值99
          maxRewardTimes: 99,
          rewardTimes: 99,
        },
        extInfo: JSON.stringify({ sourceName: 'treasureBox' })
      });
    }
  }

  const playFunction = useCallback(throttle(handler, 300), [award, cashBalance, taskInfo]);

  return playFunction;
}