import { useAtom } from "jotai";
import { useEffect, useRef, useState } from "react";
import { countdownAtom } from "../atom/countdown";

export default function useCountDown() {
  const [countdown, setCountdown] = useAtom(countdownAtom);
  const timer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [clock, setClock] = useState('');

  useEffect(() => {
    if (countdown && countdown > 0) {
      timer.current = setTimeout(() => {
        setCountdown(prevCountdown => prevCountdown !== undefined && prevCountdown > 0 ? prevCountdown - 1 : 0);
      }, 1000);
    } else if (timer.current) {
      clearTimeout(timer.current);
    }

    return () => {
      if (timer.current) {
        clearTimeout(timer.current);
      }
    };
  }, [countdown])

  useEffect(() => {
    if (countdown && countdown > 0) {
      const min = Math.floor(countdown / 60);
      const second = countdown % 60;
      setClock(`${min}:${second > 9 ? second : '0' + second}`);
    } else if (countdown === 0) {
      setClock('');
    }
    return () => {

    }
  }, [countdown])


  return clock;
}