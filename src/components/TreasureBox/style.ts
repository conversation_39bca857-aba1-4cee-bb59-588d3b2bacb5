import { StyleSheet } from "react-native";

const getStyles = () => StyleSheet.create({
  container: {
    position: 'absolute',
    bottom: 80,
    right: 8,
    zIndex: 99,
  },
  touch: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  box: {
    width: 71,
    height: 64.3,
  },
  titleText: {
    fontSize: 10,
    color: '#FFF',
    position: 'absolute',
    bottom: 6,
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    lineHeight: 13,
    textAlign: 'center',
  },
  treasure: {
    width: 71.3,
    height: 83.3
  },
  hide: {
    opacity: 0,
    position: 'absolute',
    bottom: 0,
  },
  unlock: {
    bottom: 9,
  }
});

export default getStyles;