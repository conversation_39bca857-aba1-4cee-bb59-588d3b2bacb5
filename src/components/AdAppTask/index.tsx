import React, { useEffect } from "react";
import Task from "components/Task";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { adAppTask<PERSON>tom, AdAppTaskInfo, updateAdAppTask<PERSON>tom, writeAdAppTask<PERSON>tom } from "./store/task";
import useTaskWithStatus from "./hooks/useTaskWithStatus";
//@ts-ignore
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import xmlog from "utilsV2/xmlog";
import adDPTaskCtrl from "utilsV2/adDPTask";
import log from "utils/log";
import { TaskStatus } from "typesV2/taskList";
import { AdAppTaskStatus } from "typesV2/adDPTask";
import TaskSkeleton from "components/Task/Skeleton";

export default function AdAppTask() {
  const getAdAppTask = useSetAtom(writeAdAppTaskAtom);
  const updateAdAppTask = useSetAtom(updateAdAppTaskAtom);
  const adAppInfo = useAtomValue(adAppTaskAtom) as AdAppTaskInfo;
  const task = (adAppInfo as AdAppTaskInfo)?.data?.[0];
  const taskStatus = task?.taskStatus || 0;
  const taskInfo = useTaskWithStatus();

  useEffect(() => {
    getAdAppTask({ init: true });
  }, [])

  function onShow() {
    log('debug__AdAppTask_onShow', { exposed: adAppInfo?.exposed, fromCache: task?.fromCache })
    if (adAppInfo?.exposed !== true && task?.fromCache !== true) {
      adDPTaskCtrl.onExpo({ adItem: task!, responseId: adAppInfo?.responseId! })
      updateAdAppTask({ exposed: true });
    }
    xmlog.event(42772, 'slipPage', {
      taskId: `${task?.adid}`,
      taskTitle: task?.name || '',
      currPage: '任务中心',
      adValueGrade: 'high',
      xmRequestId: adAppInfo?.requestId || '',
      responseId: `${adAppInfo?.responseId}`
    })
  }

  return typeof adAppInfo === 'string' ? <TaskSkeleton /> :
    taskInfo && task?.name ? // 任务完成后不再展示
      <ScrollAnalyticComp
        itemKey={`AdAppTask_${adAppInfo?.responseId}_${task?.adid}`} // 通过responseId和adid来确认任务唯一性
        onShow={onShow}
      >
        <Task
          name={task?.name || ''}
          desc={taskInfo?.desc || ''}
          btnText={taskInfo?.btnText}
          status={taskStatus === AdAppTaskStatus.Finished ? TaskStatus.finished : TaskStatus.unfinished}
          onPress={taskInfo?.onClick}
          value={task?.priceSecondBanner ? `最高${task?.priceSecondBanner}元` : ''}
        />
      </ScrollAnalyticComp>
      :
      null
}