import { NativeModules } from "react-native";
import { AdDPTaskItem } from "typesV2/adDPTask";
import log from "utils/log";

// const Business = NativeModules.Business;

export async function checkAppInstalled(app: string) {
  if (NativeModules?.Business?.isAppInstalled) {
    try {
      const { result } = await NativeModules?.Business.isAppInstalled({ appInfoList: [app] });
      log('debug__checkAppInstalled_result', { appInfoList: [app], result, condition: result?.[0] === 1, type: typeof result?.[0] });
      return result?.[0] === 1;
    } catch (e) {
      log('debug__isAppInstalled_err', e);
      console.info('debug_result_error', e);
    }
  }
  return false;
}


interface OpenAdAppParams {
  isDownload: boolean;
  adItem: AdDPTaskItem;
  responseId: string;
}
export async function openAdApp(params: OpenAdAppParams) {
  if (NativeModules?.AdModule?.clickWithAdDeepLink) {
    try {
      const result = await NativeModules?.AdModule?.clickWithAdDeepLink(params);
      console.info('debug_openAdApp_result', result);
      log('debug__clickWithAdDeepLink_result', result);
      return result;
    } catch (e) {
      console.info('debug_result_error', JSON.stringify(e));
      log('debug__clickWithAdDeepLink_err', e);
    }
  }
}