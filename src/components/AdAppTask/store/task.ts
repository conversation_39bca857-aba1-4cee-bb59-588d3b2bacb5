import { atom } from "jotai";
import requestAdDPTaskList from "servicesV2/requestAdDPTaskList";
import { AdAppTaskStatus, AdDPTaskListRequestResult } from "typesV2/adDPTask";
import getXMRequestId from "utilsV2/getXMRequestId";
import log from "utils/log";
import getAdAppCountdown from "../utils/getAdAppCountdown";
import { checkAppInstalled } from "../utils";

type AttachedStates = {
  requestId?: string;
  completedAt?: number; //任务完成时间戳
  exposed?: boolean // 是否曝光过
  installed?: boolean; // 是否已安装
}
async function sleep(duration = 0) {
  return new Promise(resolve => setTimeout(resolve, duration));
}

export type AdAppTaskInfo = AdDPTaskListRequestResult & AttachedStates | null;

type UpdateAdAppTaskParams = Partial<AttachedStates>;

export const countdownAtom = atom(-1);

export const adAppTaskAtom = atom<AdAppTaskInfo | undefined | 'pending'>(null);

interface WriteAdAppTaskParams {
  init?: boolean;
}
export const writeAdAppTaskAtom = atom(null, async (get, set, params?: WriteAdAppTaskParams) => {
  try {
    const [result, requestId, countdown]: [AdDPTaskListRequestResult, string, number] = await Promise.all([
      requestAdDPTaskList('highValueTask'),
      getXMRequestId(),
      getAdAppCountdown()
    ]);
    const task = result?.data?.[0];
    let newState = null;
    if (params?.init) {
      newState = { ...result, requestId };
    } else {
      const prevTask = get(adAppTaskAtom);
      log('debug__cachedTask', { prevTaskExposed: (prevTask as AdAppTaskInfo)?.exposed, responseId: (prevTask as AdAppTaskInfo)?.responseId });

      if (typeof prevTask !== 'string' && prevTask?.data?.[0]?.adid === task?.adid && prevTask?.responseId === result?.responseId) {
        newState = { ...prevTask, ...result, requestId };
      } else {
        set(adAppTaskAtom, 'pending');
        await sleep();
        newState = { ...result, requestId };
      }
    }

    if (task?.name) {
      if (task?.dpMarketUrl && task?.appPackageName) { //拉新任务
        if (task?.taskStatus !== AdAppTaskStatus.Finished) { // 任务未完成，检测是否安装
          const installed = await checkAppInstalled(task?.appPackageName);
          newState.installed = installed;
          if (countdown > 0 && task?.taskStatus !== AdAppTaskStatus.Default) {
            set(countdownAtom, countdown);
          }
        }
      }
      return set(adAppTaskAtom, newState);
    }
  } catch (e) {
    console.info('debug_cachedTask_error', { e });
  }
  set(countdownAtom, -1);
  return set(adAppTaskAtom, null);
});

export const updateAdAppTaskAtom = atom(null, async (get, set, params: UpdateAdAppTaskParams) => {
  const adAppTask = await get(adAppTaskAtom);
  if (adAppTask && typeof adAppTask !== 'string' && params) {
    set(adAppTaskAtom, { ...adAppTask, ...params });
  }
});