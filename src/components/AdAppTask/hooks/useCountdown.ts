import { useAtom, useSet<PERSON><PERSON> } from "jotai";
import { countdownAtom, writeAdAppTask<PERSON>tom } from "../store/task";
import { useEffect } from "react";

export default function useCountdown() {
  const [countdown, setCountDown] = useAtom(countdownAtom);
  const getAdAppTask = useSetAtom(writeAdAppTaskAtom);

  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountDown(countdown - 1);
      }, 1000);
      return () => {
        clearTimeout(timer);
      }
    }
    if (countdown === 0) {
      getAdAppTask();
    }

    return () => { }
  }, [countdown])
}