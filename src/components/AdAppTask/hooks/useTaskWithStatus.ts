import { useEffect, useRef, useState } from "react";
import { AdAppTaskStatus, AdDPTaskItem } from "typesV2/adDPTask";
import { checkAppInstalled, openAdApp } from "../utils";
import { Toast } from "@xmly/rn-sdk";
import { adAppTaskAtom, AdAppTaskInfo, countdownAtom, updateAdAppTaskAtom, writeAdAppTaskAtom } from "../store/task";
import { useAtomValue, useSetAtom } from "jotai";
import { adAppClickReport, AdAppTaskAwardType, rewardAdAppTask } from "servicesV2/requestAdDPTaskList";
import { AppState } from "react-native";
import type { AppStateStatus } from 'react-native';
import { addMessageAtom } from "atom/push";
import xmlog from "utilsV2/xmlog";
import log from "utils/log";
import useCountdown from "./useCountdown";

const btnTexts = ['去下载', '去打开', '领奖励', '结算中'];

export default function useTaskWithStatus() {
  const adAppTaskInfo = useAtomValue(adAppTaskAtom) as AdAppTaskInfo;
  // const completedAt = adAppTaskInfo?.completedAt || 0;
  const responseId = adAppTaskInfo?.responseId || 0;
  const installed = adAppTaskInfo?.installed;
  const requestId = adAppTaskInfo?.requestId;
  const task = adAppTaskInfo?.data?.[0];
  const adId = task?.adid || 0;
  const taskStatus = task?.taskStatus || AdAppTaskStatus.Default;
  const { dpMarketUrl = '', appPackageName = '', adid = '' } = (task as AdDPTaskItem) || {};
  const [btnText, setBtnText] = useState('去完成');
  const getAdAppTask = useSetAtom(writeAdAppTaskAtom);
  const updateAdAppTask = useSetAtom(updateAdAppTaskAtom);
  const addPushMessage = useSetAtom(addMessageAtom);
  const awardMessage = useRef('');
  const setCountdown = useSetAtom(countdownAtom);
  useCountdown();

  async function rewardOnResume() {
    addPushMessage([{
      title: '现金到账通知',
      subtitle: awardMessage.current,
    }])
    await getAdAppTask();
    AppState.removeEventListener('change', rewardOnResume);
  }

  function execRewardOnResume() {
    if (AppState.currentState === 'active') {
      rewardOnResume();
    } else {
      AppState.addEventListener('change', rewardOnResume);
    }
  }

  async function reward() {
    const commonParams = JSON.parse(task!.commonReportMap);
    const requestParams = {
      responseId: responseId,
      adId: adid!,
      // openedTimestamp: completedAt || 0,
      encryptType: commonParams?.encryptType || 1,
      awardType: AdAppTaskAwardType.Activate,
      adxRtbSettlementPrice: commonParams?.adxRtbSettlementPrice || '0',
    };
    if (dpMarketUrl) {
      requestParams.awardType = AdAppTaskAwardType.New;
      // requestParams.openedTimestamp = completedAt || 1;
    }
    const result = await rewardAdAppTask(requestParams);
    if (result?.data?.success && result?.data?.toast) {
      awardMessage.current = result?.data?.toast;
      execRewardOnResume();
    } else if (result?.data?.toast) {
      Toast.info(result?.data?.toast);
    }
  }

  function clickReport() {
    xmlog.click(40143, undefined, {
      taskId: `${task?.adid}`,
      taskTitle: task?.name || '',
      currPage: '任务中心',
      Item: btnText,
      rewardName: task?.priceSecondBanner ? `最高${task?.priceSecondBanner}元` : '',
      position: '-1',
      adValueGrade: 'high',
      responseId: `${responseId}`
    })
  }

  async function onClick() {
    clickReport();
    let isDownload = false;
    if (taskStatus === AdAppTaskStatus.Finished) {
      //领取
      await adAppClickReport({ responseId, adId, type: 'click' });
      reward();
    } else { // 下载或打开
      if (dpMarketUrl) { // 拉新任务，先检查是否安装
        const result = await checkAppInstalled(appPackageName!);
        await adAppClickReport({ responseId, adId, installed: result, type: 'click' });
        getAdAppTask();
        if (!result) {
          isDownload = true;
          updateAdAppTask({ installed: false });
        } else {
          updateAdAppTask({ installed: true });
        }
      }
      const result = await openAdApp({ isDownload, adItem: task!, responseId: `${responseId}` });
      console.info('debug_openAdApp_result', result);
      const clientCode = String(result?.clientCode);
      if (isDownload) { // 去下载
        if (clientCode === '3') {
          Toast.info('下载失败');
        }
        return;
      }
      // 去打开
      switch (clientCode) {
        case '0': // 跳转时长不足
          Toast.info('浏览时长不足');
          if (dpMarketUrl) {
            await adAppClickReport({ responseId, adId, jump3s: false, installed: true, type: 'result' });
            getAdAppTask();
          }
          break;
        case '1':
          Toast.info('任务完成');
          if (dpMarketUrl) { // 拉新任务，记录完成时间
            await adAppClickReport({ responseId, adId, jump3s: true, installed: true, type: 'result' });
            getAdAppTask();
          } else { // 拉活任务直接领取奖励
            reward();
          }
          break;
        case '3': // 其他原因失败
        default:
          if (dpMarketUrl) {
            await adAppClickReport({ responseId, adId, jump3s: false, installed: true, type: 'result' });
            getAdAppTask();
          }
          Toast.info('任务未完成');
      }
    }
  }

  async function resumeListener(state: AppStateStatus) {
    log('debug__resumeListener', state)
    if (state === 'active') {
      getAdAppTask();
    }
    if (['inactive', 'background'].includes(state)) {
      setCountdown(-1);
    }
  }

  async function checkStatus() {
    switch (taskStatus) {
      case AdAppTaskStatus.Finished: // 已完成
        setBtnText(task?.buttonText || btnTexts[2]);
        break;
      case AdAppTaskStatus.Settling: // 结算中
        if (installed) {
          setBtnText(task?.buttonText || btnTexts[3]);
          break;
        }
      default:
        if (dpMarketUrl) {
          if (installed) {
            return setBtnText(task?.buttonText || btnTexts[1]); // 去打开
          }
          setBtnText(btnTexts[0]); // 去下载
        } else { // 拉活任务展示去打开
          setBtnText(task?.buttonText || btnTexts[1]);
        }
    }
  }

  useEffect(() => {
    checkStatus();
  }, [installed, requestId, taskStatus])

  useEffect(() => {
    if (dpMarketUrl && taskStatus !== undefined && ![AdAppTaskStatus.Finished].includes(taskStatus)) {
      AppState.addEventListener('change', resumeListener);
    }

    return () => {
      AppState.removeEventListener('change', resumeListener);
    }
  }, [dpMarketUrl, taskStatus])

  return {
    btnText,
    desc: task?.description,
    onClick,
    taskStatus,
  }
}