import React, { useEffect, useRef, useState } from "react";
import { Animated, View } from "react-native";
import type { GestureResponderEvent } from 'react-native';
import getStyles from "./style";
import { BetterImage, Text } from "@xmly/rn-components";
import { useAtomValue, useSetAtom } from "jotai";
import pushStyle<PERSON>tom from "theme/push";
import { pushMessageAtom, removeMessageAtom } from "atom/push";
import log from "utils/log";
import { Toast } from "@xmly/rn-sdk";

function enable() {
  return true;
}

function Push() {
  const removeMessage = useSetAtom(removeMessageAtom);
  const pushMessage = useAtomValue(pushMessageAtom);
  const pushText = `${pushMessage?.title}_${pushMessage?.subtitle}`;
  const slideAnim = useRef(new Animated.Value(-150)).current;
  const theme = useAtomValue(pushStyleAtom);
  const styles = getStyles(theme);
  const timer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [touchStartY, setTouchStartY] = useState(0);

  const slideIn = () => {
    const options: Animated.TimingAnimationConfig = {
      toValue: 0,
      duration: 300,
      useNativeDriver: true
    }
    Animated.timing(slideAnim, options).start(({ finished }) => {
      if (finished) {
        timer.current = setTimeout(() => {
          slideOut();
        }, 3000)
      }
    });
  };

  const slideOut = () => {
    const options: Animated.TimingAnimationConfig = {
      toValue: -150,
      duration: 300,
      useNativeDriver: true
    }
    Animated.timing(slideAnim, options).start((finished) => {
      if (finished) {
        removeMessage();
      }
    });
  };

  useEffect(() => {
    if (pushMessage) {
      slideIn();
    }
  }, [pushText]);

  function onTouchStart(e: GestureResponderEvent) {
    clearTimeout(timer.current!);
    setTouchStartY(e.nativeEvent.locationY);
    return true;
  }

  function onTouchEnd(e: GestureResponderEvent) {
    const dy = e.nativeEvent.locationY - touchStartY;
    if (dy < 0 && Math.abs(dy) > 10) {
      slideOut();
    } else {
      timer.current = setTimeout(() => {
        slideOut();
      }, 1000)
    }
  }

  function onTerminate(e: GestureResponderEvent) {
    slideOut();
  }

  return pushMessage ? <Animated.View
    onStartShouldSetResponder={enable}
    onMoveShouldSetResponder={enable}
    onResponderGrant={onTouchStart}
    onResponderRelease={onTouchEnd}
    onResponderTerminate={onTerminate}
    style={[styles.container, { transform: [{ translateY: slideAnim }] }]}
  >
    <View style={styles.content}>
      <BetterImage
        source={{ uri: 'https://imagev2.xmcdn.com/storages/be9e-audiofreehighqps/9D/EB/GAqhav0Ky-PvAAAO6QMWKyow.png' }}
        imgWidth={30}
        imgHeight={36}
        style={styles.icon}
      />
      <View style={styles.title}>
        <Text style={styles.titleText}>{pushMessage.title}</Text>
        <Text numberOfLines={1} style={styles.subtitleText}>{pushMessage.subtitle}</Text>
      </View>
    </View>
  </Animated.View> : null;
}

export default React.memo(Push);