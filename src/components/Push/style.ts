import { StyleSheet } from "react-native";
import { darkTheme } from "theme/push";

const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    width: '100%',
    position: 'absolute',
    top: 52,
    left: 0,
    zIndex: 2000,
  },
  content: {
    width: 343,
    aspectRatio: 343 / 64,
    paddingHorizontal: 17,
    paddingVertical: 14,
    marginLeft: 'auto',
    marginRight: 'auto',
    backgroundColor: theme.bgColor,
    borderRadius: 12,
    shadowColor: 'rgba(0, 0, 0, 0.1)',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowRadius: 20,
    shadowOpacity: 1,
    elevation: 10,
    flexDirection: 'row',
    alignItems: 'center',
  },
  icon: {
    width: 30,
    height: 36,
  },
  title: {
    marginLeft: 13
  },
  titleText: {
    fontFamily: 'PingFang SC',
    fontSize: 14,
    fontWeight: '500',
    lineHeight: 20,
    color: theme.titleColor
  },
  subtitleText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    color: '#8D8D91',
    lineHeight: 17,
    marginTop: 2
  }
});

export default getStyles;