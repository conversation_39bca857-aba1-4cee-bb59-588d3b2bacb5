import { BetterImage, Text, Touch } from "@xmly/rn-components";
import React, { useEffect, useState } from "react";
import { ScrollView, View } from "react-native";
import { getStyles } from "./style";
import { TaskItemType, TaskStatus } from "typesV2/taskList";
import themes from "themeV2";
import { themeAtom } from "atom/theme";
import { useAtomValue } from "jotai";
import adVideoTaskThemeStyleAtom from "./theme";
import performTask from "modulesV2/performTask";
import { taskListAid } from "constantsV2";
//@ts-ignore
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import xmlog from "utilsV2/xmlog";

interface Props {
  task: TaskItemType
}

// 看视频得积分新样式
export default function AdVideoTask(props: Props) {
  const theme = useAtomValue(themeAtom);
  const commonThemeStyle = themes[theme].common;
  const adVideoTaskStyle = useAtomValue(adVideoTaskThemeStyleAtom);
  const { task } = props;
  const completed = [TaskStatus.received, TaskStatus.finished].includes(task?.status);
  const progress = task?.progress ?? 0;
  const steps = completed ? task?.stepInfos?.slice(0, progress) : task?.stepInfos;
  const styles = getStyles({ ...commonThemeStyle, ...adVideoTaskStyle });
  const stepWith = 36;
  const stepPadding = 8;
  const scrollRef = React.useRef<ScrollView>(null);
  const [rendered, setRendered] = useState(false);
  const title = task?.title ?? '看视频得积分';
  const btnText = completed ? '明天领' : '看视频';

  function completeTask(btnText: string) {
    xmlog.click(40143, undefined, {
      taskId: `${task?.id}`,
      taskTitle: title,
      currPage: '任务中心',
      Item: btnText,
      rewardName: `${task?.worth}积分`,
      position: '-1',
      taskType: 'adVideoNew',
      style: '意图卡片',
    })
    performTask(task, taskListAid, { sourceName: 'videoTask' })
  }

  useEffect(() => {
    if (progress > 0 && rendered) {
      scrollRef.current?.scrollTo({ x: progress * (stepWith + stepPadding), animated: true })
    }
  }, [progress, rendered])

  function onShow() {
    xmlog.event(42772, 'slipPage', {
      taskId: `${task?.id}`,
      taskTitle: title,
      currPage: '任务中心',
      taskType: 'adVideoNew',
      style: '意图卡片',
      xmRequestId: task?.UBTMateInfo?.xmRequestId,
    })
  }

  return <ScrollAnalyticComp
    itemKey={`AdVideoTask`}
    onShow={onShow}
  ><View style={styles.container}>
      <View style={styles.head}>
        <Text style={styles.title}>{task?.title ?? '看视频得积分'}</Text>
        <Touch
          disabled={completed}
          style={[styles.btn, completed ? styles.btnCompleted : null]}
          onPress={completeTask.bind(null, btnText)}
        >
          <Text style={[styles.btnText, completed ? styles.btnTextCompleted : null]}>{btnText}</Text>
        </Touch>
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.steps}
        ref={scrollRef}
      >
        <View style={styles.paddingLeft} onLayout={() => { setRendered(true) }}></View>
        {steps?.map((step, index) => {
          const stepCompleted = completed || index < progress;
          const icon = stepCompleted ? 'https://imagev2.xmcdn.com/storages/e5a5-audiofreehighqps/37/DE/GKwRIDoLAPC6AAALoAMq1MZp.png' : adVideoTaskStyle.coinIcon;
          const worth = progress === index && task?.worth ? task?.worth : 100;
          const worthTitle = worth > 9999 ? '' : `+${worth}`;

          return <Touch
            style={[styles.step, { width: stepWith, marginRight: stepPadding }, stepCompleted ? styles.stepCompleted : null]}
            key={index}
            disabled={stepCompleted}
            onPress={completeTask.bind(null, worthTitle)}
          >
            <BetterImage
              source={{
                uri: icon
              }}
              key={icon}
              imgHeight={23.67}
              imgWidth={28.33}
              style={styles.icon}
            />
            {stepCompleted ? <Text style={styles.completeText}>已领取</Text> : <Text style={[styles.amount]}>{worthTitle}</Text>}
          </Touch>
        })}
        <View style={styles.paddingRight}></View>
      </ScrollView>
    </View>
  </ScrollAnalyticComp>
}