import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  stepBgColor: 'rgba(255, 129, 129, 0.12)',
  stepCompletedBgColor: 'rgba(219, 219, 219, 0.08)',
  stepBorderColor: 'rgba(255, 215, 215, .3)',
  stepCompletedBorderColor: 'rgba(219, 219, 219, 0.2)',
  amountColor: '#FF4444',
  completeTextColor: '#66666B',
  btnCompletedBgColor: 'rgba(255, 255, 255, .05)',
  btnCompletedTextColor: 'rgba(255, 255, 255, .15)',
  coinIcon: 'https://imagev2.xmcdn.com/storages/cf7a-audiofreehighqps/CD/D0/GKwRIDoLA0MGAAAN7gMrtusA.png',
}

const lightTheme = {
  stepBgColor: 'rgba(255, 129, 129, 0.05)',
  stepCompletedBgColor: 'rgba(219, 219, 219, 0.1)',
  stepBorderColor: '#FFD7D7',
  stepCompletedBorderColor: '#DBDBDB',
  amountColor: '#FF4444',
  completeTextColor: '#BBBBBB',
  btnCompletedBgColor: 'rgba(19, 19, 19, .03)',
  btnCompletedTextColor: '#BBBBBB',
  coinIcon: 'https://imagev2.xmcdn.com/storages/dc29-audiofreehighqps/5B/5B/GKwRIJELAPC6AAAN6wMq1MaL.png',
}

const adVideoTaskThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default adVideoTaskThemeStyleAtom;