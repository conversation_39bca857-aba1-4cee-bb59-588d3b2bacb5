import { StyleSheet } from 'react-native';
import themes from "themeV2";
import { darkTheme } from './theme';

export const getStyles = (theme: typeof themes.dark.common & typeof darkTheme) => StyleSheet.create({
  container: {
    backgroundColor: theme.item_bg_color,
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  head: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginBottom: 14,
  },
  title: {
    fontFamily: 'PingFangSC-Semibold',
    fontSize: 17,
    fontWeight: 'bold',
    lineHeight: 20,
    letterSpacing: 0,
    color: theme.title_color,
  },
  btn: {
    width: 60,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 68, 68, .08)',
  },
  btnCompleted: {
    backgroundColor: theme.btnCompletedBgColor
  },
  btnText: {
    color: '#FF4444',
    fontSize: 12,
    fontWeight: 'bold'
  },
  btnTextCompleted: {
    color: theme.btnCompletedTextColor
  },
  steps: {
    overflow: 'scroll',
    flexDirection: 'row',
  },
  paddingLeft: {
    width: 16,
  },
  paddingRight: {
    width: 8,
  },
  step: {
    backgroundColor: theme.stepBgColor,
    borderRadius: 3.6,
    borderWidth: .3,
    borderStyle: 'solid',
    borderColor: theme.stepBorderColor,
    // width: 36,
    height: 48,
    // marginRight: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stepCompleted: {
    backgroundColor: theme.stepCompletedBgColor,
    borderColor: theme.stepCompletedBorderColor
  },
  icon: {
    width: 28.33,
    height: 23.67,
  },
  amount: {
    fontFamily: 'XmlyNumber',
    fontSize: 11,
    color: theme.amountColor,
  },
  completeText: {
    color: theme.completeTextColor,
    fontSize: 10,
    fontFamily: 'PingFang SC',
  }
});