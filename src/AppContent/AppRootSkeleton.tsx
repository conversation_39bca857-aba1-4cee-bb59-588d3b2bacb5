import React, { useContext } from 'react'
import { View, StyleSheet } from 'react-native'
import { ThemeContext } from '../contextV2/themeContext'
import HeaderStatic from '../componentsV2/common/Headers/HeaderStatic'
import styled, { withTheme } from 'styled-components'
import { ThemeStyle } from '../typesV2/themeInfo'
import TaskListSkeleton from '../componentsV2/EarnCredit/TaskCenter/TaskListSkeleton'
import ADSectionSkeleton from '../componentsV2/ADSection/ADSectionSkeleton'
import nativeInfoModule from 'modulesV2/nativeInfoModule'

type Props = {}

const Block = withTheme(styled(View)`
  width: 100%;
  border-radius: 8px;
  margin-bottom: 16px;
  background-color: ${({ theme }: { theme: ThemeStyle }) =>
    theme.common.item_bg_color};
  padding: 16px;
`)

const Row = styled(View)`
  flex-direction: row;
  align-items: center;
`
const BlockItemWrapper = styled(View)``

const InnerBlock = styled(View)`
  border-radius: 2px;
  opacity: 0.15;
  /* Normal🌞/ThinTextColor */
  background: #aaaaaa;
`

const AppRootSkeleton: React.FC<Props> = (props) => {
  const theme = useContext(ThemeContext)
  const embed = nativeInfoModule.getInfo().embed

  return (
    <View
      style={[
        StyleSheet.absoluteFillObject,
        {
          zIndex: 2,
          backgroundColor: theme.container.bg_color,
        },
      ]}
    >
      <HeaderStatic hideBack={embed} label={embed ? '' : '加载中'} style={{ marginBottom: 11 }} />
      <View
        style={{
          paddingHorizontal: 12,
        }}
      >
        <Block style={{ height: 100.6 }}>
          <Row style={{ marginBottom: 22 }}>
            <BlockItemWrapper>
              <InnerBlock style={{ width: 83, height: 18, borderRadius: 2 }} />
              <InnerBlock
                style={{ width: 33, height: 18, borderRadius: 2, marginTop: 7 }}
              />
            </BlockItemWrapper>
            <BlockItemWrapper style={{ marginLeft: 110 }}>
              <InnerBlock style={{ width: 83, height: 18, borderRadius: 2 }} />
              <InnerBlock
                style={{ width: 33, height: 18, borderRadius: 2, marginTop: 7 }}
              />
            </BlockItemWrapper>
          </Row>
          <BlockItemWrapper style={{ alignItems: 'center' }}>
            <InnerBlock style={{ width: 44, height: 8, borderRadius: 2 }} />
          </BlockItemWrapper>
        </Block>
        <ADSectionSkeleton />

        <Block
          style={{
            paddingTop: 16,
            paddingRight: 11,
            paddingLeft: 15,
            paddingBottom: 4,
            height: 557,
          }}
        >
          <TaskListSkeleton
            containerStyle={{
              flex: 1,
            }}
          />
        </Block>
      </View>
    </View>
  )
}

export default React.memo(AppRootSkeleton, () => true)
