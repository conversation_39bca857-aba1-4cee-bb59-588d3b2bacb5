import React, { Suspense } from 'react'
import { AppRootContentPropsType } from '../typesV2/common'

type Props = Omit<AppRootContentPropsType, 'withMonthlyTicket' | 'modalStatus'>
const AppLazy = React.lazy(() => import('../App'))

const AppRootContent: React.FC<Props> = (props) => {
  // const { isOldTypeMonthlyTicket } = useContext(GrayScaleContext)
  return (
    <Suspense fallback={null}>
      <AppLazy
        {...props}
        // withMonthlyTicket={isOldTypeMonthlyTicket}
        withMonthlyTicket={false}
      />
    </Suspense>
  )
}

export default AppRootContent
