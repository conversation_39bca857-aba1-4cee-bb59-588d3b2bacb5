import { useRef, useCallback } from "react";

const THROTTLE_DELAY = 1000; // 1秒内只能点击一次

export default function useThrottleCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number = THROTTLE_DELAY
) {
  const isThrottled = useRef(false);

  const throttledCallback = useCallback(async (...args: Parameters<T>) => {
    if (isThrottled.current) {
      return;
    }

    isThrottled.current = true;
    try {
      await callback(...args);
    } finally {
      setTimeout(() => {
        isThrottled.current = false;
      }, delay);
    }
  }, [callback, delay]);

  return throttledCallback;
} 