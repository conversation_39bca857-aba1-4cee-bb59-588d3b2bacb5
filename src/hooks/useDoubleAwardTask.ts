import { useSelector } from "react-redux";
import { RootState, store } from "../store";
import { useCallback } from "react";
import { SignInStatus } from "../typesV2/signInNew";

export default function useDoubleAwardTask(hasDoubleTask?: boolean) {
  const signInInfo = useSelector((state: RootState) => state?.signInInfo);
  const todaySignInStatus = signInInfo?.todaySignInStatus;
  // 如果任务本身不支持翻倍，则不验证是否有奖励待翻倍
  const uncompleted = hasDoubleTask !== false && todaySignInStatus == SignInStatus.isSignedIn && !!signInInfo?.checkInInfo?.doubleAwardTask;
  const btnText = uncompleted ? '可翻倍' : '';
  const foldText = uncompleted ? '有奖励待翻倍' : '';

  const showSignInModal = useCallback(() => {
    if (uncompleted) {
      store.dispatch.signInInfo.refresh({
        signInSuccessModalVisible: true,
        signInSuccessModalVisibleAgain: true,
      });
    }
  }, [uncompleted]);

  return {
    uncompleted,
    btnText,
    onBtnClick: showSignInModal,
    foldText,
    update: store.dispatch.signInInfo.getSignInInfo
  };
}