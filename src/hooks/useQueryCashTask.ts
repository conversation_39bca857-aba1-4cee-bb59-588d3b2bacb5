import { writeTask<PERSON><PERSON> } from "components/CashTask/store/task";
import { PageEventEmitter } from "../defs";
import { useSet<PERSON>tom } from "jotai";
import { useEffect, useRef } from "react";
// import log from "utils/log";

export default function useQueryCashTask() {
  const queryCashTask = useSetAtom(writeTaskAtom);
  const fetched = useRef(false);

  async function fetchTask() {
    // log('debug__useEffect', { fetched: fetched.current });
    await queryCashTask();
    setTimeout(() => {
      fetched.current = true;
    }, 800)
  }

  useEffect(() => {
    const pageResumeListener = PageEventEmitter.addListener('onResume', () => {
      // log('debug__onResume', { fetched: fetched.current });
      if (fetched.current) {
        setTimeout(queryCashTask, 800);
      }
    });
    fetchTask();
    return () => {
      pageResumeListener?.remove?.();
    }
  }, []);
}