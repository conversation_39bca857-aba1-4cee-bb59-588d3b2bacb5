import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useAtomValue } from 'jotai';
import { ThemeStyle } from 'typesV2/themeInfo';
import { themeAtom } from 'atom/theme';
import themes from 'themeV2';

const getSkeletonStyles = (theme: ThemeStyle) => StyleSheet.create({
  block: {
    backgroundColor: theme.common.item_bg_color,
    borderRadius: 8,
    marginHorizontal: 'auto',
  },
  item: {
    borderRadius: 2,
    opacity: 0.15,
    backgroundColor: '#aaaaaa'
  },
  row: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  }
})

export default function useSkeletonStyle() {
  const theme = useAtomValue(themeAtom);
  const skeletonStyle = getSkeletonStyles(themes[theme]);

  return skeletonStyle;
}