import { useState, useEffect, useCallback, useMemo } from 'react';
import { useAtom, useSetAtom } from 'jotai';
import { videoResourcesAtom, downloadResourcesAtom, ResourceStatus, ResourcePriority } from '../atom/videoResources';

/**
 * 视频资源信息
 */
export interface ResourceInfo {
  url: string;                    // 资源URL
  priority?: ResourcePriority;    // 资源优先级
}

/**
 * 视频资源加载配置
 */
export interface VideoResourcesOptions {
  /**
   * 是否自动加载资源
   * @default false - 不自动加载，需要手动调用load函数
   */
  autoLoad?: boolean;
}

/**
 * 资源状态对象
 */
export interface ResourceState {
  key: string;                 // 资源key
  url: string;                 // 资源URL
  path: string;                // 资源路径
  status: ResourceStatus;      // 资源状态
  priority?: ResourcePriority; // 资源优先级
  isReady: boolean;            // 是否就绪
  error?: Error;               // 错误信息
}

/**
 * 视频资源加载hook
 * @param resources 资源字典，值可以是字符串URL或包含URL和优先级的对象
 * @param options 配置选项
 * @returns 资源状态和加载控制函数
 */
export function useVideoResources<T extends Record<string, string | ResourceInfo>>(
  resources: T,
  options: VideoResourcesOptions = {}
) {
  const { autoLoad = false } = options;
  const [videoResources] = useAtom(videoResourcesAtom);
  const downloadResources = useSetAtom(downloadResourcesAtom);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // 触发下载的函数 - 可以在任何时候调用
  const load = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // 执行资源下载
      await downloadResources(resources);
    } catch (err) {
      console.error('Failed to load resources:', err);
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [downloadResources, JSON.stringify(resources)]);

  // 自动加载模式
  useEffect(() => {
    if (autoLoad) {
      load();
    }
  }, [autoLoad, load]);

  // 获取当前资源的状态
  const resourceList = useMemo(() => {
    // 获取每个资源的当前状态
    return Object.entries(resources).map(([key, resource]) => {
      const url = typeof resource === 'string' ? resource : resource.url;
      const priority = typeof resource === 'string' ? undefined : resource.priority;
      const storedResource = videoResources[key];
      
      // 如果资源存在于全局状态
      if (storedResource) {
        return {
          key,
          url,
          path: storedResource.path,
          status: storedResource.status,
          priority: storedResource.priority,
          isReady: storedResource.status === 'success',
          ...(storedResource.error ? { error: storedResource.error } : {})
        };
      } else {
        // 资源尚未加载
        return {
          key,
          url,
          path: url, // 使用原始URL作为默认值
          priority,
          status: 'idle' as ResourceStatus,
          isReady: false
        };
      }
    });
  }, [resources, videoResources]);

  // 检查是否所有请求的资源都已准备就绪
  const ready = useMemo(() => 
    resourceList.every(resource => resource.isReady),
    [resourceList]
  );

  // 为了兼容性，提取所有路径
  const paths = useMemo(() => 
    resourceList.reduce((acc, resource) => {
      acc[resource.key as keyof T] = resource.path;
      return acc;
    }, {} as Record<keyof T, string>),
    [resourceList]
  );

  // 获取高优先级资源是否就绪
  const highPriorityReady = useMemo(() => 
    resourceList
      .filter(resource => resource.priority === ResourcePriority.HIGH)
      .every(resource => resource.isReady),
    [resourceList]
  );

  return {
    // 加载状态
    isLoading,
    error,
    
    // 资源状态和路径
    resources: resourceList,
    ready,
    highPriorityReady,
    
    // 资源路径快捷访问 (为了兼容性)
    paths,
    
    // 触发加载的函数
    load
  };
} 