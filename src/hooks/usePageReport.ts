import { useCallback } from 'react';
import PageAnalytics from '@xmly/react-native-page-analytics'
import { pageExit, pageView } from '@xmly/xmlog-rn'

interface UsePageReportProps {
  pageViewCode: number;
  pageExitCode: number;
  currPage: string;
  params?: Record<string, any>;
  otherProps: Record<string, any>;
}
export function usePageReport({ pageViewCode, pageExitCode, currPage, params = {}, otherProps = {} }: UsePageReportProps) {
  const customPageView = useCallback(() => {
    pageView(pageViewCode, currPage, { currPage: currPage, ...params })
  }, [])

  const customPageExit = useCallback(() => {
    pageExit(pageExitCode, { currPage: currPage, ...params })
  }, [])

  PageAnalytics.useScreen({
    customPageView,
    customPageExit,
    // 对于有使用路由的页面，需要将路由对象navigation传入
    // 如果页面使用了react-navigation-lazy-screen, 传入addFocusListener 与 addBlurListener
    ...otherProps
  })
}