import { useRef, useCallback } from "react";

const DEBOUNCE_DELAY = 300; // 300ms内只触发一次

export default function useDebounceUpdate<T extends (...args: any[]) => any>(
  updateFn: T,
  delay: number = DEBOUNCE_DELAY
) {
  const timer = useRef<NodeJS.Timeout>();
  const pendingUpdate = useRef(false);

  const debouncedUpdate = useCallback(async (...args: Parameters<T>) => {
    if (timer.current) {
      clearTimeout(timer.current);
      pendingUpdate.current = false;
    }

    timer.current = setTimeout(async () => {
      if (!pendingUpdate.current) {
        pendingUpdate.current = true;
        await updateFn(...args);
        pendingUpdate.current = false;
        timer.current = undefined;
      }
    }, delay);
  }, [updateFn, delay]);

  return debouncedUpdate;
} 