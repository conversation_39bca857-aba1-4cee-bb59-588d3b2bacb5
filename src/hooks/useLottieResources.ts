import { useState, useEffect, useCallback, useRef } from 'react';
import log from 'utils/log';

// 缓存存储已加载的 Lottie 动画，避免重复获取
interface LottieCache {
  [key: string]: any;
}

const cache: LottieCache = {};

/**
 * 加载和缓存 Lottie 动画的 Hook
 * @param url Lottie JSON 文件的 URL
 * @returns 加载的 Lottie 动画 JSON
 */
export function useLottieResource(url: string) {
  const [source, setSource] = useState<any>(null);

  useEffect(() => {
    if (!url) return;

    async function fetchLottieJson() {
      try {
        // 如果已在缓存中，使用缓存版本
        if (cache[url]) {
          setSource(cache[url]);
          return;
        }

        // 获取并解析 JSON
        const response = await fetch(url);
        const json = await response.json();

        // 缓存结果
        cache[url] = json;
        setSource(json);
      } catch (error) {
        console.error('加载 Lottie 动画失败:', error);
      }
    }

    fetchLottieJson();
  }, [url]);

  return source;
}

/**
 * 预加载 Lottie 动画的 Hook，适用于任何 Lottie 动画
 * @param urls 单个 URL 或需要预加载的 URL 数组
 * @returns 包含加载状态和预加载函数的对象
 */
export function useLottiePreload(urls?: string | string[]) {
  const [isLoading, setIsLoading] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // 使用 useRef 存储 urls，避免每次渲染创建新的数组引用
  const urlsRef = useRef<string[]>([]);
  
  // 初始化或更新 urlsRef
  useEffect(() => {
    if (typeof urls === 'string') {
      urlsRef.current = [urls];
    } else if (Array.isArray(urls)) {
      urlsRef.current = [...urls];
    }
  }, [urls]);
  
  // 检查 URLs 是否已经加载过
  const checkIfAlreadyLoaded = useCallback(() => {
    if (urlsRef.current.length === 0) return false;
    
    // 所有 URL 都已缓存则认为已加载
    const allCached = urlsRef.current.every(url => !!cache[url]);
    if (allCached && !isLoaded) {
      setIsLoaded(true);
    }
    return allCached;
  }, [isLoaded]);
  
  // 初始检查是否已加载
  useEffect(() => {
    checkIfAlreadyLoaded();
  }, [checkIfAlreadyLoaded]);

  // 可手动调用的预加载函数，使用 useCallback 并减少依赖
  const preload = useCallback(async (urlsToLoad?: string | string[]) => {
    // 避免重复加载请求
    if (isLoading) return;
    
    let urlsToLoadArray: string[] = [];
    
    if (urlsToLoad) {
      // 如果提供了 urlsToLoad 参数，使用它
      if (typeof urlsToLoad === 'string') {
        urlsToLoadArray = [urlsToLoad];
      } else {
        urlsToLoadArray = [...urlsToLoad];
      }
    } else {
      // 否则使用 urlsRef.current
      urlsToLoadArray = [...urlsRef.current];
    }
    
    // 如果没有 URL 需要加载或全部已缓存，直接返回
    if (urlsToLoadArray.length === 0 || urlsToLoadArray.every(url => !!cache[url])) {
      setIsLoaded(true);
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      // 仅加载未缓存的 URL
      const uncachedUrls = urlsToLoadArray.filter(url => !cache[url]);
      
      if (uncachedUrls.length === 0) {
        setIsLoaded(true);
        setIsLoading(false);
        return;
      }
      
      // 并行加载所有未缓存的 URL
      await Promise.all(uncachedUrls.map(async (url) => {
        try {
          const response = await fetch(url);
          const json = await response.json();
          cache[url] = json;
        } catch (err) {
          console.error(`加载 ${url} 失败:`, err);
          // 不中断整体加载
        }
      }));
      
      setIsLoaded(true);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('预加载动画失败'));
      console.error('预加载动画失败:', err);
    } finally {
      setIsLoading(false);
    }
  }, [isLoading]); // 仅依赖 isLoading

  // 仅在首次挂载和 URLs 变化时自动预加载
  useEffect(() => {
    // 如果已经加载过，或没有 URL，跳过
    if (checkIfAlreadyLoaded() || urlsRef.current.length === 0) return;
    
    // 执行预加载
    preload();
    
    // 仅在组件挂载时运行一次
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return {
    isLoading,
    isLoaded,
    error,
    preload,
    // 暴露缓存供高级用法
    getFromCache: useCallback((url: string) => cache[url] || null, []),
  };
} 