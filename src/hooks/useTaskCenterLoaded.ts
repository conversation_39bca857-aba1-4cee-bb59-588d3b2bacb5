import { useEffect, useState } from "react";
import GlobalEventEmitter from "utilsV2/globalEventEmitter";

export default function useTaskCenterLoaded() {
  const [taskCenterLoaded, setTaskCenterLoaded] = useState(false);
  
  useEffect(() => {
    const listener = GlobalEventEmitter.addListener('taskCenterLoaded', () => {
      setTaskCenterLoaded(true);
    });
    return () => {
      listener.remove();
    }
  }, [])

  return taskCenterLoaded;
}