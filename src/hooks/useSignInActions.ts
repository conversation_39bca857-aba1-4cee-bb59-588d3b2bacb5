import { useSet<PERSON><PERSON> } from "jotai";
import useRewardGoldCoin from 'hooks/useRewardGoldCoin';
import useThrottleCallback from 'hooks/useThrottleCallback';
import { AD_POSITION, AD_SOURCE, RewardType } from "constants/ad";
import watchAd from "utils/watchAd";
import { FallbackReqType } from "constants/ad";
import { writeSignInTaskAtom } from "../components/CoinCenter/SignIn/store";
import { reSignIn } from "services/welfare";
import { Toast } from "@xmly/rn-sdk";

export function useSignInActions() {
    // const signInTask = useAtomValue(signInTaskAtom);
    const querySignInTask = useSetAtom(writeSignInTaskAtom);
    const rewardGoldCoinHook = useRewardGoldCoin();
  
    const handleAdSignInAction = useThrottleCallback(async (adCoins: number) => {
        // clickReport(adSignInBtnText);
        const res = await watchAd({
          sourceName: AD_SOURCE.AD_SIGN_IN,
          positionName: AD_POSITION.positionName,
          slotId: AD_POSITION.slotId,
          rewardType: RewardType.AD_SIGN_IN,
          coins: adCoins,
        });
        if (res.success) {
          await rewardGoldCoinHook({
            coins: adCoins,
            rewardType: RewardType.AD_SIGN_IN,
            sourceName: AD_SOURCE.AD_SIGN_IN,
            adId: res.adId,
            adResponseId: res.adResponseId,
            encryptType: res.encryptType,
            ecpm: res.ecpm,
            fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL,
          });
          querySignInTask();
        }
    });
  
    const handleReSignInAction = useThrottleCallback(async (reSignInDay: number = 0) => {
        // clickReport(reSignInDay > 0 ? '补' : '去补签');
        // if (!signInTask?.enableResignIn) {
        //   return Toast.info('每天只能补签一次');
        // }
        const res = await watchAd({
          sourceName: AD_SOURCE.AD_SIGN_IN,
          positionName: AD_POSITION.positionName,
          slotId: AD_POSITION.slotId,
          rewardType: RewardType.AD_SIGN_IN,
        });
        if (res.success) {
          const result = await reSignIn({
            adId: res?.adId,
            adResponseId: res?.adResponseId,
            encryptType: res.encryptType,
            ecpm: res.ecpm,
            fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL,
            reSignInDay
          });
          if (result?.data?.success) {
            Toast.info('补签成功');
          } else {
            Toast.info(result?.data?.toast || '补签失败');
          }
          querySignInTask();
        }
    });
  
    return { handleAdSignInAction, handleReSignInAction };
  }