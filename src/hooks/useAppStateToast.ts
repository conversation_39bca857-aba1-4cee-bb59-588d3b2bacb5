import { useEffect, useRef, useCallback, useState } from "react";
import { AppState, AppStateStatus, Platform } from "react-native";
import { PageEventEmitter } from "defs";
import { Toast } from "@xmly/rn-sdk";

/**
 * 自定义 Hook，用于处理应用前后台状态切换时的 Toast 消息展示
 * @param options 可选配置项
 * @param options.customToastHandler 自定义的 Toast 处理函数，默认使用 Toast.info
 * @returns Toast 相关的工具函数
 * 
 * 场景A：由app内其他页面返回当前页面时触发
 * 场景B：由手机再次将app置为前台恢复时触发
 */
export default function useAppStateToast(options?: {
  customToastHandler?: (message: string) => void
}) {
  // 提取自定义的 Toast 处理函数
  const { customToastHandler } = options || {};
  const isIOS = Platform.OS === 'ios';

  // 存储待显示的消息
  const pendingToast = useRef<{ message: string } | null>(null);

  // 维护页面是否在前台的状态（针对 iOS 中 onPause 触发但 AppState 仍为 active 的情况）
  const [isPageInForeground, setIsPageInForeground] = useState(true);

  // 检查应用是否在前台
  const isAppInForeground = useCallback((): boolean => {
    // 在 iOS 上，同时考虑 AppState 和页面的前台状态
    if (isIOS) {
      return AppState.currentState === 'active' && isPageInForeground;
    }
    // 在 Android 上，直接使用 AppState
    return AppState.currentState === 'active';
  }, [isPageInForeground]);

  // 显示 Toast 的处理函数
  const showPendingToast = useCallback(() => {
    if (pendingToast.current) {
      if (customToastHandler) {
        customToastHandler(pendingToast.current.message);
      } else {
        Toast.info(pendingToast.current.message);
      }
      pendingToast.current = null;
    }
  }, [customToastHandler]);

  // 处理 AppState 变化事件
  const handleAppStateChange = useCallback((nextAppState: AppStateStatus) => {
    if (nextAppState === 'active') {
      // 只有当 AppState 变为 active，才触发显示 toast
      // 对于 iOS 由于页面状态可能还是后台，所以还需要检查页面前台状态
      if (isIOS || isPageInForeground) {
        showPendingToast();
      }
    }
  }, [showPendingToast, isPageInForeground]);

  // 处理页面恢复事件
  const handlePageResume = useCallback(() => {
    // 更新页面前台状态
    setIsPageInForeground(true);

    // 在 iOS 上，当页面恢复前台时，需要检查 AppState 是否为 active
    if (isIOS) {
      if (AppState.currentState === 'active') {
        showPendingToast();
      }
    } else {
      // Android 上，页面恢复就可以显示 toast
      showPendingToast();
    }
  }, [showPendingToast]);

  // 处理页面暂停事件
  const handlePagePause = useCallback(() => {
    // 当页面进入后台时，更新页面前台状态
    setIsPageInForeground(false);
  }, []);

  // 根据应用状态显示 toast 或存储待显示的消息
  const showToast = useCallback((message: string) => {
    if (isAppInForeground()) {
      if (customToastHandler) {
        customToastHandler(message);
      } else {
        Toast.info(message);
      }
    } else {
      pendingToast.current = { message };
    }
  }, [isAppInForeground, customToastHandler]);

  // 设置事件监听器
  useEffect(() => {
    // 针对 iOS 场景 A，需要监听 onResume 和 onPause 事件
    let resumeListener: any = null;
    let pauseListener: any = null;
    if (isIOS) {
      resumeListener = PageEventEmitter.addListener('onResume', handlePageResume);
      pauseListener = PageEventEmitter.addListener('onPause', handlePagePause);
    }

    // 同时监听 AppState 变化（iOS 场景 B 和 Android 所有场景）
    AppState.addEventListener('change', handleAppStateChange);

    // 组件卸载时清除事件监听器
    return () => {
      resumeListener && resumeListener?.remove();
      pauseListener && pauseListener?.remove();
      AppState.removeEventListener('change', handleAppStateChange);
    };
  }, [handlePageResume, handlePagePause, handleAppStateChange]);

  return {
    showToast
  };
} 