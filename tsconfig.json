{"compilerOptions": {"baseUrl": "./src", "outDir": "./dist/", "module": "esnext", "target": "es5", "downlevelIteration": true, "lib": ["es2015", "es2017", "dom"], "sourceMap": true, "allowJs": true, "jsx": "react", "moduleResolution": "node", "forceConsistentCasingInFileNames": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "suppressImplicitAnyIndexErrors": true, "noUnusedLocals": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "paths": {"@/*": ["./src/*"]}}, "exclude": ["node_modules", "dist"]}