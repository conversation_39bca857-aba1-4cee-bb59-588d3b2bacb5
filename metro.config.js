/**
 * Metro configuration for React Native
 * https://github.com/facebook/react-native
 *
 * @format
 */

// const fs = require('fs')
// if (!process.env.MB_DEBUG) {
//   try {
//     const prodConfig = fs.readFileSync('./babel.config.prod.js')
//     fs.writeFileSync('./babel.config.js', prodConfig)
//   } catch (err) {}
// } else {
//   try {
//     const devConfig = fs.readFileSync('./babel.config.dev.js')
//     fs.writeFileSync('./babel.config.js', devConfig)
//   } catch (err) {}
// }

module.exports = {
  /* Your existing configuration, optional */
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: false,
        inlineRequires: false,
      },
    }),
  },
  resolver: {
    resolverMainFields: ['react-native', 'browser', 'main'],
  },
}
