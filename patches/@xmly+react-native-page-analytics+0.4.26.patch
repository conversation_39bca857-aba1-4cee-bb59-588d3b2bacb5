diff --git a/node_modules/@xmly/react-native-page-analytics/src/ScrollAnalytic3.tsx b/node_modules/@xmly/react-native-page-analytics/src/ScrollAnalytic3.tsx
index 95a416f..50756cc 100644
--- a/node_modules/@xmly/react-native-page-analytics/src/ScrollAnalytic3.tsx
+++ b/node_modules/@xmly/react-native-page-analytics/src/ScrollAnalytic3.tsx
@@ -19,6 +19,7 @@ export type Props = {
   debugTitle?: string;
   isNormalVirtualizedList?: boolean;
   children?: React.ReactNode;
+  scrollEventThrottle?: number;
 };
 
 export default class ScrollAnalytics extends React.PureComponent<Props> {
@@ -102,7 +103,7 @@ export default class ScrollAnalytics extends React.PureComponent<Props> {
   _cancelTimeout: NodeJS.Timeout | undefined;
   _onScrollHandler() {
     if (this._cancelTimeout) clearTimeout(this._cancelTimeout);
-    this._cancelTimeout = setTimeout(this._isViewable, 500);
+    this._cancelTimeout = setTimeout(this._isViewable, this.props.scrollEventThrottle || 500);
   }
 
   layoutPromiseResolve: (() => void) | undefined;
diff --git a/node_modules/@xmly/react-native-page-analytics/src/ScrollAnalyticComp.tsx b/node_modules/@xmly/react-native-page-analytics/src/ScrollAnalyticComp.tsx
index 09fa367..79493a4 100644
--- a/node_modules/@xmly/react-native-page-analytics/src/ScrollAnalyticComp.tsx
+++ b/node_modules/@xmly/react-native-page-analytics/src/ScrollAnalyticComp.tsx
@@ -18,6 +18,7 @@ export interface ScrollProps {
   onRefreshed?: () => void;
   isNormalVirtualizedList?: boolean;
   allowRepeatExpose?: boolean;
+  scrollEventThrottle?: number;
 }
 
 export enum ExposeType {
