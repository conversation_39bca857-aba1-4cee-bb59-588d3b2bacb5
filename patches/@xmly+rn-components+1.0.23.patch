diff --git a/node_modules/@xmly/rn-components/dist/FastImage/index.js b/node_modules/@xmly/rn-components/dist/FastImage/index.js
index d1b63c3..0c4cf46 100644
--- a/node_modules/@xmly/rn-components/dist/FastImage/index.js
+++ b/node_modules/@xmly/rn-components/dist/FastImage/index.js
@@ -150,7 +150,7 @@ function FastImageBase(_a) {
     }
 }
 var FastImageMemo = memo(FastImageBase, function (prevProps, props) {
-    return isEqual(prevProps.source, props.source) ||
+    return isEqual(prevProps.source, props.source) &&
         isEqual(prevProps.style, props.style);
 });
 var FastImageComponent = forwardRef(function (props, ref) { return (<FastImageMemo forwardedRef={ref} {...props}/>); });
diff --git a/node_modules/@xmly/rn-components/dist/ReSlideUpPanel/index.js b/node_modules/@xmly/rn-components/dist/ReSlideUpPanel/index.js
index 4a605d8..c194b52 100644
--- a/node_modules/@xmly/rn-components/dist/ReSlideUpPanel/index.js
+++ b/node_modules/@xmly/rn-components/dist/ReSlideUpPanel/index.js
@@ -193,7 +193,8 @@ var ReSlideUpPanel = /** @class */ (function (_super) {
             }
             var style = (_a = {
                     transform: [{ translateY: translateY, translateX: translateX }],
-                    position: 'absolute'
+                    position: 'absolute',
+                    width: '100%',
                 },
                 _a[DirctionEnum[direction]] = 0,
                 _a.zIndex = 1000,
