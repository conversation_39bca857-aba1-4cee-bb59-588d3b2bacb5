import { Platform } from "react-native";
import { EnvName } from "./src/typesV2/common";

export const isAndroid = Platform.OS === 'android'
export const isHarmony = (Platform.OS as any) === 'harmony'

export const rnVersion = '0.63';

const rnEnv = () => {
  let isInit = false
  let $$_RN_ENV: EnvName = EnvName.prod

  const getEnv = () => {
    return $$_RN_ENV
  }

  const getFormatEnvType = (envType: number) => {
    if (isAndroid) {
      switch (envType) {
        case 1:
          return EnvName.prod
        case 4:
          return EnvName.test
        case 6:
          return EnvName.uat
        default:
          return EnvName.prod
      }
    } else {
      switch (envType) {
        case 1:
          return EnvName.test
        case 2:
          return EnvName.prod
        case 3:
          return EnvName.uat
        default:
          return EnvName.prod
      }
    }
  }

  const setEnv = (envType: number) => {
    $$_RN_ENV = getFormatEnvType(envType)
    isInit = true
  }

  const isTest = () => $$_RN_ENV === EnvName.test

  const isOnline = () => $$_RN_ENV === EnvName.prod

  const isUAT = () => $$_RN_ENV === EnvName.uat

  return {
    getFormatEnvType,
    getEnv,
    setEnv,
    isTest,
    isOnline,
    isUAT,
    isInit
  }
}

export default rnEnv()