const qrcode = require('qrcode-terminal')
const os = require('os')

const baseURL = `iting://open?msg_type=94&__debug=1`
const bundleName = require('./app.json').name

function getIPAddress() {
  const interfaces = os.networkInterfaces()
  for (let devName in interfaces) {
    const iface = interfaces[devName]
    for (let i = 0; i < iface.length; i++) {
      let alias = iface[i]
      if (
        alias.family === 'IPv4' &&
        alias.address !== '127.0.0.1' &&
        !alias.internal
      ) {
        return alias.address
      }
    }
  }
}

function getProjectDevURL(bundleName, port = 8081, args) {
  const LOCAL_IP = getIPAddress()
  const ip = `${LOCAL_IP}:${port}`
  const argsArr = []
  for (let key in args) {
    argsArr.push(`${key}=${args[key]}`)
  }
  return `${baseURL}&bundle=${bundleName}&ip=${ip}&__ip=${ip}${
    argsArr.length > 0 ? `&${argsArr.join('&')}` : ''
  }`
}

function run() {
  const devURL = getProjectDevURL(bundleName)
  qrcode.generate(devURL, { small: true })
  console.log('')
  console.log(`scan to dev at: ${devURL} \n\n\n`)
  console.log('')
}

run()
