import 'react-native-gesture-handler'
import { disable<PERSON><PERSON>wBox, disableFontScaling } from '@xmly/rn-utils'
import { AppRegistry } from 'react-native'
import Root from './src/Root'

disableYellowBox()
disableFontScaling()

AppRegistry.registerComponent(__DEV__ ? 'rn_credit_center' : 'rn_credit_center_lite2', () => Root)

// if (__DEV__) {
//   const modules = require.getModules()
//   const moduleIds = Object.keys(modules)
//   const loadedModuleNames = moduleIds
//     .filter((moduleId) => modules[moduleId].isInitialized)
//     .map((moduleId) => modules[moduleId].verboseName)
//   const waitingModuleNames = moduleIds
//     .filter((moduleId) => !modules[moduleId].isInitialized)
//     .map((moduleId) => modules[moduleId].verboseName)

//   // make sure that the modules you expect to be waiting are actually waiting
//   console.log(
//     'loaded:',
//     loadedModuleNames.length,
//     'waiting:',
//     waitingModuleNames.length
//   )

//   // grab this text blob, and put it in a file named packager/modulePaths.js
//   // console.log(`module.exports = ${JSON.stringify(loadedModuleNames.sort())};`)
// }
