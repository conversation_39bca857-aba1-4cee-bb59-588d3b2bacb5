{"compilerOptions": {"target": "es5", "module": "esnext", "lib": ["es7", "dom"], "goodies": ["es2015.promise", "es2015.symbol", "es2015.symbol.wellknown", "es2015.object", "es2015.array", "es2015.string", "es2015.iterable"], "jsx": "react-native", "declaration": true, "preserveConstEnums": true, "moduleResolution": "node", "experimentalDecorators": true, "noImplicitReturns": true, "noImplicitThis": true, "noImplicitAny": true, "strictNullChecks": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "outDir": "dist", "noUnusedLocals": true, "baseUrl": "./src", "rootDir": "./src", "skipLibCheck": true, "importHelpers": true}, "compileOnSave": false}