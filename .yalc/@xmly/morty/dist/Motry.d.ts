import { SafeToRemove } from './AnimatePresence/use-presence';
import { FC } from 'react';
import { StyleProp, ViewStyle, PerpectiveTransform, RotateTransform, RotateXTransform, RotateYTransform, RotateZTransform, ScaleTransform, ScaleXTransform, ScaleYTransform, TranslateXTransform, TranslateYTransform, SkewXTransform, SkewYTransform, ImageStyle, TextStyle } from 'react-native';
import Animated from 'react-native-reanimated';
export declare type Transforms = PerpectiveTransform & RotateTransform & RotateXTransform & RotateYTransform & RotateZTransform & ScaleTransform & ScaleXTransform & ScaleYTransform & TranslateXTransform & TranslateYTransform & SkewXTransform & SkewYTransform;
export declare type TransitionConfig = (({
    type: 'timing';
} & Animated.TimingConfig));
/**
 * Allow { scale: 1 }
 *
 * If it's a sequence:
 * { scale: [0, 1] }
 *
 * Or { scale: [{ value: 0, step: 0 }, {value: 1, step: 0.1 }]}
 * to allow more granular specification of sequence values
 */
export declare type StyleValueWithSequenceArrays<T> = {
    [key in keyof T]: T[keyof T] | (({
        value: T[keyof T];
        step: number;
    }))[] | T[keyof T][];
};
export declare type StyleValueWithReplacedTransforms<StyleProp> = Omit<StyleProp, 'transform'> & Partial<Transforms>;
declare type AnimateType = ImageStyle & TextStyle & ViewStyle;
declare type AnimateWithTransitions = StyleValueWithReplacedTransforms<AnimateType>;
declare type Animate = StyleValueWithSequenceArrays<AnimateWithTransitions>;
export interface MortyTransitionType {
    duration?: number;
    easing?: Animated.EasingFunction;
    afterEnter?: () => void;
    beforeEnter?: () => void;
    beforeLeave?: () => void;
    afterLeave?: () => void;
}
interface MortyProps {
    transition?: MortyTransitionType;
    exitEasing?: Animated.EasingFunction;
    beforeExit?: () => void;
    afterExit?: () => void;
    style?: StyleProp<ViewStyle>;
    animate?: Animate;
    controller?: ReturnType<typeof useMortyController>;
    isPresent: boolean;
    isExiting: boolean;
    safeToUnmount: SafeToRemove | null | undefined;
    exit?: Animate;
    reanimatedSafeToUnmount: () => void;
    exitDuration?: number;
}
declare enum AnimationState {
    Start = 1,
    Stop = 0,
    UNSET = -1
}
export declare function useMortyController(): {
    animationState: Animated.Value<AnimationState>;
    enter: () => void;
    leave: () => void;
    resetState: () => void;
};
export declare type createAnimationOptions = {
    duration: number;
    fromValue: Animated.Value<number>;
    toValue: Animated.Value<number>;
    onBegin?: () => void;
    onFinished?: () => void;
    easing?: Animated.EasingFunction;
};
declare const MortyFC: FC<Omit<MortyProps, 'isPresent' | 'safeToUnmount' | 'reanimatedSafeToUnmount' | 'isExiting'>>;
export default MortyFC;
