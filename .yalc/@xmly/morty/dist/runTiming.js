import { block, call, clockRunning, cond, Easing, set, startClock, stopClock, timing, Value } from "react-native-reanimated";
var runTiming = function (_a) {
    var clock = _a.clock, from = _a.from, to = _a.to, duration = _a.duration, onBegin = _a.onBegin, onFinished = _a.onFinished, _b = _a.easing, easing = _b === void 0 ? Easing.linear : _b;
    var state = {
        finished: new Value(0),
        position: typeof from === 'number' ? new Value(from) : from,
        time: new Value(0),
        frameTime: new Value(0),
    };
    var config = {
        duration: duration,
        toValue: typeof to === 'number' ? new Value(to) : to,
        easing: easing,
    };
    return block([
        cond(clockRunning(clock), 0, [
            set(state.finished, 0),
            set(state.time, 0),
            set(state.position, from),
            set(state.frameTime, 0),
            set(config.toValue, to),
            startClock(clock),
            call([], function () {
                onBegin && onBegin();
            })
        ]),
        timing(clock, state, config),
        cond(state.finished, [
            stopClock(clock),
            call([], function () {
                onFinished && onFinished();
            }),
        ]),
        state.position,
    ]);
};
export default runTiming;
