import { useState, useCallback, useRef } from "react";
import { useUnmountEffect } from "./use-unmount-effect";
export function useForceUpdate() {
    var unloadingRef = useRef(false);
    var _a = useState(0), forcedRenderCount = _a[0], setForcedRenderCount = _a[1];
    useUnmountEffect(function () { return (unloadingRef.current = true); });
    return useCallback(function () {
        !unloadingRef.current && setForcedRenderCount(forcedRenderCount + 1);
    }, [forcedRenderCount]);
}
