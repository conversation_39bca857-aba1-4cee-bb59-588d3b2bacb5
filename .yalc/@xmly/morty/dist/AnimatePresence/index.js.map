{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/AnimatePresence/index.tsx"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,OAAO,EACH,MAAM,EACN,cAAc,EACd,YAAY,EACZ,QAAQ,GAGX,MAAM,OAAO,CAAA;AACd,OAAO,KAAK,KAAK,MAAM,OAAO,CAAA;AAE9B,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAA;AACnD,OAAO,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAA;AAG/C,SAAS,WAAW,CAAC,KAAwB;IACzC,OAAO,KAAK,CAAC,GAAG,IAAI,EAAE,CAAA;AAC1B,CAAC;AAED,SAAS,iBAAiB,CACtB,QAA6B,EAC7B,WAAiD;IAEjD,IAAM,YAAY,GACd,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,IAAI,GAAG,EAAgB,CAAC,CAAC,CAAC,IAAI,CAAA;IAE1E,QAAQ,CAAC,OAAO,CAAC,UAAC,KAAK;QACnB,IAAM,GAAG,GAAG,WAAW,CAAC,KAAK,CAAC,CAAA;QAE9B,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,YAAY,EAAE;YACvD,IAAI,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACvB,OAAO,CAAC,IAAI,CACR,wDAAqD,GAAG,uBAAmB,CAC9E,CAAA;aACJ;YAED,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SACxB;QAED,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IAC/B,CAAC,CAAC,CAAA;AACN,CAAC;AAED,SAAS,YAAY,CAAC,QAAmB;IACrC,IAAM,QAAQ,GAAwB,EAAE,CAAA;IAExC,0FAA0F;IAC1F,QAAQ,CAAC,OAAO,CAAC,QAAQ,EAAE,UAAC,KAAK;QAC7B,IAAI,cAAc,CAAC,KAAK,CAAC;YAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACnD,CAAC,CAAC,CAAA;IAEF,OAAO,QAAQ,CAAA;AACnB,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6DG;AACH,MAAM,CAAC,IAAM,eAAe,GAAkD,UAAC,EAO9E;QANG,QAAQ,cAAA,EACR,MAAM,YAAA,EACN,eAAc,EAAd,OAAO,mBAAG,IAAI,KAAA,EACd,cAAc,oBAAA,EACd,eAAe,qBAAA,EACf,6BAA4B,EAA5B,qBAAqB,mBAAG,IAAI,KAAA;IAE5B,6EAA6E;IAC7E,sFAAsF;IACtF,IAAI,WAAW,GAAG,cAAc,EAAE,CAAA;IAElC,IAAM,eAAe,GAAG,MAAM,CAAC,IAAI,CAAC,CAAA;IAEpC,sGAAsG;IACtG,IAAM,gBAAgB,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAA;IAE/C,sEAAsE;IACtE,wDAAwD;IACxD,IAAM,eAAe,GAAG,MAAM,CAAC,gBAAgB,CAAC,CAAA;IAEhD,wDAAwD;IACxD,IAAM,WAAW,GAAG,MAAM,CAAC,IAAI,GAAG,EAAmC,CAAC;SACjE,OAAO,CAAA;IAEZ,uDAAuD;IACvD,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,GAAG,EAAgB,CAAC,CAAC,OAAO,CAAA;IAEvD,iBAAiB,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAA;IAEhD,oFAAoF;IACpF,qCAAqC;IACrC,IAAI,eAAe,CAAC,OAAO,EAAE;QACzB,eAAe,CAAC,OAAO,GAAG,KAAK,CAAA;QAE/B,OAAO,CACH,0CACK,gBAAgB,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,CAC7B,oBAAC,aAAa,IACV,GAAG,EAAE,WAAW,CAAC,KAAK,CAAC,EACvB,SAAS,QACT,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,EACpC,qBAAqB,EAAE,qBAAqB,IAE3C,KAAK,CACM,CACnB,EATgC,CAShC,CAAC,CACH,CACN,CAAA;KACJ;IAED,0EAA0E;IAC1E,IAAI,gBAAgB,YAAO,gBAAgB,CAAC,CAAA;IAE5C,2EAA2E;IAC3E,gBAAgB;IAChB,IAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAC5D,IAAM,UAAU,GAAG,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAA;IAEpD,qFAAqF;IACrF,IAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAA;IACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;QACjC,IAAM,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAA;QAC1B,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;YAChC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;SACnB;aAAM;YACH,gEAAgE;YAChE,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;SACtB;KACJ;IAED,yFAAyF;IACzF,kFAAkF;IAClF,IAAI,eAAe,IAAI,OAAO,CAAC,IAAI,EAAE;QACjC,gBAAgB,GAAG,EAAE,CAAA;KACxB;IAED,sFAAsF;IACtF,gDAAgD;IAChD,OAAO,CAAC,OAAO,CAAC,UAAC,GAAG;QAChB,6DAA6D;QAC7D,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAAE,OAAM;QAE1C,IAAM,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAClC,IAAI,CAAC,KAAK;YAAE,OAAM;QAElB,IAAM,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QAE/C,IAAM,MAAM,GAAG;YACX,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YACvB,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;YAEnB,8CAA8C;YAC9C,IAAM,WAAW,GAAG,eAAe,CAAC,OAAO,CAAC,SAAS,CACjD,UAAC,YAAY,IAAK,OAAA,YAAY,CAAC,GAAG,KAAK,GAAG,EAAxB,CAAwB,CAC7C,CAAA;YACD,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC,CAAC,CAAA;YAE9C,iEAAiE;YACjE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;gBACf,eAAe,CAAC,OAAO,GAAG,gBAAgB,CAAA;gBAC1C,WAAW,EAAE,CAAA;gBACb,cAAc,IAAI,cAAc,EAAE,CAAA;aACrC;QACL,CAAC,CAAA;QAED,gBAAgB,CAAC,MAAM,CACnB,cAAc,EACd,CAAC,EACD,oBAAC,aAAa,IACV,GAAG,EAAE,WAAW,CAAC,KAAK,CAAC,EACvB,SAAS,EAAE,KAAK,EAChB,cAAc,EAAE,MAAM,EACtB,MAAM,EAAE,MAAM,EACd,qBAAqB,EAAE,qBAAqB,IAE3C,KAAK,CACM,CACnB,CAAA;IACL,CAAC,CAAC,CAAA;IAEF,oFAAoF;IACpF,gCAAgC;IAChC,gBAAgB,GAAG,gBAAgB,CAAC,GAAG,CAAC,UAAC,KAAK;QAC1C,IAAM,GAAG,GAAG,KAAK,CAAC,GAAsB,CAAA;QACxC,OAAO,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACtB,KAAK,CACR,CAAC,CAAC,CAAC,CACA,oBAAC,aAAa,IACV,GAAG,EAAE,WAAW,CAAC,KAAK,CAAC,EACvB,SAAS,QACT,qBAAqB,EAAE,qBAAqB,IAE3C,KAAK,CACM,CACnB,CAAA;IACL,CAAC,CAAC,CAAA;IAEF,eAAe,CAAC,OAAO,GAAG,gBAAgB,CAAA;IAE1C,IACI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;QACrC,eAAe;QACf,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAC7B;QACE,OAAO,CAAC,IAAI,CACR,6JAA6J,CAChK,CAAA;KACJ;IAED,OAAO,CACH,0CACK,OAAO,CAAC,IAAI;QACT,CAAC,CAAC,gBAAgB;QAClB,CAAC,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAC,KAAK,IAAK,OAAA,YAAY,CAAC,KAAK,CAAC,EAAnB,CAAmB,CAAC,CAC3D,CACN,CAAA;AACL,CAAC,CAAA"}