import { __assign, __extends, __spreadArrays } from "tslib";
import { usePresence } from './AnimatePresence/use-presence';
import React, { useCallback, useEffect, useRef } from 'react';
import { StyleSheet } from 'react-native';
import Animated, { Clock, cond, clockRunning, call, interpolate, set, Easing, stopClock, eq, and, neq, interpolateColors, useValue } from 'react-native-reanimated';
import runTiming from './runTiming';
var transformValue = [
    'scaleX',
    'scaleY',
    'scale',
    'rotateX',
    'rotateY',
    'rotateZ',
    'rotate',
    'translateX',
    'translateY',
    'translate'
];
var DEBUG = false; // process.env.NODE_ENV !== 'production'
var isTransform = function (prop) { return transformValue.includes(prop); };
// const debug = (msg: any) => {
//   return call([], () => { console.log(msg) })
// }
var isColor = function (styleKey) {
    return [
        'backgroundColor',
        'borderBottomColor',
        'borderColor',
        'borderEndColor',
        'borderLeftColor',
        'borderRightColor',
        'borderStartColor',
        'borderTopColor',
        'color',
    ].includes(styleKey);
};
var AnimationState;
(function (AnimationState) {
    AnimationState[AnimationState["Start"] = 1] = "Start";
    AnimationState[AnimationState["Stop"] = 0] = "Stop";
    AnimationState[AnimationState["UNSET"] = -1] = "UNSET";
})(AnimationState || (AnimationState = {}));
function checkNonDecreasing(arr) {
    for (var i = 1; i < arr.length; ++i) {
        // We can't validate animated nodes in JS.
        if (arr[i] < arr[i - 1]) {
            throw new Error('step must be monotonically non-decreasing !!!!!');
        }
    }
}
function checkNonBiggerThen1(arr) {
    for (var i = 1; i < arr.length; ++i) {
        // We can't validate animated nodes in JS.
        if (arr[i] > 1) {
            throw new Error('step item muse less or equal than 1 !!!!!');
        }
    }
}
function checkSameLength(step, to) {
    if (step.length !== to.length) {
        throw new Error("step's length must equal with animate's length plus from's length");
    }
}
export function useMortyController() {
    var animationState = useValue(AnimationState.UNSET);
    return useRef({
        animationState: animationState,
        enter: function () {
            animationState.setValue(AnimationState.Start);
        },
        leave: function () {
            animationState.setValue(AnimationState.Stop);
        },
        resetState: function () {
            animationState.setValue(AnimationState.UNSET);
        }
    }).current;
}
var Morty = /** @class */ (function (_super) {
    __extends(Morty, _super);
    function Morty() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.exitAnimationValue = new Animated.Value(0);
        _this._animationValue = new Animated.Value(0);
        _this.leaveClock = new Clock();
        _this.enterClock = new Clock();
        _this.exitClock = new Clock();
        _this.interpolateSingleStyle = function (_a) {
            var animationValue = _a.animationValue, to = _a.to, _b = _a.step, step = _b === void 0 ? [0, 1] : _b;
            if (step[0] < 0) {
                throw new Error('step can not less then 0 !!!!!');
            }
            checkNonDecreasing(step);
            checkNonBiggerThen1(step);
            checkSameLength(step, __spreadArrays(to));
            return interpolate(animationValue, {
                inputRange: __spreadArrays(step),
                outputRange: __spreadArrays(to)
            });
        };
        _this.interpolateSingleColorStyle = function (_a) {
            var animationValue = _a.animationValue, to = _a.to, _b = _a.step, step = _b === void 0 ? [0, 1] : _b;
            if (step[0] < 0) {
                throw new Error('step can not less then 0 !!!!!');
            }
            checkNonDecreasing(step);
            checkNonBiggerThen1(step);
            checkSameLength(step, __spreadArrays(to));
            return interpolateColors(animationValue, {
                inputRange: __spreadArrays(step),
                outputColorRange: __spreadArrays(to)
            });
        };
        _this.getStepAndToValue = function (key, animatedValue) {
            var step = [];
            var toValue = [];
            if (animatedValue.every(function (v) { return typeof v === 'number'; }) || (animatedValue.every(function (v) { return typeof v === 'string'; }) && isColor(key))) {
                step = animatedValue.map(function (_, i) { return i * Number((1 / (animatedValue.length - 1)).toFixed(2)); });
                step[animatedValue.length - 1] = 1;
                DEBUG && console.log({ 'step🌟🌟🌟🌟🌟🌟🌟🌟🌟': step }, animatedValue);
                toValue = animatedValue;
            }
            if (animatedValue.every(function (v) { return (typeof v.step === 'number' && typeof v.value === 'number') || (typeof v.step === 'number' && typeof v.value === 'string' && isColor(key)); })) {
                step = animatedValue.map(function (v) { return v.step; });
                toValue = animatedValue.map(function (v) { return v.value; });
            }
            if (step.length !== toValue.length || step.length < 2 || toValue.length < 2) {
                throw new Error('animate config error!');
            }
            return {
                step: step,
                toValue: toValue
            };
        };
        _this.enterAnimation = function (options) {
            var duration = options.duration, _a = options.fromValue, fromValue = _a === void 0 ? new Animated.Value(0) : _a, _b = options.toValue, toValue = _b === void 0 ? new Animated.Value(1) : _b, _c = options.onBegin, onBegin = _c === void 0 ? function () { } : _c, _d = options.onFinished, onFinished = _d === void 0 ? function () { } : _d, _e = options.easing, easing = _e === void 0 ? Easing.linear : _e;
            return runTiming({
                clock: _this.enterClock,
                from: fromValue,
                to: toValue,
                duration: duration,
                onBegin: onBegin,
                onFinished: onFinished,
                easing: easing
            });
        };
        _this.exitAnimation = function (options) {
            var duration = options.duration, _a = options.fromValue, fromValue = _a === void 0 ? new Animated.Value(0) : _a, _b = options.toValue, toValue = _b === void 0 ? new Animated.Value(1) : _b, _c = options.onBegin, onBegin = _c === void 0 ? function () { } : _c, _d = options.onFinished, onFinished = _d === void 0 ? function () { } : _d, _e = options.easing, easing = _e === void 0 ? Easing.linear : _e;
            return runTiming({
                clock: _this.exitClock,
                from: fromValue,
                to: toValue,
                duration: duration,
                onBegin: onBegin,
                onFinished: onFinished,
                easing: easing
            });
        };
        _this.leaveAnimation = function (options) {
            var duration = options.duration, _a = options.fromValue, fromValue = _a === void 0 ? new Animated.Value(1) : _a, _b = options.toValue, toValue = _b === void 0 ? new Animated.Value(0) : _b, _c = options.onBegin, onBegin = _c === void 0 ? function () { } : _c, _d = options.onFinished, onFinished = _d === void 0 ? function () { } : _d, easing = options.easing;
            return runTiming({
                clock: _this.leaveClock,
                from: fromValue,
                to: toValue,
                duration: duration,
                onBegin: onBegin,
                onFinished: onFinished,
                easing: easing
            });
        };
        _this.handleExited = function () {
            if (typeof _this.props.afterExit === 'function') {
                _this.props.afterExit();
            }
            _this.props.reanimatedSafeToUnmount();
        };
        _this.handleBeforeExit = function () {
            _this.props.controller && _this.props.controller.resetState();
            if (typeof _this.props.beforeExit === 'function') {
                _this.props.beforeExit();
            }
        };
        _this.getAnimatedStyle = function (finalStyle, animationValue) {
            if (finalStyle === void 0) { finalStyle = {}; }
            var animatedStyle = {};
            var transforms = [];
            Object.keys(finalStyle).forEach(function (key) {
                var _a, _b, _c, _d;
                var _e, _f;
                var animatedValue = finalStyle[key];
                if (Array.isArray(animatedValue) && animatedValue.length > 1) {
                    var _g = _this.getStepAndToValue(key, animatedValue), step = _g.step, toValue = _g.toValue;
                    if (isTransform(key)) {
                        transforms.push((_a = {}, _a[key] = _this.interpolateSingleStyle({ animationValue: animationValue, step: step, to: toValue }), _a));
                    }
                    else if (isColor(key)) {
                        animatedStyle[key] = _this.interpolateSingleColorStyle({ animationValue: animationValue, step: step, to: toValue });
                    }
                    else {
                        animatedStyle[key] = _this.interpolateSingleStyle({ animationValue: animationValue, step: step, to: toValue });
                    }
                }
                else {
                    if (Array.isArray(animatedValue) && animatedValue.length === 1) {
                        if (typeof animatedValue[0] === 'object' && 'step' in animatedValue[0] && typeof ((_e = animatedValue[0]) === null || _e === void 0 ? void 0 : _e.step) === 'number' && typeof ((_f = animatedValue[0]) === null || _f === void 0 ? void 0 : _f.value) === 'number') {
                            if (isTransform(key)) {
                                transforms.push((_b = {}, _b[key] = animatedValue[0].value, _b));
                            }
                            else {
                                animatedStyle[key] = animatedValue[0].value;
                            }
                        }
                        else {
                            if (isTransform(key)) {
                                transforms.push((_c = {}, _c[key] = animatedValue[0], _c));
                            }
                            else {
                                animatedStyle[key] = animatedValue[0];
                            }
                        }
                    }
                    else if (!Array.isArray(animatedValue)) {
                        if (isTransform(key)) {
                            transforms.push((_d = {}, _d[key] = animatedValue, _d));
                        }
                        else {
                            animatedStyle[key] = animatedValue;
                        }
                    }
                }
                if (transforms.length > 0) {
                    // @ts-ignore
                    animatedStyle.transform = transforms;
                }
            });
            return animatedStyle;
        };
        _this.getExitStyle = function () {
            var _a;
            var allTransforms = {};
            Object.keys((_a = _this.props.animate) !== null && _a !== void 0 ? _a : {}).forEach(function (key) {
                var _a;
                if (isTransform(key)) {
                    var value = _this.props.animate[key];
                    if (Array.isArray(value)) {
                        if (value.every(function (v) { return typeof v === 'number'; })) {
                            allTransforms[key] = value[0];
                        }
                        if (value.every(function (v) { return typeof v.step === 'number' && (typeof v.value === 'string' || typeof v.value === 'number'); })) {
                            if (typeof value[0] === 'object' && 'step' in value[0] && ((_a = value[0]) === null || _a === void 0 ? void 0 : _a.step) === 0) {
                                allTransforms[key] = value[0].value;
                            }
                        }
                    }
                }
            });
            Object.keys(_this.props.exit).forEach(function (key) {
                if (isTransform(key)) {
                    allTransforms[key] = _this.props.exit[key];
                }
            });
            var style = _this.getAnimatedStyle(__assign(__assign({}, _this.props.exit), allTransforms), _this.exitAnimationValue);
            return style;
        };
        return _this;
    }
    Morty.prototype.render = function () {
        var _a = this.props, isExiting = _a.isExiting, transition = _a.transition, controller = _a.controller;
        var style = this.getAnimatedStyle(this.props.animate, this._animationValue);
        var exitStyle = isExiting ? this.getExitStyle() : {};
        var finalStyle = StyleSheet.flatten([this.props.style, style, exitStyle]);
        return (<>
        <Animated.View style={finalStyle}>
          {this.props.children}
        </Animated.View>
        {isExiting && <Animated.Code exec={Animated.block([
            cond(clockRunning(this.enterClock), [stopClock(this.enterClock)]),
            cond(clockRunning(this.leaveClock), [stopClock(this.leaveClock)]),
            set(this._animationValue, 0),
            set(this.exitAnimationValue, this.exitAnimation({
                fromValue: this.exitAnimationValue,
                toValue: new Animated.Value(1),
                duration: this.props.exitDuration || 350,
                easing: this.props.exitEasing || Easing.linear,
                onBegin: this.handleBeforeExit,
                onFinished: this.handleExited
            })),
            DEBUG ? call([this.exitAnimationValue], function (value) { console.log(value); }) : 0,
            DEBUG && controller ? call([controller.animationState], function (value) { console.log('controller.animationState', value); }) : 0,
        ])}/>}
        {!isExiting && controller && <Animated.Code exec={Animated.block([
            cond(and(eq(controller.animationState, AnimationState.Start), neq(this._animationValue, 1)), [
                cond(clockRunning(this.leaveClock), [stopClock(this.leaveClock)], []),
                set(this._animationValue, this.enterAnimation({
                    fromValue: this._animationValue,
                    toValue: new Animated.Value(1),
                    duration: (transition === null || transition === void 0 ? void 0 : transition.duration) || 300,
                    easing: transition === null || transition === void 0 ? void 0 : transition.easing,
                    onBegin: transition === null || transition === void 0 ? void 0 : transition.beforeEnter,
                    onFinished: transition === null || transition === void 0 ? void 0 : transition.afterEnter
                }))
            ]),
            cond(and(eq(controller.animationState, AnimationState.Stop), neq(this._animationValue, 0)), [
                cond(clockRunning(this.enterClock), [stopClock(this.enterClock)], []),
                set(this._animationValue, this.leaveAnimation({
                    fromValue: this._animationValue,
                    toValue: new Animated.Value(0),
                    duration: (transition === null || transition === void 0 ? void 0 : transition.duration) || 300,
                    easing: transition === null || transition === void 0 ? void 0 : transition.easing,
                    onBegin: transition === null || transition === void 0 ? void 0 : transition.beforeLeave,
                    onFinished: transition === null || transition === void 0 ? void 0 : transition.afterLeave
                }))
            ]),
            DEBUG ? call([this._animationValue], function (value) { console.log(value); }) : 0,
            DEBUG ? call([controller.animationState], function (value) { console.log('controller.animationState', value); }) : 0,
            cond(eq(controller.animationState, AnimationState.UNSET), [set(this._animationValue, 0)]),
        ])}/>}
        {!isExiting && !controller && <Animated.Code exec={Animated.block([
            cond(clockRunning(this.exitClock), [stopClock(this.exitClock)]),
            cond(neq(this.exitAnimationValue, 0), [set(this.exitAnimationValue, 0)]),
            set(this._animationValue, this.enterAnimation({
                fromValue: this._animationValue,
                toValue: new Animated.Value(1),
                duration: (transition === null || transition === void 0 ? void 0 : transition.duration) || 300,
                easing: transition === null || transition === void 0 ? void 0 : transition.easing,
                onBegin: transition === null || transition === void 0 ? void 0 : transition.beforeEnter,
                onFinished: transition === null || transition === void 0 ? void 0 : transition.afterEnter
            })),
            DEBUG ? call([this._animationValue], function (value) { console.log(value); }) : 0,
        ])}/>}
      </>);
    };
    return Morty;
}(React.Component));
var MortyFC = function (props) {
    var exit = props.exit;
    var _a = usePresence(), isPresent = _a[0], safeToUnmount = _a[1];
    var hasExitStyle = typeof exit === 'object' && !!Object.keys(exit !== null && exit !== void 0 ? exit : {}).length;
    var isExiting = !isPresent && hasExitStyle;
    useEffect(function allowUnMountIfMissingExit() {
        if (!isPresent && !hasExitStyle) {
            safeToUnmount === null || safeToUnmount === void 0 ? void 0 : safeToUnmount();
        }
    }, [hasExitStyle, isPresent, safeToUnmount]);
    var reanimatedSafeToUnmount = useCallback(function () {
        safeToUnmount === null || safeToUnmount === void 0 ? void 0 : safeToUnmount();
    }, [safeToUnmount]);
    return <Morty {...props} isPresent={isPresent} safeToUnmount={safeToUnmount} reanimatedSafeToUnmount={reanimatedSafeToUnmount} isExiting={isExiting}/>;
};
export default MortyFC;
