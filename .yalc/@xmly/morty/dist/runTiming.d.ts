import Animated, { Clock, Value } from "react-native-reanimated";
declare type RunTimingConfig = {
    clock: Clock;
    from: Value<number>;
    to: Value<number>;
    duration: number;
    easing?: Animated.EasingFunction;
    onBegin?: () => void;
    onFinished?: () => void;
};
declare const runTiming: ({ clock, from, to, duration, onBegin, onFinished, easing }: RunTimingConfig) => Animated.Node<number>;
export default runTiming;
