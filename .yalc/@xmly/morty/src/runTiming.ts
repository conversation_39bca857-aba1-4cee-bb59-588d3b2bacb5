import Animated, { block, call, Clock, clockRunning, cond, Easing, set, startClock, stopClock, timing, Value } from "react-native-reanimated";


type RunTimingConfig = {
  clock: Clock
  from: Value<number>
  to: Value<number>
  duration: number
  easing?: Animated.EasingFunction
  onBegin?: () => void
  onFinished?: () => void
}

const runTiming = ({ clock, from, to, duration, onBegin, onFinished, easing = Easing.linear }: RunTimingConfig) => {
  const state = {
    finished: new Value(0),
    position: typeof from === 'number' ? new Value(from) : from,
    time: new Value(0),
    frameTime: new Value(0),
  };

  const config = {
    duration,
    toValue: typeof to === 'number' ? new Value(to) : to,
    easing,
  };

  return block([
    cond(clockRunning(clock), 0, [
      set(state.finished, 0),
      set(state.time, 0),
      set(state.position, from),
      set(state.frameTime, 0),
      set(config.toValue, to),
      startClock(clock),
      call([], () => {
        onBegin && onBegin()
      })
    ]),
    timing(clock, state, config),
    cond(state.finished,
      [
        stopClock(clock),
        call([], () => {
          onFinished && onFinished()
        }),
      ]
    ),
    state.position,
  ]);
}


export default runTiming