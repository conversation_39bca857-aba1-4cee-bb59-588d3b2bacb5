import { usePresence, SafeToRemove } from './AnimatePresence/use-presence'
import React, { FC, useCallback, useEffect, useRef } from 'react'
import {
  StyleProp,
  ViewStyle,
  PerpectiveTransform,
  RotateTransform,
  RotateXTransform,
  RotateYTransform,
  RotateZTransform,
  ScaleTransform,
  ScaleXTransform,
  ScaleYTransform,
  TranslateXTransform,
  TranslateYTransform,
  SkewXTransform,
  SkewYTransform,
  ImageStyle,
  TextStyle,
  StyleSheet
} from 'react-native'
import Animated, { Clock, cond, clockRunning, call, interpolate, set, Easing, stopClock, eq, and, neq, interpolateColors, useValue } from 'react-native-reanimated'
import runTiming from './runTiming'

const transformValue = [
  'scaleX',
  'scaleY',
  'scale',
  'rotateX',
  'rotateY',
  'rotateZ',
  'rotate',
  'translateX',
  'translateY',
  'translate'
]

export type Transforms = PerpectiveTransform &
  RotateTransform &
  RotateXTransform &
  RotateYTransform &
  RotateZTransform &
  ScaleTransform &
  ScaleXTransform &
  ScaleYTransform &
  TranslateXTransform &
  TranslateYTransform &
  SkewXTransform &
  SkewYTransform

export type TransitionConfig = (
  // | ({ type?: 'spring' } & Animated.SpringConfig)
  | ({ type: 'timing' } & Animated.TimingConfig)
  // | ({ type: 'decay' } & Animated.DecayConfig)
)

const DEBUG = false// process.env.NODE_ENV !== 'production'
/**
 * Allow { scale: 1 }
 *
 * If it's a sequence:
 * { scale: [0, 1] }
 *
 * Or { scale: [{ value: 0, step: 0 }, {value: 1, step: 0.1 }]}
 * to allow more granular specification of sequence values
 */
export type StyleValueWithSequenceArrays<T> = {
  [ key in keyof T ]: | T[ keyof T ] | (| ({ value: T[ keyof T ], step: number, }))[] | T[ keyof T ][]
}

const isTransform = (prop: string) => transformValue.includes(prop)

// const debug = (msg: any) => {
//   return call([], () => { console.log(msg) })
// }

const isColor = (styleKey: string) => {
  return [
    'backgroundColor',
    'borderBottomColor',
    'borderColor',
    'borderEndColor',
    'borderLeftColor',
    'borderRightColor',
    'borderStartColor',
    'borderTopColor',
    'color',
  ].includes(styleKey)
}

export type StyleValueWithReplacedTransforms<StyleProp> = Omit<
  StyleProp,
  'transform'
> & Partial<Transforms>
type AnimateType = ImageStyle & TextStyle & ViewStyle
type AnimateWithTransitions = StyleValueWithReplacedTransforms<AnimateType>
type Animate = StyleValueWithSequenceArrays<AnimateWithTransitions>

export interface MortyTransitionType {
  duration?: number
  // delay?: number
  easing?: Animated.EasingFunction
  afterEnter?: () => void // enter animation finish hook
  beforeEnter?: () => void // before enter animation running hook
  beforeLeave?: () => void // before leave animation running hook`
  afterLeave?: () => void // leave animation finish hook
}

interface MortyProps {
  transition?: MortyTransitionType
  exitEasing?: Animated.EasingFunction
  beforeExit?: () => void
  afterExit?: () => void
  style?: StyleProp<ViewStyle>
  animate?: Animate
  controller?: ReturnType<typeof useMortyController>
  isPresent: boolean
  isExiting: boolean
  safeToUnmount: SafeToRemove | null | undefined
  exit?: Animate
  reanimatedSafeToUnmount: () => void
  exitDuration?: number
}

enum AnimationState {
  Start = 1,
  Stop = 0,
  UNSET = -1
}


function checkNonDecreasing (arr: number[]) {
  for (let i = 1; i < arr.length; ++i) {
    // We can't validate animated nodes in JS.
    if (arr[ i ] < arr[ i - 1 ]) {
      throw new Error('step must be monotonically non-decreasing !!!!!')
    }
  }
}

function checkNonBiggerThen1 (arr: number[]) {
  for (let i = 1; i < arr.length; ++i) {
    // We can't validate animated nodes in JS.
    if (arr[ i ] > 1) {
      throw new Error('step item muse less or equal than 1 !!!!!')
    }
  }
}

function checkSameLength (step: number[], to: (number | string)[]) {
  if (step.length !== to.length) {
    throw new Error("step's length must equal with animate's length plus from's length")
  }
}


export function useMortyController () {
  const animationState = useValue<AnimationState>(AnimationState.UNSET)
  return useRef({
    animationState,
    enter: () => {
      animationState.setValue(AnimationState.Start)
    },
    leave: () => {
      animationState.setValue(AnimationState.Stop)
    },
    resetState: () => {
      animationState.setValue(AnimationState.UNSET)
    }
  }).current
}

export type createAnimationOptions = {
  duration: number,
  fromValue: Animated.Value<number>,
  toValue: Animated.Value<number>,
  onBegin?: () => void,
  onFinished?: () => void,
  easing?: Animated.EasingFunction
}

class Morty extends React.Component<MortyProps> {
  private exitAnimationValue = new Animated.Value<number>(0)
  private _animationValue = new Animated.Value<number>(0)
  private leaveClock = new Clock()
  private enterClock = new Clock()
  private exitClock = new Clock()

  private interpolateSingleStyle = ({ animationValue, to, step = [ 0, 1 ] }: { animationValue: Animated.Node<number>, to: number[], step: number[] }) => {
    if (step[ 0 ] < 0) {
      throw new Error('step can not less then 0 !!!!!')
    }
    checkNonDecreasing(step)
    checkNonBiggerThen1(step)
    checkSameLength(step, [ ...to ])
    return interpolate(animationValue, {
      inputRange: [ ...step ],
      outputRange: [ ...to ]
    })
  }

  private interpolateSingleColorStyle = ({ animationValue, to, step = [ 0, 1 ] }: { animationValue: Animated.Node<number>, to: string[], step: number[] }) => {
    if (step[ 0 ] < 0) {
      throw new Error('step can not less then 0 !!!!!')
    }
    checkNonDecreasing(step)
    checkNonBiggerThen1(step)
    checkSameLength(step, [ ...to ])
    return interpolateColors(animationValue, {
      inputRange: [ ...step ],
      outputColorRange: [ ...to ]
    })
  }

  private getStepAndToValue = (key: string, animatedValue: any[]) => {
    let step: number[] = []
    let toValue: (number | string)[] = []

    if (animatedValue.every((v: any) => typeof v === 'number') || (animatedValue.every(v => typeof v === 'string') && isColor(key))) {
      step = animatedValue.map((_, i) => i * Number((1 / (animatedValue.length - 1)).toFixed(2)))
      step[ animatedValue.length - 1 ] = 1
      DEBUG && console.log({ 'step🌟🌟🌟🌟🌟🌟🌟🌟🌟': step }, animatedValue)
      toValue = animatedValue
    }

    if (animatedValue.every(v => (typeof v.step === 'number' && typeof v.value === 'number') || (typeof v.step === 'number' && typeof v.value === 'string' && isColor(key)))) {
      step = animatedValue.map(v => v.step)
      toValue = animatedValue.map(v => v.value)
    }

    if (step.length !== toValue.length || step.length < 2 || toValue.length < 2) {
      throw new Error('animate config error!')
    }
    return {
      step,
      toValue
    }
  }


  private enterAnimation = (options: createAnimationOptions) => {
    const {
      duration,
      fromValue = new Animated.Value(0),
      toValue = new Animated.Value(1),
      onBegin = () => { },
      onFinished = () => { },
      easing = Easing.linear
    } = options
    return runTiming({
      clock: this.enterClock,
      from: fromValue,
      to: toValue,
      duration,
      onBegin,
      onFinished,
      easing
    })
  }


  private exitAnimation = (options: createAnimationOptions) => {
    const {
      duration,
      fromValue = new Animated.Value(0),
      toValue = new Animated.Value(1),
      onBegin = () => { },
      onFinished = () => { },
      easing = Easing.linear
    } = options
    return runTiming({
      clock: this.exitClock,
      from: fromValue,
      to: toValue,
      duration,
      onBegin,
      onFinished,
      easing
    })
  }

  private leaveAnimation = (options: createAnimationOptions) => {
    const {
      duration,
      fromValue = new Animated.Value(1),
      toValue = new Animated.Value(0),
      onBegin = () => { },
      onFinished = () => { },
      easing
    } = options
    return runTiming({
      clock: this.leaveClock,
      from: fromValue,
      to: toValue,
      duration,
      onBegin,
      onFinished,
      easing
    })
  }

  private handleExited = () => {
    if (typeof this.props.afterExit === 'function') {
      this.props.afterExit()
    }
    this.props.reanimatedSafeToUnmount()
  }

  private handleBeforeExit = () => {
    this.props.controller && this.props.controller.resetState()
    if (typeof this.props.beforeExit === 'function') {
      this.props.beforeExit()
    }
  }

  private getAnimatedStyle = (finalStyle: StyleValueWithSequenceArrays<AnimateWithTransitions> = {}, animationValue: Animated.Node<number>) => {
    const animatedStyle: any = {}
    const transforms: any[] = []

    Object.keys(finalStyle).forEach((key: keyof StyleValueWithSequenceArrays<AnimateWithTransitions>) => {
      const animatedValue = finalStyle[ key ]
      if (Array.isArray(animatedValue) && animatedValue.length > 1) {
        const { step, toValue } = this.getStepAndToValue(key, animatedValue)
        if (isTransform(key)) {
          transforms.push({ [ key ]: this.interpolateSingleStyle({ animationValue: animationValue, step, to: toValue as number[] }) })
        } else if (isColor(key)) {
          animatedStyle[ key ] = this.interpolateSingleColorStyle({ animationValue: animationValue, step, to: toValue as string[] })
        } else {
          animatedStyle[ key ] = this.interpolateSingleStyle({ animationValue: animationValue, step, to: toValue as number[] })
        }
      } else {
        if (Array.isArray(animatedValue) && animatedValue.length === 1) {
          if (typeof animatedValue[ 0 ] === 'object' && 'step' in animatedValue[ 0 ] && typeof animatedValue[ 0 ]?.step === 'number' && typeof animatedValue[ 0 ]?.value === 'number') {
            if (isTransform(key)) {
              transforms.push({ [ key ]: animatedValue[ 0 ].value })
            } else {
              animatedStyle[ key ] = animatedValue[ 0 ].value
            }
          } else {
            if (isTransform(key)) {
              transforms.push({ [ key ]: animatedValue[ 0 ] })
            } else {
              animatedStyle[ key ] = animatedValue[ 0 ]
            }
          }
        } else if (!Array.isArray(animatedValue)) {
          if (isTransform(key)) {
            transforms.push({ [ key ]: animatedValue })
          } else {
            animatedStyle[ key ] = animatedValue
          }
        }

      }
      if (transforms.length > 0) {
        // @ts-ignore
        animatedStyle.transform = transforms
      }
    })
    return animatedStyle
  }

  getExitStyle = () => {
    const allTransforms: any = {}
    Object.keys(this.props.animate as StyleValueWithSequenceArrays<AnimateWithTransitions> ?? {}).forEach((key: keyof StyleValueWithSequenceArrays<AnimateWithTransitions>) => {
      if (isTransform(key)) {
        const value = (this.props.animate as StyleValueWithSequenceArrays<AnimateWithTransitions>)[ key ]
        if (Array.isArray(value)) {
          if ((value as any).every((v: any) => typeof v === 'number')) {
            allTransforms[ key ] = value[ 0 ]
          }
          if ((value as any).every((v: any) => typeof v.step === 'number' && (typeof v.value === 'string' || typeof v.value === 'number'))) {
            if (typeof value[ 0 ] === 'object' && 'step' in value[ 0 ] && value[ 0 ]?.step === 0) {
              allTransforms[ key ] = value[ 0 ].value
            }
          }
        }
      }
    })

    Object.keys(this.props.exit as StyleValueWithSequenceArrays<AnimateWithTransitions>).forEach(key => {
      if (isTransform(key)) {
        allTransforms[ key ] = (this.props.exit as any)[ key ]
      }
    })

    const style = this.getAnimatedStyle({ ...this.props.exit, ...allTransforms }, this.exitAnimationValue)
    return style
  }

  render () {
    const { isExiting, transition, controller } = this.props
    const style = this.getAnimatedStyle(this.props.animate, this._animationValue)
    const exitStyle = isExiting ? this.getExitStyle() : {}
    const finalStyle = StyleSheet.flatten([ this.props.style, style, exitStyle ])
    return (
      <>
        <Animated.View style={ finalStyle }>
          { this.props.children }
        </Animated.View>
        {
          isExiting && <Animated.Code exec={
            Animated.block([
              cond(clockRunning(this.enterClock), [ stopClock(this.enterClock) ]),
              cond(clockRunning(this.leaveClock), [ stopClock(this.leaveClock) ]),
              set(this._animationValue, 0),
              set(this.exitAnimationValue, this.exitAnimation({
                fromValue: this.exitAnimationValue,
                toValue: new Animated.Value(1),
                duration: this.props.exitDuration || 350,
                easing: this.props.exitEasing || Easing.linear,
                onBegin: this.handleBeforeExit,
                onFinished: this.handleExited
              })),
              DEBUG ? call([ this.exitAnimationValue ], (value) => { console.log(value) }) : 0,
              DEBUG && controller ? call([ controller.animationState ], (value) => { console.log('controller.animationState', value) }) : 0,
            ])
          } />
        }
        {
          !isExiting && controller && <Animated.Code exec={
            Animated.block([
              cond(
                and(eq(controller.animationState, AnimationState.Start), neq(this._animationValue, 1)),
                [
                  cond(clockRunning(this.leaveClock), [ stopClock(this.leaveClock) ], []),
                  set(this._animationValue, this.enterAnimation({
                    fromValue: this._animationValue,
                    toValue: new Animated.Value(1),
                    duration: transition?.duration || 300,
                    easing: transition?.easing,
                    onBegin: transition?.beforeEnter,
                    onFinished: transition?.afterEnter
                  }))
                ]
              ),
              cond(
                and(eq(controller.animationState, AnimationState.Stop), neq(this._animationValue, 0)),
                [
                  cond(clockRunning(this.enterClock), [ stopClock(this.enterClock) ], []),
                  set(this._animationValue, this.leaveAnimation({
                    fromValue: this._animationValue,
                    toValue: new Animated.Value(0),
                    duration: transition?.duration || 300,
                    easing: transition?.easing,
                    onBegin: transition?.beforeLeave,
                    onFinished: transition?.afterLeave
                  }))
                ]
              ),
              DEBUG ? call([ this._animationValue ], (value) => { console.log(value) }) : 0,
              DEBUG ? call([ controller.animationState ], (value) => { console.log('controller.animationState', value) }) : 0,
              cond(eq(controller.animationState, AnimationState.UNSET), [ set(this._animationValue, 0) ]),
            ])
          } />
        }
        {
          !isExiting && !controller && <Animated.Code exec={
            Animated.block([
              cond(clockRunning(this.exitClock), [ stopClock(this.exitClock) ]),
              cond(neq(this.exitAnimationValue, 0), [ set(this.exitAnimationValue, 0) ]),

              set(this._animationValue, this.enterAnimation({
                fromValue: this._animationValue,
                toValue: new Animated.Value(1),
                duration: transition?.duration || 300,
                easing: transition?.easing,
                onBegin: transition?.beforeEnter,
                onFinished: transition?.afterEnter
              })),
              DEBUG ? call([ this._animationValue ], (value) => { console.log(value) }) : 0,
            ])
          } />
        }
      </>
    )
  }
}


const MortyFC: FC<Omit<MortyProps, 'isPresent' | 'safeToUnmount' | 'reanimatedSafeToUnmount' | 'isExiting'>> = (props) => {
  const { exit } = props
  const [ isPresent, safeToUnmount ] = usePresence()
  const hasExitStyle = typeof exit === 'object' && !!Object.keys(exit ?? {}).length
  const isExiting = !isPresent && hasExitStyle
  useEffect(
    function allowUnMountIfMissingExit () {
      if (!isPresent && !hasExitStyle) {
        safeToUnmount?.()
      }
    },
    [ hasExitStyle, isPresent, safeToUnmount ]
  )

  const reanimatedSafeToUnmount = useCallback(() => {
    safeToUnmount?.()
  }, [ safeToUnmount ])

  return <Morty { ...props } isPresent={ isPresent } safeToUnmount={ safeToUnmount } reanimatedSafeToUnmount={ reanimatedSafeToUnmount } isExiting={ isExiting } />
}

export default MortyFC