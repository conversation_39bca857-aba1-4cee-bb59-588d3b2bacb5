{"name": "@xmly/morty", "version": "0.0.1+93108743", "main": "dist/index", "module": "dist/index", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "yarn lint && tsc -p ./tsconfig.json", "dev": "tsc -w", "prepublishOnly": "NODE_ENV=production yarn build", "lint": "node_modules/eslint/bin/eslint.js src"}, "dependencies": {"react": "16.13.1", "react-native": "git+ssh://***********************:react-native-app/xm-react-native-0.56.1.git#19400311", "react-native-reanimated": "1.12.0", "react-native-redash": "9.6.0"}, "author": "luogao <<EMAIL>> (https://lglzy.cn)", "license": "MIT", "yalcSig": "9310874323b900032b26c8953e81d26a"}