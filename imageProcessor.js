const fs = require('fs')
const path = require('path')
const rnDevConfig = require('./rnDevConfig')

const outputFolderName =
  rnDevConfig && rnDevConfig.imageGenOutputFolderName
    ? rnDevConfig.imageGenOutputFolderName
    : 'appImages'

const projectImagesPath = path.resolve(
  path.join(
    process.cwd(),
    rnDevConfig && rnDevConfig.projectImagesPath
      ? `src/${rnDevConfig.projectImagesPath}`
      : 'src/images'
  )
)

const outputDir = path.resolve(
  path.join(process.cwd(), `src/${outputFolderName}`)
)

const makeModulePKG = () => {
  const modulePKGPath = path.join(outputDir, 'package.json')
  const content = `{ "name": "${outputFolderName}" }`
  fs.writeFileSync(modulePKGPath, content)
}

const readDir = (path) => {
  return new Promise((resolve, reject) => {
    fs.readdir(path, (err, data) => {
      if (err) {
        return reject(err)
      }
      resolve(data)
    })
  })
}

function isAssetTypeAnImage(type) {
  return (
    ['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'].indexOf(
      type
    ) !== -1
  )
}

const imageFileNameWithoutScale = (imageName) =>
  imageName.replace('@3x', '').replace('@2x', '')

const imageFileFormatter = (imageName) =>
  imageFileNameWithoutScale(
    imageName.replace(path.extname(imageName), '').replace(/-/g, '_')
  )

function getImageFileName(imageName) {
  const fileName = imageFileFormatter(imageName) + '.ts'
  return path.join(outputDir, fileName)
}

function getImageJSFileData(imageName) {
  const imageFileName = imageFileFormatter(imageName)
  return `
    const ${imageFileName} = require('../${
    rnDevConfig.projectImagesPath || 'images'
  }/${imageFileNameWithoutScale(imageName)}')
    export default ${imageFileName}
  `
}

const run = async () => {
  if (!fs.existsSync(outputDir)) {
    // 如果文件夹不存在，创建文件夹
    fs.mkdirSync(outputDir)
  } else {
    // 如果文件夹存在，删除整个文件夹再创建文件夹
    fs.rmdirSync(outputDir, { recursive: true })
    fs.mkdirSync(outputDir)
  }
  try {
    const data = await readDir(projectImagesPath)

    data.forEach((imageName, index) => {
      if (isAssetTypeAnImage(path.extname(imageName).slice(1))) {
        const imageFileFullPath = getImageFileName(imageName)
        fs.writeFileSync(imageFileFullPath, getImageJSFileData(imageName))
      }
    })
    makeModulePKG()
  } catch (err) {
    console.log(err)
  }
}

run()
